<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = (ENVIRONMENT==='development') ? 'welcome' : 'auth/index_v2';
$route['404_override'] = 'template/error_404/index';
$route['translate_uri_dashes'] = TRUE;


# AUTH
if (ENVIRONMENT==='development') {
	// auth for development
	$route['login']						= 'welcome';

	//v1 (localhost)
	// $route['login_process']				= 'users/login/login_proses';

	//v2
	// if (!empty($_ENV['APP_URL'])) {
		$route['login_process']			= 'auth/v2/auth_login/login_process';
	// }
}else{
	// auth for staging + production
	$route['login']						= 'auth/index_v2';
	$route['login_process']				= 'auth/login_process';
}
$route['forgot']						= 'auth/forgot';
$route['forgot_process']				= 'users/forgot/forgot_process';
$route['logout']['DELETE']				= 'auth/logout/logout_ajax';


//MULTIPLE BUSINESS
//if (ENVIRONMENT != 'production') {
	$route['login_process']			= 'auth/v2/auth_login/login_process';

	# authentication (testing)
	$route['checkpoint']['GET']			= 'auth/v2/auth_login/checkpoint_verify_token';
	$route['checkpoint']['POST']		= 'auth/v2/auth_login/checkpoint_create_session';
//}


//DISABLE DIRECT MODULE ACCESS
$route['welcome(.*)']					= 'error404';
$route['auth/(.*)']						= 'error404';
$route['production_new(.*)']			= 'error404';
$route['settings_user(.*)']				= 'error404';
$route['outlet_list(.*)']				= 'error404';
$route['business_connect(.*)']			= 'error404';
$route['business_resetdata(.*)']		= 'error404';
$route['crm_promotion(.*)']				= 'error404';
$route['crm_productdescription(.*)']	= 'error404';
$route['crm_banner(.*)']				= 'error404';
$route['crm_transaction(.*)']			= 'error404';
$route['product_catalogue_exportimport(.*)'] = 'error404';




// $route['register']					= 'users/register';
// $route['register/(:any)']			= 'users/register/$1';
// $route['activation/create/(:any)']	= 'welcome/user_key/create_activation_key/$1';
// $route['activation/(:any)/(:any)']	= 'welcome/user_key/activation/$1/$2';
// $route['activation/add']				= 'welcome/user_key/add';
// $route['activation/create/(:any)']	= 'welcome/confirmation/user_activation/create_activation_key/$1';


$route['confirmation/user-activation/key-verify/(:any)/(:any)']	= 'users/confirmation/user_activation/invitation/$1/$2';
$route['confirmation/user-activation/key-verify/(:any)/(:any)/activation_process']	= 'users/confirmation/user_activation/activation_process';


//social connect
$route['activation/business_connect/(:any)/(:any)'] = 'business_connect/connect/email_confirmation/$1/$2';

//USER ACTIVATION NEW
$route['activation/(:any)/admin/(:any)'] = 'users/user_activation/index/admin/$1/$2';
$route['activation/(:any)/admin/(:any)/activation_process'] = 'users/user_activation/activation_process';


$route['confirmation/change_email/(:any)/(:any)']	= 'users/confirmation/change_email/confirm/$1/$2';
$route['confirmation/resetdata/(:any)/(:any)']		= 'users/confirmation/reset_data/index/$1/$2';
$route['confirmation/resetpass/(:any)/(:any)']		= 'users/confirmation/reset_password/verifikasi/$1/$2'; 

//if (ENVIRONMENT !== 'production') {
	//konfirmasi pergantian email
	$route['confirmation/change_email/(:any)/(:any)']	= 'auth/v2/user_change_email/redirect_old/$1/$2'; //redirect old link format
	$route['confirmation/change_email']['GET']			= 'auth/v2/user_change_email/confirmation_page';
	$route['confirmation/change_email']['POST']			= 'auth/v2/user_change_email/change_email_process';

	//employee activation [UNDER DEVELOPMENT]
	$route['confirmation/user-activation/key-verify/(:any)/(:any)']	= 'auth/v2/user_activation/employee_activation_redirection/$1/$2';
	$route['confirmation/activation']['GET']						= 'auth/v2/user_activation/employee_activation_page';
	$route['confirmation/activation']['POST']						= 'auth/v2/user_activation/employee_activation_process';
//}


# SETTINGS
$route['settings']										= 'settings/settings_menu';//account menu

# switch account
$route['settings/account/switch']['GET']				= 'settings_user/Account_switch/index';
$route['settings/account/switch']['POST']				= 'settings_user/Account_switch/process';
$route['settings/account(.*)']							= 'settings_user/account$1';
$route['settings/business']								= 'navigation/lv2';
$route['settings/business/profile(.*)']					= 'settings_user/business$1';
$route['settings/business/report_notifications(.*)']	= 'business_reportnotif/report_notifications$1';
$route['settings/business/connect']						= 'business_connect/connect/index_v2';
// $route['settings/business/connect(.*)']	= 'business_connect/connect$1';

# BUSINESS CONNECT API
if (file_exists(APPPATH.'modules/business_connect/config/routes.php')) {
	include_once(APPPATH.'modules/business_connect/config/routes.php');
} else {
	$route['api/v1/business_connect/datatables']['POST']	= 'business_connect/social_connect_api/datatables';
	$route['api/v1/business_connect/email_add']['POST']		= 'business_connect/social_connect_api/email_add';
	$route['api/v1/business_connect/email_confirm']['POST'] = 'business_connect/social_connect_api/email_confirm';
	$route['api/v1/business_connect/resend_email_verification/(:any)']['POST'] = 'business_connect/social_connect_api/resend_email_verification/$1';
	$route['api/v1/business_connect/delete/(:any)']['DELETE']= 'business_connect/social_connect_api/delete/$1';
	$route['api/v1/business_connect/check_allow_add/(:any)']['GET']	= 'business_connect/social_connect_api/check_allow_add/$1';


	$route['api/v1/business_connect/whatsapp/scan']['GET'] = 'business_connect/whatsapp_api/scan';
	$route['api/v1/business_connect/whatsapp/scan_status']['POST'] = 'business_connect/whatsapp_api/scan_status';
	$route['api/v1/business_connect/whatsapp/status/(:any)']['GET'] = 'business_connect/whatsapp_api/status/$1';
	$route['api/v1/business_connect/whatsapp/logout/(:any)']['DELETE'] = 'business_connect/whatsapp_api/logout/$1';
}


# SETTINGS RESET DATA V2
$route['settings/business/resetdata']	= 'business_resetdata/resetdata_page';
$route['confirmation/resetdata/sales_transaction'] = 'business_resetdata/transaction/transaction_process_token';
$route['confirmation/resetdata/sales_transaction/(:any)/(:any)'] = 'business_resetdata/transaction/transaction_process/$1/$2';

//****** SETTINGS RESET DATA API
$route['api/v1/business_reset/transaction'] = 'business_resetdata/transaction/request_reset_link';


# BUSINESS SYNC REPORT DATA
if (file_exists(APPPATH.'modules/business_sync_report/config/routes.php')) {
	include_once(APPPATH.'modules/business_sync_report/config/routes.php');
}




#API REPORT
$route['api/v1/auth/token_report']['OPTIONS'] = 'auth/auth_report/refresh_token';
$route['api/v1/auth/api-web-refreshtoken']['get'] = 'auth/auth_api/refresh_token_api_web';
// $route['api/auth/token'] = function()
// {
// 	echo json_encode([
// 		'access_token' => 'ini token yang baru',
// 		'refresh_token' => 'refresh token yang baru'
// 	]);
// 	die();
// };


# DASHBOARD
$route['dashboard'] = 'dashboard/dashboard';

# REPORTS
$route['reports']					= 'reports/reports_menu';								//report menu
// $route['reports/report_cogs']		= 'cogs/report_cogs';		
// $route['reports/report_cogs']		= 'reports/report_cogs';								//report menu
$route['reports/stock']				= 'reports/stock_submenu';								//report - Stock submenu

# REPORT STOCK CARD
if (file_exists(APPPATH.'modules/report_stockcard/config/routes.php')) {
	include_once(APPPATH.'modules/report_stockcard/config/routes.php');
}else{
	$route['reports/stock/card'] 			= 'report_stockcard/stock_card/index_v3';
	$route['reports/stock/card_v3'] 			= 'report_stockcard/stock_card/index_v3';
	$route['api/v1/report-stock-card']['POST']	= 'report_stockcard/stock_card_api/v1';
	$route['api/v2/report-stock-card']['POST']	= 'report_stockcard/stock_card_api/v2';
}


if (ENVIRONMENT!='production') {
	$route['reports/purchase']							= 'navigation/lv2';

	//transfer
	$route['report_transfer(.*)']						= 'error404';
	$route['reports/purchase/transfer-mutation']		= 'report_transfer/report_transfer_page';
	$route['api/v1/report-transfer/datatables']['POST'] = 'report_transfer/report_transfer_api/v1_datatables';
	$route['api/v1/report-transfer/datatables-footer']['POST'] = 'report_transfer/report_transfer_api/v1_datatables_footer';

}

# REPORT COMMISSION
//komisi sales
$route['report_sales_commission(.*)']						= 'error404';
$route['reports/sales-commission']							= 'report_sales_commission/report_sales_commission_page/index';
$route['api/v1/report-sales-commission/datatables']['POST'] = 'report_sales_commission/report_commission_api/v2_datatables';
$route['api/v1/report-sales-commission']['GET']				= 'report_sales_commission/report_commission_api/v2_datatables';
$route['api/v1/report-sales-commission/export']['GET'] 		= 'report_sales_commission/report_commission_api/v1_export';
// $route['api/v1/report-sales-commission/export']['POST']		= 'report_sales_commission/report_commission_api/v1_export';

//komisi outlet
if (file_exists(APPPATH.'modules/report_sales_commission/config/routes.php')) {
	include_once(APPPATH.'modules/report_sales_commission/config/routes.php');
}


# PRODUCTS
$route['products']											= 'navigation/lv1';				//product menu

//PRODUCT CATEGORIES
if (ENVIRONMENT!='production') {
	$route['products/type']	= 'product_categories/render_page/type';
	$route['products/category'] = 'product_categories/render_page/category';
	$route['products/subcategory'] = 'product_categories/render_page/subcategory';
	$route['products/purchase-report-category'] = 'product_categories/render_page/purchase_report_category';
	$route['products/purchase_report_category'] = 'product_categories/render_page/purchase_report_category';
	$route['products/unit']	= 'product_categories/render_page/unit';
}

# API PRODUCTS CATEGORIES
$route['api/v1/product_type/datatables']				= 'product_categories/type_api/datatables';
$route['api/v1/product_type/create']					= 'product_categories/type_api/create';
$route['api/v1/product_type/get/(:any)']				= 'product_categories/type_api/get/$1';
$route['api/v1/product_type/update/(:any)']				= 'product_categories/type_api/update/$1';
$route['api/v1/product_type/delete/(:any)']['DELETE']	= 'product_categories/type_api/delete/$1';

$route['api/v1/product_category/datatables']				= 'product_categories/category_api/datatables';
$route['api/v1/product_category/create']					= 'product_categories/category_api/create';
$route['api/v1/product_category/get/(:any)']				= 'product_categories/category_api/get/$1';
$route['api/v1/product_category/update/(:any)']				= 'product_categories/category_api/update/$1';
$route['api/v1/product_category/delete/(:any)']['DELETE']	= 'product_categories/category_api/delete/$1';

$route['api/v1/product_subcategory/datatables']					= 'product_categories/subcategory_api/datatables';
$route['api/v1/product_subcategory/create']						= 'product_categories/subcategory_api/create';
$route['api/v1/product_subcategory/get/(:any)']					= 'product_categories/subcategory_api/get/$1';
$route['api/v1/product_subcategory/update/(:any)']				= 'product_categories/subcategory_api/update/$1';
$route['api/v1/product_subcategory/delete/(:any)']['DELETE']	= 'product_categories/subcategory_api/delete/$1';

$route['api/v1/purchase_report_category/datatables']				= 'product_categories/purchase_report_category_api/datatables';
$route['api/v1/purchase_report_category/create']					= 'product_categories/purchase_report_category_api/create';
$route['api/v1/purchase_report_category/get/(:any)']				= 'product_categories/purchase_report_category_api/get/$1';
$route['api/v1/purchase_report_category/update/(:any)']				= 'product_categories/purchase_report_category_api/update/$1';
$route['api/v1/purchase_report_category/delete/(:any)']['DELETE']	= 'product_categories/purchase_report_category_api/delete/$1';

$route['api/v1/unit/datatables']				= 'product_categories/unit_api/datatables';
$route['api/v1/unit/create']					= 'product_categories/unit_api/create';
$route['api/v1/unit/get/(:any)']				= 'product_categories/unit_api/get/$1';
$route['api/v1/unit/update/(:any)']				= 'product_categories/unit_api/update/$1';
$route['api/v1/unit/delete/(:any)']['DELETE']	= 'product_categories/unit_api/delete/$1';

$route['api/v1/taxgratuity/datatables']			= 'product_taxgratuity/taxgratuity_render/datatables';

# PRODUCT RENDER PAGE
// if (ENVIRONMENT !=='production') {
	$route['products/type/v2']						= 'product_categories/render_page_v2/type';
	$route['products/category/v2']					= 'product_categories/render_page_v2/category';
	$route['products/subcategory/v2']				= 'product_categories/render_page_v2/subcategory';
	$route['products/purchase-report-category/v2']	= 'product_categories/render_page_v2/purchase_report_category';
	$route['products/unit/v2']						= 'product_categories/render_page_v2/unit';
	$route['products/taxgratuity/v2']				= 'product_taxgratuity/taxgratuity_render/index';

	$route['products/type']						= 'product_categories/render_page_v2/type';
	$route['products/category']					= 'product_categories/render_page_v2/category';
	$route['products/subcategory']				= 'product_categories/render_page_v2/subcategory';
	$route['products/purchase-report-category']	= 'product_categories/render_page_v2/purchase_report_category';
	$route['products/unit']						= 'product_categories/render_page_v2/unit';
	// $route['products/taxgratuity']				= 'product_taxgratuity/taxgratuity_render/index';
	$route['products/taxgratuity']				= 'product_categories/render_page_v2/taxgratuity';
// }



/* PRODUCT CATALOGUE EXPORT IMPORT */
//CLIENT
$route['products/product-catalogue/export_process']			= 'products/products/product_export/export';
$route['products/product-catalogue/import/(:any)']			= 'products/product_catalogue_menu/product_import/$1';

//POS
$route['products/catalogue/export_process']					= 'products/product_catalogue_menu/product_export/export';
$route['products/catalogue/import-product/(:any)']			= 'products/product_catalogue_menu/product_import_new/$1';
#---- EXPORT/IMPORT END




#============================================================================================================
# NEW PRODUCT CATALOGUE
#============================================================================================================
//v2
$route['products/catalogue_v2'] = 'products/catalogue';
$route['products/catalogue_v2/(.*)'] = 'products/catalogue/$1';

//v3
$route['products/catalogue_v3'] = 'product_catalogue/catalogue_page';

//current
$route['products/catalogue'] = 'product_catalogue/catalogue_page';
$route['api/v1/product_unit_conversion/(:any)']['POST'] = 'product_catalogue/api_v1/catalogue_api/update_unit_conversion/$1';


# Product catalogue modal export/import
if (file_exists(APPPATH.'modules/product_catalogue_exportimport/config/routes.php')) {
	include_once(APPPATH.'modules/product_catalogue_exportimport/config/routes.php');
}else{
	$route['products_catalogue/modal_exportimport'] = 'product_catalogue_exportimport/modal_export/modal_v2';
	$route['products/catalogue/modal_exportimport'] = 'product_catalogue_exportimport/modal_export/modal_v2';
}


# PRODUCT CATALOGUE EXPORT
$route['products/catalogue/v1/export']['POST']		= 'products/product_catalogue_menu/product_export/export';
$route['products/catalogue/v2/export']['POST']		= 'product_catalogue_exportimport/v2/export';

# PRODUCT CATALOGUE IMPORT
$route['products/catalogue/v1/import_template']		= 'products/product_catalogue_menu/product_import_new/import_template';
$route['products/catalogue/v1/import']['POST']		= 'products/product_catalogue_menu/product_import_new/import_process';
$route['products/catalogue/v2/import_template']		= 'product_catalogue_exportimport/v2/import/template';
$route['products/catalogue/v2/import']['POST']		= 'product_catalogue_exportimport/v2/import/process';

# PRODUCT CATALOGUE CREATE
$route['products/catalogue/create'] = 'products/catalogue/create_v2';


// $route['api/v1/product_catalogue/datatables']['POST']	= 'products/catalogue/datatables';
$route['api/v1/product_catalogue/datatables']['POST']	= 'product_catalogue/api_v1/datatables_api/catalogue';
$route['api/v1/product_catalogue/create']['POST']		= 'product_catalogue/api_v1/catalogue_api/create';
$route['api/v1/product_catalogue/get/(:any)']['GET']	= 'product_catalogue/api_v1/catalogue_api/get/$1';
$route['api/v1/product_catalogue/product_detail/(:any)']['get'] = 'product_catalogue/api_v1/catalogue_api/get_by_product_detail_id/$1';
$route['api/v1/product_catalogue/get_detail/(:any)/(:any)']['GET']	= 'product_catalogue/api_v1/catalogue_api/get_detail/$1/$2';
$route['api/v1/product_catalogue/get_detail/(:any)/(:any)/(:any)']['GET']	= 'product_catalogue/api_v1/catalogue_api/get_detail/$1/$2/$3';
$route['api/v1/product_catalogue/update/(:any)']['POST']= 'product_catalogue/api_v1/catalogue_api/update/$1';
$route['api/v1/product_catalogue/update/(:any)']['PUT']	= 'product_catalogue/api_v1/catalogue_api/update/$1';
$route['api/v1/product_catalogue/update_detail/(:any)']['POST']	= 'product_catalogue/api_v1/catalogue_api/update_detail/$1';
$route['api/v1/product_catalogue/delete/(:any)']['DELETE']			= 'product_catalogue/api_v1/catalogue_api/delete/$1';
$route['api/v1/product_catalogue/delete/(:any)/soft']['DELETE']	= 'product_catalogue/api_v1/catalogue_api/delete_soft/$1';


# PRODUCT LINKMENU API
$route['api/v1/product_link/list']['GET'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_list/$1/$2/$3';


$route['api/v1/product_link']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_create';
$route['api/v1/product_link/']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_create';
$route['api/v1/product_link/(:any)']['GET'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu/$1';
$route['api/v1/product_link/(:any)']['PUT'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_update/$1';
$route['api/v1/product_link/(:any)']['DELETE'] = 'product_catalogue/api_v1/linkmenu_api/delete_linkmenu/$1';
$route['api/v1/product_link/(:any)/detail']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_list_detail_add/$1';
$route['api/v1/product_link/(:any)/detail']['GET'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_list_detail/$1';
$route['api/v1/product_link/(:any)/detail']['PUT'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_detail_update/$1';
$route['api/v1/product_link/(:any)/detail']['DELETE'] = 'product_catalogue/api_v1/linkmenu_api/delete_linkmenu_detail/$1';
$route['api/v1/product_link/(:any)/copy']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_detail_copy/$1';




// $route['api/v1/product_link/list/(:any)/(:any)']['GET'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_list/$1/$2/';
// $route['api/v1/product_link/list/(:any)/(:any)/(:any)']['GET'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_list/$1/$2/$3';
// $route['api/v1/product_link/detail/(:any)']['GET'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_list_detail/$1';
// $route['api/v1/product_link/detail/(:any)']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_list_detail_add/$1';
// $route['api/v1/product_link/detail']['DELETE'] = 'product_catalogue/api_v1/linkmenu_api/delete_linkmenu_detail';
// $route['api/v1/product_link/link/(:any)']['GET'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu/$1';
// $route['api/v1/product_link/create']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_create';
// $route['api/v1/product_link/update/(:any)']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_update/$1';
// $route['api/v1/product_link/update/(:any)']['PUT'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_update/$1';
// $route['api/v1/product_link/update_detail/(:any)']['PUT'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_detail_update/$1';
// $route['api/v1/product_link/copylink/(:any)']['POST'] = 'product_catalogue/api_v1/linkmenu_api/linkmenu_detail_copy/$1';







/* LINKMENU */
//OLD
$route['products/product-catalogue/import-linkmenu/(:any)']			= 'products/product_catalogue_menu/linkmenu_import/$1';
$route['products/product-catalogue/import-multipleprice/(:any)']	= 'products/product_catalogue_menu/product_multipleprice_import/$1';

//NEW
$route['products/catalogue/linkmenu/add_linkmenu_detail/(:any)'] = 'products/product_catalogue_menu/linkmenu/add_linkmenu_detail/$1';
$route['products/catalogue/linkmenu/(:any)']				= 'products/product_catalogue_menu/linkmenu/$1';
$route['products/catalogue/linkmenu/(:any)/(:any)']			= 'products/product_catalogue_menu/linkmenu/$1/$2';
$route['products/catalogue/linkmenu/(:any)/(:any)/(:any)']	= 'products/product_catalogue_menu/linkmenu/$1/$2/$3';
$route['products/catalogue/linkmenu-import/(:any)']			= 'products/product_catalogue_menu/linkmenu_import_new/$1';
#---- LINKMENU END

#---- MULTIPLEPRICE IMPORT pos START
$route['products/catalogue/multipleprice-import/(:any)']	= 'products/product_catalogue_menu/multipleprice_import_new/$1';
#---- MULTIPLEPRICE IMPORT pos END


#=====================================
# NEW LINKMENU
#=====================================
if (file_exists(APPPATH.'modules/product_linkmenu/config/routes.php')) {
	include_once(APPPATH.'modules/product_linkmenu/config/routes.php');
} else {
	$route['products/linkmenu/get_list/(:any)']					= 'product_linkmenu/linkmenu/get_list/$1';
	$route['products/linkmenu/copylink']['POST']				= 'product_linkmenu/linkmenu/copylink';
	$route['products/linkmenu/delete_mass']['POST']				= 'product_linkmenu/linkmenu/delete_mass';
}



#-- BREAKDOWN
if (file_exists(APPPATH.'modules/product_breakdown/config/routes.php')) {
	include_once(APPPATH.'modules/product_breakdown/config/routes.php');
}else{
	// $route['products/breakdown/import/(:any)']				= 'products/breakdown_menu/import_breakdown/$1';
	// $route['products/breakdown_new/import/(:any)']			= 'products/breakdown_menu/import_breakdown_new/$1'; //NEW BREAKDOWN
	// $route['products/breakdown_new/import/(:any)/(:any)']	= 'products/breakdown_menu/import_breakdown_new/$1/$2'; //NEW BREAKDOWN
	$route['products/breakdown/export/(:any)/(:any)']		= 'product_breakdown/export_breakdown/$1/$2';
	// $route['products/breakdown/import/(:any)']				= 'products/breakdown_menu/import_breakdown_new/$1';
	// $route['products/breakdown/import/(:any)/(:any)']		= 'products/breakdown_menu/import_breakdown_new/$1/$2';
	// $route['products/breakdown(.*)']						= 'products/breakdown_new$1';


	$route['products/breakdown/import/(:any)']				= 'product_breakdown/Import_breakdown/$1';
	$route['products/breakdown/import/(:any)/(:any)']		= 'product_breakdown/Import_breakdown/$1/$2';
	$route['products/breakdown']							= 'product_breakdown/Breakdown/index';
	$route['products/breakdown(.*)']						= 'product_breakdown/Breakdown$1';
}



#-- ACCOUNTS
$route['products/accounts']							= 'navigation/lv2';
$route['products/accounts/(:any)']					= 'finance/$1';
$route['products/accounts/(:any)/(:any)']			= 'finance/$1/$2';
$route['products/accounts/(:any)/(:any)/(:any)']	= 'finance/$1/$2/$3';



# PURCHASE
if (file_exists(APPPATH.'modules/purchasing/config/routes.php')) {
	include_once(APPPATH.'modules/purchasing/config/routes.php');
}else{
	$route['purchase']					= 'navigation/lv1';
	$route['purchase/purchase']			= 'navigation/lv2';
	$route['purchase/purchase_detail']	= 'navigation/lv2';
	$route['purchase/debt']				= 'navigation/lv2';
	$route['purchase/purchase_order']	= 'purchase_order';
	$route['purchase/purchase_import']	= 'purchase_export';
	$route['purchase/purchasing_product']	= 'purchasing/purchasing_product';
	$route['purchase/confirm_detail']	= 'purchasing/Purchase_confrim';
	$route['purchase/purchasing_product/edit/(:any)']	= 'purchasing/purchasing_product/edit/$1';
}

# OPERATIONAL COST
if (file_exists(APPPATH.'modules/operational_cost/config/routes.php')) {
	include_once(APPPATH.'modules/operational_cost/config/routes.php');
}
# debt
if (file_exists(APPPATH.'modules/purchase_debt/config/routes.php')) {
	include_once(APPPATH.'modules/purchase_debt/config/routes.php');
}

# PURCHASE TRANSFER
if (file_exists(APPPATH.'modules/transfer/config/routes.php')) {
	include_once(APPPATH.'modules/transfer/config/routes.php');
}else{
	$route['purchase/transfer']	= 'transfer/purchase_transfer';
	$route['purchase/transfer/edit/(:any)']	= 'transfer/purchase_transfer/edit/$1';
}

# OPERATIONAL COST
if (file_exists(APPPATH.'modules/operational_cost/config/routes.php')) {
	include_once(APPPATH.'modules/operational_cost/config/routes.php');
}


# MAINTENANCE
if (ENVIRONMENT!='production') {
	$route['purchase/maintenance(.*)']			= 'maintenance$1';
}else{
	$route['maintenance(.*)']					= 'error404';
}



# STOCK
$route['stock']								= 'stock/stock_menu';									//stock menu					

# stock estimation
if (file_exists(APPPATH.'modules/stock_estimation/config/routes.php')) {
	include_once(APPPATH.'modules/stock_estimation/config/routes.php');
}else{
	$route['stock/estimation']					= 'stock_estimation/day_category';
	$route['stock/estimation/(:any)']			= 'stock_estimation/day_category/$1';
	$route['stock/estimation/(:any)/(:any)']	= 'stock_estimation/day_category/$1/$2';
	$route['stock/estimation/(:any)/(:any)/(:any)']	= 'stock_estimation/day_category/$1/$2/$3';
}


$route['stock/opname']						= 'navigation/lv2';
$route['stock/opname/opname_inventory']		= 'stock_opname/opname_inventory';
$route['stock/opname/stock_opname']			= 'stock_opname/stock_opname/stock_opname_page';
$route['stock/opname/equipment_inventory']	= 'stock_opname/stock_opname/equpment_inventory';
$route['stock/spoil']						= 'navigation/lv2';
if (file_exists(APPPATH.'modules/stock_opname/config/routes.php')) {
	include_once(APPPATH.'modules/stock_opname/config/routes.php');
}else{
	$route['stock/stock_opname']				= 'stock_opname/stock_opname';
}




$route['stock/summary']						= 'navigation/lv2';
/* V1
$route['stock/summary/products_ingredients']= 'stock/summary/stock_summary';
$route['stock/summary/products_ingredients/(:any)/(:any)/(:any)/(:any)/(:any)/(:any)']	= 'stock/summary/stock_summary/$1/$2/$3/$4/$5/$6';
$route['stock/summary/equipments']			= 'stock/summary/summary_equipment';
$route['stock/summary/equipments/(:any)']	= 'stock/summary/summary_equipment/$1';
*/

// V2
$route['stock/summary/products_ingredients']= 'stock_summary/stock_summary';
$route['stock/summary/products_ingredients/(:any)/(:any)/(:any)/(:any)/(:any)/(:any)/(:any)']	= 'stock_summary/stock_summary/$1/$2/$3/$4/$5/$6/$7';
$route['stock/summary/equipments']			= 'stock_summary/summary_equipment';
$route['stock/summary/equipments/(:any)']	= 'stock_summary/summary_equipment/$1';


# OUTLETS
$route['outlets']					= 'navigation/lv1';								//outlet menu

# OUTLET LIST
if (file_exists(APPPATH.'modules/outlet_list/config/routes.php')) {
	include_once(APPPATH.'modules/outlet_list/config/routes.php');
} else {
	$route['outlets/outletlist(.*)']	= 'outlet_list/outletlist$1';
}


# OUTLET PRINTER
if (file_exists(APPPATH.'modules/outlet_printer/config/routes.php')) {
	include_once(APPPATH.'modules/outlet_printer/config/routes.php');
} else {
	if (ENVIRONMENT=='development') {
		$route['outlets/printer/v3']		= 'outlet_printer/printer';
	}
}

# OUTLET DEVICE
if (file_exists(APPPATH.'modules/devices/config/routes.php')) {
	include_once(APPPATH.'modules/devices/config/routes.php');
} else {
	$route['outlets/devices'] = 'devices/devices';
	$route['outlets/devices/json'] = 'devices/devices/json';
	$route['outlets/devices/delete/(:any)'] = 'devices/devices/delete/$1';
	$route['outlets/devices/update'] = 'devices/devices/update';
	$route['outlets/devices/get_active_slot'] = 'devices/devices/get_active_slot';
	$route['outlets/devices/get_data/(:any)'] = 'devices/devices/get_data/$1';
	$route['outlets/devices/logout/(:any)'] = 'devices/devices/logout/$1';
}




$route['employees']					= 'navigation/lv1';							//employee menu

if (file_exists(APPPATH.'modules/employee_module/config/routes.php')) {
	include_once(APPPATH.'modules/employee_module/config/routes.php');
}else{
	// MULTIPLE BUSINESS
	//if (ENVIRONMENT != 'production') {
		$route['employees/crewlist']	= 'employee_module/crewlist/index_v2';
		$route['employees/jabatan']		= 'employee_module/position/index_v2';
		$route['employees/crewlist/create']['post'] = 'employee_module/crewlist_api/create';

		# if (ENVIRONMENT == 'development') {
		# 	$route['employees/crewlist/update']['post'] = 'employee_module/crewlist_api/update';
		# }
	//}

	$route['employees/crewlist(.*)']	= 'employee_module/crewlist$1';
	$route['employees/jabatan(.*)']		= 'employee_module/position$1';
}

$route['employees/sallary']			= 'employees/sallary/sallary_submenu';					//employee submenu
$route['employees/internalmemo']	= 'employees/internalmemo/internalmemo_submenu';		//employee submenu
$route['customer(.*)']				= 'error404';								//customer menu
$route['promotion(.*)']				= 'error404';							//promotion menu
$route['sales']						= 'error404';									//sales menu

# PRODUCTION ========================================================================
/*
$route['production']				= 'navigation/lv1';							//production menu
// if (ENVIRONMENT==='development') {
	$route['production/itembreakdown']['GET'] = 'production_new/v3/item_breakdown_v3';
	$route['production/itembreakdown/v3']['GET'] = 'production_new/v3/item_breakdown_v3';
	$route['production/itembreakdown/v3']['POST'] = 'production_new/v3/item_breakdown_v3/create';
	$route['production/itembreakdown/update_detail_all/(:any)/(:any)']['POST'] = 'production_new/v3/item_breakdown_v3/update_detail_all/$1/$2';
// }
$route['production/itembreakdown/v2']['GET']	= 'production_new/itembreakdown';
$route['production/(.*)']			= 'production_new/$1';
$route['api/production/export']['GET'] = 'production_new/api_production/export';
// $route['api/production/query']['GET'] = 'production_new/api_production/query';
*/
if (file_exists(APPPATH.'modules/production_new/config/routes.php')) {
	include_once(APPPATH.'modules/production_new/config/routes.php');
}


# FINANCE ===========================================================================
$route['finance']					= 'navigation/lv1';								//finance menu

# CRM ===============================================================================
$route['crm']						= 'navigation/lv1';
$route['crm/member']				= 'navigation/lv2';
$route['crm/member/(.*)']			= 'crm_customer/member/$1';

$route['crm/customer']				= 'navigation/lv2';
$route['crm/customer/lists(.*)']	= 'crm_customer/customerlist/customerlist$1';
$route['crm/customer/complainfeedback(.*)'] = 'crm_customer/customerlist/complainfeedback$1';
$route['crm/customer/campaign(.*)'] = 'crm_customer/crm/campaign$1';


# CRM APPS
$route['crm/apps']							= 'navigation/lv2';
//v1
$route['crm/apps/product-description(.*)']	= 'crm_productdescription/product_description$1';
$route['api/v1/apps/product-description/(:any)']['GET'] = 'crm_productdescription/product_description_api_v1/get/$1';
$route['api/v1/apps/product-description/(:any)']['PUT'] = 'crm_productdescription/product_description_api_v1/update/$1';

//v2
$route['crm/apps/product-setting(.*)']				= 'crm_productdescription/product_description$1';
$route['api/v1/apps/product-setting/(:any)']['GET'] = 'crm_productdescription/product_description_api_v1/get/$1';
$route['api/v1/apps/product-setting/(:any)']['PUT'] = 'crm_productdescription/product_description_api_v1/update/$1';

$route['crm/apps/application-setting(.*)'] = "crm_productdescription/application_setting$1";

# CRM BANNER
$route['crm/apps/banner'] = 'crm_banner/banner';
$route['crm/apps/banner/datatable'] = 'crm_banner/banner/datatable';
$route['crm/apps/banner/create'] = 'crm_banner/banner/create';
$route['crm/apps/banner/delete/(:any)'] = 'crm_banner/banner/delete/$1';
$route['crm/apps/banner/get_by_id/(:any)'] = 'crm_banner/banner/get_banner_by_id/$1';

# CRM PROMOTION (MODULE)
$route['crm/promotion']						= 'navigation/lv2';
$route['crm/promotion/specialprice(.*)']	= 'crm_promotion/promotions/special_price$1';
$route['crm/promotion/(.*)']				= 'crm_promotion/promotions/$1';
$route['crm/promotion_ajax/(.*)']			= 'crm_promotion/promotions_ajax/$1';

# CRM TRANSACTION [UNDER DEVELOPMENT]
/*
if (ENVIRONMENT == 'development') {
	$route['crm/transaction']								= 'navigation/lv2';
	$route['crm/transaction/(:any)']						= 'crm_transaction/page/$1';
	$route['api/crm-transaction/datatable']['POST']			= 'crm_transaction/api_datatable/transaction_list';
	$route['api/crm-transaction']['GET']					= 'crm_transaction/api_transaction/get/';
	$route['api/crm-transaction/(:any)']['GET']				= 'crm_transaction/api_transaction/get/$1';
	$route['api/crm-transaction/(:any)']['POST']			= 'crm_transaction/api_transaction/action_post/$1';
	$route['api/crm-transaction-shipment/(:any)']['GET']	= 'crm_transaction/api_shipment/get_shipment/$1';
	$route['api/crm-transaction-shipment/(:any)']['POST']	= 'crm_transaction/api_shipment/update_shipment/$1';
}


# CRM TRANSACTION [UNDER DEVELOPMENT]
// if (ENVIRONMENT == 'development') {
	$route['crm/transaction']								= 'navigation/lv2';
	$route['crm/transaction/list-need-action']				= 'crm_transaction/page/list_wait';
	$route['crm/transaction/(:any)']						= 'crm_transaction/page/$1';
	$route['api/crm-transaction/datatable']['POST']			= 'crm_transaction/api_datatable/transaction_list';
	$route['api/crm-transaction']['GET']					= 'crm_transaction/api_transaction/get/';
	$route['api/crm-transaction/(:any)']['GET']				= 'crm_transaction/api_transaction/get/$1';
	$route['api/crm-transaction/(:any)']['POST']			= 'crm_transaction/api_transaction/action_post/$1';
	$route['api/crm-transaction-shipment/(:any)']['GET']	= 'crm_transaction/api_shipment/get_shipment/$1';
	$route['api/crm-transaction-shipment/(:any)']['POST']	= 'crm_transaction/api_shipment/update_shipment/$1';
	$route['api/crm-transaction-waiting/datatable']['POST'] = 'crm_transaction/api_datatable/transaction_list_wait';
	$route['api/crm-transaction-waiting/count']['GET']		= 'crm_transaction/api_transaction_waiting/total_waiting';

	//development
	$route['api/crm-transaction-settings']['GET']			= 'crm_transaction/api_transaction_settings/get_config';
	$route['api/crm-transaction-settings']['POST']			= 'crm_transaction/api_transaction_settings/update_config';
// }
*/

//UNDER DEVELOPMENT
if (file_exists(APPPATH.'modules/crm_transaction/config/routes.php')) {
	include_once(APPPATH.'modules/crm_transaction/config/routes.php');
}

# CRM CATEGORY SETTING
$route['crm/apps/category-setting(.*)']        = 'crm_productdescription/category_setting$1';
$route['crm/apps/category-setting-api(.*)']        = 'crm_productdescription/category_setting_api$1';

# HRM
$route['hrm']	= 'navigation/lv1';
$route['hrm/presention(.*)'] = 'hr_management/employees/presensi$1';
$route['hrm/break(.*)'] = 'hr_management/employees/cuti$1';
$route['hrm/(.*)'] = 'hr_management/employees/$1';

$route['api/billing-hrm']['POST'] = 'hrm_onboarding/hrm_boarding/create_billing';


# TASK-MANAGEMENT
#if (ENVIRONMENT !='production') {
	if (file_exists(APPPATH.'modules/task_management/config/routes.php')) {
		include_once(APPPATH.'modules/task_management/config/routes.php');
	}
#}


# BILLING (MODULE START)
if (file_exists(APPPATH.'modules/billing/config/routes.php')) {
	include_once(APPPATH.'modules/billing/config/routes.php');
}else{
	$route['settings/billing'] = 'billing/billing';
	$route['settings/billing/detail/(:num)'] = 'billing/billing/detail/$1';
	$route['settings/subscription'] = 'billing/subscribe';
	$route['settings/subscription/(:any)'] = 'billing/subscribe/$1';
	$route['settings/subscription-add'] = 'billing/subscribe_add';
	$route['settings/subscription-add/(:any)'] = 'billing/subscribe_add/$1';
}

$route['outlets/subscribe'] = 'billing/subscribe';
$route['outlets/subscribe/json'] = 'billing/subscribe/json';
$route['outlets/subscribe/inactive'] = 'billing/subscribe/inactive';
$route['outlets/subscribe-add'] = 'billing/subscribe_add';
$route['outlets/subscribe-add/add_to_cart'] = 'billing/subscribe_add/add_to_cart';
$route['outlets/subscribe/detail/(:any)'] = 'billing/billing/detail/$1';
$route['outlets/subscribe-add/load_cart'] = 'billing/subscribe_add/load_cart';
$route['outlets/subscribe-add/delete_cart'] = 'billing/subscribe_add/delete_cart';
$route['outlets/subscribe-add/show_cart'] = 'billing/subscribe_add/show_cart';
$route['outlets/subscribe/completeorder'] = 'billing/subscribe/completeorder';
$route['outlets/subscribe/device-action'] = 'billing/subscribe/device_action';

// route invoice
$route['invoice'] = 'billing/InvoiceController/index';
$route['payment_link/(:any)'] = 'billing/payment_link/index/$1';
# BILLING (MODULE END)

# FEEDBACK FORM (START)
$route['feedback'] = 'feedback_form/feedback';
$route['feedback/savefeedback'] = 'feedback_form/feedback/savefeedback';


# SERVERSIDE SEARCH
$route['search/product_variant']	= 'production_new/ajax/search_productvariant';
$route['search/employee']			= 'business_reportnotif/ajax_employee/employee';
$route['search/production-recipe']	= 'production_new/ajax/search_recipe';

# PROMOTION VOUCHER CLAIM PAGE
$route['voucher/claim'] = "crm_promotion/promotion_voucher_setting";
$route['voucher/claim/vc'] = "crm_promotion/promotion_voucher_setting/save_user_information";