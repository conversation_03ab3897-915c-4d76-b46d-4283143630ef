<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/*
| DESCRIPTION ABOUT THIS FILE
| ------------------------------------------------------------------------
| This file is config additional role of library of Privilege.
| Add more validation of access for user with user_type employee.
*/

/*
| -------------------------------------------------------------------
| ALLOWED ACCESS
| -------------------------------------------------------------------
| Allow access for user_type employee.
| Add url will be allow access for employee. It general purpose.
*/
$urls_whitelist = array(
	'settings',
	'settings/account',
	'settings/account/switch',
);


/*
| -------------------------------------------------------------------
| DISALLOW ACCESS
| -------------------------------------------------------------------
| Restric access for employee for admin menu.
*/
$urls_forbidden = array(
	'settings/resetdata',
	// 'settings/billing'
);


/*
| -------------------------------------------------------------------
| ACCESS WITH CHECK ROLE (render page)
| -------------------------------------------------------------------
| Add more role check for render page where not included in Privilege library.
*/
$urls_permission = array(
	//dashboard
	'_test/dashboard' => ['dashboard','view'],

	//stock
	'stock/stock_opname'							=> ['stock/opname/opname_inventory','add'],
	'stock/opname/opname_inventory/insert_opname'	=> ['stock/opname/opname_inventory','add'],
	'stock_opname/download_template/(:any)' 		=> ['stock/opname/opname_inventory','add'],
	'stock_opname/get_product' => ['stock/opname/stock_opname','view'],	

	//stock estimation
	'stock/estimation/estimation_form'									=> ['stock/estimation','add'],
	'stock_estimation/day_category/download_template/(:any)/(:any)'		=> ['stock/estimation','add'],

    //stock spoil
    'stock/spoil/(:any)/(:any)/(:any)/(:any)/(:any)/(:any)' => ['stock/spoil','view'],

	//employee
	'employees/crewlist_v1'			=> ['employees/crewlist','view'],
	'employees/crewlist_v2'			=> ['employees/crewlist','view'],
	'employees/crewlist_v3'			=> ['employees/crewlist','view'],

	//employee position
	'employees/jabatan_v1'			=> ['employees/jabatan','view'],
	'employees/jabatan_v2'			=> ['employees/jabatan','view'],
	'employees/jabatan_v3'			=> ['employees/jabatan','view'],

	//product catalogue
	'products/catalogue_v2'		   => ['products/catalogue','view'],
	'products/catalogue_v3'		   => ['products/catalogue','view'],
	'products/catalogue/v1/export' => ['products/catalogue','view'],
	'products/catalogue/v2/export' => ['products/catalogue','view'],
	'products/catalogue/v2/import_template' => ['products/catalogue','view'],

	//product import master + detail
	'products/catalogue/exportimport_master/v1/import_template' => ['products/catalogue','view'],
	'products/catalogue/exportimport_detail/v1/import_template' => ['products/catalogue','view'],

	//product link
	'products/catalogue/linkmenu-import/template' => ['products/catalogue','view'],
	'products/catalogue/linkmenu-import/template/xlsx' => ['products/catalogue','view'],

	//product multiple price
	'products/catalogue/multipleprice-import/template' => ['products/catalogue','view'],

	//product purchase report category
	'products/purchase_report_category'	=> ['products/purchase-report-category','view'],

	//product breakdown
	'products/breakdown/import/import_template/(:any)/(:any)/(:any)/(:any)/(:any)' => ['products/breakdown','view'],
	'products/breakdown/import/v1/template/xlsx/(:any)' => ['products/breakdown','view'],
	'products/breakdown/export/(:any)/(:any)' => ['products/breakdown','view'],

	//purchase
	'purchase/transfer/cetak/(:any)' => ['purchase/transfer', 'view'],
	'purchase/purchasing_product'  => ['purchase/purchase/purchase_products', 'view'],
	'purchase/purchasing_product/form_purchase'  => ['purchase/purchasing_product', 'add'],
	'purchase/purchase_import' => ['purchase/purchase/purchase_products','add'], //export import purchase
	'purchase_export/export_template' => ['purchase/purchase/purchase_products','add'], //export import purchase
	'purchase/purchasing_product/confirm/(:any)' => ['purchase/purchase/purchase_confrim', 'view'],
	'purchase/purchasing_product/retur/(:any)' => ['purchase/purchasing_product','view'],
	'purchase/purchasing_product/edit/(:any)'  => ['purchase/purchasing_product','edit'],
	'purchase/purchasing_product/confirm/(:any)' => ['purchase/purchasing_product', 'view'],

	// operational cost
	'purchase/operationalcost'  => ['operational_cost/operationalcost', 'view'],

	//purchase order
	'purchase_order/print_document/(:any)/(:any)/(:any)' => ['purchase/purchase_order','view'],

	//purchase debt
	'purchase/debt/debt_list/(:any)/(:any)/(:any)' => ['purchase/debt/debt_list','view'],
	'purchase/debt/debt_payment/(:any)/(:any)/(:any)' => ['purchase/debt/debt_payment','view'],

	//finance
	'finance/report/home_report_finance' => ['finance/report','view'],
	'finance/report/stock_card/export_data' => ['finance/report/stock_card','view'],

	//crm
	'crm/member/lists/generate_member_report' => ['crm/member/lists','view'],

	//production
	'production/itembreakdown/v(:any)' => ['production/itembreakdown', 'view'],
	'production/itembreakdown/import_template' => ['production/itembreakdown','view'],

	//hrm
	'hr_management/employees/schedule/export_excel' => ['hrm/schedule','view'],
	'hr_management/employees/presensi/download_excel' => ['hrm/presention','view'],

	// transfer
	'purchase/transfer/edit/(:any)' => ['purchase/transfer','edit'],

	// finance
	'finance/report/jurnal_umum/(:any)' => ['finance/report/jurnal_umum','view'],

	// settings billing
	'settings/billing/detail/(:any)' => ['settings/billing','view'],

	// settings subscribtion
	'settings/subscription-add' => ['settings/subscription','view'],
	'billing/subscribe/completeorder' => ['settings/subscription','view'],

	// settings subscribtion
	'activation/business_connect/(:any)/(:any)' => ['settings/business/connect','view'],
);



/*
| -------------------------------------------------------------------
| AJAX REQUEST WITH CHECK ROLE
| -------------------------------------------------------------------
| Add validation on ajax request.
*/
$ajax_urls_permission = array(
	//OUTLETS
	'outlets/outletlist/update' => ['outlets/outletlist','edit'],
);

/* End of file user_role.php */
/* Location: ./application/config/user_role.php */
