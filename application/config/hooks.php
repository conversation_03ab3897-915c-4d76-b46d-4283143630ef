<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|	https://codeigniter.com/user_guide/general/hooks.html
|
*/

$hook['post_controller_constructor'][] = array(
  'filepath' => 'hooks',
  'filename' => 'Api_pos_web_hook.php',
  'class'    => 'Api_pos_web_hook',
  'function' => 'generate_and_save_token',
);


//HRM onboarding
$hook['post_controller_constructor'][] = array(
  'filepath' => 'hooks',
  'filename' => 'Hrm_onboarding.php',
  'class'    => 'Hrm_onboarding',
  'function' => 'is_hrm_feature',
);
