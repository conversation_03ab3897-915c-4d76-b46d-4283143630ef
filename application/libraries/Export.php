<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Export
{
	// page size
	const PAGE_SIZE_LEGAL  = 'legal';
	const PAGE_SIZE_FOLIO  = 'folio';
	const PAGE_SIZE_LETTER = 'letter';
	// page orientation
	const PAGE_ORIENTATION_PORTRAIT = 'portrait';
	const PAGE_ORIENTATION_LANSCAPE = 'lanscape';
	// footer type
	const FOOTER_TYPE_SIMPLE  = 0;
	const FOOTER_TYPE_SIMPLE1 = 1;
	const FOOTER_TYPE_DETAIL  = 2;
	// mPDF instance
	public $mpdf;
	// CI instance
	protected $CI;

	protected $content     = '';
	protected $title       = '';
	protected $fileName    = '';
	protected $orientation = self::PAGE_ORIENTATION_PORTRAIT;
	protected $leftMargin  = '10';
	protected $rightMargin = '10';
	protected $pageSize    = self::PAGE_SIZE_LEGAL;
	protected $footerType  = self::FOOTER_TYPE_DETAIL;

	public function __construct()
	{
		$this->CI = &get_instance();
		// set php config
		ini_set("memory_limit", "-1");
		ini_set("max_execution_time", "-1");
		// set default title
		$this->title = $this->getDefaultTitle();
		// set default file name
		$this->fileName = $this->getDefaultFileName();
		// Create an instance of the class:
		// $this->mpdf = new mPDF('utf-8', $this->pageSize);		
		$this->mpdf = new \Mpdf\Mpdf([
		    'mode' => 'utf-8',
		    'format' => $this->pageSize,
		    'orientation' => $this->orientation
		]);
		// default config
		$this->setDetaultConfig();
	}

	private function setDetaultConfig()
	{
		$this->mpdf->defaultheaderfontsize = 6;
		$this->mpdf->defaultheaderfontstyle = "BI";
		$this->mpdf->defaultheaderline = 1;
		$this->mpdf->defaultfooterfontsize = 6;
		$this->mpdf->defaultfooterfontstyle = "BI";
		$this->mpdf->defaultfooterline = 1;
	}

	public function load($content, $data = array())
	{
		$content = ltrim($content, '/');
		$content = str_replace('.php', '', $content);
		$file_path = APPPATH . "exports/$content.php";
		if (!is_file($file_path)) {
			$exp_content = explode('/', $content);
			array_splice($exp_content, 1, 0, 'exports');
			$content = implode('/', $exp_content);
			$file_path = APPPATH . "modules/$content.php";
			if (!is_file($file_path)) {
				show_error("Unable to load the requested file: $file_path");
			}
		}
		extract($data);
		ob_start();
		include($file_path);
		$this->content = ob_get_clean();
		return $this;
	}

	public function data($data)
	{
		if (!is_string($data)) {
			show_error("Parameter data harus bertipe string");
		}
		$this->content = $data;
		return $this;
	}

	public function set_hider($title=null,$outlet=null,$tgl=null){
		$CI =& get_instance();
		$dataOutlet = $CI->db->get('outlets',['outlet_id'=>'2'])->row();
			// print_r($dataOutlet);die;// 
		$hider = '<table style="border-collapse:collapse;" width="100%" align="center" border="0" cellspacing="0" cellpadding="4">
		<tr>
			 <td width="20%" rowspan="1" align="center"><img src="'.$dataOutlet->outlet_logo.'"width="100" height="105"></td>
			 <td width="80%" align="center" style="border-bottom:none;border-left:none;"><font size="4"><b>'.strtoupper($title).'<br> OUTLET '.strtoupper($dataOutlet->name).'</b><br>'.$dataOutlet->address.'</font>
			 </td>
		</tr>
		<tr>
			 <td align="center" colspan="5"><hr style="height:3px;margin-bottom: 0px;"><hr style="margin-top: 1px;"></td>
		</tr>
	</table>';
		return $hider;
	}

	public function pdf($fileName = null, $dest = 'I')
	{
		if ($fileName) {
			$this->setFileName($fileName);
		}
		// set margin
		$CI =& get_instance();
		switch ($this->footerType) {
			case self::FOOTER_TYPE_SIMPLE:
				$this->mpdf->SetFooter(' |Halaman {PAGENO} dari {nb}|www.uniq.id');
				break;
			case self::FOOTER_TYPE_SIMPLE1:
				$this->mpdf->SetFooter('Dicetak oleh '.$CI->session->userdata('user_name').'|Halaman {PAGENO} dari {nb}|www.uniq.id');
				break;
			default:
				$this->mpdf->SetFooter('Dicetak oleh '.$CI->session->userdata('user_name').' pada ' . date("d-m-Y") . ' ' . date('H:i:s') . ' WIB |Halaman {PAGENO} dari {nb}|www.uniq.id');
				break;
		}
		// add page
		$pageSize = $this->pageSize;
		if ($this->orientation === self::PAGE_ORIENTATION_LANSCAPE) {
			$pageSize .= '-L';
		}
		$this->mpdf->AddPage($this->orientation, '', '', '', '', $this->leftMargin, $this->rightMargin, '', '', '', '', '', '', '', '', '', '', '', '', '', $pageSize);
		// set tittle
		$this->mpdf->SetTitle($this->title);
		// Write some HTML code:
		$this->mpdf->WriteHTML($this->content);
		// Output a PDF file directly to the browser
		$this->mpdf->Output($this->fileName . '.pdf', $dest);
	}

	public function excel($fileName = null)
	{
		if ($fileName) {
			$this->setFileName($fileName);
		}
		header("Cache-Control: no-cache, no-store, must-revalidate");
		header("Content-Type: application/vnd.ms-excel");
		header("Content-Disposition: attachment; filename=$this->fileName.xls");
		echo $this->content;
	}

	public function word($fileName = null)
	{
		if ($fileName) {
			$this->setFileName($fileName);
		}
		header("Cache-Control: no-cache, no-store, must-revalidate");
		header("Content-Type: application/vnd.ms-word");
		header("Content-Disposition: attachment; filename=$this->fileName.doc");
		echo $this->content;
	}

	public function html()
	{
		return $this->content;
	}

	public function setMargin($leftMargin = null, $rightMargin = null)
	{
		if (is_null($rightMargin)) {
			$rightMargin = $leftMargin;
		}
		$this->leftMargin  = $leftMargin;
		$this->rightMargin = $rightMargin;
		return $this;
	}

	public function setOrientation($orientation)
	{
		$this->orientation = $orientation;
		return $this;
	}

	// Backward compatible method
	public function setOrientasi($orientation)
	{
		return $this->setOrientation($orientation);
	}

	public function setPageSize($pageSize)
	{
		$this->pageSize = $pageSize;
		return $this;
	}

	public function setFooterType($footerType)
	{
		$this->footerType = $footerType;
		return $this;
	}

	public function setTitle($title)
	{
		$this->title = $title;
		return $this;
	}

	public function setFileName($fileName)
	{
		$this->fileName = preg_replace("/[^A-Za-z0-9-.]/", '_', str_replace(' - ', '-', $fileName));
		return $this;
	}

	private function getDefaultTitle()
	{
		$className    = $this->CI->router->class;
		$defaultTitle = preg_replace("/[^A-Za-z0-9 ]/", ' ', $className);
		return ucwords(strtolower($defaultTitle));
	}

	private function getDefaultFileName()
	{
		$className       = $this->CI->router->class;
		$defaultFileName = preg_replace("/[^A-Za-z0-9 ]/", '_', $className);
		return strtolower($defaultFileName);
	}
}
