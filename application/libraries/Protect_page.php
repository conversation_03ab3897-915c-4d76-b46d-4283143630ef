<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Protect_page
{
	protected $ci;

	public function __construct()
	{
        $this->ci =& get_instance();

        //buat session saat pertama kali akses
        if (empty($this->ci->session->userdata('user_logged'))) {
        	$createfirstsession = array(
	        	'user_logged' => FALSE
	        );
	        
	        $this->ci->session->set_userdata( $createfirstsession );
        }
	}

	public function userlogged($value='')
	{
		$userlogged = $this->ci->session->userdata('user_logged');
		$user_type = $this->ci->session->userdata('user_type');
		switch ($value) {
			case TRUE:
				//halaman hanya dapat diakses jika sudah login
				if ($userlogged !== TRUE || $userlogged === FALSE) {
					redirect(site_url()); //redirect
					die();
				}
				break;
			case FALSE:
				//halaman dapat diakses hanya untuk yang belum login
				if ($userlogged === TRUE || $userlogged !== FALSE) {
					if ($user_type=='admin') {
						redirect('dashboard'); //redirect ke dashboard untuk admin
					}else{
						//redirect ke menu yang bisa diakses oleh employee
						$arr = $this->ci->privilege->role_web();
						$role_web = array_keys($arr);

						$arr_role_web_user = $this->ci->privilege->refresh_role(); //get latest user role
						$role_web_user = array_keys($arr_role_web_user);

						$result = array_intersect($role_web, $role_web_user); //ambil data yang sama di role_web dan current_role_web_user
						$redirect_to = (!empty($result)) ? current($result) : 'settings/account';
						redirect($redirect_to);
					}
					die();
				}
				break;
			
			default:
				redirect(site_url()); //redirect ke halaman utama
				die();
				break;
		}
		return;
	}

	//hanya bisa diakses oleh dev UNIQ
	public function developerOnly($feature_name=null)
	{
		/*
		if ($this->ci->session->userdata('developer_mode')==false) {
			//echo "Access disallowed!";
			$data = array(
				'pagename' => $feature_name
			);
			$this->ci->load->view('themes/comingsoon_v', $data);
			die();
		}
		*/
	}

	

}

/* End of file Protect_page.php */
/* Location: ./application/libraries/Protect_page.php */
