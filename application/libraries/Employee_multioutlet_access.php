<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Employee_multioutlet_access
{
	protected $ci;
	protected $dbtable;

	public function __construct()
	{
        $this->ci =& get_instance();
        $this->dbtable = 'employee_outlet';
	}

	public function cek_akses_outlet()
	{
		if ($this->ci->session->userdata('user_type')=='employee') {
			//ambil akses
			$this->ci->db->select('outlet_fkid');
			$this->ci->db->where('employee_fkid', $this->ci->session->userdata('user_id'));
			$result = $this->ci->db->get($this->dbtable);

			$multiaccess = array();
			foreach ($result->result() as $a) {
				$outlet_id = $a->outlet_fkid;
				array_push($multiaccess, $outlet_id);
			}

			//buat session multioutlet
			$array = array(
				'outlet_access' => $multiaccess
			);
			
			$this->ci->session->set_userdata( $array );
		}
	}

	public function datatables()
	{
		if ($this->ci->session->userdata('user_type')=='employee') {

			if ($this->ci->session->userdata('outlet_access')!=null) {
	            $access = $this->ci->session->userdata('outlet_access');
	            $no=1;
	            foreach ($access as $outlet_id) {
	                if ($no==1) {
	                	$this->ci->datatables->where('outlet_id', $outlet_id);
	                }
	                else{
	                	$this->ci->datatables->or_where('outlet_id', $outlet_id);
	                }
	                $no++;
	            }
	        }
		}
	}

	/*
	public function outlet_access($outlet_column_id) //masih tes
	{
		//akses multi outlet
        $this->load->library('Employee_multioutlet_access');
        $multiaccess = new Employee_multioutlet_access();
        $multiaccess->datatables();

        if ($this->session->userdata('outlet_access')!=null) {
            $access = $this->session->userdata('outlet_access');
            foreach ($access as $a) {
                //$this->datatables->where('outlet_id', $access);
                echo $a;
            }
        }
	}
	*/

}

/* End of file Employee_multioutlet_access.php */
/* Location: ./application/libraries/Employee_multioutlet_access.php */
