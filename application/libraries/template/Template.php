<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Template
{
	protected $ci;
	protected $dev;
	protected $is_admin = false;
	protected $template_assets = 'assets/themes/';
	protected $view_header = 'themes/v2/header';
	protected $view_footer = 'themes/v2/footer';	
	protected $css = array();
	protected $js = array(
		'top'    => array(),
		'bottom' => array(),
	);

	public function __construct()
	{
        $this->ci =& get_instance();
        $this->dev = $this->ci->session->userdata('developer_mode');
        $this->is_admin = ($this->ci->session->userdata('user_type')=='admin') ? true : false;

        //load
        $this->ci->load->helper('url');
	}

	public function view($path='', $data=array())
	{
		$data['page_title'] = (!empty($data['page_title'])) ? $data['page_title'] : '';
		$data['api_token'] = $this->ci->api_web->getToken();

		$html = '';
		$html.= $this->ci->load->view($this->view_header, $data, true);
		// $html.= $this->ci->load->view('themes/v2/header_navigation', $data, true);
		$html.= $this->ci->load->view($path, $data, true);
		$html.= $this->ci->load->view($this->view_footer, $data, true);

		//-- inject chat box -->
		if (file_exists(APPPATH.'modules/chat_box/views/chat_v.php')) {
			//load variables
			$html_chat = $this->ci->load->view('chat_box/chat_v', [], true);
			$html = str_replace('</body>', $html_chat."\n</body>", $html);
		}

		//inject api-report
		$html = str_replace('<body>', "<body>\n". $this->api_report_data(), $html);
		echo $html;
	}

	public function api_report_data()
	{
		$ci =& get_instance();
		$ci->load->library('app/Api_report');
		$ci->load->library('app/Api_chat');
		$api_report = $ci->api_report->data();
		$chat_token = $ci->api_chat->create_chainlit_jwt(['token' => ($api_report->token) ?? '']); //
		$data = [
			'token_report' => ($api_report->token) ?? '',
			'token_report_expired' => ($api_report->token_expired) ?? 0,
			'chat_token' => $chat_token,
		];
		return $this->ci->load->view('auth/data_api_v', $data, true);
	}

	public function disable_page()
	{
		$data['page_title'] = 'Access Forbidden';
		$this->view('themes/v2/access_forbidden_v', $data);die();
	}

	public function feature_expired($feature)
	{
		$data['page_title'] = 'Subscription Expired!';
		// $html.= $this->ci->load->view($this->view_header, $data, true);
		// switch ($feature) {
		// 	case 'crm':
		// 		$html.= $this->view('themes/v2/subscription_end_crm', $data);
		// 		break;
			
		// 	default:
		// 		$html.= $this->view('themes/v2/access_forbidden_v', $data);
		// 		break;
		// }
		// $html.= $this->ci->load->view($this->view_footer, $data, true);
		// echo $html;


		switch ($feature) {
			case 'crm':
				$data['feature'] = 'CRM';
				break;
			case 'hrm':
				$data['feature'] = 'HR Management';
				break;
			default:
				$data['feature'] = 'POS';
				break;
		}
		$this->view('themes/v2/subscription_feature_expired', $data);
		die();
	}

	public function boarding_page_hrm()
	{
		$this->ci->load->library('auth/Recaptcha');

		$data['page_title'] = 'HR Management';

		$data['recaptcha_widget'] = $this->ci->recaptcha->getWidget(['data-callback'=>'recaptcha_callback']);
		$data['recaptcha_script'] = $this->ci->recaptcha->getScriptTag();
		$data['recaptcha_sitekey'] = $this->ci->config->item('recaptcha_site_key');
		$data['form_url']			= site_url('api/billing-hrm');
		$this->view('hrm_onboarding/hrm_v', $data);
	}

	public function navigation()
	{
		$this->ci->load->library('app/Privilege');
		$user_type = $this->ci->session->userdata('user_type');
		$data = $this->ci->privilege->role_web();


		//PENAMBAHAN NAVIGASI
		$data['outlets']['sub']['subscribe'] = array(
			'text' => 'Subscribe',
			'url' => 'outlets/subscribe',
		);

		// PENAMBAHAN NAVIGASI DI SETTINGS
		$data['settings'] = array(
			'text' => 'Settings',
			'url' => 'settings',
			'sub' => array(
				array(
					'text' => 'Account',
					'url' => 'settings/account'
				)
			)
		);

		if ($this->ci->privilege->check('settings/business','view')) {
			$data['settings']['sub'] = array_merge($data['settings']['sub'] , array(
				array(
					'text' => 'Business',
					'url' => 'settings/business',
					'sub' => array(
						($this->ci->privilege->check('settings/business/profile','view')) ?
						array(
							'text' => 'Business Profile',
							'url' => 'settings/business/profile'
						) : null,
						($this->ci->privilege->check('settings/business/report_notifications','view')) ?
						array(
							'text' => 'Report Notifications',
							'url' => 'settings/business/report_notifications'
						) : null,
						($this->ci->privilege->check('settings/business/connect','view')) ?
						array(
							'text' => 'Social Connect',
							'url' => 'settings/business/connect'
						) : null,
						($user_type == 'admin') ?
						array(
							'text' => 'Reset Data',
							'url' => 'settings/business/resetdata'
						) : null,
						($user_type == 'admin') ?
						array(
							'text' => 'Sync Report Data',
							'url' => 'settings/business/sync-report'
						) : null,
					)
				),
			));
		}


		if ($this->ci->privilege->check('settings/billing','view')) {
			$data['settings']['sub'] = array_merge($data['settings']['sub'], array(
				array(
					'text' => 'Billing',
					'url' => 'settings/billing'
				),
			));
		}


		if ($this->ci->privilege->check('settings/subscription','view')) {
			$data['settings']['sub'] = array_merge($data['settings']['sub'], array(
				array(
					'text' => 'Subscribtion',
					'url' => 'settings/subscription'
				),
			));
		}

		return $data;
	}

	public function js($path, $position = 'top')
	{
		$position = $position == 'top' ? 'top' : 'bottom';
		if (!is_array($path)) {
			$path = array($path);
		}
		foreach ($path as $value) {
			array_push($this->js[$position], $value);
		}
	}

	public function css($path)
	{
		if (!is_array($path)) {
			$path = array($path);
		}
		foreach ($path as $value) {
			array_push($this->css, $value);
		}
	}

	public function buildCss()
	{
		$html = '';
		foreach ($this->css as $css) {
			$path = base_url($css);
			if (filter_var($css, FILTER_VALIDATE_URL)) {
				$path = $css;
			}
			$html .= '<link rel="stylesheet" href="' . $path . '">' . "\n";;
		}
		return $html;
	}

	public function buildJs($position)
	{
		$html = '';
		foreach ($this->js[$position] as $js) {
			$path = base_url($js);
			if (filter_var($js, FILTER_VALIDATE_URL)) {
				$path = $js;
			}
			$html .= '<script type="text/javascript" src="' . $path . '"></script>' . "\n";
		}
		return $html;
	}

}

/* End of file Template.php */
/* Location: ./application/libraries/template/Template.php */
