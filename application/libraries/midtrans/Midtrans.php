<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Midtrans
{
	protected $ci;
	public static $serverKey;
	public static $isProduction = false;
	public static $curlOptions = array();
	const SANDBOX_BASE_URL = 'https://api.sandbox.midtrans.com/v2';
	const PRODUCTION_BASE_URL = 'https://api.midtrans.com/v2';
	const SNAP_SANDBOX_BASE_URL = 'https://app.sandbox.midtrans.com/snap/v1';
	const SNAP_PRODUCTION_BASE_URL = 'https://app.midtrans.com/snap/v1';

	public function __construct()
	{
		$this->ci =& get_instance();

		//setting config
		$this->ci->load->config('config_midtrans');
		Midtrans::$serverKey = $this->ci->config->item('midtrans_serverkey'); //$params['server_key'];
		Midtrans::$isProduction = $this->ci->config->item('midtrans_production'); //$params['production'];
	}

	public static function getBaseUrl()
	{
		return Midtrans::$isProduction ? Midtrans::PRODUCTION_BASE_URL : Midtrans::SANDBOX_BASE_URL;
	}

	public static function getSnapBaseUrl()
	{
		return Midtrans::$isProduction ? Midtrans::SNAP_PRODUCTION_BASE_URL : Midtrans::SNAP_SANDBOX_BASE_URL;
	}

	public static function get($url, $server_key, $data_hash)
	{
		return self::remoteCall($url, $server_key, $data_hash, false);
	}

	public static function post($url, $server_key, $data_hash)
	{
		return self::remoteCall($url, $server_key, $data_hash, true);
	}

	public static function remoteCall($url, $server_key, $data_hash, $post = true)
	{
		$ch = curl_init();
		$curl_options = array(
			CURLOPT_URL => $url,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Accept: application/json',
				'Authorization: Basic ' . base64_encode($server_key . ':')
			),
			CURLOPT_RETURNTRANSFER => 1,
			CURLOPT_CAINFO => dirname(__FILE__) . "/midtrans/cacert.pem"
		);

		// merging with Veritrans_Config::$curlOptions
		if (count(Midtrans::$curlOptions)) {
			// We need to combine headers manually, because it's array and it will no be merged
			if (Midtrans::$curlOptions[CURLOPT_HTTPHEADER]) {
				$mergedHeders = array_merge($curl_options[CURLOPT_HTTPHEADER], Midtrans::$curlOptions[CURLOPT_HTTPHEADER]);
				$headerOptions = array( CURLOPT_HTTPHEADER => $mergedHeders );
			}
			else {
				$mergedHeders = array();
			}

			$curl_options = array_replace_recursive($curl_options, Midtrans::$curlOptions, $headerOptions);
		}

		if ($post) {
			$curl_options[CURLOPT_POST] = 1;
			if ($data_hash) {
				$body = json_encode($data_hash);
				$curl_options[CURLOPT_POSTFIELDS] = $body;
			}
			else {
				$curl_options[CURLOPT_POSTFIELDS] = '';
			}
		}

		curl_setopt_array($ch, $curl_options);
		$result = curl_exec($ch);
		$info = curl_getinfo($ch);
		// curl_close($ch);

		/* remove */
		// if ($result === FALSE) {
		// 	throw new Exception('CURL Error: ' . curl_error($ch), curl_errno($ch));
		// }
		// else {
		// 	$result_array = json_decode($result);
		// 	if ($info['http_code'] != 201 && !in_array($result_array->status_code, array(200, 201, 202, 407)) ) {
		// 		$message = 'Midtrans Error (' . $info['http_code'] . '): '. implode(',', $result_array->error_messages);
		// 		throw new Exception($message, $info['http_code']);
		// 	}
		// 	else {
		// 		return $result_array;
		// 	}
		// }
		/* remove */
		return $result; //output dalam json
	}

	public static function getSnapToken($params)
	{
		$result = Midtrans::post(Midtrans::getSnapBaseUrl() . '/transactions', Midtrans::$serverKey, $params);
		$result_array = json_decode($result);
		return $result;
	}

	public static function status($id)
	{
		return Midtrans::get(Midtrans::getBaseUrl() . '/' . $id . '/status', Midtrans::$serverKey, false);
	}

	public static function approve($id)
	{
		$result = Midtrans::post(Midtrans::getBaseUrl() . '/' . $id . '/approve', Midtrans::$serverKey, false);
		$result_array = json_decode($result);
		return $result;
	}

	public static function cancel($id)
	{
		$result = Midtrans::post(Midtrans::getBaseUrl() . '/' . $id . '/cancel', Midtrans::$serverKey, false);
		$result_array = json_decode($result);
		return $result;
	}

	public static function expire($id)
	{
		$result = Midtrans::post(Midtrans::getBaseUrl() . '/' . $id . '/expire', Midtrans::$serverKey, false);
		$result_array = json_decode($result);
		return $result;
	}
	
}

/* End of file Midtrans.php */
/* Location: ./application/libraries/payment/Midtrans.php */
