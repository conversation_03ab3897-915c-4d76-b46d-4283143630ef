<?php
defined('BASEPATH') OR exit('No direct script access allowed');

use Google\Cloud\PubSub\PubSubClient;

class Pubsub
{
	protected $ci;
	protected $topic = [];


	public function __construct()
	{
		$this->ci =& get_instance();
		$this->topic = [
			'production' => getenv('PUBSUB_PRODUCTION'),
			'purchase'	 => getenv('PUBSUB_PURCHASE')
		];
	}

	private function authorization()
	{
		$keyFilePath = APPPATH.'../uniq-pubsub-publisher.json';
		$path = json_decode(file_get_contents($keyFilePath), true);

		$pubSub = new PubSubClient([
			'projectId' => $path['project_id'],
			'keyFilePath' => $keyFilePath
		]);

		return $pubSub;
	}

	public function publish($topic, $message)
	{
		try {
			$pubSub = $this->authorization();

			// Get an instance of a previously created topic.
			$pubTopic = $pubSub->topic($topic);

			// Publish a message to the topic.
			$response = $pubTopic->publish([
				'data' => $message,
				// 'attributes' => [
				// 	'location' => 'Detroit'
				// ]
			]);

			file_put_contents("php://stderr", "sending pubsub success, topic ".$topic." message: ".$message." | resp: ".json_encode($response)."\n");
			return $response;
		} catch (Exception $e) {
			file_put_contents("php://stderr", "\n [ERROR OCCURED] sending pubsub failed, topic ".$topic." message: ".$message." | err: ".$e."\n");
			// echo "$e";
		}

	}


	public function publishProduction($outlet_id, $production_id)
	{
		$message = json_encode([
			'admin_id' => $this->ci->session->userdata('admin_id'),
			'outlet_id' => strval($outlet_id),
			'production_id' => strval($production_id),
		]);

		$topic = $this->topic['production'];
		return $this->publish($topic, $message);
	}

	public function publishPurchase($message)
	{
		$topic = $this->topic['purchase'];
		return $this->publish($topic, $message);
	}

}

/* End of file Pubsub.php */
/* Location: ./application/libraries/google/Pubsub.php */
