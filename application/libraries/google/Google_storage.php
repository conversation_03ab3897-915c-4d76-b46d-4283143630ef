<?php
defined('BASEPATH') OR exit('No direct script access allowed');

use Google\Cloud\Storage\StorageClient;
use google\appengine\api\cloud_storage\CloudStorageTools;

class Google_storage
{
	protected $ci;
	protected $url = 'https://storage.googleapis.com/';

	public function __construct()
	{
        $this->ci =& get_instance();
        $this->ci->config->load('google');
	}

	private function bucket_auth()
	{
		$storage = new StorageClient([
			'projectId' => "uniq-187911",
			'keyFilePath' => $this->ci->config->item('gstorage_authfile')
		]);

		$bucket = $storage->bucket($this->ci->config->item('gstorage_bucket'));
		return $bucket;
	}

	public function upload($file_input_name, $path, $filename_custom='', $is_private=false)
	{
		$result_file_url = '';
		file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." uploading.... \n");
		if (!empty($_FILES[$file_input_name]['name'])) {
			//get file
			$file = fopen($_FILES[$file_input_name]['tmp_name'], 'r');
			$file_ext = pathinfo($_FILES[$file_input_name]['name'],PATHINFO_EXTENSION);
			if ($file_ext == pathinfo($filename_custom, PATHINFO_EXTENSION)) {
				//remove extension on $filename_custom
				$filename_custom = preg_replace('/\\.[^.\\s]{3,4}$/', '', $filename_custom);
			}

			//rename file
			$file_name_generated = preg_replace("[^\w.-]", '', md5( date('Y-m-d H:i:s')) ); //remove non alphanumeric
			$file_name = (empty($filename_custom)) ? $file_name_generated : $filename_custom;
			$file_name .= '.'. $file_ext; //add extention on file name

			//setting path
			$file_fullpath = $path.$file_name;


			// Upload a file to the bucket.
			$bucket = $this->bucket_auth();
			if ($is_private==true) {
				$bucket->upload($file, [
					'name' => $file_fullpath,
				]);
			}else{
				$bucket->upload($file, [
					'name' => $file_fullpath,
					'predefinedAcl' => 'publicRead',
				]);//->update(['acl' => []], ['predefinedAcl' => 'PUBLICREAD']);
			}


			$result_file_url = CloudStorageTools::getPublicUrl("gs://".$bucket->name()."/". $file_fullpath, true);
			file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." uploaded to storage:  $result_file_url \n");
		}

		return $result_file_url;
	}

	public function delete($file_url)
	{
		//cek file url
		// if (!empty($file_url)) {
		if (!empty($file_url) && substr($file_url, 0, strlen($this->url)) == $this->url) {
			$file_url = urldecode($file_url);
			$bucket = $this->bucket_auth();

			//get file (object) path
			$objectName = "https://storage.googleapis.com/".$bucket->name()."/";
			$objectName = substr($file_url, strlen($objectName));

			$object = $bucket->object($objectName);
			if ($object->exists()) {
				$object->delete();
				return TRUE;
			}
		}
		return FALSE;
	}

	public function file_exists($file_url)
	{
		$exists = false;
		if (!empty($file_url) && substr($file_url, 0, strlen($this->url)) == $this->url) {
			$file_url = urldecode($file_url);
			$bucket = $this->bucket_auth();

			//get file (object) path
			$objectName = $this->url . $bucket->name()."/";
			$objectName = substr($file_url, strlen($objectName));
			$object = $bucket->object($objectName);
			if ($object->exists()) {
				$exists = true;
			}
		}
		return $exists;
	}

	

}

/* End of file Google_storage.php */
/* Location: ./application/libraries/google/Google_storage.php */
