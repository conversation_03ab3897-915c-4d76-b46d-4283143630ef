<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_key_lib
{
	protected $ci;
	protected $alphabet;
	protected $numeric;
	protected $specialchar;
	protected $string_key;
	protected $string_pass;

	public function __construct()
	{
        $this->ci =& get_instance();

        //load
        $this->ci->load->helper('password');
		$this->ci->load->helper('sendemail');
		$this->ci->load->model('account/Userkey_model');

		//inisialisasi membuat random key
		$this->alphabet		= 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		$this->numeric 		= '**********';
		$this->specialchar	= '@#$^*()_+=/?%';

		$this->string_key	= $this->alphabet.$this->numeric.$this->specialchar;
		$this->string_pass	= $this->alphabet.$this->numeric;
	}

	public function _kodeRandom($string, $panjang)
	{
		$karakter = '';
		$karakter .= $string;

		$string = '';
		for ($i=0; $i < $panjang; $i++) { 
			$pos = rand(0, strlen($karakter)-1);
			$string .= $karakter{$pos};
		}
		return $string;
	}

	public function create_activation_key($email=null, $user_type=null)
	{
		//user type = admin / employee
		//buat key
		$string_random = $this->string_key;

		//buat data untuk dimasukkan ke database userkey
		$key = $this->_kodeRandom($string_random, 10);
		$key_md5 = md5($key);
		$key_encrypt = password_encrypt($key_md5);
		$email = urldecode($email);

		$data_insert = array(
			'user_level' => $user_type,
			'email' => $email,
			'secret_key' => $key_encrypt,
		);
		$response = $this->ci->Userkey_model->create_activation_key($data_insert); // memasukkan data ke database

		//konten untuk isi email
		if ($response===true) {
			$link_aktivasi = site_url('activation/'.$key_md5.'/'.urlencode($email));
			$data_message['link_aktivasi'] = $link_aktivasi;
			$message = $this->ci->load->view('email_template/user_activation_v', $data_message, true);

			return sendMail($email, 'Activation', $message);
		}
	}

	public function create_password($email)
	{
		//generate password
		$string_random = $this->string_pass;

		//buat password
		$password = $this->_kodeRandom($string_random, 5);

		//data untuk dimasukkan database
		$data_createpass['email'] = $email;
		$data_createpass['password'] = $password;
		$response = $this->ci->Userkey_model->employee_web_activation($data_createpass); // insert database
		if ($response===true) {
			//kirim password
			$message_data = array(
				'email' => $email,
				'password' => $password
			);
			$message = $this->ci->load->view('email_template/user_account_info_v', $message_data, TRUE);
			sendMail($email, 'UNIQ Account Information', $message);
			echo 'Please check your email to get login details. <a href="'.site_url('').'">[ Login ]</a>';
		}
	}
}

/* End of file User_key.php */
/* Location: ./application/libraries/User_key_lib.php */
