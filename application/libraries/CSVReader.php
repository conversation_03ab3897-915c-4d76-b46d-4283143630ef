<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class CSVReader {

    var $fields;            /** columns names retrieved after parsing */ 
    var $separator  =   ';';    /** separator used to explode each line */
    var $enclosure  =   '"';    /** enclosure used to decorate each field */

    var $max_row_size   =   4096;    /** maximum row size to be used for decoding */

    /*custom */
    var $header_titikkoma = array();
    var $keys = array();
    var $header_count = 0;
    var $csv_format; // petik | titikkoma

    function detectDelimiter($fh)
    {
        $delimiters = [";", "|", ",", "\t"];
        $data_1 = array(); $data_2 = array();
        $delimiter = $delimiters[0];
        foreach($delimiters as $d) {
            $data_1 = fgetcsv($fh, 4096, $d);
            if(sizeof($data_1) > sizeof($data_2)) {
                $delimiter = $d;
                $data_2 = $data_1;
            }
            rewind($fh);
        }

        return $delimiter;
    }

    function header($p_Filepath)
    {
        //open file
        $file           =   fopen($p_Filepath, 'r');
        $this->separator = $this->detectDelimiter($file);
        
        //cek header data
        $this->fields   =   fgetcsv($file, $this->max_row_size, $this->separator, $this->enclosure);
        return $this->fields;
    }

    function parse_file($p_Filepath) 
    {
        $file           =   fopen($p_Filepath, 'r');
        $this->fields   =   fgetcsv($file, $this->max_row_size, $this->separator, $this->enclosure);
        $keys_values        =   explode(',',$this->fields[0]); //header

        //menentukan format CSV
        $this->csv_format = (count($keys_values)>1) ? 'petik' : 'titikkoma';

        /*custom start */
        $this->header_count = count($this->fields);
        foreach ($this->fields as $field) {
        	$header_titikkoma[] = $field;//explode(',',$field);
        	$this->keys = $field;
        }
        // echo print_r($temp);
        // echo print_r($header_titikkoma);
        /*custom end */

        $content            =   array();
        $keys           =   $this->escape_string($keys_values);
        

        $i  =   1;
        while(($row = fgetcsv($file, $this->max_row_size, $this->separator, $this->enclosure)) != false ) 
        {
        	switch ($this->csv_format) {
        		case 'petik':
        			if( $row != null ) { // skip empty lines
		                $values         =   explode(',',$row[0]); //value dari header
		                if(count($keys) == count($values)){
		                    $arr        =   array();
		                    $new_values =   array();
		                    $new_values =   $this->escape_string($values);
		                    for($j=0;$j<count($keys);$j++){
		                        if($keys[$j]    !=  ""){
		                            $arr[$j] =   $new_values[$j];
		                        }
		                    }
		                    $content[$i]    =   $arr;
		                    $i++;
		                }
		            }
        			break;
        		case 'titikkoma':
        			if( $row != null ) { // skip empty lines
		            	$temp = array();
		            	for ($col=0; $col < $this->header_count; $col++) { 
		            		$temp[] = $row[$col];
		            	}
		            	$content[$i] = $temp;
		            	$i++;
		            }
        			break;
        		default: break;
        	}
        	/*import untuk petik
            if( $row != null ) { // skip empty lines

                $values         =   explode(',',$row[0]); //value dari header
                if(count($keys) == count($values)){
                    $arr        =   array();
                    $new_values =   array();
                    $new_values =   $this->escape_string($values);
                    for($j=0;$j<count($keys);$j++){
                        if($keys[$j]    !=  ""){
                            $arr[$j] =   $new_values[$j];
                        }
                    }
                    $content[$i]    =   $arr;
                    $i++;
                }
            }
            */
            
            /*
            // import untuk titik koma
            if( $row != null ) { // skip empty lines
            	$temp = array();
            	for ($col=0; $col < $this->header_count; $col++) { 
            		$temp[] = $row[$col];
            	}
            	$content[] = $temp;
            	$i++;
            }
            */
            
        }
        fclose($file);
        return $content;
    }

    function escape_string($data)
    {
        $result =   array();
        foreach($data as $row){
            $result[]   =   str_replace('"', '',$row);
        }
        return $result;
    }   
}


/* End of file CSVReader.php */
/* Location: ./application/libraries/_testing/CSVReader.php */
