<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Myservice_lib
{
	protected $ci;

	public function __construct()
	{
        $this->ci =& get_instance();
        $this->ci->load->model('settings/Billing_model');
	}

	public function konfirmasi($billing_id=null)
    {
    	//cek billing valid atau tidak
    	$cek_billing = $this->ci->Billing_model->get_public_billing_by_id($billing_id);
    	if ($cek_billing) {
			//kalau billing belum expired
			/*
			1. ganti status billing menjadi success
			2. tambah subscribe perangkat
			*/
			if (($cek_billing->billing_status!='billing') && ($cek_billing->billing_status!='pending') && ($cek_billing->billing_status!='pending_challenge')) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Billing Maybe Expired, Confirmed, or Canceled.'.$billing_status
				);
			}
			else{
    			$databillingupdate = array(
    				'billing_status' => 'success',
    				'time_confirm' => current_millis(),
    				//'sysuser_fkid' => $this->session->userdata('user_id'),
    			);
    			$billingupdate_response = $this->ci->Billing_model->update_public_billing($billing_id, $databillingupdate);
    			if ($billingupdate_response) {
    				//update subscribtion
    				$subscribtion_update = $this->ci->Billing_model->update_subscribe($billing_id);
    				if ($subscribtion_update) {
    					$draw_json = array(
							'status' => 'success',
							'message' => 'Konfirmasi Berhasil'
						);
    				}
    				else{
    					$draw_json = array(
    						'status' => 'error',
    						'message' => 'Konfirmasi Gagal'
    					);
    				}
    			}
    			else{
    				$draw_json = array(
    					'status' => 'error',
    					'message' => 'Konfirmasi Error'
    				);
    			}
    		}//end cek status billing
    	}
    	else{
    		$draw_json = array(
    			'status' => 'error',
    			'message' => 'Billing Not Found'
    		);
    	}

    	//output json
    	return format_json((!empty($draw_json)) ? $draw_json : array());
    }

}

/* End of file Myservice_lib.php */
/* Location: ./application/libraries/Myservice_lib.php */
