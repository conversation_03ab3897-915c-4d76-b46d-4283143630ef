<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_role
{
	protected $ci;
	protected $role;
	protected $role_return = null;
	protected $data = array();

	//inisialisasi bila role crud tidak ditemukan maka inisialisasikan CREATE / READ / UPDATE / DELETE
	protected $null = array('create' => true, 'read' => true, 'update' => true, 'delete' => true ); //jadikan false semua


	public function __construct()
	{
        $this->ci =& get_instance();
        $lastupdate = $this->ci->session->userdata('lastupdate');
        $nextupdate = $lastupdate+60000;

        //cek dan setting role
        if ($this->ci->session->userdata('user_logged')===TRUE && $this->ci->session->userdata('user_type')=='employee') {
        	if (empty($this->ci->session->userdata('role')) OR empty($lastupdate) OR
        		($nextupdate)<=current_millis()) {
        		//ambil role terbaru dari database
        		$role = $this->_getRole();

        		/* simpan role ke dalam session START */
				$update_role_session = array(
					'role' => json_decode($role),
					'lastupdate' => current_millis(),
				);
				$this->ci->session->set_userdata( $update_role_session ); //simpan role ke session
				/* simpan role ke dalam session END */
        	}
	        

	        

	        //protected $role
	        $this->role = $this->ci->session->userdata('role'); //setting role ke variabel pada library
	        
	        //konversi format variabel null
	        $this->null = json_decode(json_encode($this->null));
    	}
	}

	//AMBIL ROLE DARI DATABASE
	public function _getRole()
	{
		$user_id = $this->ci->session->userdata('user_id');

		//get current role v1
		/*
        $this->ci->db->select('role');
        $this->ci->db->from('view_employee');
        $this->ci->db->where('employee_id', $this->ci->session->userdata('user_id'));
        $coba = $this->ci->db->get();
        $dbdata = $coba->row();
        */

        //get current role v2
        $this->ci->db->select('e.role');
        $this->ci->db->from('employee e');
        $this->ci->db->where('e.employee_id', $user_id);
        $dbdata = $this->ci->db->get()->row();

        //return
        return $dbdata->role;
	}

	//buat disabled access pada halaman yang tidak diizinkan
	public function _disablepage($data=null)
	{
		$this->ci->load->view('disablepage_v', $data);
		die();
	}

	//setting role
	public function _role($page, $data, $crud, $page_error)
	{
		/*
		NB: $page_error diisi dengan array
		contoh:
		$page_error = array('type'=>'disablepage');
		$page_error = array('type'=>'redirect','link'=>'http://uniq.tk')
		*/
		if ($this->ci->session->userdata('user_type')=='employee') {
			//inisialisasi
			//$page = 'dashboard';
			//$this->data['pagename'] = 'Dashboard';
			$this->data = $data;
			$this->role_return = (!empty($this->role->$page)) ? $this->role->$page : $this->null; //tidak usah diubah

			//crud setting (tidak usah diubah)
			switch ($crud) {
				case 'read_access':
					if ($this->role_return->read!==TRUE) {
						if (empty($page_error)) {
							redirect(site_url());
						}
						if ($page_error['type']=='page') {
							$this->_disablepage($this->data);
						}
						elseif ($page_error['type']=='move') {
							redirect($page_error['link']);
							die();
						}
					}
					break;
				case 'read':
					$this->role_return = $this->role_return->read;
					break;
				
				default:
					die('Role undefined');
					break;
			}
		}

		return $this->role_return; //tidak usah diubah
	}

	//DAFTAR FITUR DAN SUB-FITUR DAN SUB-SUBFITUR
	public function accesslist($tipe)
	{
		$description = json_decode(file_get_contents(APPPATH."controllers/employees/description.json"));
		$menu = array();
		switch ($tipe) {
			case 'mainmenu':
				//main menu role
				$menu = array(
					array('dashboard','Dashboard'),
					array('reports','Reports'),
					array('products','Products'),
					array('purchase','Purchase'),
					array('stock','Stock'),
					array('outlets','Outlets'),
					array('employees','Employees'),
					array('customer','Customer'),
					array('promotion','Promotion'),
					array('sales','Sales'),
					array('production','Production'),
					array('authorized','Authorized'),
				);
				break;
			case 'mobilemenu': /*mobile menu belum diganti */
				$menu = array(
					array('inputkasmasuk','Add Cash In',$description[0]->mobile->inputkasmasuk),
					array('inputkaskeluar','Add Cash Out',$description[0]->mobile->inputkaskeluar),
					array('inputpembelian','Add Purchase',$description[0]->mobile->inputpembelian),
					array('setting_printer','Printer Setting',$description[0]->mobile->setting_printer),
					array('tutupkasir','Close Cash Register',$description[0]->mobile->tutupkasir),
					array('reprint_nota','Re-print Receipt',$description[0]->mobile->reprint_nota),
					array('reprint_order','Re-print Order',$description[0]->mobile->reprint_order),
					array('reprint_tutupkasir','Re-print Close Cash Register',$description[0]->mobile->reprint_tutupkasir),//add
					array('gantiharga','Change Price',$description[0]->mobile->gantiharga),
					array('gantidiskonperbill','Change Discount on Bill',$description[0]->mobile->gantidiskonperbill), //array('gantidiskontaxgratuity','Change Discount on Tax Gratuity'),
					array('bukalaciuang','Open Cash Drawer',$description[0]->mobile->bukalaciuang),
					array('pembayaran','Payment',$description[0]->mobile->pembayaran),
					array('compliment','Compliment',$description[0]->mobile->compliment),
					array('voucher','Voucher',$description[0]->mobile->voucher),
					array('gantivoucherperitem','Change Voucher on Item',$description[0]->mobile->gantivoucherperitem), //add
					array('simpankeorderlist','Save to Order List',$description[0]->mobile->simpankeorderlist), //add
					array('duty','Duty Meals',$description[0]->mobile->duty),
					array('gantidiskonperitem','Change Discount on Item',$description[0]->mobile->gantidiskonperitem),//array('gantidiskon','Change Discount'),
					array('gantipajak','Change Tax',$description[0]->mobile->gantipajak),
					array('reprint_refund','Re-print Refund',$description[0]->mobile->reprint_refund),
					array('refund','Refund',$description[0]->mobile->refund),
					array('inputbarangrusak','Add Damaged Item',$description[0]->mobile->inputbarangrusak),
					array('inputbaranghabis','Add Out Item',$description[0]->mobile->inputbaranghabis),
					array('inputpromo','Add Promo',$description[0]->mobile->inputpromo),
					array('masterlogin','Master Login',$description[0]->mobile->masterlogin),
					array('meja_split','Split Table',$description[0]->mobile->meja_split), //gabung meja
					array('meja_move','Move Table',$description[0]->mobile->meja_move), //pindah meja
					array('viewcloseregister','View Close Register Record',$description[0]->mobile->viewcloseregister),
					array('viewtotalachievement','View Total Achievement',$description[0]->mobile->viewtotalachievement),
					array('viewtransactionhistory','View Transaction History Record',$description[0]->mobile->viewtransactionhistory),
					array('product_create', 'Add Product',$description[0]->mobile->product_create),
					array('print_dailyrecap', 'Print Daily Recap',$description[0]->mobile->print_dailyrecap),
					array('authorization','Authorization',$description[0]->mobile->authorization),
				);
				break;
			case 'mobilemenu_authorization':
				$menu = array(
					array('diskonall','Discount All',$description[0]->mobile->otorisasi->diskonall),
					array('diskonperitem','Discount on Item',$description[0]->mobile->otorisasi->diskonperitem),
					array('freeitem','Free Item',$description[0]->mobile->otorisasi->freeitem),
					array('refund','Refund',$description[0]->mobile->otorisasi->refund),
					array('reprint_refund','Re-print Refund',$description[0]->mobile->otorisasi->reprint_refund),
					array('reprint_receipt','Re-print Receipt',$description[0]->mobile->otorisasi->reprint_receipt),
					array('closeshift','Close Shift',$description[0]->mobile->otorisasi->closeshift),
					array('reprint_closeshift','Re-print Close Shift',$description[0]->mobile->otorisasi->reprint_closeshift),
					array('void','Void',$description[0]->mobile->otorisasi->void),
				);
				break;
			case 'stock_lv1':
				$menu = array(
					array('stock_daycategory','Day Category'),
					array('stock_estimation','Stock Estimation'),
					array('stock_summary','Stock Summary'),
					array('stock_opname','Stock Opname'),
					array('stock_spoil','Spoil'),
					array('stock_equipmentrequest','Equipment Request'),
				);
				break;
			case 'products_lv1':
				$menu = array(
					array('products_type','Type'),
					array('products_category','Category'),
					array('products_subcategory','Sub-Category'),
					array('products_purchasereportcategory','Purchase Report Category'),
					array('products_unit','Unit'),
					array('products_catalogue', 'Products Catalogue'),
					array('products_description','Products Description'),
					array('products_opcostcatalogue','Operational Cost Catalogue'),
					array('products_breakdown','Breakdown'),
					array('products_taxgratuity','Tax &amp; Gratuity'),
				);
				break;
			case 'purchase_lv1':
				$menu = array(
					array('purchase_supplier','Supplier'),
					array('purchase_purchase','Purchase'),
					array('purchase_debt','Debt'),
					array('purchase_transfer','Transfer'),
				);
				break;
			default:
				$menu = array();
				break;
		}
		return $menu;
	}

	//DASHBOARD (MAIN MENU)
	public function dashboard($crud=null)
	{
		$page = 'dashboard'; //role yang diambil dari DB
		$data['pagename'] = 'Dashboard'; //nama halaman yang diambil
		$page_error['type'] = 'page';
		return $this->_role($page, $data, $crud, $page_error);
	}


	//REPORTS (MAIN MENU)
	public function reports($crud=null)
	{
		$page = 'reports'; //role yang diambil dari DB
		$data['pagename'] = 'Reports'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}

	/* Report sub-navbar LV1 START */
	public function reports_sales($crud=null)
	{
		$page = 'reports_sales'; //role yang diambil dari DB
		$data['pagename'] = 'Sales Report'; //nama halaman yang diambil
		$page_error['type'] = 'move'; //tipe disable page
		$page_error['link'] = site_url('reports/history'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function reports_history($crud=null)
	{
		$page = 'reports_history'; //role yang diambil dari DB
		$data['pagename'] = 'History Report'; //nama halaman yang diambil
		$page_error['type'] = 'move'; //tipe disable page
		$page_error['link'] = site_url('reports/purchase'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function reports_purchase($crud=null)
	{
		$page = 'reports_purchase'; //role yang diambil dari DB
		$data['pagename'] = 'Purchase Report'; //nama halaman yang diambil
		$page_error['type'] = 'move'; //tipe disable page
		$page_error['link'] = site_url('reports/stock'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function reports_stock($crud=null)
	{
		$page = 'reports_stock'; //role yang diambil dari DB
		$data['pagename'] = 'Stock Report'; //nama halaman yang diambil
		$page_error['type'] = 'move'; //tipe disable page
		$page_error['link'] = site_url('reports/finance'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function reports_finance($crud=null)
	{
		$page = 'reports_finance'; //role yang diambil dari DB
		$data['pagename'] = 'Finance Report'; //nama halaman yang diambil
		$page_error['type'] = 'move'; //tipe disable page
		$page_error['link'] = site_url('reports/auto-email'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function reports_autoemail($crud=null)
	{
		$page = 'reports_autoemail'; //role yang diambil dari DB
		$data['pagename'] = 'Auto E-mail Report'; //nama halaman yang diambil
		$page_error['type'] = 'move'; //tipe disable page
		$page_error['link'] = site_url('reports'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	/* Report sub-navbar LV1 END */


	//PRODUCTS (MAIN MENU)
	public function products($crud=null)
	{
		$page = 'products'; //role yang diambil dari DB
		$data['pagename'] = 'Products'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}

	/* Products sub-navbar LV1 START */
	public function products_type($crud=null)
	{
		$page = 'products_type'; //role yang diambil dari DB
		$data['pagename'] = 'Products Type'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/category'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_category($crud=null)
	{
		$page = 'products_category'; //role yang diambil dari DB
		$data['pagename'] = 'Products Category'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/subcategory'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_subcategory($crud=null)
	{
		$page = 'products_subcategory'; //role yang diambil dari DB
		$data['pagename'] = 'Products Sub-Category'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/purchse-report-category'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_purchasereportcategory($crud=null)
	{
		$page = 'products_purchasereportcategory'; //role yang diambil dari DB
		$data['pagename'] = 'Purchase Report Category'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/unit'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_unit($crud=null)
	{
		$page = 'products_unit'; //role yang diambil dari DB
		$data['pagename'] = 'Unit'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/product-catalogue'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_catalogue($crud=null)
	{
		$page = 'products_catalogue'; //role yang diambil dari DB
		$data['pagename'] = 'Products Catalogue'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/product-description'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_description($crud=null)
	{
		$page = 'products_description'; //role yang diambil dari DB
		$data['pagename'] = 'Products Description'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/operationalcost-catalogue'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_opcostcatalogue($crud=null)
	{
		$page = 'products_opcostcatalogue'; //role yang diambil dari DB
		$data['pagename'] = 'Operational Cost Catalogue'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/breakdown'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_breakdown($crud=null)
	{
		$page = 'products_breakdown'; //role yang diambil dari DB
		$data['pagename'] = 'Breakdown'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/taxgratuity'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function products_taxgratuity($crud=null)
	{
		$page = 'products_taxgratuity'; //role yang diambil dari DB
		$data['pagename'] = 'Products Tax & Gratuity'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	/* Products sub-navbar LV1 END */


	//PURCHASE (MAIN MENU)
	public function purchase($crud=null)
	{
		$page = 'purchase'; //role yang diambil dari DB
		$data['pagename'] = 'Purchase'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}
	/* Purchase sub-navbar LV1 START */
	public function purchase_supplier($crud=null)
	{
		$page = 'purchase_supplier'; //role yang diambil dari DB
		$data['pagename'] = 'Supplier'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/category'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function purchase_purchase($crud=null)
	{
		$page = 'purchase_purchase'; //role yang diambil dari DB
		$data['pagename'] = 'Purchase'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/category'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function purchase_debt($crud=null)
	{
		$page = 'purchase_debt'; //role yang diambil dari DB
		$data['pagename'] = 'Debt'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/category'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}
	public function purchase_transfer($crud=null)
	{
		$page = 'purchase_transfer'; //role yang diambil dari DB
		$data['pagename'] = 'Transfer'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		//$page_error['link'] = site_url('products/category'); //link yang akan dituju
		return $this->_role($page, $data, $crud, $page_error);
	}



	//STOCK (MAIN MENU)
	public function stock($crud=null)
	{
		$page = 'stock'; //role yang diambil dari DB
		$data['pagename'] = 'Stock'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		$return = $this->_role($page, $data, $crud, $page_error);
		return $return;
	}
	public function stock_daycategory($crud=null)
	{
		$page = 'stock_daycategory'; //role yang diambil dari DB
		$data['pagename'] = 'Day Category'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		$return = $this->_role($page, $data, $crud, $page_error);

		$this->stock('read_access');
		return $return;
		
	}
	public function stock_estimation($crud=null)
	{
		$page = 'stock_estimation'; //role yang diambil dari DB
		$data['pagename'] = 'Stock Estimation'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		$return = $this->_role($page, $data, $crud, $page_error);

		$this->stock('read_access');
		return $return;
	}
	public function stock_summary($crud=null)
	{
		$page = 'stock_summary'; //role yang diambil dari DB
		$data['pagename'] = 'Stock Summary'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		$return = $this->_role($page, $data, $crud, $page_error);

		$this->stock('read_access');
		return $return;
	}
	public function stock_opname($crud=null)
	{
		$page = 'stock_opname'; //role yang diambil dari DB
		$data['pagename'] = 'Stock Opname'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		$return = $this->_role($page, $data, $crud, $page_error);

		$this->stock('read_access');
		return $return;
	}
	public function stock_spoil($crud=null)
	{
		$page = 'stock_spoil'; //role yang diambil dari DB
		$data['pagename'] = 'Stock Spoil'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		$return = $this->_role($page, $data, $crud, $page_error);

		$this->stock('read_access');
		return $return;
	}
	public function stock_equipmentrequest($crud=null)
	{
		$page = 'stock_equipmentrequest'; //role yang diambil dari DB
		if (site_url(uri_string()) != site_url('stock/equipment-request')) {
			$data['pagename'] = 'Stock';
		}
		else{
			$data['pagename'] = 'Equipment Request'; //nama halaman yang diambil
		}
		$page_error['type'] = 'page'; //tipe disable page
		$return = $this->_role($page, $data, $crud, $page_error);

		$this->stock('read_access');
		return $return;
	}


	//OUTLETS
	public function outlets($crud=null)
	{
		$page = 'outlets'; //role yang diambil dari DB
		$data['pagename'] = 'Outlets'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}

	//EMPLOYEES
	public function employees($crud=null)
	{
		$page = 'employees'; //role yang diambil dari DB
		$data['pagename'] = 'Employees'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}

	//CUSTOMER
	public function customer($crud=null)
	{
		$page = 'customer'; //role yang diambil dari DB
		$data['pagename'] = 'Customer'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}

	//PROMOTION
	public function promotion($crud=null)
	{
		$page = 'promotion'; //role yang diambil dari DB
		$data['pagename'] = 'Promotion'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}

	//SALES
	public function sales($crud=null)
	{
		$page = 'sales'; //role yang diambil dari DB
		$data['pagename'] = 'Sales'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}

	//PRODUCTION
	public function production($crud=null)
	{
		$page = 'production'; //role yang diambil dari DB
		$data['pagename'] = 'Production'; //nama halaman yang diambil
		$page_error['type'] = 'page'; //tipe disable page
		return $this->_role($page, $data, $crud, $page_error);
	}
}

/* End of file User_role.php */
/* Location: ./application/libraries/User_role.php */
