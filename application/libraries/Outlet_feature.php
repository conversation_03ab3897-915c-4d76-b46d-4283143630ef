<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Outlet_feature
{
	protected $ci;

	public function __construct()
	{
		$this->ci =& get_instance();
	}

	public function featurelist($feature)
	{
		$featurelist = array();
		switch ($feature) {
			case 'mainfeature':
				$featurelist = array(
					array('authorization', 'Authorization', 'Manage authorization settings for various actions.'),
					array('tableinput', 'Table Input', 'Displays a table selection input in transactions, allowing users to manage and assign tables.'),
					array('splitbill', 'Split Bill', 'Enables splitting a bill among multiple customers.'),
					array('splittable', 'Split Table', 'Allows dividing a table into separate sections for different groups.'),
					array('movetable', 'Move Table', 'Enables transferring customers from one table to another.'),
					array('notelist', 'Note List', 'Acts as a transaction cart, allowing users to save transactions and complete payment later.'),
					array('member', 'Member', 'Displays the member list, making it easier for cashiers to search for members and assign them to a transaction or bill.'),
					array('discount', 'Discount', 'Allows cashiers to apply discounts to a transaction.'),
					array('refund', 'Refund', 'Enables cashiers to process transaction refunds.'),
					array('free', 'Free', 'Allows cashiers to mark a transaction as free or apply a 100% discount.'),
					array('void', 'Void', 'Allows cashiers to cancel transactions in the cart. Requires the Note List feature to be enabled.'),
					array('reprint_closeregister', 'Re-print Close Register', 'Allows users to reprint closing register reports.'),
					array('viewcloseregister', 'View Close Register Record', 'Grants access to view the closing register history.'),
					array('viewtotalachievement', 'View Total Achievement', 'Displays the total transactions completed in the current shift.'),
					array('viewtransactionhistory', 'View Transaction History Record', 'Provides access to the complete transaction history.'),
					array('waitingtime', 'Waiting Time', 'Allows users to input and track the estimated waiting time for a transaction.'),
					array('numberofcustomers', 'Number of Customers', 'Displays an input field for entering the number of customers (pax) in a transaction.'),
					array('employeename', 'Employee Name', 'Requires the cashier to select an employee handling the transaction, ensuring proper tracking.'),
					array('offlinemode', 'Offline Mode', 'Enables the system to function without an internet connection.'),
					array('transaction_zerostock', 'Transaction when Run Out of Stock', 'Determines whether transactions can proceed when items are out of stock.'),
					array('transaction_requires_tag', 'Transaction Requires Tag', 'Requires cashiers to select a tag before saving a transaction.'),
				);				
				break;
			case 'authorization':
				$featurelist = array(
					array('refund','Refund'),
					array('void','Void'),
					array('reprint_refund','Re-print Refund'),
					array('reprint_receipt','Re-print Receipt'), //reprint nota
					array('reprint_closeshift','Re-print Close Shift'),
					array('closeshift','Close Shift'),
					array('diskonall','Discount All'),
					array('diskonperitem','Discount on Item'),
					array('free','Free'),
				);
				break;
			default:
				$featurelist = array();
				break;
		}

		return $featurelist;
	}

}

/* End of file Outlet_feature.php */
/* Location: ./application/libraries/Outlet_feature.php */
