<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_key_resetdata_lib
{
	protected $ci;

	public function __construct()
	{
		$this->ci =& get_instance();

		//load
        $this->ci->load->helper('password');
		$this->ci->load->helper('sendemail');
		$this->ci->load->model('account/Userkey_model');
	}

	//fungsi untuk buat key
	public function _kodeRandom($panjang_key)
	{
		$string		= 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		$string		.= '**********';

		$karakter = '';
		$karakter .= $string;

		$string = '';
		for ($i=0; $i < $panjang_key; $i++) { 
			$pos = rand(0, strlen($karakter)-1);
			$string .= $karakter{$pos};
		}
		return $string;
	}


	//kirim email konfirmasi reset data
	public function create_confirm_resetdata($email, $plain_datakey)
	{
		//user type = admin / employee

		//buat key konfirmasi penggantian email
		$key = $this->_kodeRandom(10);
		$key_md5 = md5($key); //simpan hasil generate key ke MD5
		$key_DB = $plain_datakey.'&key='.$key_md5; //plain text yang akan dikirim ke user
		$key_encrypt = password_encrypt($key_DB); //HASH KEY untuk dimasukkan DB user_key
		
		//masukkan data ke DB users_key
		$data_insert = array(
			'user_level' => 'admin',
			'email' => $email,
			'secret_key' => $key_encrypt,
			'key_type' => 'resetdata'
		);
		$response = $this->ci->Userkey_model->create_key($data_insert, 600); // memasukkan data ke database

		//konten untuk isi email
		if ($response===true) {
			$link_konfirmasi = site_url('confirmation/resetdata/'.urlencode($key_DB).'/'.urlencode($email));

			$data_message['link_konfirmasi'] = $link_konfirmasi;
			$message = $this->ci->load->view('email_template/user_resetdata_v', $data_message, true);

			return sendMail($email, 'Reset Data Confirmation', $message);
		}
	}

}

/* End of file User_key_resetdata_lib.php */
/* Location: ./application/libraries/user_key/User_key_resetdata_lib.php */
