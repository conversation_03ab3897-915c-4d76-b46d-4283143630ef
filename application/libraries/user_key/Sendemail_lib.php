<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/*
Library ini digunakan untuk mengirim email kepada user
*/

class Sendemail_lib
{
	protected $ci;

	public function __construct()
	{
        $this->ci =& get_instance();
        $this->ci->load->helper('sendemail');
	}

	//pemberitahuan bahwa akun berhasil diaktivasi
	public function user_activation_success($email, $plain_password)
	{
		$data = array(
			'email' => $email,
			'password' => $plain_password
		);

		$message_template = $this->ci->load->view('email_template/user_activation_success_v', $data, TRUE);
		sendMail($email, 'UNIQ Account Information', $message_template);
	}

}

/* End of file Sendemail_lib.php */
/* Location: ./application/libraries/user_key/Sendemail_lib.php */
