<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_key_change_email_lib
{
	protected $ci;

	public function __construct()
	{
        $this->ci =& get_instance();

        $this->ci->load->helper('encryption/aes');
        $this->ci->load->helper('sendemail');
		$this->ci->load->model('account/Userkey_model');
	}

	//fungsi untuk buat key
	public function _kodeRandom($panjang_key)
	{
		$string		= 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		$string		.= '**********';

		$karakter = '';
		$karakter .= $string;

		$string = '';
		for ($i=0; $i < $panjang_key; $i++) { 
			$pos = rand(0, strlen($karakter)-1);
			$string .= $karakter{$pos};
		}
		return $string;
	}

	//kirim persetujuan pergantian email
	public function create_confirm_change_email($oldemail, $newemail, $user_type)
	{
		//user type = admin / employee

		//buat key konfirmasi penggantian email
		$key = $this->_kodeRandom(10);
		$key_md5 = md5($key); //simpan hasil generate key ke MD5
		$key_DB = 'newemail='.$newemail.'&key='.$key_md5; //plain text yang akan dikirim ke user
		$key_encrypt = password_encrypt($key_DB); //HASH KEY untuk dimasukkan DB user_key
		
		//masukkan data ke DB users_key
		$data_insert = array(
			'user_level' => $user_type,
			'email' => $oldemail,
			'secret_key' => $key_encrypt,
		);
		$response = $this->ci->Userkey_model->confirm_change_email($data_insert); // memasukkan data ke database


		//encrypt data
		$ci = $this->ci;
		$string = json_encode([
			'business_id' => $ci->session->userdata('admin_id'),
			'user_type' => $ci->session->userdata('user_type'),
			'user_id' => $ci->session->userdata('user_id'),
			'email_old' => $oldemail,
			'email_new' => $newemail
		]);
		$data_encrypt = encryptaes256($string, $key_md5);
		$data_encrypt = base64_encode($data_encrypt);

		//konten untuk isi email
		if ($response===true) {
			//v1
			// $link_konfirmasi = site_url('confirmation/change_email/'.urlencode($key_DB).'/'.urlencode($oldemail)) . "?data=".$data_encrypt;

			//v2
			$link_konfirmasi = site_url('confirmation/change_email?to='.$newemail.'&data='.$data_encrypt.'&key='.$key_md5);

			$data_message['link_konfirmasi'] = $link_konfirmasi;
			$data_message['newemail'] = $newemail;
			$message = $this->ci->load->view('email_template/user_change_email_v', $data_message, true);

			return sendMail($oldemail, 'Change E-mail Confirmation', $message);
		}
	}	

}

/* End of file User_key_changeemail_lib.php */
/* Location: ./application/libraries/user_key/User_key_change_email_lib.php */
