<?php
use Firebase\JWT\JWT;

defined('BASEPATH') OR exit('No direct script access allowed');

class Api_chat
{
    protected $ci;
    protected $api;
    protected $api_token;
    protected $chainlit_secret_key;
    protected $ci_session_id; //current user session id
    protected $user_id; //current user id
    protected $cookie_name = 'uniq-chat-api';

    public function __construct()
    {
        $this->ci =& get_instance();

        $chainlit_secret_key = getenv('CHAINLIT_SECRET_KEY');
        if (empty($chainlit_secret_key) || $chainlit_secret_key === FALSE) {
            $chainlit_secret_key = '';
        }
        $this->chainlit_secret_key = $chainlit_secret_key;

        //get CI session ID
		$this->ci_session_id = get_cookie( $this->ci->config->item('sess_cookie_name') );
        $this->user_id = 'user_'.$this->ci->session->userdata('user_id').'_'.getenv('CI_ENV');//$this->session->userdata('user_id');
        $this->user_name = $this->ci->session->userdata('user_name');
        $this->token = get_cookie( $this->cookie_name );
        $this->api_token =  $this->ci->api_web->getToken();

        // file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." --- session: ".json_encode($this->ci->session)."\n");
        // file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." --- cookie: ".$this->token."\n");
        // file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." --- cookie: ".$this->api_token."\n");

        // Initialize Guzzle client
        $host = rtrim(getenv('SERVICE_CHAT'), '/') . '/';
        $this->api = new \GuzzleHttp\Client(['base_uri' => $host, 'verify' => false]);
    }

    public function create_chainlit_jwt(): string
    {        
        if ($this->token != "" && !empty($this->token)) {
            // file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." --- use chat token from cookie '".substr($this->token, 10)."'\n");
			return $this->token;
		}

        $currentTime = time();
        $expire = time() + (60 * 60 * 3);
        // Create the payload for the JWT
        $payload = [
            'identifier' => $this->user_id,
            'metadata' => array_merge(['session_id' => $this->ci_session_id, 'user_name' => $this->user_name, 'api_token' => $this->api_token, 'env' => getenv('CI_ENV')],),
            'exp' => $expire, // 15 days expiration
        ];

        // Encode the JWT using HS256 algorithm and your secret key
        $jwt = JWT::encode($payload, $this->chainlit_secret_key, 'HS256');

        //set cookie
        $secure = (ENVIRONMENT!=='development') ? true : false;
        $cookie_expired = ($expire - $currentTime) ?? $currentTime;
        set_cookie($this->cookie_name,  $jwt, $cookie_expired, null, "/", null, $secure, true);
        
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." --- chat token generated for '".$this->user_name."'\n");
        return $jwt;
    }
}