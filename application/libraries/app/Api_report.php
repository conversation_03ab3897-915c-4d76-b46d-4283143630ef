<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Api_report
{
	protected $ci;
	protected $api;
	protected $api_token;
	protected $ci_session_expired;
	protected $cookie_name = 'uniq-report'; //service report cookie name
	protected $currentSessionID;
	protected $remembermeName;

	public function __construct()
	{
        $this->ci =& get_instance();
        // $this->ci->load->helper('cookie'); //already load on autoload

        //get CI session expired time
        $this->ci_session_expired = (($this->ci->config->item('sess_expiration')) ?? 3600 * 3) + 3600 ;

        //get CI session ID
        $this->currentSessionID = get_cookie( $this->ci->config->item('sess_cookie_name') );

        //get rememberme Name
        $this->ci->config->load('auth/auth',true);
        $this->remembermeName = $this->ci->config->item('remember_cookie_name','auth');


        //set default endpoint of Guzzle request
        $this->api_token = getenv('SERVICE_REPORT_TOKEN');
        $host = rtrim(getenv('SERVICE_REPORT'), '/') . '/';
		$this->api = new \GuzzleHttp\Client(['base_uri' => $host,'verify' => false]);
	}

	public function auth($email, $password)
	{
		try {
			$response = $this->api->request('POST','v1/auth/login',[
				'headers' => [
					'Authorization' => $this->api_token,
					'issuer' => $this->currentSessionID
				],
				'form_params' => [
					'email' => $email,
					'password' => $password
				]
			]);
			$jsonRaw = $response->getBody()->getContents();
			if (is_json($jsonRaw)) {
				$jsonData = json_decode($jsonRaw);
				if ($jsonData->status==true) {
					//set cookie
					$token_scheme = $jsonData->data->authorization->type;
					$token = $jsonData->data->authorization->token;
					$token_expired = $jsonData->data->authorization->expired;
					$token_refresh = $jsonData->data->authorization->refresh_token;

					$data = json_encode([
						'token' => $token_scheme." ".$token,
						'token_expired' => $token_expired,
						'refresh' => $token_refresh
					]);
					$data = base64_encode($data);

					//set cookie
					$secure = (ENVIRONMENT!=='development') ? true : false;
					set_cookie($this->cookie_name, $data, $this->ci_session_expired, null, "/", null, $secure, true);
				}
			}
		} catch (Exception $e) {
			//.....
		}
	}

	public function get_new_token()
	{
		$jsonRaw = null;
		$valid = false;

		//get new token with refresh token
		if ($valid===false && !empty($this->data()->refresh)) {

			$refresh_token = $this->data()->refresh;

			try {
				$response = $this->api->request('POST','v1/auth/token', [
					'headers' => [
						'Authorization' => $this->api_token
					],
					'form_params' => [
						'refresh_token' => $refresh_token
					]
				]);
				$jsonRaw = $response->getBody()->getContents();
				$valid = (is_json($jsonRaw)) ? true : false;

			} catch (Exception $e) {
				// echo "error:";
				// echo "\n";
				// echo $e;
			}
		}


		//get new token with rememberme
		if ($valid===false && (get_cookie($this->remembermeName)<>"") || $this->ci->session->flashdata($this->remembermeName)<>"") {
			$rememberme = get_cookie($this->remembermeName<>"") ?? $this->ci->session->flashdata($this->remembermeName);

			try {
				$response = $this->api->request('POST','v1/auth/token_by_rememberme', [
					'headers' => [
						'Authorization' => $this->api_token,
						'rememberme' => $rememberme
					]
				]);
				$jsonRaw = $response->getBody()->getContents();
				$valid = (is_json($jsonRaw)) ? true : false;

			} catch (Exception $e) {
				// echo "error:";
				// echo "\n";
				// echo $e;
			}
		}


		//get new token with current session
		if ($valid===false) {
			
			try {
				$response = $this->api->request('POST','v1/auth/token_by_phpsession', [
					'headers' => [
						'Authorization' => $this->api_token,
						'sessionID' => $this->currentSessionID
					]
				]);
				$jsonRaw = $response->getBody()->getContents();
				$valid = (is_json($jsonRaw)) ? true : false;

			} catch (Exception $e) {
				// echo "error:";
				// echo "\n";
				// echo $e;
			}
		}


		if (is_json($jsonRaw)) {
			$jsonData = json_decode($jsonRaw);

			//set cookie
			$token_scheme = $jsonData->type;
			$token = $jsonData->token;
			$token_expired = $jsonData->expired;
			$token_refresh = $jsonData->refresh_token;

			$data = ([
				'token' => $token_scheme." ".$token,
				'token_expired' => $token_expired,
				'refresh' => $token_refresh
			]);
			$dataBase64 = base64_encode(json_encode($data));

			//set cookie
			$secure = (ENVIRONMENT!=='development') ? true : false;
			set_cookie($this->cookie_name, $dataBase64, $this->ci_session_expired, null, "/", null, $secure, true);

			//use flash data to handle writing cookie
			$this->ci->session->set_flashdata($this->cookie_name, $data);
			// $this->ci->session->set_userdata( ['uniq-service'=>['report'=>$data]]);

			return $jsonData;
		}
	}

	/* get current data of api-report */
	public function data()
	{
		if (!empty($this->ci->session->flashdata($this->cookie_name))) {
			return (object) $this->ci->session->flashdata($this->cookie_name);
		}else{
			$data = get_cookie($this->cookie_name);
			if (!empty($data)) {
				$data = base64_decode($data);
				if (is_json($data)) {
					return json_decode($data);
				}
			}
			return false;;
		}
	}
	public function data1_bak()
	{
		$data = get_cookie($this->cookie_name);
		if (!empty($data)) {
			$data = base64_decode($data);
			if (is_json($data)) {
				return json_decode($data);
			}else{
				return false;
			}
		}else{
			// return (object) $this->ci->session->userdata('uniq-service')['report'];
			return (object) $this->ci->session->flashdata($this->cookie_name);
		}
	}

	//remove all api-report data
	public function clear()
	{
		if (!empty($this->data())) {

			//revoke current api-report token
			try {
				$response = $this->api->request('POST','v1/auth/token_refresh_revoke', [
					'headers' => [
						'Authorization' => $this->api_token
					],
					'form_params' => [
						'refresh_token' => $this->data()->refresh
					]
				]);
				$jsonRaw = $response->getBody()->getContents();
				$valid = (is_json($jsonRaw)) ? true : false;

			} catch (Exception $e) {
				// echo "error:";
				// echo "\n";
				// echo $e;
			}
			
		}

		//remove cookie on client
		delete_cookie($this->cookie_name);
	}

}

/* End of file Api_report.php */
/* Location: ./application/libraries/app/Api_report.php */
