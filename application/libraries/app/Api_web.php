<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Api_web
{
	protected $ci;
	protected $ci_session_id; //current user session id
	protected $api; //guzzle api request
	protected $api_url; //endpoint url
	protected $cookie_name = 'uniq-api';
	protected $token;

	public function __construct()
	{
		$this->ci =& get_instance();
		$this->api_url = rtrim(getenv('SERVICE_API'), '/') . '/';


		//get CI session ID
		$this->ci_session_id = get_cookie( $this->ci->config->item('sess_cookie_name') );

		//set guzzle config
		$this->api = new \GuzzleHttp\Client(['base_uri' => $this->api_url, 'verify'=>false]);
	}

	public function create_token_by_session()
	{
		$token = "";
		try {
			$response = $this->api->request('GET','auth/v1/session',[
				'headers' => [
					'Authorization' => $this->ci_session_id,
				]
			]);
			$jsonRaw = $response->getBody()->getContents();
			$statusCode = $response->getStatusCode();
			if (is_json($jsonRaw) && $statusCode == 200) {
				$jsonData = json_decode($jsonRaw, true);

				//ambil token
				$token = json_encode($jsonData);
			}
		} catch (Exception $e) {
			//.....
		}

		return $token;
	}


	public function getApiUrl()
	{
		return $this->api_url;
	}

	public function getToken()
	{
		//v1
		// $token = get_cookie($this->cookie_name."_type")." ".get_cookie($this->cookie_name);
		// $token = ($this->token ?? $token);

		//v2
		if ($this->token != "" || !empty($this->token)) {
			$token = $this->token;
		}else{
			$token = base64_decode(get_cookie($this->cookie_name));
			$token = json_decode($token,true)['token'];
		}

		return $token;
	}

	public function setToken($value='')
	{
		$this->token = $value;
	}


	// ------------------------------------------------------------------------
	public function generate_and_save_token()
	{
		$currentTime = time();
		$jsonToken 	= $this->create_token_by_session();
		if (is_json($jsonToken)) {
			$token 		= json_decode($jsonToken, true);
			$token_data = base64_encode(json_encode([
				'token' => $token['access_token_type'].' '.$token['access_token'],
				'expired' => $token['access_token_expired']
			]));

			//set cookie
			$secure = (ENVIRONMENT!=='development') ? true : false;
			$cookie_expired = ($token['access_token_expired'] - $currentTime) ?? $currentTime;
			set_cookie($this->cookie_name,  $token_data, $cookie_expired, null, "/", null, $secure, true);

			//share token
			$this->setToken($token['access_token_type'].' '.$token['access_token']);
		}
	}
}

/* End of file Api_web.php */
/* Location: ./application/libraries/app/Api_web.php */
