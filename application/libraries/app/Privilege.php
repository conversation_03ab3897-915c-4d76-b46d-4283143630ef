<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Privilege
{
	protected $ci;
	protected $dev;

	public function __construct()
	{
        $this->ci =& get_instance();
        $this->dev = $this->ci->session->userdata('developer_mode');
	}

	public function role_web()
	{
		$data = array();

		$data['dashboard'] = array(
			'text' => 'Dashboard',
			'url' => 'dashboard',
			'access_page' => array( 'view' => true ),
			'access_detail' => array(
				'view_cashflow' => 'View Cash Flow',
				'view_entertainincome' => 'View Entertain Income',
				'view_sales_analysis' => 'View Sales Analysis',
				'view_sales_transaction' => 'View Sales Transaction',
				'view_sales_top' => 'View Sales Top',
				'view_sales_categories' => 'View Sales by Categories',
				'date_min' => array(
					'text' => 'Minimal Date Range',
					'type' => 'date',
				)
			)
		);
		// if (ENVIRONMENT!='production') {
		// 	$dataX = array('date_min' => array(
		// 					'text' => 'Minimal Date Range',
		// 					'type' => 'date'
		// 				)
		// 			);
		// 	$data['dashboard']['access_detail'] = array_merge($data['dashboard']['access_detail'], $dataX);
		// }

		
		$data['reports'] = array(
			'text' => 'Reports',
			'url' => 'reports',
			'access_page' => array('view'=>true),
			'access_detail' => [],
		);
		if (ENVIRONMENT!='production') {
			$dataX = array('date_min' => array(
							'text' => 'Minimal Date Range',
							'type' => 'date'
						)
					);
			$data['reports']['access_detail'] = array_merge($data['reports']['access_detail'], $dataX);
		}
			//LEVEL 2 REPORTS
			$data['reports']['sub']['reports_sales'] = array(
				'text' => 'Sales',
				'url' => 'reports/sales',
				'access_page' => array('view'=>true),
				'sub' => array(
					array(
						'text' => 'Sales Summary',
						'url' => 'reports/sales/sales_summary',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Sales History',
						'url' => 'reports/sales/history',
						'access_page' => array('view'=>true),
						'access_detail' => array(
							'view_transaksi' => 'View Transaksi',
							'view_detail' => 'View Detail',
							'view_void' => 'View Void',
						)
					),
					array(
						'text' => 'Closing Shift',
						'url' => 'reports/sales/closing_shift',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Sales By',
						'url' => 'reports/sales/sales_by',
						'access_page' => array('view'=>true),
						'access_detail' => array(
							'by_product' => 'View Salels by Product',
							'by_category' => 'View Sales by Category',
							'by_server' => 'View Sales by Server',
							'by_sever_detail' => 'View Sales by Server Detail',
							'by_media' => 'View Sales by Media',
							'by_order_type' => 'View Sales by Order Type',
							'by_pax' => 'View Sales by Pax',
						)
					),
					array(
						'text' => 'Entertain Sales',
						'url' => 'reports/sales/entertain',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Ranking',
						'url' => 'reports/sales/rangking_trl',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Sales Analys',
						'url' => 'reports/sales/sales_analysy',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Tax & Gratuity',
						'url' => 'reports/sales/tax_gratuity',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'COGS',
						'url' => 'reports/sales/report_cogs',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Penjualan Outlet',
						'url' => 'reports/sales/sales_outlet',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Komisi Penjualan',
						'url' => 'reports/sales-commission',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Komisi Outlet',
						'url' => 'reports/outlet-commission',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Piutang',
						'url' => 'reports/sales/piutang',
						'access_page' => array('view' => true),
					  ),
				)
			);
			$data['reports']['sub']['reports_stock'] = array(
				'text' => 'Stock',
				'url' => 'reports/stock',
				'access_page' => array('view'=>true),
				'sub' => array(
					array(
						'text' => 'Stock Card',
						'url' => 'reports/stock/card',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Harta Stock',
						'url' => 'reports/stock/harta_stock',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Stock Opname',
						'url' => 'reports/stock/stock_opname',
						'access_page' => array('view' => true),
					  ),
				)
			);
			$data['reports']['sub']['reports_crm'] = array(
				'text' => 'CRM',
				'url' => 'reports/crm',
				'access_page' => array('view'=>true),
				'sub' => array(
					array(
						'text' => 'Acquisition',
						'url' => 'reports/crm/acquisition',
						'access_page' => array('view'=>true),
					),
					array(
						'text' => 'Transaction',
						'url' => 'reports/crm/transaction',
						'access_page' => array('view'=>true),
					),
				)
			);
			if (ENVIRONMENT!='production') {
				$data['reports']['sub']['purchase'] = array(
					'text' => 'Purchase',
					'url' => 'reports/purchase',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Transfer',
							'url' => 'reports/purchase/transfer-mutation',
							'access_page' => array('view' => true),
							'access_detail'=>array(
								'view_price'=> 'View Price',
							)
						),
						array(
							'text' => 'Supplier',
							'url' => 'reports/purchase/supplier',
							'access_page' => array('view' => true),
						),
						array(
							'text' => 'Purchase Recap',
							'url' => 'reports/purchase/purchase_recap',
							'access_page' => array('view' => true),
						),
						array(
							'text' => 'Transfer Analys',
							'url' => 'reports/purchase/transfer_analys',
							'access_page' => array('view' => true),
						),
					)
				);
			}

		/**
		 * REPORT TASK 
		 */
		$data['reports']['sub']['task'] = [
			"text" => "Task",
			"url" => "reports/task",
			"access_page" => [
			"view" => true,
			],
			"sub" => [
			[
				"text" => "Performance",
				"url" => "reports/task/performance",
				"access_page" => [
				"view" => true
				]
			],
			[
				"text" => "Time Analysis",
				"url" => "reports/task/time_analysis",
				"access_page" => [
				"view" => true
				]
			],
			]
		];

		$data['products'] = array(
			'text' => 'Products',
			'url' => 'products',
			'access_page' => array('view'=>true)
		);
			//LEVEL 2 PRODUCTS
			$data['products']['sub']['type'] = array(
				'text' => 'Type',
				'url' => 'products/type',
				'access_page' => array('view'=>true)
			);
			$data['products']['sub']['category'] = array(
				'text' => 'Category',
				'url' => 'products/category',
				'access_page' => array('view'=>true)
			);
			$data['products']['sub']['subcategory'] = array(
				'text' => 'Sub-Category',
				'url' => 'products/subcategory',
				'access_page' => array('view'=>true)
			);
			$data['products']['sub']['purchasereportcategory'] = array(
				'text' => 'Purchase Report Category',
				'url' => 'products/purchase-report-category',
				'access_page' => array('view'=>true)
			);
			$data['products']['sub']['unit'] = array(
				'text' => 'Unit',
				'url' => 'products/unit',
				'access_page' => array('view'=>true)
			);
			$data['products']['sub']['taxgratuity'] = array(
				'text' => 'Tax & Gratuity',
				'url' => 'products/taxgratuity',
				'access_page' => array('view'=>true)
			);		
			$data['products']['sub']['catalogue'] = array(
				'text' => 'Catalogue',
				'url' => 'products/catalogue',
				'access_page' => array('view'=>true,'add'=>true,'edit'=>true,'delete'=>true),
				'access_detail' => array(
					'exportimport_product_master' => 'Export & Import Product Master',
					'exportimport_product_detail' => 'Export & Import Product Detail',
				)
			);

			if (ENVIRONMENT=='development') {
				$data['products']['sub']['description'] = array(
					'text' => 'Product Description',
					'url' => 'products/product-description',
					'access_page' => array('view'=>true)
				);
			}

			// if (ENVIRONMENT=='production') {
				$data['products']['sub']['breakdown'] = array(
					'text' => 'Breakdown',
					'url' => 'products/breakdown',
					'access_page' => array('view'=>true)
				);
			// } else{
			// 	$data['products']['sub']['breakdown'] = array(
			// 		'text' => 'Breakdown',
			// 		'url' => 'products/breakdown_new',
			// 		'access_page' => array('view'=>true)
			// 	);
			// }



			//dev mode (LV 2)
			/*
			if (ENVIRONMENT=='development') {
				$data['products']['sub']['accounts'] = array(
					'text' => '*Accounts',
					'url' => 'products/accounts',
					'access_page' => array('view'=>true)
				);
					//LEVEL 3 PRODUCTS -> ACCOUNTS
					$data['products']['sub']['accounts']['sub']['acc_category'] = array(
						'text' => 'Category',
						'url' => 'products/accounts/accounts_category',
						'access_page' => array('view' => true),
					);
					$data['products']['sub']['accounts']['sub']['acc_lists'] = array(
						'text' => 'Lists',
						'url' => 'products/accounts/accounts_list',
						'access_page' => array('view' => true),
					);
			}
			*/
		//LEVEL 1
		$data['purchase'] = array(
			'text' => 'Purchase',
			'url' => 'purchase',
			'access_page' => array('view'=>true),
			'sub' => array(
				array(
					'text' => 'Supplier',
					'url' => 'purchase/supplier',
					'access_page' => array('view'=>true),
				),
				array(
					'text' => 'Purchase',
					'url' => 'purchase/purchase',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Purchase Order',
							'url' => 'purchase/purchase_order',
							'access_page' => array('view'=>true),
							'access_detail' => array(
								'print' => 'Print Document',
								'confirm' => 'Konfirmasi',
								'edit_order' => 'Edit Order'
							)
						),
						array(
							'text' => 'Purchasing',
							'url' => 'purchase/purchasing_product',
							'access_page' => array(
								'view'=>true,
								'add'=>true,
								'edit'=>true,
								'delete'=>true
							),
							'access_detail'=>array(
								'change_date'=> 'Change Date',
								'retur'=> 'Retur Product',
							)
						),
						array(
							'text' => 'History Confirm',
							'url' => 'purchase/confirm_detail',
							'access_page' => array('view'=>true),
						),									
						array(
							'text' => 'History Retur',
							'url' => 'purchase/retur',
							'access_page' => array('view'=>true),
						),
					)
				),
				array(
					'text' => 'Purchase Detail',
					'url' => 'purchase/purchase_detail',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Ingredients Detail',
							'url' => 'purchase/purchase/ingridients_detail',
							'access_page' => array('view'=>true),
						),
						array(
							'text' => 'Equipments Detail',
							'url' => 'purchase/purchase/equipment_detail',
							'access_page' => array('view'=>true),
						),
						array(
							'text' => 'Products Detail',
							'url' => 'purchase/purchase/product_detail',
							'access_page' => array('view'=>true),
						),
					)
				),
				array(
					'text' => 'Debt',
					'url' => 'purchase/debt',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Debt List',
							'url' => 'purchase/debt/debt_list',
							'access_page' => array('view'=>true),
						),
						array(
							'text' => 'Payment History',
							'url' => 'purchase/debt/debt_payment',
							'access_page' => array('view'=>true),
						),
					)
				),
				array(
					'text' => 'Transfer',
					'url' => 'purchase/transfer',
					'access_page' => array(
						'view'=>true,
						'edit'=>true
					),
					'access_detail'=>array(
						'view_price'=> 'View Price',
					)
				),								
				array(
					'text' => 'Operational',
					'url' => 'purchase/operationalcost',
					'access_page' => array('view'=>true),
				),
				(ENVIRONMENT!=='production') ?
				array(
					'text' => 'Maintenance',
					'url' => 'purchase/maintenance',
					'access_page' => array('view'=>true),
					'access_detail' => array(
						'approve' => 'Approvement',
					)
				) : null,
			)
		);

		//LEVEL 1 STOCK
		$data['stock'] = array(
			'text' => 'Stock',
			'url' => 'stock',
			'access_page' => array('view'=>true)
		);
			//LEVEL 2
			$data['stock']['sub']['stockestimation'] = array(
				'text' => 'Stock Estimation',
				'url' => 'stock/estimation',
				'access_page' => array('view'=>true,'add'=>true)
			);
			$data['stock']['sub']['stocksummary'] = array(
				'text' => 'Stock Summary',
				'url' => 'stock/summary',
				'access_page' => array('view'=>true),
				'sub' => array(
					//LEVEL3
					'summary_productsingredients' => array(
						'text' => 'Products & Ingredients',
						'url' => 'stock/summary/products_ingredients',
						'access_page' => array('view'=>true)
					),
					'summary_equipment' => array(
						'text' => 'Equipments',
						'url' => 'stock/summary/equipments',
						'access_page' => array('view'=>true)
					)
				)
			);
			

			$data['stock']['sub']['stockopname'] = array(
				'text' => 'Opname',
				'url' => 'stock/opname',
				'access_page' => array('view'=>true),
				'sub' => array(
					//LEVEL 3
					'opname_opnameinventory' => array(
						'text' => 'Opname & Inventory',
						'url' => 'stock/opname/opname_inventory',
						'access_page' => array('view'=>true,'add'=>true),
						'access_detail'=>array(
							'view_detail'=> 'View Detail',
						)
					),
					'opname_stockopname' => array(
						'text' => 'Stock Opname',
						'url' => 'stock/opname/stock_opname',
						'access_page' => array('view'=>true),
						
					),
					'opname_equipmentinventory' => array(
						'text' => 'Equipment Inventory',
						'url' => 'stock/opname/equipment_inventory',
						'access_page' => array('view'=>true)
					)
				)
			);
			$data['stock']['sub']['spoil'] = array(
				'text' => 'Spoil',
				'url' => 'stock/spoil',
				'access_page' => array('view'=>true),
				'sub' => array(
					//LEVEL 3
					'spoil_damage' => array(
						'text' => 'Damage',
						'url' => 'stock/spoil/spoil_damage',
						'access_page' => array('view'=>true),
					),
					'spoil_productingredient' => array(
						'text' => 'Products & Ingredients',
						'url' => 'stock/spoil/spoil_productingridient',
						'access_page' => array('view'=>true),
					),
					'spoil_equipment' => array(
						'text' => 'Equipment',
						'url' => 'stock/spoil/spoil_equipment',
						'access_page' => array('view'=>true),
					),
				)
			);
			if ($this->dev) {
				$data['stock']['sub']['equipmentrequest'] = array(
					'text' => 'Equipment Request',
					'url' => 'stock/equipment_request',
					'access_page' => array('view'=>true)
				);
			}


		//LEVEL 1
		$data['outlets'] = array(
			'text' => 'Outlets',
			'url' => 'outlets',
			'access_page' => array('view'=>true)
		);
			//LEVEL 2 OUTLETS
			$data['outlets']['sub']['list'] = array(
				'text' => 'Outlet List',
				'url' => 'outlets/outletlist',
				'access_page' => array('view'=>true,'add'=>true,'edit'=>true,'delete'=>true),
			);
			$data['outlets']['sub']['shift'] = array(
				'text' => 'Shift',
				'url' => 'outlets/shift',
				'access_page' => array('view'=>true)
			);
			$data['outlets']['sub']['paymentmedia'] = array(
				'text' => 'Payment Media',
				'url' => 'outlets/paymentmedia',
				'access_page' => array('view'=>true)
			);
			$data['outlets']['sub']['bankaccount'] = array(
				'text' => 'Bank Account',
				'url' => 'outlets/bankaccount',
				'access_page' => array('view'=>true)
			);
			$data['outlets']['sub']['devices'] = array(
				'text' => 'Devices',
				'url' => 'outlets/devices',
				'access_page' => array('view'=>true)
			);
			$data['outlets']['sub']['printer'] = array(
				'text' => 'Printer',
				'url' => 'outlets/printer',
				'access_page' => array('view'=>true)
			);
			$data['outlets']['sub']['kitchen_display'] = array(
				'text' => 'Kitchen Display',
				'url' => 'outlets/kitchen_display',
				'access_page' => array('view' => true)
			);

		//LEVEL 1
		$data['employees'] = array(
			'text' => 'Employees',
			'url' => 'employees',
			'access_page' => array('view'=>true)
		);
			//LEVEL 2 EMPLOYEES
			$data['employees']['sub']['crewlist'] = array(
				'text' => 'Crew List',
				'url' => 'employees/crewlist',
				'access_page' => array('view'=>true,'add'=>true,'edit'=>true,'delete'=>true),
			);
			$data['employees']['sub']['jabatan'] = array(
				'text' => 'Crew Position',
				'url' => 'employees/jabatan',
				'access_page' => array('view'=>true)
			);
			if ($this->dev) {
				$data['employees']['sub']['jadwal'] = array(
					'text' => 'Jadwal',
					'url' => 'employees/jadwal',
				);
				$data['employees']['sub']['absensi'] = array(
					'text' => 'Absensi',
					'url' => 'employees/absensi',
				);
				$data['employees']['sub']['sallary'] = array(
					'text' => 'Sallary',
					'url' => 'employees/sallary',
				);
				$data['employees']['sub']['internalmemo'] = array(
					'text' => 'Internal Memo',
					'url' => 'employees/internalmemo',
				);
					//LEVEL 3 EMPLOYEES -> INTERNAL MEMO
					$data['employees']['sub']['internalmemo']['sub']['smsblast'] = array(
						'text' => 'SMS Blast',
						'url' => 'employees/internalmemo/smsblast',
					);
					$data['employees']['sub']['internalmemo']['sub']['emailblast'] = array(
						'text' => 'E-mail Blast',
						'url' => 'employees/internalmemo/emailblast',
					);
			}


		//LEVEL 1 CRM
		$data['crm'] = array(
			'text' => 'CRM',
			'url' => 'crm',
			'access_page' => array('view'=>true),
			'sub' => array(
				array(
					'text' => 'Member',
					'url' => 'crm/member',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Member Type',
							'url' => 'crm/member/type',
							'access_page' => array('view'=>true),
						),
						array(
							'text' => 'Member List',
							'url' => 'crm/member/lists',
							'access_page' => array('view'=>true),
						),
						array(
							'text' => 'Point Collection',
							'url' => 'crm/member/point_collection',
							'access_page' => array('view'=>true),
						),
					)
				),
				array(
					'text' => 'Customer',
					'url' => 'crm/customer',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Customer List',
							'url' => 'crm/customer/lists',
							'access_page' => array('view'=>true),
						),
						array(
							'text' => 'Complain & Feedback',
							'url' => 'crm/customer/complainfeedback',
							'access_page' => array('view'=>true),
						),
						array(
							'text' => 'Campaign',
							'url' => 'crm/customer/campaign',
							'access_page' => array('view'=>true),
						),
					)
				),
				array(
					'text' => 'Apps',
					'url' => 'crm/apps',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Product Setting',
							'url' => 'crm/apps/product-setting',
							'access_page' => array('view'=>true, 'edit'=> true),
						),
						array(
							'text' => 'Category Setting',
							'url' => 'crm/apps/category-setting',
							'access_page' => array('view' => true, 'edit' => true),
						  ),
						array(
							'text' => 'Banner',
							'url' => 'crm/apps/banner',
							'access_page' => array('view'=>true, 'edit'=> true),
						),	
						array(
							'text' => 'Application Setting',
							'url' => 'crm/apps/application-setting',
							'access_page' => array('view' => true, 'edit' => true),
						  ),					
					)
				),
				array(
					'text' => 'Promotion',
					'url' => 'crm/promotion',
					'access_page' => array('view'=>true),
					'access_detail' =>  array(
						'publish' => 'Publish Promotion',
					),
					'sub' => array(
						array(
							'text' => 'Special Price',
							'url' => 'crm/promotion/specialprice',
							'access_page' => array('view'=>true, 'edit'=> true),
						),
						array(
							'text' => 'Discount',
							'url' => 'crm/promotion/discount',
							'access_page' => array('view'=>true, 'edit'=> true),
						),
						array(
							'text' => 'Free',
							'url' => 'crm/promotion/free',
							'access_page' => array('view'=>true, 'edit'=> true),
						),
						array(
							'text' => 'Deals',
							'url' => 'crm/promotion/deals',
							'access_page' => array('view'=>true, 'edit'=> true),
						),
						array(
							'text' => 'Apply Voucher Manual',
							'url' => 'crm/promotion/inject_vouchers',
						)
					)
				),
				array(
					'text' => 'Transaction',
					'url' => 'crm/transaction',
					'access_page' => array('view'=>true),
					'sub' => array(
						array(
							'text' => 'Transaction List',
							'url' => 'crm/transaction/list',
							'access_page' => array('view'=>true)
						),
						array(
							'text' => 'Transaction Settings',
							'url' => 'crm/transaction/settings',
							'access_page' => array('view'=>true)
						),
					)
				)
			)
		);


		//LEVEL 1 PRODUCTION
		// if (ENVIRONMENT!='production') {
			$data['production'] = array(
				'text' => 'Production',
				'url' => 'production',
				'access_page' => array('view'=>true),
			);
			//LV2
			$data['production']['sub']['itembreakdown'] = array(
				'text' => 'Item Breakdown',
				'url' => 'production/itembreakdown',
				'access_page' => array('view' => true),
			);
			$data['production']['sub']['production'] = array(
				'text' => 'Production Data',
				'url' => 'production/productionlist',
				'access_page' => array('view' => true, 'add' => true),
				'access_detail' => array(
					'autofill_primary' => 'Auto Fill Primary Product',
					'autofill_ingredient' => 'Auto Fill Ingredient Use',
					'autofill_endproduct' => 'Auto Fill End Products',
					'auto_count_by_primary_product' => 'Auto Count by Primary Product'
				),
			);
		// }

		//LEVEL 1 FINANCE
		if (ENVIRONMENT=='development' || getenv('ENABLE_FINANCE') == "true") {
			$data['finance'] = array(
                'label' => 'OPEN BETA',
				'text' => 'Finance',
				'url' => 'finance',
				'access_page' => array('view'=>true),
			);
			$data['finance']['sub']['accounts_list'] = array(
				'text' => 'Accounts List',
				'url' => 'finance/accounts/accounts_list',
				'access_page' => array('view' => true),
			);
			$data['finance']['sub']['accounts_setting'] = array(
				'text' => 'Accounts Setting',
				'url' => 'finance/accounts_setting',
				'access_page' => array('view' => true),
                'sub' => array(
					array(
						'text' => 'Product',
						'url' => 'finance/accounts/setting_product',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Payment',
						'url' => 'finance/accounts/setting_payment',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Tax & Gratuity',
						'url' => 'finance/accounts/setting_gratuity',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Purchase category',
						'url' => 'finance/accounts/setting_prc_category',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Account Default',
						'url' => 'finance/accounts/account_default',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Cashflow Category',
						'url' => 'finance/accounts/cashflow_category',
						'access_page' => array('view'=>true)
					)
				)
			);
			$data['finance']['sub']['report_finance'] = array(
				'text' => 'Report Finance',
				'url' => 'finance/report/home_report_finance',
				'access_page' => array('view' => true),
				'sub' => array(
					array(
						'text' => 'Jurnal Umum',
						'url' => 'finance/report/jurnal_umum',
						'access_page' => array(
							'view'=>true,
							'edit'=>true
							)
					),
					array(
						'text' => 'Laba Rugi',
						'url' => 'finance/report/laba_rugi',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Trial Balance',
						'url' => 'finance/report/trial_balance',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Buku Besar',
						'url' => 'finance/report/buku_besar',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Neraca',
						'url' => 'finance/report/neraca',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Harta Stok',
						'url' => 'finance/report/stock_card',
						'access_page' => array('view'=>true)
					),
					array(
						'text' => 'Cash Flow',
						'url' => 'finance/report/cash_flow',
						'access_page' => array('view'=>true)
					),
				)
			);

			$data['finance']['sub']['pagu'] = array(
				'text' => 'Pagu',
				'url' => '#',
				'access_page' => array('view' => true),
				'sub' => array(
					array(
						'text' => 'Account Pagu',
						'url' => 'finance/accounts/account_pagu',
						'access_page' => array('view' => true)
					),
					array(
						'text' => 'Pagu Laba Rugi',
						'url' => 'finance/pagu/pagu_laba_rugi',
						'access_page' => array('view' => true)
					),
					array(
						'text' => 'Pagu Cash Flow',
						'url' => 'finance/pagu/pagu_cash_flow',
						'access_page' => array('view' => true)
					)
				)
			);

			$data['finance']['sub']['jurnal_input'] = array(
				'text' => 'Jurnal Input',
				'url' => 'finance/jurnal/jurnal_input',
				'access_page' => array('view' => true),
			);

			if (ENVIRONMENT=='development' || getenv('ENABLE_FINANCE') == "true") {
				$data['finance']['sub']['assets']=array(
					'text' => 'Assets',
					'url' => 'finance/assets',
					'access_page' => array('view'=>true),
					'sub'=>[
						[
							'text' => 'Assets',
							'url' => 'finance/assets/assets_list',
							'access_page' => array('view'=>true),
						],
						[
							'text' => 'Penyusutan',
							'url' => 'finance/assets/penyusutan',
							'access_page' => array('view'=>true),
						],
						[
							'text' => 'Pembelian Aset',
							'url' => 'finance/assets/purchasing_assets',
							'access_page' => array('view'=>true),
						]
					]
				);
			}
	

		}

		//LEVEL 1 HR
		$data['hrm'] = array(
			'label' => 'OPEN BETA',
			'text' => 'HRM',
			'url' => 'hrm',
			'access_page' => array('view'=>true),
			'sub' => array(
				array(
					'text' => 'Data Karyawan',
					'url' => 'hrm/master_employee',
					'access_page' => array('view'=>true)
				),
				array(
					'text' => 'Data Tipe Karyawan',
					'url' => 'hrm/master_type',
					'access_page' => array('view'=>true)
				),
				array(
					'text' => 'Data Kelompok (Shift)',
					'url' => 'hrm/master_shift',
					'access_page' => array('view'=>true)
				),
				array(
					'text' => 'Penjadwalan',
					'url' => 'hrm/schedule',
					'access_page' => array('view'=>true)
				),
				array(
					'text' => 'Absensi',
					'url' => 'hrm/presention',
					'access_page' => array('view'=>true)
				),
				array(
					'text' => 'Cuti',
					'url' => 'hrm/break',
					'access_page' => array('view'=>true, 'add'=>true, 'edit'=>true, 'delete'=>true),
					'access_detail' => array(
						'change_status_cuti' => 'Ubah Status Cuti'
					),
				),
				(ENVIRONMENT!=='production') ?
				array(
					'text' => 'Penggajian',
					'url' => 'hrm/sallary',
					'access_page' => array('view'=>true)
				) : null,
			)
		);


		//LEVEL 1 TASK-MANAGEMENT
		//if (ENVIRONMENT !== 'production') {
			$data['tm'] = array(
				'text' => 'Task Mg.',
				'url' => 'tm',
				'access_page' => array('view' => true),
				'sub' => array(
					array(
						'text' => 'Master Data',
						'url'  => 'tm/master',
						'access_page' => array('view'=>true),
						'sub' => array(
							[
								'text' => 'Category', //'Kategori',
								'url' => 'tm/master/category',
								'access_page' => array('view'=>true)
							],
							[
								'text' => 'Form',
								'url' => 'tm/master/form',
								'access_page' => array('view'=>true)
							],
							[
								'text' => 'Questioner',//'Kuisioner',
								'url' => 'tm/master/question',
								'access_page' => array('view'=>true)
							],
							[
								'text' => 'Reporting', //'Reporting',
								'url' => 'tm/master/reporting',
								'access_page' => array('view' => true)
							]
						)
					),
					array(
						'text'	=> 'Task List',
						'url' 	=> 'tm/task-list',
						'access_page' => array('view'=>true)
					)
				)
			);
		//}

		$data['settings'] = array(
			'text' => 'Settings',
			'url' => 'settings',
			'access_page' => array('view'=>true),
			'sub' => array(
				[
					'text' => 'Business',
					'url' => 'settings/business',
					'access_page' => array('view'=>true),
					'sub' => [
						[
							'text' => 'Business Profile',
							'url' => 'settings/business/profile',
							'access_page' => array('view'=>true),
						],
						[
							'text' => 'Report Notifications',
							'url' => 'settings/business/report_notifications',
							'access_page' => array('view'=>true),
						],
						[
							'text' => 'Social Connect',
							'url' => 'settings/business/connect',
							'access_page' => array('view'=>true),
						],
					]
				],
				[
					'text' => 'Billing',
					'url' => 'settings/billing',
					'access_page' => array('view'=>true),
				],
				[
					'text' => 'Subscribtion',
					'url' => 'settings/subscription',
					'access_page' => array('view'=>true),
				],
			),
		);
		
		return $data;
	}

	public function role_mobile()
	{
		$data = array(
			array('inputkasmasuk','Add Cash In'),
			array('inputkaskeluar','Add Cash Out'),
			array('inputpembelian','Add Purchase'),
			array('setting_printer','Printer Setting'),
			array('tutupkasir','Close Cash Register'),
			array('reprint_nota','Re-print Receipt'),
			array('reprint_order','Re-print Order'),
			array('reprint_tutupkasir','Re-print Close Cash Register'),//add
			array('gantiharga','Change Price'),
			array('gantidiskonperbill','Change Discount on Bill'), //array('gantidiskontaxgratuity','Change Discount on Tax Gratuity'),
			array('bukalaciuang','Open Cash Drawer'),
			array('pembayaran','Payment'),
			array('compliment','Compliment'),
			array('voucher','Voucher'),
			array('gantivoucherperitem','Change Voucher on Item'), //add
			array('simpankeorderlist','Save to Order List'), //add
			array('duty','Duty Meals'),
			array('gantidiskonperitem','Change Discount on Item'),//array('gantidiskon','Change Discount'),
			array('gantipajak','Change Tax'),
			array('reprint_refund','Re-print Refund'),
			array('refund','Refund'),
			array('inputbarangrusak','Add Damaged Item'),
			array('inputbaranghabis','Add Out Item'),
			array('inputpromo','Add Promo'),
			array('masterlogin','Master Login'),
			array('meja_split','Split Table'), //gabung meja
			array('meja_move','Move Table'), //pindah meja
			array('viewcloseregister','View Close Register Record'),
			array('viewtotalachievement','View Total Achievement'),
			array('viewtransactionhistory','View Transaction History Record'),
			array('product_create', 'Add Product'),
			array('print_dailyrecap', 'Print Daily Recap'),
			array('authorization','Authorization', array(
				array('diskonall','Discount All'),
				array('diskonperitem','Discount on Item'),
				array('freeitem','Free Item'),
				array('refund','Refund'),
				array('reprint_refund','Re-print Refund'),
				array('reprint_receipt','Re-print Receipt'),
				array('closeshift','Close Shift'),
				array('reprint_closeshift','Re-print Close Shift'),
				array('void','Void'),
			)),
		);

		return $data;
	}

	public function check($url, $action='view', $detail=null)
	{
		//init
		$table = 'employee_role';
		$valid = false;

		if ($this->ci->session->userdata('user_type')=='employee') {
			//cek role db
			$this->ci->db->where(array(
				'employee_fkid' => $this->ci->session->userdata('user_id'),
				'url' => $url
			));
			$row = $this->ci->db->get($table)->row();
			if ($row) {
				$role_access = $row->role_access;

				switch ($action) {
					case 'view':
						$valid = ($row->role_view==true) ? true : false;
						break;
					case 'add':
						$valid = ($row->role_add==true) ? true : false;
						break;
					case 'edit':
						$valid = ($row->role_edit==true) ? true : false;
						break;
					case 'delete':
						$valid = ($row->role_delete==true) ? true : false;
						break;
					default:
						if (!is_json($role_access) || empty($detail)) {
							return false;
						}

						$valid = (!empty(json_decode($role_access, true)[$detail])) ? true : false;
						break;
				}

				// if (!empty($detail)) {
				// 	$valid = (!empty(json_decode($role_access, true)[$detail])) ? true : false;
				// }
			}
			return $valid;
		}
		
		return true;
	}

	public function check_outlet_access($outlet_id=null)
	{
		$access = true;
		if ($this->ci->session->userdata('user_type')=='employee') {
			if (!in_array($outlet_id, $this->ci->session->userdata('outlet_access'))) {
				$access = false;
			}
		}

		return $access;
	}

	public function disable_page()
	{
		// $html='';
		// $data['page_title'] = 'Access Forbidden';
		// $html.= $this->ci->load->view('themes/v2/header_v2', $data,true);
		// $html.= $this->ci->load->view('themes/v2/access_forbidden_v', $data, true);
		// $html.= $this->ci->load->view('themes/v2/footer_v2', $data, true);
		// echo $html;die();
		$this->ci->output->set_status_header(403);
		$this->ci->load->library('template/Template');
		$html = $this->ci->template->disable_page();
		echo $html;
	}

	public function check_on_config($url=null)
	{
		$check_result = false;
		$url_check = array();
		$is_ajax = 'xmlhttprequest' == strtolower( $_SERVER['HTTP_X_REQUESTED_WITH'] ?? '' );

		//load role config
		if (file_exists(APPPATH.'config/app/role_employee.php')) {
			include(APPPATH.'config/app/role_employee.php');
		}


		if (!$is_ajax) {
			//check forbidden menu
			if (!empty($urls_forbidden) AND is_array($urls_forbidden) && $check_result===false) {
				$url_check = $urls_forbidden;
				if (in_array($url, $url_check)) {
					$this->disable_page();
				}
			}

			//check whitelist menu
			if (!empty($urls_whitelist) AND is_array($urls_whitelist) && $check_result===false) {
				$url_check = $urls_whitelist;
				if (in_array($url, $url_check)) {
					$check_result = true;
				}
			}

			//check permission
			if (!empty($urls_permission) AND is_array($urls_permission) && $check_result===false) {
				$url_check = $urls_permission;
				$is_match = false;

				//cek non-regex
				if (isset($url_check[$url])) {
					$is_match = true;
				}
				
				//cek dengan regex
				if ($is_match==false) {
					$keys = array_keys($url_check);
					$result = preg_grep('/(:any)/', $keys);
					foreach ($result as $key => $r) {
						$check = str_replace(array(':any', ':num'), array('[^/]+', '[0-9]+'), $r);
						$is_match = (preg_match('#^'.$check.'$#', $url, $matches));
						if ($is_match) {
							$url = str_replace('([^/]+)', '(:any)', $check);
							break;
						}
					}
				}

				//check exist & check role
				if ($is_match==false) {
					$is_match = array_key_exists($url, $url_check);
				}
				if ($is_match==1) {
					$role = $url_check[$url];
					$role_url = $role[0];
					$role_action = $role[1];
					$check_result = $this->check($role_url, $role_action);
				}else{
					$check_result = false;
				}
			}
		}else{
			$check_result = true; //default value for ajax request

			//check protected ajax
			if (!empty($ajax_urls_permission) AND is_array($ajax_urls_permission)) {
				$url_check = $ajax_urls_permission;
				$is_protected = array_key_exists($url, $url_check);
				if ($is_protected) {
					$role = $url_check[$url];
					$role_url = $role[0];
					$role_action = $role[1];
					$check_result = $this->check($role_url, $role_action);
				}
			}
		}

		return $check_result;
	}

	public function refresh_role()
	{
		$session_temp = array();
		if ($this->ci->session->userdata('user_type') == 'employee') {
			$this->ci->db->where('employee_fkid', $this->ci->session->userdata('user_id'));
			$role_list = $this->ci->db->get('employee_role')->result();
			foreach ($role_list as $a) {
				$session_temp[$a->url] = array(
					'view' => $a->role_view,
					'add' => $a->role_add,
					'edit' => $a->role_edit,
					'delete' => $a->role_delete,
					'access' => $a->role_access
				);

				array_merge($session_temp);
			}
			
			$this->ci->session->set_userdata( array(
				'role_web' => $session_temp
			));
		}
		return $session_temp;
	}

	public function refresh_outlet_access()
	{
		if ($this->ci->session->userdata('user_type') == 'employee') {
			$this->ci->db->select("GROUP_CONCAT(outlet_fkid SEPARATOR ',') AS outlet_ids");
			$this->ci->db->from('employee_outlet');
			$this->ci->db->where('employee_fkid', $this->ci->session->userdata('user_id'));
			$result = $this->ci->db->get()->row();

			$this->ci->session->set_userdata( array(
				'outlet_access' => explode(', ', $result->outlet_ids)
			));
		}
	}

}

/* End of file Privilege.php */
/* Location: ./application/libraries/app/Privilege.php */
