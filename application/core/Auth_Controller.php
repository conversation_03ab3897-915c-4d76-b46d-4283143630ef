<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth_Controller extends CI_Controller {

	protected $api;

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->config->load('auth/auth',true);
		$this->load->library('auth/Authlib');
		//$this->authlib->create_user_session_by_type_and_id('admin', '534');
		//CHECK LOGIN STATUS (dari session ataupun cookie dari remember me)
		$this->logged_in_status();

		//set api endpoint
		// $api_url = getenv('SERVICE_API');
		$this->api = rtrim(getenv('SERVICE_API'), '/');


		//CHECK SUBSCRIBTION (ambil informasi langganan)
		//ambil feature yang dibuka
		$subscription_left = 0;
		$feature_menu = '';
		switch ($this->uri->segment(1)) {
			case 'crm':
			case 'hrm':
			case 'finance':
				$feature_menu = $this->uri->segment(1);
			break;

			default:
				$feature_menu = 'device';
			break;
		}
		//check expird dari feature yang dibuka
		if (isset($this->session->userdata('subscription')[$feature_menu])) {
			$subscription_left = $this->session->userdata('subscription')[$feature_menu]['expired'] - current_millis();
		}

		//RE-CHECK subscription status (cek ulang status subscribtion untuk langganan yang akan habis), dan request bukan ajax
		if ($subscription_left<=86400000 && !$this->input->is_ajax_request()) {
			$this->check_subscription($this->session->userdata('admin_id'));
		}


		//API REPORT (check token)
		if (!$this->input->is_ajax_request()) {
			$api_report_data = $this->api_report->data();
			$api_report_expired = ($api_report_data->token_expired) ?? 0;
			if (time() >= ($api_report_expired - 60*15)) {
				$this->api_report->get_new_token();
			}
		}


		//IGNORE REDIRECT TO subscribtion page if no subscribtion
		//atur halaman yang bisa diakses tanpa redirect ke subscribtion page
		$show_subscribtion_page = false;
		switch ($this->uri->segment(1)) {
			case 'outlets':
				switch ($this->uri->segment(2)) {
					case 'subscribe':
					case 'subscribe-add':
						# code...
					break;

					default:
					$show_subscribtion_page = true;
					break;
				}
			break;
			case 'settings':
				switch ($this->uri->segment(2)) {
					case 'account':
					case 'billing':
					case 'subscription':
					case 'subscription-add':
						# code...
					break;

					default:
					$show_subscribtion_page = true;
					break;
				}
			break;
			case 'billing':
				# code...
			break;
			case 'api':
				switch ($this->uri->segment(2)) {
					case 'billing-hrm':
						# code...
						break;

					default:
						$show_subscribtion_page = true;
						break;
				}
				break;
			default:
				$show_subscribtion_page = true;
			break;
		}

		//check redirection
		// if (empty($this->session->userdata('subscription'))) {
		// 	if ($show_subscribtion_page===true) {
		// 		redirect('settings/subscription-add');
		// 		die();
		// 	}
		// }else{
			if (!isset($this->session->userdata('subscription')[$feature_menu]) && $show_subscribtion_page===true) {
				/*
				if (ENVIRONMENT != 'development') {
					$this->template->feature_expired($feature_menu);
					die();
				}
				*/


				if ($this->session->userdata('user_type')=='admin' && $this->uri->segment(1)=='hrm') {
					$this->load->model('hrm_onboarding/Check_subscribtion_model');
					$already_use_hrm_trial = $this->Check_subscribtion_model->hrm_subscribtion_check()->row();
					if (!empty($already_use_hrm_trial)) {
						$this->template->feature_expired($feature_menu);
						die();
					}

                }elseif($this->session->userdata('user_type')=='admin' && $this->uri->segment(1)=='finance'){
                    $this->load->model('finance/setup/M_setup');
					$already_use_finance_trial = $this->M_setup->finance_subscribtion_check()->row();
                    // dd($already_use_finance_trial);
					if (!empty($already_use_finance_trial)) {
                        // dd("tes");
						$this->template->feature_expired($feature_menu);
						die();
					}else{
                        // dd('finance/setup/setup_finance');
                        $this->load->helper('url');
                        return redirect(base_url('finance/setup/setup_finance'));
                        die;
                    }
				}else{
					$this->template->feature_expired($feature_menu);
					die();
				}

			}
		// }



		//check privilege employee
		// if ($this->session->userdata('user_type')=='employee' && !$this->input->is_ajax_request()) {
		// 	$this->employee_webrole($this->session->userdata('user_id')); //get new employee role
		// 	$this->_check_employee_role(); //check access
		// }
		if ($this->session->userdata('user_type')=='employee') {
			if (!$this->input->is_ajax_request()) {
				//REQUEST NON-AJAX (RENDER PAGE)
				$this->privilege->refresh_role(); //get latest employee web role
				$this->_check_employee_role(); //check access
			}else{
				//REQUEST WITH AJAX
				// $this->_check_employee_role(); //check access
				$path_url = uri_string();
				$check_role = $this->privilege->check_on_config($path_url); //check role by config
				if (!$check_role) {
					echo format_json(array(
						'status' => 'error',
						'message' => 'Access Forbidden!'
					));
					die();
				}
			}
		}
	}


	public function _check_employee_role()
	{
		$path_url = uri_string(); //get current path after

		//check role
		$check_role = $this->privilege->check_on_config($path_url); //check role by config
		if (!$check_role) {

			//cek view (berdasarkan index url)
			$method = $this->router->fetch_method(); //get method of controller
			if ($method=='index' || (!$this->input->is_ajax_request() && $this->uri->segment(1)!='api')) {
				switch ($path_url) {
					//allow access
					// case 'settings/account':
					// 	# code...
					// 	break;

					//disallow employee access
					// case 'settings/resetdata':
					// case 'settings/affiliate':
					// case 'settings/billing':
					// 	$this->privilege->disable_page();
					// 	break;

					// //manual check
					// case 'stock/stock_opname':
					// 	$role = $this->privilege->check('stock/opname/opname_inventory','add');
					// 	if (!$role) {
					// 		$this->privilege->disable_page();
					// 	}
					// 	break;

					default:
						/* CHECK EMPLOYEE ACCESS */

						//check mainmenu access (navigasi lv1, menu yang ada di navbar)
						$role = $this->privilege->check($this->uri->segment(1),'view');
						if (!$role) {
							$this->privilege->disable_page();
						}

						//check submenu access
						$role = $this->privilege->check($path_url,'view');
						if (!$role) {
							$this->privilege->disable_page();
						}
						break;
				}//end switch
			}
		}

	}

	private function check_subscription($admin_id)
	{
		//init
		$array['subscription'] = array();


		//check trial
		$this->db->select('
			b.billing_id,
			s.name AS service_name,
			s.service_feature AS feature,
			b.invoice,
			s.service_length_day,
			b.time_confirm AS trial_start,
			b.time_confirm + (bd.service_period * bd.service_length_day * 86400000) AS trial_end,
			b.billing_status,
			b.admin_fkid
		')
		->from('system_billing_detail bd')
		->join('system_billing b', 'b.billing_id=bd.billing_fkid', 'left')
		->join('system_service s', 'bd.sys_service_fkid=s.sys_service_id', 'left');
		$this->db->where('b.admin_fkid', $admin_id);
		$this->db->where('b.time_confirm + (bd.service_period * bd.service_length_day * 86400000) >=', current_millis());
		$this->db->like('b.invoice', '/trial', 'BOTH');
		$trial_list = $this->db->get()->result();
		if ($trial_list) {
			// $array['subscription']['device'] = array(
			// 	'type' => 'trial',
			// 	'expired' => $row->trial_end
			// );

			foreach ($trial_list as $rs) {
				$array['subscription'][$rs->feature] = array(
					'type' => 'trial',
					'expired' => $rs->trial_end
				);
			}
		}



		//cek subscription active
		$this->db->select('
			sb.feature,
			sb.admin_fkid,
			max(service_time_expired) AS service_time_expired
		');
		$this->db->from('system_subscribe sb');
		$this->db->where('sb.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('sb.service_time_start <=', current_millis());
		$this->db->where('sb.service_time_expired >=', current_millis());
		// $this->db->where('sb.feature', 'device');
		$this->db->group_by('sb.admin_fkid, sb.feature');
		$row_subscription = $this->db->get()->result();
		// if ($row_subscription) {
		// 	$array['subscription']['pos'] = array(
		// 		'type' => 'device',
		// 		'expired' => $row_subscription->service_time_expired
		// 	);
		// }


		foreach ($row_subscription as $rs) {
			// if (empty($array['subscription'][$rs->feature])) {
				$array['subscription'][$rs->feature] = array(
					'type' => 'subscribe',
					'expired' => $rs->service_time_expired
				);
			// }
		}


		$this->session->set_userdata( $array );
	}


	public function logged_in_status()
	{
		//cek cookie saat session expired dan refresh session yang expired
		$cookie = get_cookie($this->config->item('remember_cookie_name','auth'));
		if ($this->session->userdata('user_logged')==false && $cookie<>"") {
			$this->authlib->create_user_session_by_cookie($cookie);
		}


		//cek login (bila diakses lewat ajax)
		if ($this->input->is_ajax_request() || $this->uri->segment(1)=='api') {
			if ($this->session->userdata('user_logged')==false) {
				$response = array(
					'status' => 'error',
					'message' => 'You are not logged in!'
				);
				echo json_encode($response);
				die();
			}
		}
		else{
			if ($this->session->userdata('user_logged')==false) {
				$url = current_url();
				$redirect_url = site_url('?continue='.urlencode($url));
				redirect($redirect_url);
				die();
			}
		}
	}

	protected function response_json($data, $status_code = 200)
	{
		return $this->output
			->set_content_type('application/json')
			->set_status_header($status_code)
			->set_output(json_encode($data));
	}

	protected function response($data, $status_code = 200)
	{
		return $this->output
			->set_content_type('application/json')
			->set_status_header($status_code)
			->set_output($data);
	}

}

/* End of file Auth_Controller.php */
/* Location: ./application/core/Auth_Controller.php */
