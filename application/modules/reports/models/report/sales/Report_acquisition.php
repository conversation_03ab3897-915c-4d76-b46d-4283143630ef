<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Report_acquisition extends CI_Model {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
	}

	// entertainn discount
    public function byAcquisitionHourly($param)
    {
        $admin  = $this->session->userdata('admin_id');
         $stardate =  $param['startDate'];
         $enddate =$param['endDate'];                 
        $this->db->select("
            device, count(*) as total,
            FROM_UNIXTIME(tbl.register_date/1000+25200, '%H' ) as date_format,
            ANY_VALUE(tbl.register_date) as register_date
            from (
            SELECT m.name, COALESCE(ANY_VALUE(unt.device), 'android') as device,
            md.register_date
            from members m
            join members_detail md  on m.member_id=md.member_fkid
            left join user_notification_token unt on unt.user_id=md.members_detail_id
            where md.admin_fkid= $admin
            and md.register_date BETWEEN $stardate AND  $enddate
            group by md.members_detail_id
            ) tbl group by FROM_UNIXTIME(tbl.register_date/1000+25200, '%H'), device
          
        ");        
        $this->db->limit($param['limit'],$param['offset']);             
        $data = $this->db->get()->result_array();
        return json_encode($data);

    }

	public function byAcquisition($param)
    {
        $admin  = $this->session->userdata('admin_id');
        $stardate =  $param['startDate'];
        $enddate =$param['endDate'];
        $dateformat = '%d/%m/%Y';
        
        $this->db->select("
            device, count(*) as total,
            FROM_UNIXTIME(tbl.register_date/1000+25200, '$dateformat' ) as date_format,
            ANY_VALUE(tbl.register_date) as register_date
            from (
            SELECT m.name, COALESCE(ANY_VALUE(unt.device), 'android') as device,
            md.register_date
            from members m
            join members_detail md  on m.member_id=md.member_fkid
            left join user_notification_token unt on unt.user_id=md.members_detail_id
            where md.admin_fkid= $admin
            and md.register_date BETWEEN $stardate AND  $enddate
            group by md.members_detail_id
            ) tbl group by FROM_UNIXTIME(tbl.register_date/1000+25200, '$dateformat'), device          
        ");
        
         $this->db->limit($param['limit'],$param['offset']);
         $this->db->order_by('register_date');
             
        $data = $this->db->get()->result_array();
        return json_encode($data);

    }

    public function acquisitionV2($param) {        
        $dateFormatMap = [
            'daily'   => '%d-%m-%Y',
            'monthly' => '%m-%Y',
            'yearly'  => '%Y',
            'hourly'  => '%H'
        ];
        file_put_contents("php://stderr", "----> group by: ".$param['group_by']." | format: ".$dateFormatMap[$param['group_by']]."\n");

        $adminId  = $this->session->userdata('admin_id');
        $startDate =  $param['startDate'];
        $endDate =$param['endDate']; 
        $timeZone = $param['timeZone'];
        $dateFormat = $dateFormatMap[$param['group_by']];

        $this->db->select("
        count(*) total,
        DATE_FORMAT(FROM_UNIXTIME(register_date/1000 + $timeZone ), '$dateFormat' ) _date
        from members_detail md 
        where md.admin_fkid= $adminId and md.register_date BETWEEN $startDate and $endDate
        group by _date
        ");

        $this->db->limit($param['limit'], $param['offset']);             
        $result = $this->db->get()->result_array();
        
        file_put_contents("php://stderr",  basename(__FILE__).":".__LINE__.": query: " . preg_replace("/[\n\r\t]/", " ", $this->db->last_query()) . "\n");
        return $result;
    }

    public function deviceCount($param){
        $dateFormatMap = [
            'daily'   => '%d-%m-%Y',
            'monthly' => '%m-%Y',
            'yearly'  => '%Y',
            'hourly'  => '%H'
        ];
        $adminId  = $this->session->userdata('admin_id');
        $startDate =  $param['startDate'];
        $endDate =$param['endDate']; 
        $timeZone = $param['timeZone'];
        $dateFormat = $dateFormatMap[$param['group_by']];

        $this->db->select("
        count(*) total,
        sum(if(unt.device = 'android', 1, 0)) _android, sum(if(unt.device = 'ios', 1, 0)) _ios,
        DATE_FORMAT(FROM_UNIXTIME(data_created/1000+$timeZone), '$dateFormat') _date
        from user_notification_token unt 
        join members_detail md on md.members_detail_id=unt.user_id and md.admin_fkid= $adminId
        where unt.app='crm' 
        and unt.data_created BETWEEN $startDate and $endDate
        group by _date
        ");

        $this->db->limit($param['limit'],$param['offset']);             
        return $this->db->get()->result_array();
    }

    public function byAcquisitionMountly($param)
    {
        $admin  = $this->session->userdata('admin_id');
         $stardate =  $param['startDate'];
         $enddate =$param['endDate'];        
        $this->db->select("
            device, count(*) as total,
            FROM_UNIXTIME(tbl.register_date/1000+25200, '%m/%Y') as date_format,
            ANY_VALUE(tbl.register_date) as register_date
            from (
            SELECT m.name, COALESCE(ANY_VALUE(unt.device), 'android') as device,
            md.register_date
            from members m
            join members_detail md  on m.member_id=md.member_fkid
            left join user_notification_token unt on unt.user_id=md.members_detail_id
            where md.admin_fkid= $admin
            and md.register_date BETWEEN $stardate AND  $enddate
            group by md.members_detail_id
            ) tbl group by FROM_UNIXTIME(tbl.register_date/1000+25200, '%m/%Y'), device
          
        ");
        
        $this->db->limit($param['limit'],$param['offset']);
             
        $data = $this->db->get()->result_array();
        return json_encode($data);

    }

     public function byAcquisitionYearly($param){
        $admin  = $this->session->userdata('admin_id');
         $stardate =  $param['startDate'];
         $enddate =$param['endDate'];

        $this->db->select("
            device, count(*) as total,
            FROM_UNIXTIME(tbl.register_date/1000+25200, '%Y') as date_format,
            ANY_VALUE(tbl.register_date) as register_date
            from (
            SELECT m.name, COALESCE(ANY_VALUE(unt.device), 'android') as device,
            md.register_date
            from members m
            join members_detail md  on m.member_id=md.member_fkid
            left join user_notification_token unt on unt.user_id=md.members_detail_id
            where md.admin_fkid= $admin
            and md.register_date BETWEEN $stardate AND  $enddate
            group by md.members_detail_id
            ) tbl group by FROM_UNIXTIME(tbl.register_date/1000+25200, '%Y'), device

        ");

         $this->db->limit($param['limit'],$param['offset']);

        $data = $this->db->get()->result_array();
        return json_encode($data);

    }

    public function acquisitionV3($param) {
        $adminId = $this->session->userdata('admin_id');
        $startDate = $param['startDate'];
        $endDate = $param['endDate'];
        $timeZone = isset($param['timeZone']) ? $param['timeZone'] : 25200; // Default to +7 timezone (25200 seconds)

        $this->db->select("
            count(*) total,
            DATE_FORMAT(FROM_UNIXTIME(register_date / 1000 + $timeZone), '%m-%Y') _date,
            DATE_FORMAT(FROM_UNIXTIME(register_date / 1000 + $timeZone), '%m-%Y') date_format,
            sum(IF(last_device='android', 1, 0)) android,
            sum(IF(last_device='ios', 1, 0)) ios
        ");

        $this->db->from('members_detail md');
        $this->db->where('md.admin_fkid', $adminId);
        $this->db->where('md.register_date >=', $startDate);
        $this->db->where('md.register_date <=', $endDate);
        $this->db->group_by('_date');

        if (isset($param['limit'])) {
            $this->db->limit($param['limit'], isset($param['offset']) ? $param['offset'] : 0);
        } else {
            $this->db->limit(30);
        }

        $result = $this->db->get()->result_array();

        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__.": query: " . preg_replace("/[\n\r\t]/", " ", $this->db->last_query()) . "\n");
        return $result;
    }
}

/* End of file Report_sales_rangking_model.php */
/* Location: ./application/models/report/Report_sales_rangking_model.php */