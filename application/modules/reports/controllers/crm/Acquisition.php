<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Acquisition extends Auth_Controller
{
    public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        set_time_limit(1000);
        $this->load->model('report/sales/Report_acquisition', 'acquisition');
    }

    public function index()
    {
        $link = current_url().'/';
        file_put_contents("php://stderr", "report acquisition... $link \n");

        $data = array(
            'ajaxReportAcquisitionsDaily' => $link.'reportAcquisition2/',
            'ajaxReportAcquisitionsHourly' => $link.'reportAcquisitionHourly/',
            'ajaxReportAcquisitionsMonthly' => $link.'acquisitionV2/',
            'ajaxReportAcquisitionsYearly' => $link.'reportAcquisitionYearly/',
            'ajaxReportAcquisitions' => $link.'acquisitionV2/',
        );
        $data['page_title'] = 'Sales By acquisition';
        $this->template->view('reports/crm/acquisition', $data);
    }

    public function reportAcquisitionHourly()
    {
        header('Content-Type: application/json');
        /* custom json output  start */
        $param = [
           "startDate" => $this->input->post('startDate'),
           "endDate" => $this->input->post('endDate'),
           "timeZone" => $this->input->post('timeZone'),
           "limit" => $this->input->post('limit'),
           "offset" => $this->input->post('offset'),
           "Time" => $this->input->post('Time'),
           "Chart" => $this->input->post('Chart'),
        ];

        $jsondata = $this->acquisition->byAcquisitionHourly($param);
        $json_decode = json_decode($jsondata); //pecah data json
        $dataArray = array();

        foreach ($json_decode as $a) {
            // print_r($a);
            // die();
            $device = $a->device;
            $total = $a->total;
            $register_date = $a->register_date;
            $date_format = $a->date_format;

            $temp_data = array(
                'device' => $device,
                'total' => $total,
                'register_date' => $register_date,
                'date_format' => $date_format,

            );
            array_push($dataArray, $temp_data);

        }

        $draw_json = array(
           'data' => $dataArray,
           'recordsTotal' => count($dataArray),
           'recordsFiltered' => count($dataArray),
           'draw' => '1'
        );
        return $this->response_json($draw_json);
    }
    public function reportAcquisition2()
    {
        header('Content-Type: application/json');
        /* custom json output  start */
        $param = [
           "startDate" => $this->input->post('startDate'),
           "endDate" => $this->input->post('endDate'),
           "timeZone" => $this->input->post('timeZone'),
           "limit" => $this->input->post('limit'),
           "offset" => $this->input->post('offset'),
           "Time" => $this->input->post('Time'),
            "Chart" => $this->input->post('Chart'),
        ];

        $jsondata = $this->acquisition->byAcquisition($param);
        $json_decode = json_decode($jsondata); //pecah data json
        $dataArray = array();

        foreach ($json_decode as $a) {
            // print_r($a);
            // die();
            $device = $a->device;
            $total = $a->total;
            $register_date = $a->register_date;
            $date_format = $a->date_format;

            $temp_data = array(
                'device' => $device,
                'total' => $total,
                'register_date' => $register_date,
                'date_format' => $date_format,

            );
            array_push($dataArray, $temp_data);

        }

        $draw_json = array(
           'data' => $dataArray,
           'recordsTotal' => count($dataArray),
           'recordsFiltered' => count($dataArray),
           'draw' => '1'
        );
        return $this->response_json($draw_json);
    }

    public function acquisitionV2()
    {
        header('Content-Type: application/json');
        /* custom json output  start */
        $param = [
            "startDate" => $this->input->post('startDate'),
            "endDate" => $this->input->post('endDate'),
            "timeZone" => $this->input->post('timeZone'),
            "limit" => $this->input->post('limit'),
            "offset" => $this->input->post('offset'),
            "Time" => $this->input->post('Time'),
             "Chart" => $this->input->post('Chart'),
             'group_by' => $this->input->post('groupBy'),
        ];

        // $param['startDate'] = 1640970000000;
        // $param['endDate'] = 1735491600000;

        $members = $this->acquisition->acquisitionV2($param);
        $devices = $this->acquisition->deviceCount($param);

        file_put_contents("php://stderr", "member: ".json_encode($members)."\n");
        file_put_contents("php://stderr", "device: ".json_encode($devices)."\n");

        //member
        //[{"total":"1","_date":"2022-01"},{"total":"5","_date":"2022-03"},{"total":"1","_date":"2022-04"},{"total":"1","_date":"2022-07"},{"total":"30","_date":"2022-12"},{"total":"53","_date":"2023-01"},{"total":"4","_date":"2023-02"},{"total":"47","_date":"2023-03"},{"total":"6","_date":"2023-04"},{"total":"1","_date":"2023-05"},{"total":"1","_date":"2023-08"},{"total":"1","_date":"2023-10"},{"total":"1","_date":"2024-04"}]

        //device
        // [{"total":"3","_android":"0","_ios":"3","_date":"2023-01"},{"total":"9","_android":"0","_ios":"9","_date":"2023-02"},{"total":"8","_android":"7","_ios":"1","_date":"2023-03"},{"total":"6","_android":"6","_ios":"0","_date":"2023-05"},{"total":"8","_android":"8","_ios":"0","_date":"2023-07"},{"total":"1","_android":"1","_ios":"0","_date":"2024-04"},{"total":"1","_android":"1","_ios":"0","_date":"2024-05"}]

        //note:
        //total in device can be greater or lower than total in member

        // Create an associative array for devices with date as key
        $devicesByDate = [];
        foreach ($devices as $device) {
            $devicesByDate[$device['_date']] = $device;
        }

        $result = []; //for old version

        // Update $members array with Android and iOS totals
        foreach ($members as &$member) {
            $date = $member['_date'];

            // Get Android total from $devicesByDate, default to 0
            $androidTotal = $devicesByDate[$date]['_android'] ?? $member['total'];
            if ($androidTotal > $member['total']) {
                $androidTotal = $member['total'];
            }

            // Calculate iOS total
            $iosTotal = $member['total'] - $androidTotal;

            // Update member data
            $member['android'] = $androidTotal;
            $member['ios'] = $iosTotal;
            $member['date_format'] = $date;

            $memberAndroid = array(
                'device' => 'android',
                'total' => $androidTotal,
                'date_format' => $date,
            );
            $memberIos = array(
                'device' => 'ios',
                'total' => $iosTotal,
                'date_format' => $date,
            );
            array_push($result, $memberAndroid, $memberIos);
        }

        // file_put_contents("php://stderr", "---> member: ".json_encode($result)."\n");
        $draw_json = array(
            'data' => $members,
            'recordsTotal' => count($members),
            'recordsFiltered' => count($members),
            'draw' => '1'
        );
        return $this->response_json($draw_json);
    }

    public function reportAcquisitionMountly()
    {
        header('Content-Type: application/json');
        /* custom json output  start */
        $param = [
            "startDate" => $this->input->post('startDate'),
            "endDate" => $this->input->post('endDate'),
            "timeZone" => $this->input->post('timeZone'),
            "limit" => $this->input->post('limit'),
            "offset" => $this->input->post('offset'),
            "Time" => $this->input->post('Time'),
             "Chart" => $this->input->post('Chart'),
        ];

        //testing
        // $this->acquisitionV2();

        $jsondata = $this->acquisition->byAcquisitionMountly($param);
        $json_decode = json_decode($jsondata); //pecah data json
        $dataArray = array();

        foreach ($json_decode as $a) {
            // print_r($a);
            // die();
            $device = $a->device;
            $total = $a->total;
            $register_date = $a->register_date;
            $date_format = $a->date_format;

            $temp_data = array(
                'device' => $device,
                'total' => $total,
                'register_date' => $register_date,
                'date_format' => $date_format,

            );
            array_push($dataArray, $temp_data);
        }

        $draw_json = array(
            'data' => $dataArray,
            'recordsTotal' => count($dataArray),
            'recordsFiltered' => count($dataArray),
            'draw' => '1'
        );
        return $this->response_json($draw_json);
    }

    public function reportAcquisitionYearly()
    {
        header('Content-Type: application/json');
        /* custom json output  start */
        $param = [
          "startDate" => $this->input->post('startDate'),
          "endDate" => $this->input->post('endDate'),
          "timeZone" => $this->input->post('timeZone'),
          "limit" => $this->input->post('limit'),
          "offset" => $this->input->post('offset'),
          "Time" => $this->input->post('Time'),
           "Chart" => $this->input->post('Chart'),
        ];

        $jsondata = $this->acquisition->byAcquisitionYearly($param);
        $json_decode = json_decode($jsondata); //pecah data json
        $dataArray = array();

        foreach ($json_decode as $a) {
            // print_r($a);
            // die();
            $device = $a->device;
            $total = $a->total;
            //   $register_date = $a->register_date;
            $date_format = $a->date_format;

            $temp_data = array(
                'device' => $device,
                'total' => $total,
                // 'register_date' => $register_date,
                'date_format' => $date_format,

            );
            array_push($dataArray, $temp_data);

        }

        $draw_json = array(
          'data' => $dataArray,
          'recordsTotal' => count($dataArray),
          'recordsFiltered' => count($dataArray),
          'draw' => '1'
        );
        return $this->response_json($draw_json);
    }

    public function reportAcquisitionV3()
    {
        header('Content-Type: application/json');
        /* custom json output  start */
        $param = [
            "startDate" => $this->input->post('startDate'),
            "endDate" => $this->input->post('endDate'),
            "timeZone" => $this->input->post('timeZone'),
            "limit" => $this->input->post('limit'),
            "offset" => $this->input->post('offset'),
            "Time" => $this->input->post('Time'),
            "Chart" => $this->input->post('Chart'),
        ];

        $data = $this->acquisition->acquisitionV3($param);

        $draw_json = array(
            'data' => $data,
            'recordsTotal' => count($data),
            'recordsFiltered' => count($data),
            'draw' => '1'
        );
        return $this->response_json($draw_json);
    }

}
