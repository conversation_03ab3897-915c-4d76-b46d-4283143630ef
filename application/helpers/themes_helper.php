<?php
defined('BASEPATH') OR exit('No direct script access allowed');

function themes_header($title=null)
{
	//get an instance of CI so we can access our configuration
	$CI =& get_instance();

	//setting themes
	$data['title'] = $title;
	$CI->load->view('themes/header_v',$data);
}

function themes_subnav($file=null)
{
	//get an instance of CI so we can access our configuration
	$CI =& get_instance();

	//load sub-navbar
	if (!empty($file)) {
		$CI->load->view('themes/navigation/header_navbar');
		$CI->load->view('themes/navigation/'.$file);
	}
}

function themes_footer()
{
	//get an instance of CI so we can access our configuration
	$CI =& get_instance();

	//setting themes
	$CI->load->view('themes/footer_v');
}


/* End of file themes_helper.php */
/* Location: ./application/helpers/themes_helper.php */
