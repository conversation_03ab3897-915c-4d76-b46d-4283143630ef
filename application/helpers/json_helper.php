<?php
//ubah jadi data json
function format_json($array)
{
	header('Content-Type: application/json');
	header('Access-Control-Allow-Origin: *');
	return json_encode($array, JSON_PRETTY_PRINT);
}

//cek format json atau bukan
function isJson($json_data) {
	//v1
    // $json = json_decode($json_data);
    // return $json && $json_data != $json;

	//v2
    json_decode($json_data);
 	return (json_last_error() == JSON_ERROR_NONE);
}
function is_json($json)
{
	return isJson($json);
}
?>