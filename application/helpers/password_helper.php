<?php
defined('BASEPATH') OR exit('No direct script access allowed');

function password_encrypt($string)
{
	$password_hash = password_hash($string, PASSWORD_BCRYPT, array('uniqcript'=>23));
	return $password_hash;
}

function password_validation($password, $password_stored)
{
	if (password_verify($password, $password_stored)) {
		//password benar
		$result = true;
	}
	else{
		//password salah
		$result = false;
	}

	return $result;
}

/* End of file password_helper.php */
/* Location: ./application/helpers/password_helper.php */