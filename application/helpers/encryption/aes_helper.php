<?php

if (!function_exists('encryptaes256')) {
	function encryptaes256($text=null, $passphrase=null)
	{
		$salt = openssl_random_pseudo_bytes(8);

		$salted = $dx = '';
		while (strlen($salted) < 48) {
			$dx = md5($dx . $passphrase . $salt, true);
			$salted .= $dx;
		}

		$key = substr($salted, 0, 32);
		$iv = substr($salted, 32, 16);

		// encrypt with PKCS7 padding
		return base64_encode('Salted__' . $salt . openssl_encrypt($text . '', 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv));
	}
}

if (!function_exists('decryptaes256')) {
	function decryptaes256($encrypted=null, $passphrase=null)
	{
		$encrypted = base64_decode($encrypted);
		$salted = substr($encrypted, 0, 8) == 'Salted__';

		if (!$salted) {
			return null;
		}

		$salt = substr($encrypted, 8, 8);
		$encrypted = substr($encrypted, 16);

		$salted = $dx = '';
		while (strlen($salted) < 48) {
			$dx = md5($dx . $passphrase . $salt, true);
			$salted .= $dx;
		}

		$key = substr($salted, 0, 32);
		$iv = substr($salted, 32, 16);

		return openssl_decrypt($encrypted, 'aes-256-cbc', $key, true, $iv);
	}
}
