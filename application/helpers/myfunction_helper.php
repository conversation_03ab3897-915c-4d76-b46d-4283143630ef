<?php
defined('BASEPATH') OR exit('No direct script access allowed');

//MENAMBAHKAN TITIK PADA BILANGAN RIBUAN
function formatAngka($bilangan)
{
	$CI =& get_instance(); //inisialisasi CI
	
	//format pemisah
	$curr_separator = $CI->session->userdata('currency_separator');
	switch ($CI->session->userdata('currency_separator')) {
		case 'point':
			$curr_separator = '.';
			$curr_decimal = ',';
			break;
		case 'comma':
			$curr_separator = ',';
			$curr_decimal = '.';
			break;
		default:
			$curr_separator = '';
			$curr_decimal = '';
			break;
	}
	
	return number_format($bilangan, 0, $curr_decimal, $curr_separator);
}
function formatDesimal($bilangan, $jumlah_desimal)
{
	// $jumlah_desimal = $desimal;
	// $pemisah_desimal ="."; 
	// $pemisah_ribuan ="";

	// return number_format($bilangan, $jumlah_desimal, $pemisah_desimal, $pemisah_ribuan);

	$CI =& get_instance(); //inisialisasi CI
	
	//format pemisah
	$curr_separator = $CI->session->userdata('currency_separator');
	switch ($CI->session->userdata('currency_separator')) {
		case 'point':
			$curr_separator = '.';
			$curr_decimal = ',';
			break;
		case 'comma':
			$curr_separator = ',';
			$curr_decimal = '.';
			break;
		default:
			$curr_separator = '';
			$curr_decimal = '';
			break;
	}
	
	return number_format($bilangan, $jumlah_desimal, $curr_decimal, $curr_separator);
}
function formatUang($bilangan)
{
	$currency = formatAngka($bilangan);
	$CI =& get_instance(); //inisialisasi CI
	
	// //format pemisah
	// $curr_separator = $CI->session->userdata('currency_separator');
	// switch ($CI->session->userdata('currency_separator')) {
	// 	case 'point':
	// 		$curr_separator = '.';
	// 		$curr_decimal = ',';
	// 		break;
	// 	case 'comma':
	// 		$curr_separator = ',';
	// 		$curr_decimal = '.';
	// 		break;
	// 	default:
	// 		$curr_separator = '';
	// 		$curr_decimal = '';
	// 		break;
	// }
	
	// $currency = number_format($bilangan, 0, $curr_decimal, $curr_separator);

	//format uang
	$curr_symbol = $CI->session->userdata('currency_symbol');
	$curr_position = $CI->session->userdata('currency_position');
	switch ($curr_position) {
		case 'left':
			$currency = $curr_symbol.$currency;
			break;
		case 'right':
			$currency = $currency.$curr_symbol;
			break;
		default: break;
	}

	return $currency;
}

function clear_format($money)
{
    $cleanString = preg_replace('/([^0-9\.,])/i', '', $money);
    $onlyNumbersString = preg_replace('/([^0-9])/i', '', $money);

    $separatorsCountToBeErased = strlen($cleanString) - strlen($onlyNumbersString) - 1;

    $stringWithCommaOrDot = preg_replace('/([,\.])/', '', $cleanString, $separatorsCountToBeErased);
    $removedThousandSeparator = preg_replace('/(\.|,)(?=[0-9]{3,}$)/', '',  $stringWithCommaOrDot);

    return (float) str_replace(',', '.', $removedThousandSeparator);
}

function unique($panjang)
{
	$karakter= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

	$string = '';
	for ($i=0; $i < $panjang; $i++) { 
		$pos = rand(0, strlen($karakter)-1);
		$string .= $karakter{$pos};
	}
	return $string;
}

function loading_datatables()
{
	$gambar = base_url('themes/img/ajax-loader.gif');
	$data = "<img src='".$gambar."'/>";
	return $data;
}

function reverseDate($date, $delimiter = '-')
{
	$exp_date = explode($delimiter, $date);
	return implode($delimiter, array_reverse($exp_date));
}

// url
function current_url_full()
{
	return site_url().ltrim($_SERVER['REQUEST_URI'], '/');
}

// role_daterange(url rol,employee_fkid)
function role_daterange($url_menu,$employee_fkid)
{
	$CI =& get_instance(); //inisialisasi CI
	$date = '';
	$data = $CI->db->select('role_access')->from("employee_role er")->where(["er.employee_fkid"=>$employee_fkid,"er.url"=>$url_menu])->get()->row();
	if (!empty($data) && $data->role_access != '0') {
		$role_access = json_decode($data->role_access);
		$date = $role_access->date_min;	
	}
	return $date;
}

function decrypt_url($string) {

    $output = false;
    $secret_key     = "1111111111111111";
    $secret_iv      = "2456378494765431";
    $encrypt_method = "aes-256-cbc";

    // hash
    $key    = hash("sha256", $secret_key);

    // iv – encrypt method AES-256-CBC expects 16 bytes – else you will get a warning
    $iv = substr(hash("sha256", $secret_iv), 0, 16);

    //do the decryption given text/string/number

    $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
    return $output;
}

function encrypt_url($string) {
    $output = false;
    $secret_key     = "1111111111111111";
    $secret_iv      = "2456378494765431";
    $encrypt_method = "aes-256-cbc";

    // hash
    $key    = hash("sha256", $secret_key);

    // iv – encrypt method AES-256-CBC expects 16 bytes – else you will get a warning
    $iv     = substr(hash("sha256", $secret_iv), 0, 16);

    //do the encryption given text/string/number
    $result = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
    $output = base64_encode($result);
    return $output;
}

function outlet_employee($param)
{
	$CI =& get_instance(); //inisialisasi CI
	$CI->load->model('outlet/Outlets_model', 'Outlets_model');
	$outlet = $CI->Outlets_model->outlet_employe();
	$tmp=[];
	foreach ($outlet as $a) {
		array_push($tmp,$a->outlet_id);
	}
	$outletString = implode("','",$tmp);
	if ($param == '') {
		$param = $outletString;
	}
	return  "('".$param."')";
}

function filtermember_type($param)
{
	$CI =& get_instance(); //inisialisasi CI
	$CI->load->model('report/sales/Report_transaction', 'transaction');
	$member = $CI->transaction->filtermember_type();
	$tmp=[];
	foreach ($member as $a) {
		array_push($tmp,$a->type_id);
	}
	$memberString = implode("','",$tmp);
	if ($param == '') {
		$param = $memberString;
	}
	return  "('".$param."')";
}

function filtergroup_type($param)
{
	$CI =& get_instance(); //inisialisasi CI
	$newArr = '';
	$tmp=[];
	for ($i=1; $i <= 4 ; $i++) { 
		
		array_push($tmp,$i);
	}
	$memberString = implode(", " , $tmp);
	
	return  str_replace("'","","'$param'");
}

function filter_shift($param)
{
	$CI =& get_instance(); //inisialisasi CI
	$data = $CI->db->select('shift_id')
	->from("shift")
	->where('data_status', 'on') //cari hanya data aktif
	->where('admin_fkid', $CI->session->userdata('admin_id')) //ambil berdasarkan owner
	->order_by('name', 'asc')
	->get()->result();
	 $tmp =[];
	foreach ($data as $a) {
		array_push($tmp,$a->shift_id);
	}
	$shiftString = implode("','",$tmp);
	if ($param == '') {
		$param = $shiftString;
	}
	return "('".$param."')";
}

function filter_category($param)
{
	$CI =& get_instance(); //inisialisasi CI
	$data = $CI->db->select('product_category_id')
	->from("products_category")
	->where('data_status', 'on') //cari hanya data aktif
	->where('admin_fkid', $CI->session->userdata('admin_id')) //ambil berdasarkan owner
	->order_by('name', 'asc')
	->get()->result();
	 $tmp =[];
	foreach ($data as $a) {
		array_push($tmp,$a->product_category_id);
	}
	$categoryString = implode("','",$tmp);
	if ($param == '') {
		$param = $categoryString;
	}
	return "('".$param."')";
}

function filter_subcategory($param)
{
	$CI =& get_instance(); //inisialisasi CI
	$data = $CI->db->select('product_subcategory_id')
	->from("products_subcategory")
	->where('data_status', 'on') //cari hanya data aktif
	->where('admin_fkid', $CI->session->userdata('admin_id')) //ambil berdasarkan owner
	->order_by('name', 'asc')
	->get()->result();
	 $tmp =[];
	foreach ($data as $a) {
		array_push($tmp,$a->product_subcategory_id);
	}
	$subcategoryString = implode("','",$tmp);
	if ($param == '') {
		$param = $subcategoryString;
	}
	return "('".$param."')";
}

function unix_time($date, $timeOffset) {
	// Convert the date string to a Unix timestamp
	$timestamp = strtotime($date);

	// Calculate the Unix timestamp for the beginning of the day (00:00:00) with offset
	$startOfDay = mktime(0, 0, 0, date('n', $timestamp), date('j', $timestamp), date('Y', $timestamp)) - $timeOffset;

	// Calculate the Unix timestamp for the end of the day (23:59:59) with offset
	$endOfDay = mktime(23, 59, 59, date('n', $timestamp), date('j', $timestamp), date('Y', $timestamp)) - $timeOffset;

	// Return an associative array with both timestamps
	return ['start' => $startOfDay, 'end' => $endOfDay];
}

function date_to_milis($date,$timezone) {
	// date format (Y-m-d H:i:s)
	$milis = (strtotime($date)+$timezone)*1000;
	return $milis;
}
/* End of file myfunction_helper.php */
/* Location: ./application/helpers/myfunction_helper.php */
