<?php
defined('BASEPATH') OR exit('No direct script access allowed');


//ambil waktu sekarang dengan format currentmillis
function current_millis()
{
	$current = round(microtime(true) * 1000);
	return $current;
}

//get user localtime
function user_localtime($format)
{
	$CI =& get_instance(); //inisialisasi CI
	$timezone = $CI->session->userdata('timezone');
	date_default_timezone_set($timezone);
	$current = date($format, current_millis()/1000);
	return $current;
}

//konversi current millis ke local user time
function millis_to_localtime($format, $current_millis)
{
	$CI =& get_instance(); //inisialisasi CI
	$timezone = $CI->session->userdata('timezone');
	date_default_timezone_set($timezone);
	$current = date($format, $current_millis/1000);
	return $current;
}


//konversi currentmillis ke date('Y-m-d H:i:s')
function millis_to_date($format, $current_millis)
{
	$current = date($format, $current_millis/1000);
	return $current;
}

//cek timezone client
function isValidTimezoneId($timezoneId)
{
	try{
		new DateTimeZone($timezoneId);
		return TRUE;
	}catch(Exception $e){
		return FALSE;
	}
}

//ambil selisih utc server dengan local dalam detik
function user_gmt()
{
	$jam_start = date('H');
	$menit_start = date('i');
	$jam_end = user_localtime('H');
	$menit_end = user_localtime('i');
	  
	$hasil = (intVal($jam_end) - intVal($jam_start)) * 60 + (intVal($menit_end) - intVal($menit_start));
	$hasil = $hasil / 60;
	return $hasil = number_format($hasil)*60*60;

	//echo "Selisih antara ".$jam_start.":".$menit_start." dan ".$jam_end.":".$menit_end." adalah : ".$hasil;
}

//ambil perbedaan timemillis user dengna server
function user_millis_diff()
{
	$CI =& get_instance(); //inisialisasi CI
	$this_tz_str = $CI->session->userdata('timezone');
	$this_tz = new DateTimeZone($this_tz_str);
	$now = new DateTime("now", $this_tz);
	$offset = $this_tz->getOffset($now);
	return $offset * 1000;
}

/* End of file time_helper.php */
/* Location: ./application/helpers/time_helper.php */