<?php
defined('BASEPATH') OR exit('No direct script access allowed');

if (!function_exists('sendEMail')) {
	function sendEMail($to, $subject, $message)
	{
		$ci =& get_instance();
		$ci->load->helper('sendemail');
		$response = sendMail($to, $subject, $message);

		return array(
			'status' => 'success',
			'message' => 'Send E-mail Success!'
		);
	}
}

/*
function sendEmail($to, $subject, $message)
{
	$data_to_json = array(
		"fromAddress"	=> "<EMAIL>",
		"toAddress"		=> $to,
		"subject"		=> $subject,
		"content"		=> $message
	);


	// Generated by curl-to-PHP: http://incarnate.github.io/curl-to-php/
	$ch = curl_init();

	curl_setopt($ch, CURLOPT_URL, "https://mail.zoho.com/api/accounts/5959564000000008002/messages");
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data_to_json));
	curl_setopt($ch, CURLOPT_POST, 1);

	$headers = array();
	$headers[] = "Authorization: 6269fea601cf0fe94cde7b67fd849499";
	$headers[] = "Content-Type: application/json";
	$headers[] = "Cache-Control: no-cache";
	curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

	$result = curl_exec($ch);
	if (curl_errno($ch)) {
		$curl_error = 'Error:' . curl_error($ch);
	}
	curl_close ($ch);


	//decode data
	$return_data = array();
	$result_data = json_decode($result);
	if ($result_data->status->code=="200") {
		$return_data = array(
			'status' => 'success',
			'message' => 'Send E-mail Success!'
		);
	}
	else{
		$return_data = array(
			'status' => 'error',
			'message' => $curl_error
		);
	}

	return $return_data;
}
*/

/* End of file email_helper.php */
/* Location: ./application/helpers/email_helper.php */