<?php
defined('BASEPATH') OR exit('No direct script access allowed');

if (!function_exists('sendMail')) {

	function sendMail($to, $subject, $message)
	{
		/*
		if (in_array(ENVIRONMENT, ['production','staging','testing'])) {
			return sendMail_v3($to, $subject, $message);
		}else{
			return sendMail_v2($to, $subject, $message);
		}
		*/
		return sendMail_v3($to, $subject, $message);
	}

	function sendMail_v3($to, $subject, $message)
	{
		$CI =& get_instance();
		$CI->load->library('google/Pubsub');

		$pubsub_message = json_encode([
			'title' 	=> $subject,
			'message' 	=> $message,
			'media' 	=> 'email',
			'receiver' 	=> $to,
		]);

		return $CI->pubsub->publish('messaging-gateway-production', $pubsub_message);
	}

	function sendMail_v2($to, $subject, $message)
	{
		$client = new \GuzzleHttp\Client();
		try {
			$response = $client->request('POST', 'https://apimessenger.uniq.id/send/email', [
				'headers' => [
					'Authorization' => getenv('SERVICE_MESSENGER_TOKEN')
				],
				'form_params' => [
					'email' => $to,
					'subject' => $subject,
					'content' => $message,
				]
			]);
			$jsonRaw = $response->getBody()->getContents();
			if (is_json($jsonRaw)) {
				$data = json_decode($jsonRaw);
				if (!empty($data->status)) {
					return $data->status;
				}else{
					return false;
				}
			}
		} catch (Exception $e) {
			return $e;
		}
	}

	/*
	function sendMail_v1($to, $subject, $message)
	{
		#  GENERAL SETTING
		#  $to 				= penerima. contoh: $to = array('<EMAIL>');
		#  $subject 		= subject email
		#  $message 		= isi email
		#
		
		//mail config
		$CI =& get_instance(); //inisialisasi CI
		$CI->load->config('config_email'); //ambil info config untuk email
		$CI->load->library('email');
		$config = array(
			'protocol'	=> $CI->config->item('mail_protocol'),
			'smtp_host'	=> $CI->config->item('mail_smtp_host'),
			'smtp_port'	=> $CI->config->item('mail_smtp_port'),
			'smtp_user'	=> $CI->config->item('mail_username'), // email gmail
			'smtp_pass'	=> $CI->config->item('mail_password'), // password
			'charset' 	=> "utf-8",
			'mailtype'	=> "html",
			'newline'	=> "\r\n",
		);

		$CI->email->initialize($config);
		$sender 		= $CI->config->item('mail_sendername'); //nama pengirim
		$sendermail		= $CI->config->item('mail_username'); //email pengirim
		
		$CI->email->from($sendermail, $sender);
		$CI->email->to($to);
		$CI->email->subject($subject);
		$CI->email->message($message);
		
		//error_reporting(0); //hidden error
		$CI->email->message($message);
		if ($CI->email->send()) {
			//$return = 'Email sent.';
			$return = true;
			//echo "email dikirim";
		} else {
			//show_error($CI->email->print_debugger()); //tampilkan error
			log_message('error',$CI->email->print_debugger());
			$return = false;
		}
		//echo $CI->email->print_debugger();

		return $return;
	}
	*/
}

/* End of file email_helper.php */
/* Location: ./application/helpers/email_helper.php */
