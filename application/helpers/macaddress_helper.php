<?php
defined('BASEPATH') OR exit('No direct script access allowed');

function getMacWin(){ 
	ob_start();
	system('ipconfig -a');
	$mycom=ob_get_contents(); // Capture the output into a variable
	ob_clean(); // Clean (erase) the output buffer
	$findme = "Ethernet";
	$pmac = strpos($mycom, $findme); // Find the position of Physical text
	$mac=substr($mycom,($pmac+36),17); // Get Physical Address
	return $mac;
}
function getMacUnix()
{
	ob_start();
	system('ifconfig -a');
	$mycom = ob_get_contents(); // Capture the output into a variable
	ob_clean(); // Clean (erase) the output buffer
	$findme = "Physical";
	//Find the position of Physical text 
	$pmac = strpos($mycom, $findme); 
	$mac = substr($mycom, ($pmac + 37), 18);
	return $mac;
}

function getMacWinAll()
{
	ob_start();
	system('ipconfig /all');
	$mycom=ob_get_contents();
	ob_clean();
	$findme = 'Physical Address';
	$pmac = strpos($mycom, $findme);
	$mac=substr($mycom,($pmac+36),17);
	return $mac;
}

function ipdanmac()
{
	if (isset($_SERVER['HTTP_CLIENT_IP']))
        $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
    else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_X_FORWARDED']))
        $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
    else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
        $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_FORWARDED']))
        $ipaddress = $_SERVER['HTTP_FORWARDED'];
    else if(isset($_SERVER['REMOTE_ADDR']))
        $ipaddress = $_SERVER['REMOTE_ADDR'];
    else
        $ipaddress = 'UNKNOWN';

    $macCommandString   =   "arp " . $ipaddress . " | awk 'BEGIN{ i=1; } { i++; if(i==3) print $3 }'";

    $mac = exec($macCommandString);

    return ['ip' => $ipaddress, 'mac' => $mac];
}







function getMacZebrenitza()
{
	//https://zebrenitza.wordpress.com/2013/03/13/membaca-mac-address-client-dengan-php/
	$_IP_ADDRESS = $_SERVER['REMOTE_ADDR'];
	$_PERINTAH = "arp -a $_IP_ADDRESS";
	ob_start();
	system($_PERINTAH);
	$_HASIL = ob_get_contents();
	ob_clean();
	$_PECAH = strstr($_HASIL, $_IP_ADDRESS);
	$_PECAH_STRING = explode($_IP_ADDRESS, str_replace(" ", "", $_PECAH));
	$_HASIL = substr($_PECAH_STRING[1], 0, 17);
	echo "IP Anda : ".$_IP_ADDRESS."
	MAC ADDRESS Anda : ".$_HASIL;
}
function GetMAC(){
    ob_start();
    system('getmac');
    $Content = ob_get_contents();
    ob_clean();
    return substr($Content, strpos($Content,'\\')-20, 17);
}
function GetClientMac(){
    $macAddr=false;
    $arp=`arp -n`;
    $lines=explode("\n", $arp);

    foreach($lines as $line){
        $cols=preg_split('/\s+/', trim($line));

        if ($cols[0]==$_SERVER['REMOTE_ADDR']){
            $macAddr=$cols[2];
        }
    }
    return $macAddr;
}


/*
Referensi Get MAC ADDRESS:
https://stackoverflow.com/questions/5074139/how-to-get-mac-address-of-client-using-php
https://stackoverflow.com/questions/5074139/how-to-get-mac-address-of-client-using-php
*/

/* End of file macaddress_helper.php */
/* Location: ./application/helpers/macaddress_helper.php */