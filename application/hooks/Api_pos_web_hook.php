<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Api_pos_web_hook
{
	protected $ci;
	protected $cookie_name = 'uniq-api';

	public function __construct()
	{
		$this->ci =& get_instance();
	}

	public function generate_and_save_token()
	{
		if ($this->ci->session->userdata('user_logged') && !$this->ci->input->is_ajax_request()) {
			// $this->ci->load->library('app/Api_web'); //auto-loaded

			if (!get_cookie($this->cookie_name)) {
				/* v1
				$currentTime = time();

				$jsonToken 	= $this->ci->api_web->create_token_by_session();

				if (is_json($jsonToken)) {
					$token 		= json_decode($jsonToken, true);
					$token_data = base64_encode(json_encode([
						'token' => $token['access_token_type'].' '.$token['access_token'],
						'expired' => $token['access_token_expired']
					]));

					//set cookie
					$secure = (ENVIRONMENT!=='development') ? true : false;
					$cookie_expired = ($token['access_token_expired'] - $currentTime) ?? $currentTime;
					set_cookie($this->cookie_name,  $token_data, $cookie_expired, null, "/", null, $secure, true);

					//share token
					$this->ci->api_web->setToken($token['access_token_type'].' '.$token['access_token']);
				}
				*/

				//v2
				$this->ci->api_web->generate_and_save_token();
			}
		}

		if (!$this->ci->session->userdata('user_logged') && get_cookie($this->cookie_name)) {
			delete_cookie($this->cookie_name);
		}
	}
}

/* End of file Api_pos_web_hook.php */
/* Location: ./application/hooks/Api_pos_web_hook.php */
