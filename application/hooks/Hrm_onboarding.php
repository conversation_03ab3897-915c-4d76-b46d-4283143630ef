<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Hrm_onboarding
{
	protected $ci;

	public function __construct()
	{
        $this->ci =& get_instance();
	}

	public function is_hrm_feature()
	{
		if ($this->ci->uri->segment(1) == 'hrm' && !$this->ci->input->is_ajax_request()) {
		 	$session = $this->ci->session->userdata();
		 	if (empty($session['subscription']['hrm'])) {
		 		$this->page();
		 	}
		}
	}

	public function page()
	{
		$this->ci->template->boarding_page_hrm();
		die();
	}

}

/* End of file Hrm_onboarding.php */
/* Location: ./application/hooks/Hrm_onboarding.php */
