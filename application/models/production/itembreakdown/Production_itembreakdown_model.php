<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Production_itembreakdown_model extends CI_Model
{

    public $table = 'production_itembreakdown';
    public $table_view = 'view_production_itembreakdown';
    public $id = 'itembreakdown_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json() {
        $this->datatables->select('itembreakdown_id,name,product_fkid,qty,unit_name,admin_fkid,data_created,data_modified,product_name,unit_name,product_adminid,unit_adminid');
        $this->datatables->from($this->table_view);
        //add this line for join
        //$this->datatables->join('table2', 'view_production_itembreakdown.field = table2.field');
        $this->datatables->where(array(
            'admin_fkid' => $this->session->userdata('admin_id'),
            'product_adminid' => $this->session->userdata('admin_id'),
            'unit_adminid' => $this->session->userdata('admin_id')
        ));
        return $this->datatables->generate();
    }
    public function json_recipeadddetail()
    {
        $this->datatables->select('product_id, product_name, unit_fkid, unit_name');
        $this->datatables->from('view_catalogue');
        $this->datatables->where(array(
            'product_adminid' => $this->session->userdata('admin_id'),
            'unit_adminid' => $this->session->userdata('admin_id'),
            'data_status' => 'on'
        ));
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where(array(
            'admin_fkid' => $this->session->userdata('admin_id'),
            'product_adminid' => $this->session->userdata('admin_id'),
            'unit_adminid' => $this->session->userdata('admin_id')
        ));
        return $this->db->get($this->table_view)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where(array(
            'admin_fkid' => $this->session->userdata('admin_id'),
            'product_adminid' => $this->session->userdata('admin_id'),
            'unit_adminid' => $this->session->userdata('admin_id')
        ));
        return $this->db->get($this->table_view)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('', $q);
	$this->db->or_like('itembreakdown_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('product_fkid', $q);
	$this->db->or_like('qty', $q);
	$this->db->or_like('unit_name', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->or_like('product_name', $q);
	$this->db->or_like('unit_name', $q);
	$this->db->or_like('product_adminid', $q);
	$this->db->or_like('unit_adminid', $q);
	$this->db->from($this->table_view);
        $this->db->where(array(
            'admin_fkid' => $this->session->userdata('admin_id'),
            'product_adminid' => $this->session->userdata('admin_id'),
            'unit_adminid' => $this->session->userdata('admin_id')
        ));
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('', $q);
	$this->db->or_like('itembreakdown_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('product_fkid', $q);
	$this->db->or_like('qty', $q);
	$this->db->or_like('unit_name', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->or_like('product_name', $q);
	$this->db->or_like('unit_name', $q);
	$this->db->or_like('product_adminid', $q);
	$this->db->or_like('unit_adminid', $q);
	$this->db->limit($limit, $start);
        $this->db->where(array(
            'admin_fkid' => $this->session->userdata('admin_id'),
            'product_adminid' => $this->session->userdata('admin_id'),
            'unit_adminid' => $this->session->userdata('admin_id')
        ));
        return $this->db->get($this->table_view)->result();
    }

    // insert data
    function insert($data)
    {
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $data['data_modified'] = current_millis();

        $this->db->where(array(
            $this->id => $id,
            'admin_fkid' => $this->session->userdata('admin_id')
        ));
        //$this->db->where($this->id, $id);
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->delete($this->table);
    }


    //INSERT AVAILABLE OUTLET
    function available_outlet($itembreakdown_id)
    {
        $this->db->select('ao.*, o.name AS outlet_name');
        $this->db->from('production_itembreakdown_outlet ao');
        $this->db->join('outlets o', 'ao.outlet_fkid = o.outlet_id', 'left');
        $this->db->where('ao.data_status', true);
        $this->db->where('ao.itembreakdown_fkid', $itembreakdown_id);
        $this->db->order_by('outlet_name', 'asc');
        return $this->db->get()->result();
    }

    function get_available_outlet($itembreakdown_id, $outlet_id)
    {
        $this->db->where(array(
            'itembreakdown_fkid' => $itembreakdown_id,
            'outlet_fkid' => $outlet_id
        ));
        return $this->db->get('production_itembreakdown_outlet')->row();
    }
    function insert_available_outlet($data)
    {
        $data['data_modified'] = current_millis();
        $data['data_status'] = true;
        return $this->db->insert('production_itembreakdown_outlet', $data);
    }

    function update_available_outlet_by_itembreakdown($itembreakdown_id, $data)
    {
        $data['data_modified'] = current_millis();
        $this->db->where('itembreakdown_fkid', $itembreakdown_id);
        return $this->db->update('production_itembreakdown_outlet', $data);
    }
    function update_available_outlet($itemoutlet_id, $data)
    {
        $data['data_modified'] = current_millis();
        $this->db->where('itemoutlet_id', $itemoutlet_id);
        return $this->db->update('production_itembreakdown_outlet', $data);
    }

}

/* End of file Production_itembreakdown_model.php */
/* Location: ./application/models/Production_itembreakdown_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-12-04 02:45:43 */
/* http://harviacode.com */