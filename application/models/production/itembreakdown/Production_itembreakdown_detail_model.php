<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Production_itembreakdown_detail_model extends CI_Model {

	public $table = 'production_itembreakdown_detail';
    public $id = 'ibdetail_id';
    public $order = 'DESC';

    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get($this->table)->row();
    }

	function get_detail_all($itembreakdown_id, $outlet_id)
    {
        $this->db->select('rd.*, p.name AS product_name, u.name AS unit_name');
        $this->db->from($this->table.' rd');
        $this->db->join('products p', 'rd.product_fkid = p.product_id', 'left');
        $this->db->join('unit u', 'p.unit_fkid = u.unit_id', 'left');
        $this->db->where(array(
            'rd.itembreakdown_fkid' => $itembreakdown_id,
            'rd.outlet_fkid' => $outlet_id,
            'rd.admin_fkid' => $this->session->userdata('admin_id'),
            'rd.data_status' => true,
            'p.data_status' => 'on'
        ));
        $this->db->order_by('product_name', 'asc');
        return $this->db->get()->result();
    }

    function get_hpp($product_id, $outlet_id)
    {
        $this->db->select('price_buy AS hpp, price_sell');
        $this->db->where(array(
            'product_fkid' => $product_id,
            'outlet_fkid' => $outlet_id,
            'data_status' => 'on'
        ));
        return $this->db->get('view_catalogue_detail')->row();
    }

    //insert detail
    function insert_detail($data)
    {
        $data['ibdetail_id'] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        $data['data_status'] = true;
        return $this->db->insert($this->table, $data);
    }

    function update_detail($id, $data)
    {
        $data['data_modified'] = current_millis();

        $this->db->where(array(
            'admin_fkid' => $this->session->userdata('admin_id'),
            'ibdetail_id' => $id,
        ));
        return $this->db->update($this->table, $data);
    }
    
    function delete_detail($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->delete($this->table);
    }

    function delete_detail_by_outlet($outlet_id, $detail_type)
    {
        $this->db->where('detail_type', $detail_type);
        $this->db->where('outlet_fkid', $outlet_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->delete($this->table);
    }

    //cek product
    function cek_produk($product_master_id, $outlet_id)
    {
        $this->db->where('product_fkid', $product_master_id);
        $this->db->where('outlet_fkid', $outlet_id);
        return $this->db->get('view_catalogue_detail');
    }

    function active_produk($product_detail_id)
    {
        $object = array(
            'data_status' => 'on',
            'data_modified' => date('Y-m-d H:i:s')
        );
        $this->db->where('product_detail_id', $product_detail_id);
        return $this->db->update('products_detail', $object);
    }

}

/* End of file Production_itembreakdown_detail_model.php */
/* Location: ./application/models/production/itembreakdown/Production_itembreakdown_detail_model.php */