<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Production_model extends CI_Model
{

    public $table = 'production';
    public $id = 'production_id';
    public $table_detail = 'production_detail';
    public $detail_id = 'productiondetail_id';
    public $order = 'DESC';


    // datatables
    function json() {
        $this->datatables->select('
            p.production_id,
            p.itembreakdown_fkid,
            ib.name AS recipe_name,
            p.outlet_fkid,
            o.name AS outlet_name,
            p.qty_recipe,
            p.qty_primary,
            p.admin_fkid,
            p.data_created,
            p.data_modified
            ');
        $this->datatables->from($this->table.' p');
        //add this line for join
        $this->datatables->join('production_itembreakdown ib', 'p.itembreakdown_fkid = ib.itembreakdown_id', 'left');
        $this->datatables->join('outlets o', 'p.outlet_fkid = o.outlet_id', 'left');
        //$this->datatables->join('table2', 'production.field = table2.field');
        $this->datatables->where(array(
            'p.admin_fkid' => $this->session->userdata('admin_id'), //berdasarkan yang login
            'o.admin_fkid' => $this->session->userdata('admin_id'),
            'ib.admin_fkid' => $this->session->userdata('admin_id')
        )); 
        $this->datatables->add_column('action', anchor(site_url('production/read/$1'),'Read')." | ".anchor(site_url('production/update/$1'),'Update')." | ".anchor(site_url('production/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'production_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get($this->table)->row();
    }

    //get detail by master id
    function get_detail_by_id($master_id)
    {
        $this->db->select('
            prd.*,
            pr.admin_fkid,
            p.name AS product_name,
            u.name AS unit_name
        ');
        $this->db->from($this->table_detail.' prd');
        $this->db->join('production pr', 'prd.production_fkid = pr.production_id', 'left');
        $this->db->join('products p', 'prd.product_fkid = p.product_id', 'left');
        $this->db->join('unit u', 'p.unit_fkid = u.unit_id', 'left');
        $this->db->where('pr.admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where('prd.production_fkid', $master_id);
        $this->db->order_by('product_name', 'asc');
        return $this->db->get()->result();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('production_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('itembreakdown_fkid', $q);
	$this->db->or_like('qty', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('production_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('itembreakdown_fkid', $q);
	$this->db->or_like('qty', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert_master($data)
    {
        $data['production_id'] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['employee_fkid'] = ($this->session->userdata('user_type')=='employee') ? $this->session->userdata('user_id') : null;
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        return $this->db->insert($this->table, $data);
    }

    function insert_masterdetail($data){
        $data['productiondetail_id'] = null;
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        return $this->db->insert($this->table_detail, $data);
    }

    // update data
    function update($id, $data)
    {
        // $this->db->where($this->id, $id);
        // $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->delete($this->table);
    }

    public function get_for_opname($id,$last2date,$date,$outlet_id,$timeZone)
    {
        $this->db->select("
            sum((select pdd.qty where pdd.detail_type='ingridient')) as ingridient,
            sum((select pdd.qty where pdd.detail_type='endproduct')) as endproduct,
            sum((select pdd.qty where pdd.detail_type='residual')) as residual,                         

            ");
        $this->db->from('production_detail pdd');
        $this->db->join('products p','p.product_id=pdd.product_fkid');
        $this->db->join('products_detail pd','pd.product_fkid=p.product_id');
        if($last2date != 0){

            $this->db->where("pdd.data_created > ".$last2date."");
            
        }
        $this->db->where("pdd.data_created < ".$date."");

        $this->db->where('p.admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where('pd.product_detail_id = '.$id);
        $this->db->where('pd.outlet_fkid = '. $outlet_id);

        $query = $this->db->GROUP_BY('pd.product_detail_id')->get($this->table);
        // $query= $this->db->get();

        $result = array();

        foreach ($query->result() as $row)
        {
          $temp_data = array(
              'ingridient' => $row->ingridient,
              'endproduct' => $row->endproduct,
              'residual' => $row->residual,
              );
          array_push($result,$temp_data);
        }

        return $result;
    }

}

/* End of file Production_model.php */
/* Location: ./application/models/Production_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-12-25 08:03:40 */
/* http://harviacode.com */