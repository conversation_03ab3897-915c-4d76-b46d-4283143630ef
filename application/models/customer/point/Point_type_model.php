<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Point_type_model extends CI_Model {

	public $table	= 'points_type';
	public $id		= 'point_type_id';

	public function datatables()
	{
		$this->datatables->select('
			point_type_id AS id,
			name,
			value,
			(IF(is_active=1,"Yes","No")) AS active,
		');
		$this->datatables->from($this->table);
		$this->datatables->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->datatables->generate();
	}

}

/* End of file Point_type_model.php */
/* Location: ./application/models/customer/point/Point_type_model.php */