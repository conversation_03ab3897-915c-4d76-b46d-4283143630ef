<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Point_collection_model extends CI_Model {

	public $table	= 'point_collection';
	public $id 		= 'point_collection_id';

	public function datatables()
	{
		$this->datatables->select('
			point_collection_id AS id,
			pc.name,
			p.name AS member_name,
			(IF(pc.data_status=1,"Yes","No")) AS status_active,
		');
		$this->datatables->from($this->table.' pc');
		$this->datatables->join('members_type mt', 'pc.member_type_fkid = mt.type_id', 'left');
		$this->datatables->join('products p', 'mt.product_fkid = p.product_id', 'left');
		$this->datatables->where('pc.admin_fkid', $this->session->userdata('admin_id'));
		return $this->datatables->generate();
	}

	public function get_by_id($id)
	{
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table)->row();
	}

	public function insert($data)
	{
		$data['admin_fkid'] = $this->session->userdata('admin_id');
		$data['data_created'] = current_millis();
		$data['data_modified'] = current_millis();
		$data['data_status'] = true;
		return $this->db->insert($this->table, $data);
	}

	public function update($id, $data)
	{
		$data['data_modified'] = current_millis();
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $data);
	}

	//MASTER DETAIL
	function insertbatch_detail_nominal($data)
	{
		return $this->db->insert_batch($this->table.'_detail_nominal', $data);
	}




	//POINT COLLECTION ACTIVE OUTLET
	public function get_active_outlet($id)
	{
		$this->db->select('
			ao.outlet_fkid AS outlet_id,
			o.name AS outlet_name
		');
		$this->db->from($this->table.'_outlet ao');
		$this->db->join('outlets o', 'ao.outlet_fkid = o.outlet_id', 'left');
		$this->db->where('ao.point_collection_fkid', $id);
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get()->result();
	}

	public function delete_by_parent_id($id)
	{
		$this->db->where('point_collection_fkid', $id);
		$result = $this->db->delete($this->table.'_outlet');
		return $result;
	}



	//POINT COLLECTION DETAIL
	//get produk list on point collection
	public function get_product_list($id)
	{
		$this->db->select('
			pod.point_collection_detail_id,
			p.name AS product_name,
			pod.product_fkid AS product_id,
			pod.point,
		');
		$this->db->from($this->table.'_detail pod');
		$this->db->join('products p', 'pod.product_fkid = p.product_id', 'left');
		$this->db->where('pod.point_collection_fkid', $id);
		$this->db->where('pod.admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get()->result();
	}

}

/* End of file Point_collection_model.php */
/* Location: ./application/models/customer/point/Point_collection_model.php */