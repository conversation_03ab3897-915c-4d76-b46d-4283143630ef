<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Member_type_model extends CI_Model {

	public $table	= 'members_type';
	public $id		= 'type_id';

	function datatables()
	{
		$this->datatables->select('
			mt.type_id,
			mt.product_fkid AS id,
			p.name,
			mt.point_target AS point,
			mt.spent_target AS target,
			mt.price,
			(IF(p.data_status="on","Yes","No")) AS active,
			(SELECT count(ml.member_id) FROM members ml WHERE ml.type_fkid=mt.type_id ) AS count_member,
		');
		$this->datatables->from($this->table.' mt');
		$this->datatables->join('products p', 'p.product_id = mt.product_fkid', 'left');
		// $this->datatables->join('members ml', 'mt.type_id = ml.type_fkid', 'left');
		$this->datatables->where('mt.admin_fkid', $this->session->userdata('admin_id'));
		$this->datatables->where('catalogue_type', 'member');
		// $this->datatables->group_by('mt.type_id');
		return $this->datatables->generate();
	}

	function get_by_id($id)
	{
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table)->row();
	}

	function create($data)
	{
		$data[$this->id] = null;
		$data['admin_fkid'] = $this->session->userdata('admin_id');
		$data['data_created'] = current_millis();
		$data['data_modified'] = current_millis();
		return $this->db->insert($this->table, $data);
	}

	function update($id, $data)
	{
		$data['data_modified'] = current_millis();
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $data);
	}

	function delete($id)
	{
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->delete($this->table);
	}

	function form_select()
	{
		$this->db->select('
			mt.type_id AS id,
			p.name AS name
		');
		$this->db->from($this->table.' mt');
		$this->db->join('products p', 'mt.product_fkid = p.product_id', 'left');
		$this->db->where('mt.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('p.data_status', 'on');
		$this->db->order_by('name', 'asc');
		return $this->db->get()->result();
	}

}

/* End of file Member_type_model.php */
/* Location: ./application/models/customer/member/Member_type_model.php */