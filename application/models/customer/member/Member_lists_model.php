<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Member_lists_model extends CI_Model {

	public $table	= 'members';
	public $id		= 'member_id';
	public $table_type = 'members_type';
	public $table_product = 'products';

	function datatables()
	{
		$this->datatables->select('
			m.member_id AS id,
			m.name AS member_name,
			m.email,
			m.phone,
			p.name AS type_name,
			m.member_from AS origin,
			(CASE 
				WHEN m.status = 0 THEN "Not Active"
				WHEN m.status = 1 THEN "Active"
				WHEN m.status = 2 THEN "Banned"
			END) AS status,
			0 AS total_point,
			0 AS point_redeem,
		');
		$this->datatables->from($this->table.' m');
		$this->datatables->join($this->table_type.' t', 'm.type_fkid = t.type_id', 'left');
		$this->datatables->join($this->table_product.' p', 't.product_fkid = p.product_id', 'left');
		$this->datatables->where('m.admin_fkid', $this->session->userdata('admin_id'));
		return $this->datatables->generate();
	}

	function get_by_id($id)
	{
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table)->row();
	}

	function update($id, $data)
	{
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $data);
	}

}

/* End of file Member_lists_model.php */
/* Location: ./application/models/customer/member/Member_lists_model.php */