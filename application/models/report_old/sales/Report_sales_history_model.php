<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Report_sales_history_model extends CI_Model {

	public $table = 'sales_detail';
    public $id = 'sales_detail_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    public function jsonTransaksi($parm)
    {
        $this->db->select("
        s.sales_id AS nomor_nota,
        s.display_nota AS display_nota,
        s.time_created AS tanggal,
        s.status AS status,
        
        s.qty_customers AS pax,
        SUM(s.discount) AS discountSales,
        SUM(sd.discount) AS discountSd,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'discount' AND sales.sales_id = sd.sales_fkid),0) AS discountTax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'service' AND sales.sales_id = sd.sales_fkid),0) AS service,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'tax' AND sales.sales_id = sd.sales_fkid),0) AS tax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'voucher' AND sales.sales_id = sd.sales_fkid),0) AS voucherTax,

        
        s.grand_total AS grand_total,
        s.voucher AS voucherSales,
        s.voucher_info AS keterangan_voucherS,
        s.discount_info AS keterangan_discountS,
        MIN(sd.discount_info) AS keterangan_discountSd,
        sum(sd.sub_total) AS sub_total,
        MIN(o.name) AS outlet_name,
        MIN(sf.name) AS shift_name,
        MIN(em.name) AS employee,
        (SELECT sum(sub_total) from sales_void sv where sv.sales_fkid = s.sales_id) as sub_void,
        sum((select sum(total) from sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales,
        ifnull((select promotion_value from sales_promotion where sales_fkid = s.sales_id),0) as promo
        ");

        $this->db->from('sales s');
        $this->db->join('outlets o','s.outlet_fkid = o.outlet_id','left');
        $this->db->join('employee em','em.employee_id = s.employee_fkid','left');
        $this->db->join('open_shift os','s.open_shift_fkid = os.open_shift_id','left');
        $this->db->join('shift sf','os.shift_fkid = sf.shift_id','left');
        $this->db->join('sales_detail sd', 'sd.sales_fkid = s.sales_id', 'left');
        $this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where('s.data_status', 'on');
       switch ($parm['dataStatus']) {
           case 'success':
               $this->db->where('s.status','success');
               break;
           case 'refund':
               $this->db->where('s.status','refund');
               break;
       }
        if ($parm['shift'] !='0') {
            $this->db->where('shift_id', $parm['shift']);;
        }
        if ($parm['outlet'] !='0') {
            $this->db->where('outlet_id', $parm['outlet']);
        }
        if ($parm['dataType'] == '1') {
            $this->db->where("s.time_created BETWEEN ".$parm['startDate']." AND ".$parm['endDate']);
        }else{
            $this->db->where("os.time_open BETWEEN ".$parm['startDate']." AND ".$parm['endDate']);
        }
        $this->db->group_by('s.sales_id');
        $this->db->limit($parm['limit'],$parm['offset']);
        return $this->db->get()->result_array();
    }

    public function getPayment($sales_id)
    {
        $this->db->select('method,payment_id,pay');
        $this->db->from('sales_payment');
        $this->db->where('sales_fkid', $sales_id);
        return $this->db->get()->result_array();
    }

    public function getPayDetail($payment_id)
    {
        $this->db->select('mb.name,sp.pay');
        $this->db->from('payment_media_bank mb');
        $this->db->join('sales_payment_bank sb', 'sb.bank_fkid = mb.bank_id', 'left');
        $this->db->join('sales_payment sp', 'sp.payment_id = sb.sales_payment_fkid', 'left');
        $this->db->where('sb.sales_payment_fkid', $payment_id);
        return $this->db->get()->row();
    }


    // json sukses v2
    public function jsonSuccsessV2($shift,$outlet,$timeZone,$startDate,$endDate,$dataType)
    {
        $this->db->select("
        s.sales_id AS nomor_nota,
        s.display_nota AS display_nota,
        s.time_created AS tanggal,
        s.status AS status,
        s.qty_customers AS pax,
        SUM(s.discount) AS discountSales,
        SUM(sd.discount) AS discountSd,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'discount' AND sales.sales_id = sd.sales_fkid),0) AS discountTax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'service' AND sales.sales_id = sd.sales_fkid),0) AS service,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'tax' AND sales.sales_id = sd.sales_fkid),0) AS tax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'voucher' AND sales.sales_id = sd.sales_fkid),0) AS voucherTax,
        
        s.voucher AS voucherSales,
        s.voucher_info AS keterangan_voucherS,
        s.discount_info AS keterangan_discountS,
        MIN(sd.discount_info) AS keterangan_discountSd,
        sum(sd.sub_total) AS sub_total,
        MIN(o.name) AS outlet_name,
        MIN(sf.name) AS shift_name,
        MIN(em.name) AS employee,
        (SELECT sum(sv.sub_total) from sales_void sv where sv.sales_fkid = s.sales_id) as sub_void,
        s.grand_total,
        sum((select sum(total) from sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales
        ");

        $this->db->from('sales s');
        $this->db->join('outlets o','s.outlet_fkid = o.outlet_id','left');
        $this->db->join('employee em','em.employee_id = s.employee_fkid','left');
        $this->db->join('open_shift os','s.open_shift_fkid = os.open_shift_id','left');
        $this->db->join('shift sf','os.shift_fkid = sf.shift_id','left');
        // $this->db->join('sales_payment sp','sp.sales_fkid = s.sales_id','left');
        // $this->db->join('sales_tax st','st.sales_fkid = s.sales_id','left');
        // $this->db->join('gratuity g','g.gratuity_id = st.tax_fkid','left');        
        $this->db->join('sales_detail sd', 'sd.sales_fkid = s.sales_id', 'left');
 
        $this->db->where('s.status','success');
        $this->db->where('s.data_status','on');
        $this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));

        if (!empty($shift) || $shift !=0 ) {
            $this->db->where('shift_id', $shift);;
        }
        if (!empty($outlet) || $outlet !=0) {
            $this->db->where('outlet_id', $outlet);
        }
        if ($dataType == 1) {
            if (!empty($startDate)) {
                $this->db->where("date_format(from_unixtime((s.time_created / 1000 + ".$timeZone.")), '%Y-%m-%d') >=", $startDate);
            }
            if (!empty($endDate)) {
                $this->db->where("date_format(from_unixtime((s.time_created / 1000 + ".$timeZone.")), '%Y-%m-%d') <=", $endDate);
            }
        }else{
             if (!empty($startDate)) {
                $this->db->where("date_format(from_unixtime((os.time_open / 1000 + ".$timeZone.")), '%Y-%m-%d') >=", $startDate);
            }
            if (!empty($endDate)) {
                $this->db->where("date_format(from_unixtime((os.time_open / 1000 + ".$timeZone.")), '%Y-%m-%d') <=", $endDate);
            }
        }

        $this->db->group_by('s.sales_id');

        return $this->db->get()->result_array();
    }


    //json history revund V2
    public function jsonRefundV2($shift,$outlet,$timeZone,$startDate,$endDate,$dataType)
    {
        $this->db->select("
        s.sales_id AS nomor_nota,
        s.display_nota AS display_nota,
        sr.time_created AS tanggal,
        s.status AS status,
        s.qty_customers AS pax,
        SUM(s.discount) AS discountSales,
        SUM(sd.discount) AS discountSd,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'discount' AND sales.sales_id = sd.sales_fkid),0) AS discountTax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'service' AND sales.sales_id = sd.sales_fkid),0) AS service,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'tax' AND sales.sales_id = sd.sales_fkid),0) AS tax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'voucher' AND sales.sales_id = sd.sales_fkid),0) AS voucherTax,

        (SELECT sum(sv.sub_total) from sales_void sv where sv.sales_fkid = s.sales_id) as sub_void,
        s.voucher AS voucherSales,
        s.voucher_info AS keterangan_voucherS,
        s.discount_info AS keterangan_discountS,
        MIN(sd.discount_info) AS keterangan_discountSd,
        sum(sd.sub_total) AS sub_total,
        MIN(o.name) AS outlet_name,
        MIN(sf.name) AS shift_name,
        MIN(em.name) AS employee,
        s.grand_total,
        sr.reason as ket_refund,
        sum((select sum(total) from sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales
        ");

        $this->db->from('sales s');
        $this->db->join('outlets o','s.outlet_fkid = o.outlet_id','left');
        $this->db->join('employee em','em.employee_id = s.employee_fkid','left');
        $this->db->join('open_shift os','s.open_shift_fkid = os.open_shift_id','left');
        $this->db->join('shift sf','os.shift_fkid = sf.shift_id','left');
        // $this->db->join('sales_payment sp','sp.sales_fkid = s.sales_id','left');
        $this->db->join('sales_tax st','st.sales_fkid = s.sales_id','left');
        $this->db->join('gratuity g','g.gratuity_id = st.tax_fkid','left');
        $this->db->join('sales_detail sd', 's.sales_id = sd.sales_fkid', 'left');
        $this->db->join('sales_refund sr', 's.sales_id = sr.sales_fkid', 'left');

 
        $this->db->where('s.status','refund');
        $this->db->where('s.data_status','on');
        $this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));

        if (!empty($shift) || $shift !=0) {
            $this->db->where('shift_id', $shift);;
        }
        if (!empty($outlet) || $outlet !=0) { 
            $this->db->where('outlet_id', $outlet);
        }
        if ($dataType == 1) {
            if (!empty($startDate)) {
                $this->db->where("date_format(from_unixtime((sr.time_created / 1000 + ".$timeZone.")), '%Y-%m-%d') >=", $startDate);
            }
            if (!empty($endDate)) {
                $this->db->where("date_format(from_unixtime((sr.time_created / 1000 + ".$timeZone.")), '%Y-%m-%d') <=", $endDate);
            }
        }else{
            if (!empty($startDate)) {
                $this->db->where("date_format(from_unixtime((os.time_open / 1000 + ".$timeZone.")), '%Y-%m-%d') >=", $startDate);
            }
            if (!empty($endDate)) {
                $this->db->where("date_format(from_unixtime((os.time_open / 1000 + ".$timeZone.")), '%Y-%m-%d') <=", $endDate);
            }
        }   

        $this->db->group_by('s.sales_id');

        return $this->db->get()->result_array();
    }


    //json detail v2
    public function jsonDetailV2($param)
    {
        $this->db->select("
            o.name AS outlet_name,
            sf.name AS shift_name,
            s.customer_name AS customer_name,
            s.sales_id AS nomor_nota,
            s.display_nota AS display_nota,
            s.time_created AS tanggal,
            s.status AS status,
            s.qty_customers AS pax,
            s.discount AS discountSales,
            s.grand_total AS grand_total,
            s.voucher AS voucherSales,
            s.voucher_info AS keterangan_voucherS,
            s.discount_info AS keterangan_discountS,
            s.sales_id,
            sd.price AS price,
            sd.qty AS qty,
            sd.discount_info AS keterangan_discountSd,
            sd.sub_total AS sub_total,
            sd.discount AS discountSd,
            p.name AS product_name,
            p.sku AS sku,
            sd.product_detail_fkid,
            sd.sales_detail_id,
            sc.name as sub_category,
            c.name as category,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='service' and tx.sales_detail_fkid=sd.sales_detail_id),0) as service,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='tax' and tx.sales_detail_fkid=sd.sales_detail_id),0) as tax,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='discount' and tx.sales_detail_fkid=sd.sales_detail_id),0) as discountTax,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='voucher' and tx.sales_detail_fkid=sd.sales_detail_id),0) as voucherTax,
            
            emd.name AS oprator,
            em.name AS employee,
            ifnull(pdv.variant_name,'') as variant,
            ifnull(sum((select sdc.total as dis_detail where sdc.type='discount' and sdc.sales_detail_fkid=sd.sales_detail_id)),0) as dis_detail,

            ifnull(sum((select sdc.total as dis_detail where sdc.type='voucher' and sdc.sales_detail_fkid=sd.sales_detail_id)),0) as vo_detail,
            
            (SELECT (ifnull((SELECT promotion_value FROM sales_detail_promotion sdp WHERE sdp.sales_detail_fkid IS NOT NULL AND sdp.sales_detail_fkid = sd.sales_detail_id ),0)+IFNULL((SELECT total FROM sales_detail_discount sdd WHERE sdd.sales_detail_fkid = sd.sales_detail_id AND sdd.type = 'promotion'),0))) AS promo

        ");
        $this->db->from('sales_detail sd');
        $this->db->join('sales s','s.sales_id = sd.sales_fkid','left'); 
        $this->db->join('products p','sd.product_fkid = p.product_id','left');
        $this->db->join('products_subcategory sc', 'sc.product_subcategory_id = p.product_subcategory_fkid', 'left');
        $this->db->join('products_category c', 'c.product_category_id = p.product_category_fkid', 'left');
        $this->db->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid', 'left');  
        $this->db->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');    
        $this->db->join('sales_detail_discount sdc', 'sdc.sales_detail_fkid = sd.sales_detail_id','left');
        $this->db->join('employee em','em.employee_id = s.employee_fkid','left');
        $this->db->join('employee emd','emd.employee_id = sd.employee_fkid','left');
        $this->db->join('open_shift os','s.open_shift_fkid = os.open_shift_id','left');
        $this->db->join('shift sf','os.shift_fkid = sf.shift_id','left');
        $this->db->join('outlets o','s.outlet_fkid = o.outlet_id','left');
        $this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where('s.data_status', 'on');

        if ($param['shift']!='0') {
            $this->db->where('sf.shift_id', $param['shift']);
        }
        if ($param['outlet']!='0') {
            $this->db->where('o.outlet_id', $param['outlet']);
        }
        if ($param['dataType'] == '1') {
            $this->db->where("s.time_created BETWEEN ".$param['startDate']." AND ".$param['endDate']);
        }else{
             $this->db->where("os.time_open BETWEEN ".$param['startDate']." AND ".$param['endDate']);
        }        
        $this->db->group_by('sd.sales_detail_id');
        $this->db->limit($param['limit'],$param['offset']);
        return $this->db->get()->result_array();

    }


    public function serverDetail($param)
    {
        $this->db->select(" 
            o.admin_fkid AS admin_fkid,
            o.name AS outlet_name,
            sf.name AS shift_name,
            s.customer_name AS customer_name,
            s.sales_id AS nomor_nota,
            s.time_created AS tanggal,
            s.status AS status,
            s.qty_customers AS pax,
            s.discount AS discountSales,
            s.grand_total AS grand_total,
            s.voucher AS voucherSales,
            s.voucher_info AS keterangan_voucherS,
            s.discount_info AS keterangan_discountS,
            MIN(sp.method) AS pembayaran,
            sd.price AS price,
            sd.qty AS qty,
            sd.discount_info AS keterangan_discountSd,
            sd.sub_total AS sub_total,
            sd.discount AS discountSd,
            p.name AS product_name,
            p.sku AS sku,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'tax')),0) AS tax,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'discount')),0) AS discountTax,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'service')),0) AS service,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'voucher')),0) AS voucherTax,
            emd.name AS oprator,
            (SELECT sum(sv.qty) from sales_void sv WHERE sv.product_detail_fkid=sd.product_detail_fkid and sv.sales_detail_fkid = sd.sales_detail_id) as qty_void,
            (SELECT sum(sv.sub_total) from sales_void sv WHERE sv.product_detail_fkid=sd.product_detail_fkid and sv.sales_detail_fkid = sd.sales_detail_id) as sub_void,
            em.name AS employee,
            ifnull(pdv.variant_name,'') as variant,
            ");
        $this->db->from('sales_detail sd');
        $this->db->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid', 'left');
        $this->db->join('sales s','s.sales_id = sd.sales_fkid','left');
        $this->db->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
        $this->db->join('products p','sd.product_fkid = p.product_id','left');
        $this->db->join('sales_void sv','sv.product_fkid = p.product_id','left');
        $this->db->join('sales_payment sp','sp.sales_fkid = s.sales_id','left');
        $this->db->join('employee em','em.employee_id = s.employee_fkid','left');
        $this->db->join('employee emd','emd.employee_id = sd.employee_fkid','left');
        $this->db->join('open_shift os','s.open_shift_fkid = os.open_shift_id','left');
        $this->db->join('shift sf','os.shift_fkid = sf.shift_id','left');
        $this->db->join('sales_detail_tax sdt','sdt.sales_detail_fkid = sd.sales_detail_id','left');
        $this->db->join('gratuity g','g.gratuity_id = sdt.tax_fkid','left');
        $this->db->join('outlets o','s.outlet_fkid = o.outlet_id','left');
        $this->db->where('s.data_status', 'on');

        $this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));

        if ($param['shift'] !=0) {
            $this->db->where('sf.shift_id', $param['shift']);
        }
        if ($param['outlet'] != 0) {
            $this->db->where('o.outlet_id', $outlet);
        }
       if ($param['dataType'] == '1') {
            $this->db->where("s.time_created BETWEEN ".$param['startDate']." AND ".$param['endDate']);
        }else{
             $this->db->where("os.time_open BETWEEN ".$param['startDate']." AND ".$param['endDate']);
        }     
        $this->db->group_by('sd.sales_detail_id');
        $this->db->limit($param['limit'],$param['offset']);
        return $this->db->get()->result_array();
            
    }


    //report void
     public function jsonVoidDetail($param)
    {
        $this->db->select("
                o.admin_fkid as admin_fkid,
                o.outlet_id AS outlet_id,
                s.customer_name as customer_name,
                s.sales_id AS nomor_nota,
                s.display_nota AS display_nota,
                s.time_created AS tanggal,
                s.status as status,
                p.name as product_name,
                p.sku as sku,
                s.qty_customers AS pax,
                o.name AS outlet_name,
                sf.name AS shift_name,
                sv.qty as qty_void,
                (sv.price*-1) as price_void,
                (sv.sub_total*-1) as sub_void,
                sv.info as info_void,
                sv.sales_void_id as id,
                em.name as employee,
                ifnull(pdv.variant_name,'') as variant,
            ");
        $this->db->from('sales s');
        $this->db->join('outlets o','s.outlet_fkid = o.outlet_id','left');
        $this->db->join('employee em','em.employee_id = s.employee_fkid','left');
        $this->db->join('open_shift os','s.open_shift_fkid=os.open_shift_id','left');
        $this->db->join('shift sf','os.shift_fkid=sf.shift_id','left');
        $this->db->join('sales_void sv','sv.sales_fkid=s.sales_id','left');
        $this->db->join('products p','sv.product_fkid=p.product_id','left');
        $this->db->join('products_detail pd', 'pd.product_detail_id = sv.product_detail_fkid', 'left');
        $this->db->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
        $this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where('sv.qty !=','null');
        $this->db->where('s.data_status','on');

        if ($param['shift'] != '0') {
            $this->db->where('shift_id', $param['shift']);
        }
        if ($param['outlet'] !='0') {
            $this->db->where('outlet_id', $param['outlet']);
        }
        if ($param['dataType'] == '1') {
            $this->db->where("s.time_created BETWEEN ".$param['startDate']." AND ".$param['endDate']);
        }else{
            $this->db->where("os.time_open BETWEEN ".$param['startDate']." AND ".$param['endDate']);  
        }
        $this->db->limit($param['limit'],$param['offset']);
        return $this->db->get()->result_array();
    }

     public function getVoid($sales_id,$product_id)
    {
        $this->db->select("
            sum(sv.qty) as qty_void,
            sum(sv.sub_total) as sub_void
        ");
        $this->db->from('sales_void sv');
        $this->db->join('sales s', 's.sales_id = sv.sales_fkid', 'left');
        $this->db->where('sv.sales_fkid', $sales_id);
        $this->db->where('sv.product_detail_fkid', $product_id);
        $this->db->where('sv.sales_detail_fkid', $sales_detail_id);
        $this->db->where('s.status', 'success');
        return $this->db->get()->row();
    }

    public function getvoid_BySalesDetail($sales_detail_id,$product_detail_fkid,$sales_id)
    {
        $this->db->select("
            sum(sv.qty) as qty_void,
            sum(sv.sub_total) as sub_void
        ");
        $this->db->where('sv.sales_detail_fkid', $sales_detail_id);
        $this->db->where('sv.product_detail_fkid', $product_detail_fkid);
        // $this->db->where('sv.sales_fkid', $sales_id);
        return $this->db->get('sales_void sv')->row();
    }

}

/* End of file Report_sales_history.php */
/* Location: ./application/models/report/Report_sales_history.php */