<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login_model extends CI_Model {

	function get_by_email($user_type, $email)
	{
		switch ($user_type) {
			case 'admin':
				$this->db->where('email', $email);
				$row = $this->db->get('admin')->row();
				break;
			case 'employee':
				$this->db->select('e.*, a.settings, a.developer_mode');
				$this->db->from('employee e');
				$this->db->join('admin a', 'e.admin_fkid = a.admin_id', 'left');
				$this->db->where('e.email', $email);
				$this->db->where('e.access_status_web', 'activated');
				$row = $this->db->get()->row();
				break;
		}

		return $row;
	}

	function update_lastlogin()
	{
		switch ($this->session->userdata('user_type')) {
			case 'admin':
				$table = 'admin';
				$pid = 'admin_id';
				break;
			case 'employee':
				$table = 'employee';
				$pid = 'employee_id';
				break;
			default: break;
		}

		//data yang akan diupdate
		$object = array(
			'last_login' => current_millis()
		);
		$this->db->where($pid, $this->session->userdata('user_id'));
		return $this->db->update($table, $object);
	}

}

/* End of file Login_model.php */
/* Location: ./application/models/core/Login_model.php */