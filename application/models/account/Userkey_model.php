<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Userkey_model extends CI_Model {

	public $table = 'users_key';

	public function create_key($data, $expired_in_second)
	{
		$datenow = current_millis();
		$time_expired = $expired_in_second; // (1jam) dalam detik
		$dateexpired = $datenow + ($time_expired * 1000);
		

		$object = array(
			'key_id' => null,
			'key_type' => $data['key_type'],
			'user_level' => $data['user_level'],
			'email' => $data['email'],
			'secret_key' => $data['secret_key'],
			'status' => true,
			'data_expired' => $dateexpired,
			'data_created' => $datenow
		);

		return $result = $this->db->insert($this->table, $object);
	}
	public function create_activation_key($data, $time_expired=3600)
	{
		$datenow = current_millis();
		// $time_expired = 60*60*24; // dalam detik
		$dateexpired = $datenow + ($time_expired * 1000);
		

		$object = array(
			'key_id' => null,
			'key_type' => 'activation',
			'user_level' => $data['user_level'],
			'email' => $data['email'],
			'secret_key' => $data['secret_key'],
			'status' => true,
			'data_expired' => $dateexpired,
			'data_created' => $datenow
		);

		return $result = $this->db->insert($this->table, $object);
	}

	public function admin_web_activation($email)
	{
		$object['activation_status'] = 'activated';
		
		$this->db->where(array(
			'email' => $email,
			'data_modified' => current_millis()
		));
		return $this->db->update('admin', $object);
	}

	public function employee_web_activation($data)
	{
		$email = $data['email'];
		$password = $data['password'];
		
		$object = array(
			'password' => password_encrypt($password),
			'access_status_web' => 'activated'
		);

		$this->db->where('email', $email);
		return $result = $this->db->update('employee', $object);
	}

	public function disable_activation_link($email)
	{
		$object_key['status'] = false;

		$this->db->where('key_type', 'activation');
		$this->db->where('email', $email);
		return $result = $this->db->update($this->table, $object_key);
	}


	//BUAT GANTI EMAIL
	public function confirm_change_email($data)
	{
		$datenow = current_millis();
		$time_expired = 6000; // (1jam) dalam detik
		$dateexpired = $datenow + ($time_expired * 1000);

		$object = array(
			'key_id' => null,
			'key_type' => 'change_email',
			'user_level' => $data['user_level'],
			'email' => $data['email'],
			'secret_key' => $data['secret_key'],
			'status' => true,
			'data_expired' => $dateexpired,
			'data_created' => $datenow
		);

		return $result = $this->db->insert($this->table, $object);
	}
	public function disable_change_email_link($email)
	{
		$object_key['status'] = false;

		$this->db->where('key_type', 'change_email');
		$this->db->where('email', $email);
		return $result = $this->db->update($this->table, $object_key);
	}

	












	/* ON REMAKE */


	public function activation_admin($email, $password_hash)
	{
		$result_updatepass = $this->db->query("CALL user_change_password('".$email."', '".$password_hash."', '".ENVIRONMENT."')");

		//object to update
		$object = array(
			'activation_status' => 'activated',
			// 'password' => $password_hash,
			'date_join' => current_millis(),
			'developer_mode' => false,
		);

		//update process
		$this->db->where('email', $email);
		$this->db->where('activation_status !=', 'activated');
		return $this->db->update('admin', $object);
	}

	public function disabled_key($email, $key_type)
	{
		$object['status'] = false;

		$this->db->where('key_type', $key_type);
		$this->db->where('email', $email);
		return $result = $this->db->update($this->table, $object);
	}
}

/* End of file Userkey_model.php */
/* Location: ./application/models/core/Userkey_model.php */