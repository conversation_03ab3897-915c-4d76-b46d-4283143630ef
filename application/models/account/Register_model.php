<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Register_model extends CI_Model {

	public $table_admin = 'admin';
	public $table_employee = 'employee';
	public $id = 'admin_id';
	
	public function __construct()
	{
		parent::__construct();
		$this->load->helper('password');
	}

	//cek apakah email sudah dipakai atau belum
	function cek_email($email, $type)
	{
		// $type = 'admin' / 'employee'
		$this->db->select('email');
		$this->db->from($type);
		$this->db->where('email', $email);
		return $this->db->get()->row();
	}

	//menambah akun owner
	function insert_admin($data)
	{
		$data['admin_id'] = null;
		$data['password'] = password_encrypt($data['password']);
		$data['activation_status'] = 'pending';
		$data['data_created'] = current_millis();
		$data['data_modified'] = current_millis();
		return $this->db->insert($this->table_admin, $data);
	}

}

/* End of file Register_model.php */
/* Location: ./application/models/core/Register_model.php */