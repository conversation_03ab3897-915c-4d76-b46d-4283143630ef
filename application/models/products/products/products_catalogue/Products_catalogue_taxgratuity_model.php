<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_catalogue_taxgratuity_model extends CI_Model {

    public $table = 'products_detail_taxdetail';
    public $id = 'product_detail_fkid';

    /* TAX GRATUITY START */
    public function get_taxgratuity($masterdetail_id)
    {
        $this->db->where($this->id, $masterdetail_id);
        $this->db->where('data_status', 'on');
        return $this->db->get($this->table)->result();
    }
    public function insert_taxgratuity($data)
    {
        $data['taxdetail_id'] = null;
        $data['data_created'] = date('Y-m-d H:i:s');
        $data['data_status'] = 'on';
        return $this->db->insert($this->table, $data);
    }
    public function update_taxgratuity_datastatus_by_masterdetailid($masterdetail_id, $value)
    {
        $object = array('data_status' => $value);
        $this->db->where('product_detail_fkid', $masterdetail_id);
        return $this->db->update($this->table, $object);
    }
    public function get_taxgratuity_by_masterdetailid($masterdetail_id)
    {
        $this->db->where('product_detail_fkid', $masterdetail_id);
        $data = $this->db->get($this->table);
        return $return = array(
            'data' => $data->result(),
            'num_rows' => $data->num_rows()
        );
    }
    public function update_taxgratuity($masterdetail_id, $data)
    {
        // $this->db->where('multipleprice_id', $multipleprice_id);
        // return $result = $this->db->update($this->table, $data);

        $this->db->where('product_detail_fkid', $masterdetail_id);
        $this->db->where('tax_fkid', $data['tax_fkid']);
        return $result = $this->db->update('products_detail_taxdetail', $data);
    }

}

/* End of file Products_catalogue_taxgratuity_model.php */
/* Location: ./application/models/products/products/products_catalogue/Products_catalogue_taxgratuity_model.php */