<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Products_catalogue_model extends CI_Model
{

    public $table_master = 'products';
    public $table_masterdetail = 'products_detail';
    public $table_viewmaster = 'view_products';
    public $table_viewmasterdetail = 'view_products_outlet';
    public $id = 'product_id';
    public $order = 'DESC';

    

    function __construct()
    {
        parent::__construct();
    }

    // datatables master products
    function json() {
        $this->datatables->select('product_id,product_name,product_type_fkid,product_category_fkid,product_subcategory_fkid,purchase_report_category_fkid,unit_fkid,barcode,sku,photo,admin_fkid,data_created,data_modified,data_status,product_type_name,product_category_name,product_subcategory_name,prc_name,unit_name,product_type_adminid,product_category_adminid,product_subcategory_adminid,prc_adminid,unit_adminid');
        $this->datatables->from('view_products');
        //add this line for join
        //$this->datatables->join('table2', 'view_products.field = table2.field');
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil master product berdasarkan owner
        $this->datatables->add_column('action', anchor(site_url('view_products/read/$1'),'Read')." | ".anchor(site_url('view_products/update/$1'),'Update')." | ".anchor(site_url('view_products/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), '');
        return $this->datatables->generate();
    }

    /* untuk breakdown by aziz */
    function json_breakdown() {
        $this->datatables->select('product_id,product_name,product_type_fkid,product_category_fkid,product_subcategory_fkid,purchase_report_category_fkid,unit_fkid,admin_fkid,data_status,product_type_name,product_category_name,product_subcategory_name,prc_name,unit_name,product_type_adminid, product_category_adminid,product_subcategory_adminid,prc_adminid,unit_adminid');
        $this->datatables->from('view_catalogue');
        //add this line for join
        //$this->datatables->join('table2', 'view_products.field = table2.field');
        $this->datatables->where('stock_management', true);
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil master product berdasarkan owner
        $this->datatables->where('data_status', 'on');
        $this->datatables->add_column('action', '<button class="btn btn-primary btn-xs" onclick="actionAddToForm($1)">Add</button>');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        //$this->db->where('data_status', 'on'); //tampilkan semua product
        $this->db->where('product_admin_id', $this->session->userdata('admin_id')); //ambil product berdasarkan owner
        $this->db->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil outlet berdasarkan owner
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table_viewmaster)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //tampilkan semua product
        //$this->db->where('product_admin_id', $this->session->userdata('admin_id')); //ambil product berdasarkan owner
        //$this->db->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil outlet berdasarkan owner
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table_viewmaster)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
    $this->db->or_like('product_id', $q);
    $this->db->or_like('product_name', $q);
    $this->db->or_like('product_type_fkid', $q);
    $this->db->or_like('product_category_fkid', $q);
    $this->db->or_like('product_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('barcode', $q);
    $this->db->or_like('sku', $q);
    $this->db->or_like('photo', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_modified', $q);
    $this->db->or_like('data_status', $q);
    $this->db->or_like('product_type_name', $q);
    $this->db->or_like('product_category_name', $q);
    $this->db->or_like('product_subcategory_name', $q);
    $this->db->or_like('prc_name', $q);
    $this->db->or_like('unit_name', $q);
    $this->db->or_like('product_type_adminid', $q);
    $this->db->or_like('product_category_adminid', $q);
    $this->db->or_like('product_subcategory_adminid', $q);
    $this->db->or_like('prc_adminid', $q);
    $this->db->or_like('unit_adminid', $q);
    $this->db->from($this->table_viewmaster);
        //$this->db->where('data_status', 'on'); //tampilkan semua product
        $this->db->where('product_admin_id', $this->session->userdata('admin_id')); //ambil product berdasarkan owner
        $this->db->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil outlet berdasarkan owner
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
    $this->db->or_like('product_id', $q);
    $this->db->or_like('product_name', $q);
    $this->db->or_like('product_type_fkid', $q);
    $this->db->or_like('product_category_fkid', $q);
    $this->db->or_like('product_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('barcode', $q);
    $this->db->or_like('sku', $q);
    $this->db->or_like('photo', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_modified', $q);
    $this->db->or_like('data_status', $q);
    $this->db->or_like('product_type_name', $q);
    $this->db->or_like('product_category_name', $q);
    $this->db->or_like('product_subcategory_name', $q);
    $this->db->or_like('prc_name', $q);
    $this->db->or_like('unit_name', $q);
    $this->db->or_like('product_type_adminid', $q);
    $this->db->or_like('product_category_adminid', $q);
    $this->db->or_like('product_subcategory_adminid', $q);
    $this->db->or_like('prc_adminid', $q);
    $this->db->or_like('unit_adminid', $q);
    $this->db->limit($limit, $start);
        //$this->db->where('data_status', 'on'); //tampilkan semua product
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil master product berdasarkan owner
        return $this->db->get($this->table_viewmaster)->result();
    }


    // insert data
    public function insert_master($data)
    {
        $data['product_id'] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //owner id
        $data['data_created'] = date('Y-m-d H:i:s');
        $data['data_modified'] = date('Y-m-d H:i:s');
        $data['data_status'] = 'on';
        $insert = $this->db->insert($this->table_master, $data);
        $get_primarykey = $this->db->insert_id(); // ambil primary key
        return $return = array(
            'status_insert' => $insert, 
            'primary_key' => $get_primarykey
            );
    }
    public function update_master($master_id, $data)
    {
        $object = array(
            'name' => $data['name'],
            'product_type_fkid' => $data['product_type_fkid'],
            'product_category_fkid' => $data['product_category_fkid'],
            'product_subcategory_fkid' => $data['product_subcategory_fkid'],
            'purchase_report_category_fkid' => $data['purchase_report_category_fkid'],
            'unit_fkid' => $data['unit_fkid'],
            'barcode' => $data['barcode'],
            'sku' => $data['sku'],
            'data_modified' => date('Y-m-d H:i:s')
        );
        if (!empty($data['photo'])) {
            $object['photo'] = $data['photo'];
        }


        $this->db->where('product_id', $master_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //owner id
        return $this->db->update($this->table_master, $object);
    }
    public function insert_masterdetail($data)
    {
        $data['product_detail_id'] = null;
        $data['data_status'] = 'on';
        $data['data_modified'] = date('Y-m-d H:i:s');
        $insert = $this->db->insert('products_detail', $data);
        $get_primarykey = $this->db->insert_id(); // ambil primary key
        return $return = array(
            'status_insert' => $insert, 
            'primary_key' => $get_primarykey
            );
    }
    public function update_masterdetail_by_masterid($master_id, $data)
    {
        $this->db->where('product_fkid', $master_id);
        return $this->db->update($this->table_masterdetail, $data);
    }
    public function update_masterdetail_datastatus_by_outlet($outlet_id, $data_status_value)
    {
        $object = array(
            'data_status' => $data_status_value
        );

        $this->db->where('outlet_fkid', $outlet_id);
        return $this->db->update($this->table_masterdetail, $object);
    }
    public function get_masterdetail($master_id) //ambil menu tersedia di outlet mana saja
    {
        $this->db->where('product_fkid', $master_id);
        $this->db->where('product_detail_data_status', 'on');
        $this->db->order_by('outlet_name', 'asc');
        return $this->db->get($this->table_viewmasterdetail)->result();
    }
    public function get_masterdetail_on_outlet($master_id, $outlet_id)
    {
        $this->db->where('product_fkid', $master_id);
        $this->db->where('outlet_fkid', $outlet_id);
        $get = $this->db->get($this->table_masterdetail);
        if ($get->num_rows()==1) {
            $return = array(
                'status'=> true,
                'count_data' => $get->num_rows(),
                'data' => $get->row()
            );
        }
        elseif ($get->num_rows>1) {
            $return = array(
                'status' => false,
                'count_data' => $get->num_rows(),
                'message' => 'Duplicate Data'
            );
        }
        else{
            $return = array(
                'status' => false,
                'count_data' => $get->num_rows(),
                'message' => 'Record Not Found'
            );
        }

        return $return;
    }
    public function update_masterdetail($masterdetail_id, $data)
    {
        $object = array(
            'price_update' => $data['price_update'],
            'price_sell' => $data['price_sell'],
            'voucher' => $data['voucher'],
            'discount' => $data['discount'],
            'active' => $data['menu_active'],
            'data_modified' => date('Y-m-d H:i:s')
            );
        $this->db->where('product_detail_id', $masterdetail_id);
        $this->db->where('data_status', 'on');
        return $this->db->update('products_detail', $object);
    }

    


    function insert($data)
    {
        //$data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        $insert = $this->db->insert($this->table_master, $data);
        //return $this->db->insert($this->table_master, $data);
        $get_primarykey = $this->db->insert_id(); // ambil primary key

        return $return = array(
            'status_insert' => $insert, 
            'primary_key' => $get_primarykey
            );
        //return $return;
    }

    // update data
    function update($id, $data)
    {
        $object = array(
            'name' => $data['name'],
            'product_type_fkid' => $data['product_type_fkid'],
            'product_category_fkid' => $data['product_category_fkid'],
            'product_subcategory_fkid' => $data['product_subcategory_fkid'],
            'purchase_report_category_fkid' => $data['purchase_report_category_fkid'],
            'unit_fkid' => $data['unit_fkid']
        );
        if ($data['photo']!=null) {
            $object['photo'] = $data['photo'];
        }
        $this->db->where($this->id, $id);
        //$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //$this->db->where('data_status', 'on'); //cari hanya data aktif //tampilkan semua product
        return $this->db->update($this->table_master, $object);
        // echo $this->db->last_query();
        // die();
    }
    public function update_detail($id_detail, $data)
    {
        $object = array(
            'price' => $data['price'],
            'voucher' => $data['voucher'],
            'discount' => $data['discount'],
            'active' => $data['active'],
            'data_modified' => date('Y-m-d H:i:s')
        );
        $this->db->where($this->id, $id_detail);
        return $this->db->update($this->table_masterdetail, $object);
    }

    // delete data
    function delete($id)
    {
        //cek data lewat di view_product
        $this->db->where('product_id', $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //$this->db->where('data_status', 'on'); //cari hanya data aktif //tampilkan semua product
        $cek = $this->db->get('view_product');
        if ($cek->num_rows()>1) {
            return false;
        }
        else{
            //action hapus
            $this->db->where($this->id, $id);
            //$this->db->where('data_status', 'on'); //cari hanya data aktif //tampilkan semua product
            return $this->db->delete($this->table_masterdetail);
        }
    }


    
    /* TAX GRATUITY START */
    // function dipindah ke Products_catalogue_taxgratuity_model
    /* TAX GRATUITY END */


    /* MULTIPLE PRICE START */
    // function dipindah ke Products_catalogue_multipleprice_model
    /* MULTIPLE PRICE END */


    /* LINK MENU START */
    // function dipindah ke Products_catalogue_link_model
    /* LINK MENU END */


    // datatables product by outlet
    function json2($outlet_id) {
        $this->datatables->select('product_detail_id,product_fkid,outlet_fkid,price,voucher,discount,active,data_modified,product_detail_data_status,product_id,product_name,product_type_fkid,product_category_fkid,product_subcategory_fkid,photo,admin_fkid,data_created,data_status,product_type_name,product_category_name,product_subcategory_name,outlet_name,outlet_admin_id,product_admin_id');
        $this->datatables->from('view_product');
        //add this line for join
        //$this->datatables->join('table2', 'view_product.field = table2.field');
        $this->datatables->where('product_admin_id', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->datatables->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //kalau outlet tidak kosong
        if (!empty($outlet_id)) {
            $this->datatables->where('outlet_fkid', $outlet_id);
        }
        $this->datatables->add_column('action', anchor(site_url('view_product/read/$1'),'Read')." | ".anchor(site_url('view_product/update/$1'),'Update')." | ".anchor(site_url('view_product/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), '');
        return $this->datatables->generate();
    }

    

}

/* End of file Products_catalogue_model.php */
/* Location: ./application/models/Products_catalogue_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-08-08 06:47:56 */
/* http://harviacode.com */