<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Products_catalogue_model extends CI_Model
{

    public $table_ori = 'products_catalogue';
    public $table_oridetail = 'products_catalogue_detail';
    public $table = 'view_product';
    public $id = 'product_detail_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json($outlet_id) {
        $this->datatables->select('product_detail_id,product_fkid,outlet_fkid,price,voucher,discount,active,data_modified,product_detail_data_status,product_id,product_name,product_type_fkid,product_category_fkid,product_subcategory_fkid,photo,admin_fkid,data_created,data_status,product_type_name,product_category_name,product_subcategory_name,outlet_name,outlet_admin_id,product_admin_id');
        $this->datatables->from('view_product');
        //add this line for join
        //$this->datatables->join('table2', 'view_product.field = table2.field');
        $this->datatables->where('product_admin_id', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->datatables->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //kalau outlet tidak kosong
        if (!empty($outlet_id)) {
            $this->datatables->where('outlet_fkid', $outlet_id);
        }
        $this->datatables->add_column('action', anchor(site_url('view_product/read/$1'),'Read')." | ".anchor(site_url('view_product/update/$1'),'Update')." | ".anchor(site_url('view_product/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), '');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        //$this->db->where('data_status', 'on'); //tampilkan semua product
        $this->db->where('product_admin_id', $this->session->userdata('admin_id')); //ambil product berdasarkan owner
        $this->db->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil outlet berdasarkan owner
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        //$this->db->where('data_status', 'on'); //tampilkan semua product
        $this->db->where('product_admin_id', $this->session->userdata('admin_id')); //ambil product berdasarkan owner
        $this->db->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil outlet berdasarkan owner
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
    $this->db->or_like('product_detail_id', $q);
    $this->db->or_like('product_fkid', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('voucher', $q);
    $this->db->or_like('discount', $q);
    $this->db->or_like('active', $q);
    $this->db->or_like('data_modified', $q);
    $this->db->or_like('product_detail_data_status', $q);
    $this->db->or_like('product_id', $q);
    $this->db->or_like('product_name', $q);
    $this->db->or_like('product_type_fkid', $q);
    $this->db->or_like('product_category_fkid', $q);
    $this->db->or_like('product_subcategory_fkid', $q);
    $this->db->or_like('photo', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->or_like('product_type_name', $q);
    $this->db->or_like('product_category_name', $q);
    $this->db->or_like('product_subcategory_name', $q);
    $this->db->or_like('outlet_name', $q);
    $this->db->or_like('outlet_admin_id', $q);
    $this->db->or_like('product_admin_id', $q);
    $this->db->from($this->table);
        //$this->db->where('data_status', 'on'); //tampilkan semua product
        $this->db->where('product_admin_id', $this->session->userdata('admin_id')); //ambil product berdasarkan owner
        $this->db->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil outlet berdasarkan owner
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('', $q);
    $this->db->or_like('product_detail_id', $q);
    $this->db->or_like('product_fkid', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('voucher', $q);
    $this->db->or_like('discount', $q);
    $this->db->or_like('active', $q);
    $this->db->or_like('data_modified', $q);
    $this->db->or_like('product_detail_data_status', $q);
    $this->db->or_like('product_id', $q);
    $this->db->or_like('product_name', $q);
    $this->db->or_like('product_type_fkid', $q);
    $this->db->or_like('product_category_fkid', $q);
    $this->db->or_like('product_subcategory_fkid', $q);
    $this->db->or_like('photo', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->or_like('product_type_name', $q);
    $this->db->or_like('product_category_name', $q);
    $this->db->or_like('product_subcategory_name', $q);
    $this->db->or_like('outlet_name', $q);
    $this->db->or_like('outlet_admin_id', $q);
    $this->db->or_like('product_admin_id', $q);
    $this->db->limit($limit, $start);
        //$this->db->where('data_status', 'on'); //tampilkan semua product
        $this->db->where('product_admin_id', $this->session->userdata('admin_id')); //ambil product berdasarkan owner
        $this->db->where('outlet_admin_id', $this->session->userdata('admin_id')); //ambil outlet berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        //$data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        $insert = $this->db->insert($this->table_ori, $data);
        //return $this->db->insert($this->table_ori, $data);
        $get_primarykey = $this->db->insert_id(); // ambil primary key

        return $return = array(
            'status_insert' => $insert, 
            'primary_key' => $get_primarykey
            );
        //return $return;
    }

    // update data
    function update($id, $data)
    {
        $object = array(
            'name' => $data['name'],
            'product_type_fkid' => $data['product_type_fkid'],
            'product_category_fkid' => $data['product_category_fkid'],
            'product_subcategory_fkid' => $data['product_subcategory_fkid'],

        );
        if ($data['photo']!=null) {
            $object['photo'] = $data['photo'];
        }
        $this->db->where($this->id, $id);
        //$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //$this->db->where('data_status', 'on'); //cari hanya data aktif //tampilkan semua product
        return $this->db->update($this->table_ori, $object);
        // echo $this->db->last_query();
        // die();
    }
    public function update_detail($id_detail, $data)
    {
        $object = array(
            'price' => $data['price'],
            'voucher' => $data['voucher'],
            'discount' => $data['discount'],
            'active' => $data['active'],
            'data_modified' => date('Y-m-d H:i:s')
        );
        $this->db->where($this->id, $id_detail);
        return $this->db->update($this->table_oridetail, $object);
    }

    // delete data
    function delete($id)
    {
        //cek data lewat di view_product
        $this->db->where('product_id', $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //$this->db->where('data_status', 'on'); //cari hanya data aktif //tampilkan semua product
        $cek = $this->db->get('view_product');
        if ($cek->num_rows()>1) {
            return false;
        }
        else{
            //action hapus
            $this->db->where($this->id, $id);
            //$this->db->where('data_status', 'on'); //cari hanya data aktif //tampilkan semua product
            return $this->db->delete($this->table_oridetail);
        }
    }


    
    /* TAX GRATUITY START */
    // function dipindah ke Products_catalogue_taxgratuity_model
    /* TAX GRATUITY END */


    /* MULTIPLE PRICE START */
    // function dipindah ke Products_catalogue_multipleprice_model
    /* MULTIPLE PRICE END */


    /* LINK MENU START */
    // function dipindah ke Products_catalogue_link_model
    /* LINK MENU END */

}

/* End of file Products_catalogue_model.php */
/* Location: ./application/models/Products_catalogue_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-08-08 06:47:56 */
/* http://harviacode.com */