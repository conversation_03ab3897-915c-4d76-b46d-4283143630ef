<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_catalogue_link_model extends CI_Model {

	public $id = 'product_detail_fkid';
	public $table = 'products_catalogue_link';

	/* LINK MENU START */
    //get product link
    public function get_product_link($product_id)
    {
        $this->db->where($this->id, $product_id);
        $this->db->order_by('order_no', 'asc');
        return $this->db->get($this->table)->result();
    }

    // CRUD product addlink
    //insert product addlink
    public function insert_link($data)
    {
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert($this->table, $data);
    }
    public function update_link($id, $data)
    {
        $this->db->where('pcl_id', $id);
        return $this->db->update($this->table, $data);
    }
    public function get_link_by_id($id)
    {
        $this->db->where('pcl_id', $id);
        $this->db->where('data_status', 'on');
        return $this->db->get($this->table)->row();
    }
    public function delete_link($id)
    {
        $this->db->where('pcl_id', $id);
        $this->db->where('data_status', 'on');
        return $this->db->delete('products_catalogue_link');
    }


    //INI untuk link detail
    public function get_product_linkdetail_by_id($link_id)
    {
        $this->db->where('pcl_fkid', $link_id);
        $this->db->order_by('product_name', 'asc');
        return $this->db->get('view_product_link_detail')->result();
    }
    public function insert_product_to_link($data)
    {
        $data['pcl_detail_id'] = null;
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert('products_catalogue_link_detail', $data);
    }
    public function update_product_to_link($data)
    {
        $this->db->where('pcl_detail_id', $data['pcl_detail_id']);
        return $this->db->update('products_catalogue_link_detail', $data);
    }
    public function delete_product_to_link($id)
    {
        $this->db->where('pcl_detail_id', $id);
        return $this->db->delete('products_catalogue_link_detail');
    }

    /* LINK MENU END */

}

/* End of file Products_catalogue_link_model.php */
/* Location: ./application/models/products/products/products_catalogue/Products_catalogue_link_model.php */