<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_catalogue_taxgratuity_model extends CI_Model {

    public $table = 'products_catalogue_detail_taxdetail';
    public $id = 'product_detail_fkid';

    /* TAX GRATUITY START */
    public function get_taxgratuity($product_detail_id)
    {
        $this->db->where($this->id, $product_detail_id);
        return $this->db->get($this->table)->result();
    }
    public function insert_taxgratuity($data)
    {
        $data['taxdetail_id'] = null;
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert($this->table, $data);
    }
    public function delete_taxgratuity($product_id)
    {
        $this->db->where($this->id, $product_id);
        return $this->db->delete($this->table);
    }

}

/* End of file Products_catalogue_taxgratuity_model.php */
/* Location: ./application/models/products/products/products_catalogue/Products_catalogue_taxgratuity_model.php */