<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_catalogue_multipleprice_model extends CI_Model {

	public $table = 'products_catalogue_detail_multipleprice';

	/* MULTIPLE PRICE START */
    //get multiple price
    public function getMultiplePrice($id)
    {
        $this->db->select('*');
        $this->db->from($this->table); //table
        $this->db->where('product_detail_fkid', $id);
        $this->db->order_by('qty', 'asc');
        return $this->db->get()->result();
    }
    public function insert_multiprice($data)
    {
    	$object = array(
    		'multipleprice_id' => null,
    		'product_detail_fkid' => $data['product_detail_fkid'],
    		'qty' => $data['qty'],
    		'price' => $data['price']
    	);

        //$data['multipleprice_id'] = null;
        $data['data_created'] = date('Y-m-d H:i:s');
        $insertMultiprice = $this->db->insert($this->table, $data); //table
        //echo $this->db->last_query();
        return $insertMultiprice;
    }
    public function delete_multipleprice($productdetail_id)
    {
        $this->db->where('product_detail_fkid', $productdetail_id);
        return $this->db->delete($this->table);
    }
    /* MULTIPLE PRICE END */

}

/* End of file Products_catalogue_multipleprice_model.php */
/* Location: ./application/models/products/products/products_catalogue/Products_catalogue_multipleprice_model.php */