<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_linkmenu_model extends CI_Model {

	public $id = 'linkmenu_id';
	public $table = 'products_linkmenu';
	public $table_detail = 'products_linkmenu_detail';
	public $tb_view_detail = 'view_linkmenu_detail';


	//LINK MENU MASTER (NEW) start
	//ambil data bersarkan nama, outlet, master product
	function get_by_name_outlet_product($linkmenu_name, $outlet_id, $product_id)
	{
		$this->db->where(array(
			'name' => $linkmenu_name,
			'outlet_fkid' => $outlet_id,
			'product_fkid'=> $product_id,
			'admin_fkid'  => $this->session->userdata('admin_id'),
		));
		return $this->db->get($this->table)->row();
	}
	function get_by_name_outlet_productdetail($linkmenu_name, $outlet_id, $product_detail_id)
	{
		$this->db->where(array(
			'name' => $linkmenu_name,
			'outlet_fkid' => $outlet_id,
			'product_detail_fkid'=> $product_detail_id,
			'admin_fkid'  => $this->session->userdata('admin_id'),
		));
		return $this->db->get($this->table)->row();
	}

	//insert master
	function insert($data)
	{
		$data['linkmenu_id'] = null;
		$data['admin_fkid'] = $this->session->userdata('admin_id');
		$data['data_created'] = current_millis();
		$data['data_modified'] = current_millis();
		$data['data_status'] = 'on';
		return $this->db->insert($this->table, $data);
	}

	//update master by id
	function update_by_id($linkmenu_id, $data)
	{
		$data['data_modified'] = current_millis();
		$this->db->where('linkmenu_id', $linkmenu_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $data);
	}
	function delete_by_id($linkmenu_id)
	{
		$this->db->where('linkmenu_id', $linkmenu_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->delete($this->table);
	}
	//LINK MENU MASTER (NEW) end

	//LINK MENU DETAIL (NEW) start
	function get_detail_by_linkmenu_id($linkmenu_id)
	{
		$this->db->where('linkmenu_fkid', $linkmenu_id);
		$this->db->where_in('active', array('on_all','on_link'));
		$this->db->where('linkmenu_adminid', $this->session->userdata('admin_id'));
		$this->db->order_by('product_name_onlinkdetail', 'asc');
		return $this->db->get($this->tb_view_detail)->result();
	}

	function insertbatch_detail($data)
	{
		return $this->db->insert_batch($this->table_detail, $data);
	}

	function delete_detail_by_linkmenu($linkmenu_id)
	{
		$this->db->where('linkmenu_fkid', $linkmenu_id);
		return $this->db->delete($this->table_detail);
	}
	//LINK MENU DETAIL (NEW) end






	public function get_linkmenu_by_outlet($data)
	{
		$this->db->where(array(
			'outlet_fkid' => $data['outlet_id'],
			'product_fkid' => $data['product_id'],
			'admin_fkid' => $this->session->userdata('admin_id'),
		));
		$this->db->order_by('order_no', 'asc');
		return $this->db->get($this->table);
	}

	public function get_linkmenu_by_productdetail($product_detail_id)
	{
		$this->db->where('product_detail_fkid', $product_detail_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table);
	}

	public function get_linkmenu_by_id($linkmenu_id, $product_id)
	{
		$this->db->where(array(
			'linkmenu_id' => $linkmenu_id,
			'product_fkid'=> $product_id,
			'admin_fkid'  => $this->session->userdata('admin_id'),
		));
		return $this->db->get($this->table);
	}

	public function insert_linkmenu($data)
	{
		$data['linkmenu_id'] = null;
		$data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        $data['data_status'] = 'on';
        return $this->db->insert($this->table, $data);
	}

	public function update_linkmenu($linkmenu_id, $data)
	{
		$object = array(
			'name' => $data['name'],
			'description' => $data['description'],
			'order_no' => $data['order_no'],
			'is_multiplechoice' => $data['is_multiplechoice'],
			'data_modified' => current_millis()
		);

		$this->db->where('linkmenu_id', $linkmenu_id);
		$this->db->where('product_fkid', $data['product_fkid']);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $object);
	}

	public function delete_linkmenu($linkmenu_id, $product_id)
	{
		$this->db->where('linkmenu_id', $linkmenu_id);
		$this->db->where('product_fkid', $product_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->delete($this->table);
	}


	//LINKMENU DETAIL
	public function get_linkmenudetail_by_detail_id($linkmenu_detail_id)
	{
		$this->db->where(array(
			'linkmenu_detail_id' => $linkmenu_detail_id,
			'linkmenu_adminid' => $this->session->userdata('admin_id')
		));
		return $this->db->get($this->tb_view_detail);
	}

	public function update_linkmenudetail($linkmenu_detail_id, $data)
	{
		$object = array(
			'price_add' => $data['price_add'],
			'data_modified' => current_millis()
		);

		$this->db->where('linkmenu_detail_id', $linkmenu_detail_id);
		$this->db->where('linkmenu_fkid', $data['linkmenu_id']);
		return $this->db->update($this->table_detail, $object);
	}

	public function delete_linkmenudetail($linkmenu_detail_id)
	{
		$this->db->where('linkmenu_detail_id', $linkmenu_detail_id);
		return $this->db->delete($this->table_detail);
	}


	//ADD PRODUCT TO LINKMENU
    function json_product_by_outlet($outlet_id) {
        // $this->datatables->select('product_detail_id,product_fkid,product_name,barcode,sku,unit_name,price_sell,variant_name');
        $this->datatables->select("
        	product_detail_id,
        	product_fkid,
        	product_name,
        	variant_fkid,
        	variant_name,
        	coalesce(concat(product_name, ' (', variant_name, ')'), product_name) AS primary_product,
        	barcode,
        	sku,
        	unit_name,
        	price_sell
        ");
        $this->datatables->from('view_catalogue_detail');
        $this->datatables->where('outlet_fkid', $outlet_id);
        $this->datatables->where('data_status', 'on');
        $this->datatables->where('product_detail_data_status', 'on');
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id'));
        $this->datatables->where('(active = "on_all" OR active= "on_link")');
        return $this->datatables->generate();
    }

    public function get_product_onoutlet($product_detail_id)
    {
    	$this->db->where('product_detail_id', $product_detail_id);
    	$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
    	return $this->db->get('view_catalogue_detail');
    }

    public function add_linkmenu_product($data)
    {
    	$object = array(
    		'linkmenu_detail_id' => null,
    		'linkmenu_fkid' => $data['linkmenu_id'],
    		'product_detail_fkid' => $data['product_detail_id'],
    		'price_add' => $data['price_add'],
    		'data_created' => current_millis(),
    		'data_modified' => current_millis()
    	);
    	return $this->db->insert($this->table_detail, $object);
    }

    public function delete_linkmenudetail_by_linkmenu_productdetail($linkmenu_id, $product_detail_id)
    {
    	$this->db->where('product_detail_fkid', $product_detail_id);
    	$this->db->where('linkmenu_fkid', $linkmenu_id);
    	return $this->db->delete('products_linkmenu_detail');
    }

}

/* End of file Products_linkmenu_model.php */
/* Location: ./application/models/products/products_catalogue/Products_linkmenu_model.php */