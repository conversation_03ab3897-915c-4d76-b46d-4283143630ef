<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_model extends CI_Model {

	//view table
	public $table_view = 'view_catalogue';

	//table
	public $table = 'products';
	
	//ids
	public $id = 'product_id';
	
	//order
	public $order = 'DESC';

	//datatables (new)
	function datatables()
	{
		$this->datatables->select('
			p.product_id,
			p.name AS product_name,
			p.catalogue_type,
			p.barcode,
			p.sku,
			p.photo,
			pt.name AS product_type_name,
			pc.name AS product_category_name,
			psc.name AS product_subcategory_name,
			prc.name AS prc_name,
			u.name AS unit_name'
		);
		$this->datatables->from('products_detail pd');
		$this->datatables->join('products p', 'pd.product_fkid = p.product_id', 'right outer');
		$this->datatables->join('products_type pt', 'p.product_type_fkid = pt.products_type_id', 'left');
		$this->datatables->join('products_category pc', 'p.product_category_fkid = pc.product_category_id', 'left');
		$this->datatables->join('products_subcategory psc', 'p.product_subcategory_fkid = psc.product_subcategory_id', 'left');
		$this->datatables->join('purchase_report_category prc', 'p.purchase_report_category_fkid = prc.purchase_report_category_id', 'left');
		$this->datatables->join('unit u', 'p.unit_fkid = u.unit_id', 'left');
		$this->datatables->where('p.admin_fkid', $this->session->userdata('admin_id'));
		$this->datatables->where('p.catalogue_type !=', 'member');
		$this->datatables->where('p.data_status', 'on');

		//have filter outlet
		if (!empty($this->input->post('advancefilter_outlet'))) {
			$this->datatables->where('pd.outlet_fkid', $this->input->post('advancefilter_outlet'));
			$this->datatables->where('pd.data_status', 'on');
		}
		$this->datatables->group_by('p.product_id');
		$this->datatables->order_by('sku,product_name', 'asc');
		return $this->datatables->generate();
	}

	// datatables
	function json() {
		$this->datatables->select('product_id,product_name,catalogue_type,product_type_fkid,product_category_fkid,product_subcategory_fkid,purchase_report_category_fkid,unit_fkid,stock_management,barcode,sku,photo,admin_fkid,data_created,data_modified,data_status,product_type_name,product_category_name,product_subcategory_name,prc_name,unit_name,product_adminid,product_type_adminid,product_category_adminid,product_subcategory_adminid,prc_adminid,unit_adminid');
		$this->datatables->from($this->table_view);
		//add this line for join
		//$this->datatables->join('table2', 'view_catalogue.field = table2.field');
		$this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil master product berdasarkan
		$this->datatables->where('catalogue_type !=', 'member');
		$this->datatables->where('data_status', 'on');
		return $this->datatables->generate();
	}

	function json_breakdown()
	{
		$this->datatables->select('product_id, product_name, catalogue_type');
		$this->datatables->from('view_catalogue');
		$this->datatables->where('catalogue_type !=', 'member');
		$this->datatables->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->datatables->where('data_status', 'on');
		return $this->datatables->generate();
	}

	function json_detail()
	{
		$this->datatables->select('
			p.product_id,
			p.name AS product_name,
			p.catalogue_type,
			p.barcode,
			p.sku,
			p.photo,
			pt.name AS product_type_name,
			pc.name AS product_category_name,
			psc.name AS product_subcategory_name,
			prc.name AS prc_name,
			u.name AS unit_name'
		);
		$this->datatables->from('products_detail pd');
		$this->datatables->join('products p', 'pd.product_fkid = p.product_id', 'left');
		$this->datatables->join('products_type pt', 'p.product_type_fkid = pt.products_type_id', 'left');
		$this->datatables->join('products_category pc', 'p.product_category_fkid = pc.product_category_id', 'left');
		$this->datatables->join('products_subcategory psc', 'p.product_subcategory_fkid = psc.product_subcategory_id', 'left');
		$this->datatables->join('purchase_report_category prc', 'p.purchase_report_category_fkid = prc.purchase_report_category_id', 'left');
		$this->datatables->join('unit u', 'p.unit_fkid = u.unit_id', 'left');
		$this->datatables->where('p.admin_fkid', $this->session->userdata('admin_id'));
		$this->datatables->where('p.catalogue_type !=', 'member');
		$this->datatables->where('p.data_status', 'on');

		$this->datatables->where('pd.outlet_fkid', $this->input->post('advancefilter_outlet'));
		$this->datatables->where('pd.data_status', 'on');
		$this->datatables->group_by('p.product_id');
		return $this->datatables->generate();
	}


	//get data by master product_id
	function get_by_id($product_id)
	{
		$this->db->select('
			p.*,
			p.name AS product_name,
			u.name AS unit_name,
		');
		$this->db->from($this->table.' p');
		$this->db->join('unit u', 'u.unit_id = p.unit_fkid', 'left');
		$this->db->where($this->id, $product_id);
		$this->db->where('catalogue_type !=', 'member');
		$this->db->where('p.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('p.data_status', 'on');
		return $this->db->get()->row();
		
		// 'product_type_adminid' => $this->session->userdata('admin_id'),
		// 'product_category_adminid' => $this->session->userdata('admin_id'),
		// 'product_subcategory_adminid' => $this->session->userdata('admin_id'),
		// 'prc_adminid' => $this->session->userdata('admin_id'),
		// 'unit_adminid' => $this->session->userdata('admin_id'),
	}

	//get data by master product sku
	function get_by_sku($sku)
	{
		$this->db->where(array(
			'admin_fkid' => $this->session->userdata('admin_id'),
			'sku' => $sku,
			'data_status' => 'on',
		));
		return $this->db->get($this->table)->row();
	}

	//get data by master barcode
	 function get_by_barcode($barcode)
	{
		$this->db->where(array(
			'admin_fkid' => $this->session->userdata('admin_id'),
			'barcode' => $barcode,
			'data_status' => 'on',
		));
		return $this->db->get($this->table)->row();
	}

	//get data by master name
	function get_by_name($product_name, $is_sensitive=false)
	{
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('data_status', 'on');
		// $this->db->where('name', $product_name);
		if ($is_sensitive===false) {
			$this->db->where('name', $product_name);
		}else{
			//v1
			$product_name = $this->db->escape_str($product_name);
			$this->db->where("name like binary '$product_name'");

			//v2
			// $this->db->where('name like binary', $product_name);
		}
		return $this->db->get($this->table)->row();
	}

	function get_by_name_sku($product_name, $sku)
	{
		if (empty($sku)) {
			$sku = null;
		}
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('name', $product_name);
		$this->db->where('sku', $sku);
		$this->db->where('data_status', 'on');
		return $this->db->get($this->table)->row();
	}

	//get all data
	function get_all()
	{
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('data_status', 'on');
		$this->db->order_by('name', 'asc');
		return $this->db->get($this->table)->result();
	}

	function get_all_view()
	{
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('data_status', 'on');
		$this->db->order_by('product_name', 'asc');
		return $this->db->get($this->table_view)->result();
	}


	//ACTION
	public function insert($data)
	{
		//replace some typo
		if ($data['catalogue_type']=='ingredient') {
			$data['catalogue_type'] = 'ingridient';
		}

		$data['product_id'] = null;
		$data['admin_fkid'] = $this->session->userdata('admin_id'); //owner id
		$data['data_created'] = current_millis();
		$data['data_modified'] = current_millis();
		$data['data_status'] = 'on';
		return $insert = $this->db->insert($this->table, $data);
	}

	public function update($product_id, $data)
	{
		//replace some typo
		if (!empty($data['catalogue_type']) && $data['catalogue_type']=='ingredient') {
			$data['catalogue_type'] = 'ingridient';
		}

		
		if (!empty($data['photo'])) {
            $data['photo'] = $data['photo'];
        }
        if (isset($data['barcode'])) {
        	$data['barcode'] = ($data['barcode'] == '' || empty($data['barcode'])) ? null : $data['barcode'];
        }
        if (isset($data['sku'])) {
        	$data['sku']	 = ($data['sku'] == '' || empty($data['sku'])) ? null : $data['sku'];
        }
		$data['data_modified'] = current_millis();
		$this->db->where(array(
			$this->id => $product_id,
			'admin_fkid' => $this->session->userdata('admin_id'),
		));
		return $this->db->update($this->table, $data);
	}

	public function delete($product_id)
	{
		//init var
		$this->db->db_debug = FALSE; //set debug
		$allow_off = true;

		if ($this->session->userdata('user_type')=='employee') {
			$this->db->where_in('outlet_fkid', $this->session->userdata('outlet_access'));
			$this->db->where('product_fkid', $product_id);
			$response = $this->db->delete('products_detail');

			//cek apakah di detail ada yang aktif
			$this->db->where('product_fkid', $product_id);
			$this->db->where('data_status', 'on');
			$this->db->from('products_detail');
			$count = $this->db->count_all_results();
			$allow_off = ($count>0) ? false : true;
		}

		if ($allow_off) {
			//query
			$this->db->where(array(
				$this->id => $product_id,
				'admin_fkid' => $this->session->userdata('admin_id')
			));
			$response = $this->db->delete($this->table);
		}

		return $response;
	}

	public function delete_soft($product_id)
	{
		$allow_off = true;

		if ($this->session->userdata('user_type')=='employee') {
			$this->db->where_in('outlet_fkid', $this->session->userdata('outlet_access'));
			$this->db->where('product_fkid', $product_id);
			$response = $this->db->update('products_detail', [
				'data_modified' => current_millis(),
				'data_status' => 'off',
			]);

			//cek apakah di detail ada yang aktif
			$this->db->where('product_fkid', $product_id);
			$this->db->where('data_status', 'on');
			$this->db->from('products_detail');
			$count = $this->db->count_all_results();
			$allow_off = ($count>0) ? false : true;
		}

		if ($allow_off) {
			$response = $this->update($product_id, [
				'data_status' => 'off',
				'barcode' => null,
				'sku' => null,
			]);
		}

		return $response;
	}

}

/* End of file Products_model.php */
/* Location: ./application/models/products/products_catalogue/Products_model.php */