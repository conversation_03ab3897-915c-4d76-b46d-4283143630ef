<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_catalogue_taxgratuity_model extends CI_Model {

    public $table = 'products_detail_taxdetail';
    public $id = 'product_detail_fkid';

    /* TAX GRATUITY START */
    public function get_taxgratuity($masterdetail_id)
    {
        $this->db->select('
            t.name AS tax_name,
            td.taxdetail_id,
            td.tax_fkid,
            td.product_detail_fkid,
            td.data_status
        ');
        $this->db->from($this->table.' td');
        $this->db->join('gratuity t', 'td.tax_fkid = t.gratuity_id', 'left');

        $this->db->where($this->id, $masterdetail_id);
        $this->db->where('td.data_status', 'on');
        return $this->db->get()->result();
    }
    public function insert_taxgratuity($data)
    {
        $data['taxdetail_id'] = null;
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        $data['data_status'] = 'on';
        return $this->db->insert($this->table, $data);
    }
    public function update_taxgratuity_datastatus_by_masterdetailid($masterdetail_id, $value)
    {
        $object = array(
            'data_modified' => current_millis(),
            'data_status' => $value
        );
        $this->db->where('product_detail_fkid', $masterdetail_id);
        return $this->db->update($this->table, $object);
    }
    public function get_taxgratuity_by_masterdetailid($masterdetail_id)
    {
        $this->db->where('product_detail_fkid', $masterdetail_id);
        $data = $this->db->get($this->table);
        return $return = array(
            'data' => $data->result(),
            'num_rows' => $data->num_rows()
        );
    }
    public function update_taxgratuity($masterdetail_id, $data)
    {
        // $this->db->where('multipleprice_id', $multipleprice_id);
        // return $result = $this->db->update($this->table, $data);

        $data['data_modified'] = current_millis();

        $this->db->where('product_detail_fkid', $masterdetail_id);
        $this->db->where('tax_fkid', $data['tax_fkid']);
        return $result = $this->db->update('products_detail_taxdetail', $data);
    }

    public function delete_by_productdetail($product_detail_id)
    {
        $this->db->where('product_detail_fkid', $product_detail_id);
        return $this->db->delete($this->table);
    }


    /* DIGUNAKAN UNTUK IMPORT CSV */
    function get_taxdetail_by_tax_masterdetail($tax_id, $product_detail_id)
    {
        $this->db->where('tax_fkid', $tax_id);
        $this->db->where('product_detail_fkid', $product_detail_id);
        return $this->db->get('products_detail_taxdetail')->row();
    }
    function update_taxdetail_by_tax_masterdetail($tax_id, $product_detail_id, $data)
    {
        $data['data_modified'] = current_millis();
        $this->db->where('tax_fkid', $tax_id);
        $this->db->where('product_detail_fkid', $product_detail_id);
        return $this->db->update('products_detail_taxdetail', $data);
    }

}

/* End of file Products_catalogue_taxgratuity_model.php */
/* Location: ./application/models/products/products_catalogue/Products_catalogue_taxgratuity_model.php */