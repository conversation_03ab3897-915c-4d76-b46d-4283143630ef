<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_detail_model extends CI_Model {

	//view table
	public $table_view = 'view_catalogue_detail';

	//table
	public $table = 'products_detail';
	
	//ids
	public $id = 'product_detail_id';
	public $parent_id = 'product_fkid';
	public $product_fkid = 'product_fkid';
	
	//order
	public $order = 'DESC';


	//datatables
	public function json_breakdown_detail()
	{
		$this->datatables->select('
			product_id,
			product_name,
			unit_name,
			variant_fkid AS variant_id,
			IFNULL(variant_name,"") AS variant_name
		');
		$this->datatables->from($this->table_view);
		$this->datatables->where('catalogue_type !=', 'member');
		$this->datatables->where('stock_management', true);
		$this->datatables->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->datatables->where('product_detail_data_status', 'on');
		$this->datatables->where('data_status', 'on');
		$this->datatables->group_by('product_id, variant_fkid');
		$this->db->order_by('product_name, variant_name', 'asc');
		return $this->datatables->generate();
	}
	
	//get data detail by product_id
	public function get_by_id($id)
	{
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table_view)->row();
	}
	public function get_by_product_id($product_id)
	{
		$this->db->where($this->parent_id, $product_id);
		$this->db->where('product_detail_data_status', 'on');
		$this->db->order_by('outlet_name', 'asc');
		// $this->db->order_by('variant_name', 'asc');
		return $this->db->get($this->table_view)->result();
	}
	
	//get data detail by master product_id
	public function get_by_parent_and_outlet($product_id, $outlet_id) //change to get_by_product_and_outlet()
	{
		$this->db->where(array(
			$this->product_fkid => $product_id,
			'outlet_fkid' => $outlet_id,
		));
		return $this->db->get($this->table_view)->row();
	}

	//get data detail by product_id dan outlet_id
	public function get_by_product_and_outlet($product_id, $outlet_id)
	{
		$this->db->where(array(
			$this->parent_id => $product_id,
			'outlet_fkid' => $outlet_id,
		));
		return $this->db->get($this->table_view)->row();
	}

	public function get_by_product_outlet($product_id, $outlet_id)
	{
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->get($this->table_view);
	}

	//get data detail by product_id, outlet_id, dan variant name
	public function get_by_product_outlet_variant($product_id, $outlet_id, $variant_id=null)
	{
		$variant_id = ($variant_id=='null') ? null : $variant_id;
		
		// $this->db->where('product_detail_data_status', 'on');
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		$this->db->where('variant_fkid', $variant_id);
		$this->db->where('product_adminid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table_view)->row();
	}

	public function get_by_product_outlet_variantname($product_id, $outlet_id, $variant_name)
	{
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		$this->db->where('variant_name', $variant_name);
		return $this->db->get($this->table_view)->row();
	}

	public function _get_variant_by_sku($sku)
	{
		$this->db->where('variant_sku', $sku);
		$this->db->where('product_adminid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table_view)->row();
	}

	
	public function insert($data)
	{
		if (empty($data['transfer_markup_type']) || empty($data['transfer_markup'])) {
			$data['transfer_markup_type'] = 'percent';
			$data['transfer_markup'] = 0;
		}
		if (empty($data['commission_staff_type']) || empty($data['commission_staff'])) {
			$data['commission_staff_type'] = 'percent';
			$data['commission_staff'] = 0;
		}
		if (empty($data['commission_customer_type']) || empty($data['commission_customer'])) {
			$data['commission_customer_type'] = 'percent';
			$data['commission_customer'] = 0;
		}

		$data['data_modified'] = current_millis();
		$data['data_status'] = 'on';
		return $insert = $this->db->insert($this->table, $data);
	}

	public function update($id, $data)
	{
		$data['data_modified'] = current_millis();

		$this->db->where($this->id, $id);
		return $this->db->update($this->table, $data);
	}

	public function update_by_parent_id($parent_id, $data) //change to update_by_product_id
	{
		$data['data_modified'] = current_millis();
		$this->db->where($this->parent_id, $parent_id);
		return $this->db->update($this->table, $data);
	}

	public function update_by_product_id($product_id, $data)
	{
		$data['data_modified'] = current_millis();
		$this->db->where($this->product_fkid, $product_id);
		return $this->db->update($this->table, $data);
	}

	public function update_by_product_and_outlet($product_id, $outlet_id, $data)
	{
		$data['data_modified'] = current_millis();
		$this->db->where($this->product_fkid, $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->update($this->table, $data);
	}

	public function update_by_product_outlet_variant($product_id, $outlet_id, $variant_id, $data)
	{
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		$this->db->where('variant_fkid', $variant_id);
		return $this->db->update($this->table, $data);
	}

	public function delete($product_detail_id)
	{
		$this->db->where(array(
			$this->id => $product_detail_id,
			'admin_fkid' => $this->session->userdata('admin_id')
		));
		return $this->db->delete($this->table);
	}

	public function delete_by_product_outlet($product_id, $outlet_id)
	{
		//set debug
		$this->db->db_debug = FALSE;

		//query
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->delete($this->table);
	}

}

/* End of file Products_detail_model.php */
/* Location: ./application/models/products/products_catalogue/Products_detail_model.php */
