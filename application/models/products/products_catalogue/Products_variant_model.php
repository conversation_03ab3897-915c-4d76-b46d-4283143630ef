<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_variant_model extends CI_Model {

	public $table = 'products_detail_variant';
	public $table_view = 'view_catalogue_detail';
	public $id = 'variant_id';

	public function get_by_active($product_id)
	{
		$this->db->distinct();
		$this->db->select('v.*');
		$this->db->from($this->table.' v');
		$this->db->join('products_detail pd', 'pd.variant_fkid = v.variant_id', 'right');
		$this->db->join('products p', 'pd.product_fkid = p.product_id', 'left');
		$this->db->where('pd.product_fkid', $product_id);
		$this->db->where('pd.data_status', 'on');
		$this->db->where('p.admin_fkid', $this->session->userdata('admin_id'));
		// $this->db->where('(v.data_status=1 or v.data_status IS NULL)');
		// $this->db->where('v.data_status', 1); //on kan lagi kalo trigger annas sudah oke
		$this->db->order_by('variant_name', 'asc');
		return $this->db->get()->result();
	}

	public function get_by_product($product_id)
	{
		$this->db->where('product_fkid', $product_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('data_status', 1);
		$this->db->order_by('variant_name', 'asc');
		return $this->db->get($this->table)->result();
	}

	public function get_by_product_outlet($product_id, $outlet_id)
	{
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->order_by('variant_name', 'asc');
		return $this->db->get($this->table_view)->result();
	}

	public function get_by_variantname_product($variant_name, $product_id, $is_sensitive=false)
	{
		if ($is_sensitive===false) {
			$this->db->where('variant_name', $variant_name);
		}else{
			$this->db->where('variant_name like binary', $variant_name);
		}
		$this->db->where('product_fkid', $product_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table)->row();
	}

	public function get_by_variant_product($variant_id, $product_id)
	{
		$this->db->where($this->id, $variant_id);
		$this->db->where('product_fkid', $product_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table)->row();
	}

	public function get_by_variantsku_product($variant_sku, $product_id)
	{
		$this->db->where('variant_sku', $variant_sku);
		$this->db->where('product_fkid', $product_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table)->row();
	}

	public function insert($data)
	{
		$data['variant_sku'] = (!empty($data['variant_sku'])) ? $data['variant_sku'] : null;
		$data['variant_barcode'] = (!empty($data['variant_barcode'])) ? $data['variant_barcode'] : null;
		$data['admin_fkid'] = $this->session->userdata('admin_id');
		$data['data_created'] = current_millis();
		$data['data_modified'] = current_millis();
		return $this->db->insert($this->table, $data);
	}

	public function update($id, $data)
	{
		$data['variant_sku'] = (!empty($data['variant_sku'])) ? $data['variant_sku'] : null;
		$data['variant_barcode'] = (!empty($data['variant_barcode'])) ? $data['variant_barcode'] : null;
		$data['data_modified'] = current_millis();
		$this->db->where($this->id, $id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $data);
	}

	

}

/* End of file Products_variant_model.php */
/* Location: ./application/models/products/products_catalogue/Products_variant_model.php */