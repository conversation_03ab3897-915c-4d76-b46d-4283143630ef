<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Ingridients_catalogue_model extends CI_Model
{

    public $table = 'ingridients_catalogue';
    public $id = 'ingridients_catalogue_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    public function json_by_outlet($outlet_id)
    {
        $this->datatables->select('
            ingridients_catalogue.ingridients_catalogue_id,
            ingridients_catalogue.name,
            ingridients_catalogue.outlet_fkid,
            ingridients_catalogue.products_type_fkid,
            ingridients_catalogue.ingridients_category_fkid,
            ingridients_catalogue.ingridients_subcategory_fkid,
            ingridients_catalogue.purchase_report_category_fkid,
            ingridients_catalogue.price,
            ingridients_catalogue.price_update,
            ingridients_catalogue.price_sell,
            ingridients_catalogue.unit_fkid,
            ingridients_catalogue.photo,
            ingridients_catalogue.data_created,
            ingridients_catalogue.data_status,

            outlets.name AS outlet_name,
            outlets.admin_fkid,
            products_type.name AS product_type_name,
            ingridients_category.name AS ingridient_category_name,
            ingridients_subcategory.name AS ingridient_subcategory_name,
            purchase_report_category.name AS prc_name,
            unit.name AS unit_name
            
            ');
        //add this line for join
        //$this->datatables->join('table2', 'ingridients_catalogue.field = table2.field');
        $this->datatables->from('ingridients_catalogue');
        $this->datatables->join('outlets', 'outlets.outlet_id = ingridients_catalogue.outlet_fkid', 'left');
        $this->datatables->join('products_type', 'ingridients_catalogue.products_type_fkid = products_type.products_type_id', 'left');
        $this->datatables->join('ingridients_category', 'ingridients_catalogue.ingridients_category_fkid = ingridients_category.ingridients_category_id', 'left');
        $this->datatables->join('ingridients_subcategory', 'ingridients_catalogue.ingridients_subcategory_fkid = ingridients_subcategory.ingridients_subcategory_id', 'left');
        $this->datatables->join('purchase_report_category', 'ingridients_catalogue.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->join('unit', 'ingridients_catalogue.unit_fkid = unit.unit_id', 'left');



        //$this->datatables->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.outlet_id', $outlet_id); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        $this->datatables->add_column('action', anchor(site_url('ingridients_catalogue/read/$1'),'Read')." | ".anchor(site_url('ingridients_catalogue/update/$1'),'Update')." | ".anchor(site_url('ingridients_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'ingridients_catalogue_id');
        return $this->datatables->generate();
    }

    // datatables
    function json() {
        $this->datatables->select('
            ingridients_catalogue.ingridients_catalogue_id,
            ingridients_catalogue.name,
            ingridients_catalogue.outlet_fkid,
            ingridients_catalogue.products_type_fkid,
            ingridients_catalogue.ingridients_category_fkid,
            ingridients_catalogue.ingridients_subcategory_fkid,
            ingridients_catalogue.purchase_report_category_fkid,
            ingridients_catalogue.price,
            ingridients_catalogue.price_update,
            ingridients_catalogue.price_sell,
            ingridients_catalogue.unit_fkid,
            ingridients_catalogue.photo,
            ingridients_catalogue.data_created,
            ingridients_catalogue.data_status,

            outlets.name AS outlet_name,
            outlets.admin_fkid,
            products_type.name AS product_type_name,
            ingridients_category.name AS ingridient_category_name,
            ingridients_subcategory.name AS ingridient_subcategory_name,
            purchase_report_category.name AS prc_name,
            unit.name AS unit_name
            
            ');
        //add this line for join
        //$this->datatables->join('table2', 'ingridients_catalogue.field = table2.field');
        $this->datatables->from('ingridients_catalogue');
        $this->datatables->join('outlets', 'outlets.outlet_id = ingridients_catalogue.outlet_fkid', 'left');
        $this->datatables->join('products_type', 'ingridients_catalogue.products_type_fkid = products_type.products_type_id', 'left');
        $this->datatables->join('ingridients_category', 'ingridients_catalogue.ingridients_category_fkid = ingridients_category.ingridients_category_id', 'left');
        $this->datatables->join('ingridients_subcategory', 'ingridients_catalogue.ingridients_subcategory_fkid = ingridients_subcategory.ingridients_subcategory_id', 'left');
        $this->datatables->join('purchase_report_category', 'ingridients_catalogue.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->join('unit', 'ingridients_catalogue.unit_fkid = unit.unit_id', 'left');


        //$this->datatables->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        $this->datatables->add_column('action', anchor(site_url('ingridients_catalogue/read/$1'),'Read')." | ".anchor(site_url('ingridients_catalogue/update/$1'),'Update')." | ".anchor(site_url('ingridients_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'ingridients_catalogue_id');
        return $this->datatables->generate();
    }

    function jsonSpoil($outlet_id) {
        $this->datatables->select('
            ingridients_catalogue.ingridients_catalogue_id,
            ingridients_catalogue.name,
            ingridients_catalogue.price,
            ingridients_catalogue.price_update,
            ingridients_catalogue.price_sell,
            unit.name AS unit_name,
            ingridients_catalogue.data_status,
            ingridients_catalogue.data_created

            
            ');
        //add this line for join
        $this->datatables->from('ingridients_catalogue');
        
        $this->datatables->join('outlets', 'ingridients_catalogue.outlet_fkid = outlets.outlet_id');
        $this->datatables->join('unit', 'ingridients_catalogue.unit_fkid = unit.unit_id');
        
        //$this->datatables->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan 
        $this->datatables->where('ingridients_catalogue.outlet_fkid', $outlet_id); //ambil 
        $this->datatables->add_column('action', anchor(site_url('ingridients_catalogue/read/$1'),'Read')." | ".anchor(site_url('ingridients_catalogue/update/$1'),'Update')." | ".anchor(site_url('ingridients_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'ingridients_catalogue_id');
        return $this->datatables->generate();
    }
    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        //$this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        return $this->db->get($this->table)->result();
    }

    /* custom cek data */
    function _cekDataID($id)
    {
        //cek apakah data merupakan milik yang login
        $this->db->select('ingridients_catalogue.ingridients_catalogue_id,
                            ingridients_catalogue.outlet_fkid,
                            outlets.admin_fkid');
        $this->db->from('ingridients_catalogue');
        $this->db->join('outlets', 'ingridients_catalogue.outlet_fkid = outlets.outlet_id', 'left');
        $this->db->where('ingridients_catalogue.ingridients_catalogue_id', $id);
        $this->db->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        //$this->db->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        return $cekData = $this->db->get();
    }
    function _cekDataOutlet($id_outlet)
    {
        $this->db->where('outlet_id', $id_outlet);
        $this->db->where('admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        //$this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get('outlets');
    }
    /* custom cek data end */
    

    // get data by id
    function get_by_id($id)
    {
        //inisialisasi
        $hasilCekData = $this->_cekDataID($id)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            //eksekusi ambil data
            $this->db->where($this->id, $id);
            //$this->db->where('data_status', 'on'); //cari hanya data aktif
            return $this->db->get($this->table)->row();
        }

        
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('ingridients_catalogue_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('products_type_fkid', $q);
    $this->db->or_like('ingridients_category_fkid', $q);
    $this->db->or_like('ingridients_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('price_update', $q);
    $this->db->or_like('price_sell', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->from($this->table);
    //$this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('ingridients_catalogue_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('products_type_fkid', $q);
    $this->db->or_like('ingridients_category_fkid', $q);
    $this->db->or_like('ingridients_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('price_update', $q);
    $this->db->or_like('price_sell', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->limit($limit, $start);
    //$this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        //inisialisasi
        $id_outlet = $data['outlet_fkid'];
        $hasilCekData = $this->_cekDataOutlet($id_outlet)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            $data[$this->id] = null;
            $data['price_update'] = $data['price'];
            //$data['data_status'] = 'on';
            $data['data_created'] = date('Y-m-d H:i:s');
            //return $this->db->insert($this->table, $data);
            $insert = $this->db->insert($this->table, $data);
            $get_primarykey = $this->db->insert_id(); // ambil primary key

            return $return = array(
                'status_insert' => $insert, 
                'primary_key' => $get_primarykey
                );
        }
    }

    // update data
    function update($id, $data)
    {
        //inisialisasi
        $id_outlet = $data['outlet_fkid'];
        $hasilCekData = $this->_cekDataOutlet($id_outlet)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            $this->db->where($this->id, $id);
            //$data['price_update'] = $data['price'];
            //$this->db->where('data_status', 'on'); //cari hanya data aktif
            return $this->db->update($this->table, $data);
        }
    }

    // delete data
    function delete($id)
    {
        //inisialisasi
        $hasilCekData = $this->_cekDataID($id)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            $this->db->where($this->id, $id);
            //$this->db->where('data_status', 'on'); //cari hanya data aktif
            return $this->db->delete($this->table);
        }
    }

    /* multiprice */
    //get multiple price
    function getMultiplePrice($id)
    {
        $this->db->select('*');
        $this->db->from('ingridients_catalogue_multipleprice');
        $this->db->where('ingridient_fkid', $id);
        $this->db->order_by('qty', 'asc');
        return $this->db->get()->result();
    }
    public function insert_multipleprice($data)
    {
        $data['ic_multipleprice_id'] = null;
        $data['data_created'] = date('Y-m-d H:i:s');
        $insertMultiprice = $this->db->insert('ingridients_catalogue_multipleprice', $data);
        //echo $this->db->last_query();
        return $insertMultiprice;
    }
    public function delete_multipleprice($ingridient_id)
    {
        $this->db->where('ingridient_fkid', $ingridient_id);
        return $this->db->delete('ingridients_catalogue_multipleprice');
    }

    
    /* TAX GRATUITY START */
    public function get_taxgratuity($ingridient_id)
    {
        $this->db->where('ingridient_fkid', $ingridient_id);
        return $this->db->get('ingridients_catalogue_taxdetail')->result();
    }
    public function insert_taxgratuity($data)
    {
        $data['taxdetail_id'] = null;
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert('ingridients_catalogue_taxdetail', $data);
    }
    public function delete_taxgratuity($ingridient_id)
    {
        $this->db->where('ingridient_fkid', $ingridient_id);
        return $this->db->delete('ingridients_catalogue_taxdetail');
    }
    /* TAX GRATUITY END */


    /* custom form ingridient list for breakdown */
    function form_select_ingridient($outlet_id)
    {
        $this->db->where('outlet_fkid', $outlet_id);
        $this->db->where('data_status', 'on');
        $this->db->order_by('name', 'asc');
        return $this->db->get($this->table);
    }

    

}

/* End of file Ingridients_catalogue_model.php */
/* Location: ./application/models/Ingridients_catalogue_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-24 13:21:33 */
/* http://harviacode.com */