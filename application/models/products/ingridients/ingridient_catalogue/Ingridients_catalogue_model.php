<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Ingridients_catalogue_model extends CI_Model
{

    public $table_master = 'ingridients';
    public $table_masterdetail = 'ingridients_detail';
    public $table_viewmaster = 'view_ingridients';
    public $table_viewmaster_detail = 'view_ingridients_outlet';
    public $id = 'ingridient_id'; //ID di outlet
    public $order = 'DESC';

    

    function __construct()
    {
        parent::__construct();
    }

    // datatables master ingridients
    function json() {
        $this->datatables->select('ingridient_id,ingridient_name,product_type_fkid,ingridient_category_fkid,ingridient_subcategory_fkid,purchase_report_category_fkid,photo,admin_fkid,data_created,data_status,product_type_name,ingridient_category_name,ingridient_subcategory_name,prc_name,unit_name,ingridient_category_adminid,ingridient_subcategory_adminid,prc_adminid');
        $this->datatables->from('view_ingridients');
        //add this line for join
        //$this->datatables->join('table2', 'view_ingridients.field = table2.field');
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil owner master ingridient 
        $this->datatables->add_column('action', anchor(site_url('view_ingridients/read/$1'),'Read')." | ".anchor(site_url('view_ingridients/update/$1'),'Update')." | ".anchor(site_url('view_ingridients/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), '');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil owner master ingridient 
        return $this->db->get($this->table_viewmaster)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil owner master ingridient 
        return $this->db->get($this->table_viewmaster)->row();
    }

    // get total rows
    function total_rows($q = NULL) {
    $this->db->or_like('ingridient_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('product_type_fkid', $q);
    $this->db->or_like('ingridient_category_fkid', $q);
    $this->db->or_like('ingridient_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('photo', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->or_like('product_type_name', $q);
    $this->db->or_like('ingridient_category_name', $q);
    $this->db->or_like('ingridient_subcategory_name', $q);
    $this->db->or_like('prc_name', $q);
    $this->db->or_like('unit_name', $q);
    $this->db->or_like('ingridient_category_adminid', $q);
    $this->db->or_like('ingridient_subcategory_adminid', $q);
    $this->db->or_like('prc_adminid', $q);
    $this->db->from($this->table_viewmaster);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil owner master ingridient 
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
    $this->db->or_like('ingridient_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('product_type_fkid', $q);
    $this->db->or_like('ingridient_category_fkid', $q);
    $this->db->or_like('ingridient_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('photo', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->or_like('product_type_name', $q);
    $this->db->or_like('ingridient_category_name', $q);
    $this->db->or_like('ingridient_subcategory_name', $q);
    $this->db->or_like('prc_name', $q);
    $this->db->or_like('unit_name', $q);
    $this->db->or_like('ingridient_category_adminid', $q);
    $this->db->or_like('ingridient_subcategory_adminid', $q);
    $this->db->or_like('prc_adminid', $q);
    $this->db->limit($limit, $start);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil owner master ingridient 
        return $this->db->get($this->table_viewmaster)->result();
    }


    // insert data
    public function insert_master($data)
    {
        $data['ingridient_id'] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //owner id
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        $insert = $this->db->insert($this->table_master, $data);
        $get_primarykey = $this->db->insert_id(); // ambil primary key
        return $return = array(
            'status_insert' => $insert, 
            'primary_key' => $get_primarykey
            );
    }
    public function insert_masterdetail($data)
    {
        $data['ingridient_detail_id'] = null;
        $data['data_modified'] = date('Y-m-d H:i:s');
        $data['data_status'] = 'on';
        $insert = $this->db->insert('ingridients_detail', $data);
        $get_primarykey = $this->db->insert_id(); // ambil primary key
        return $return = array(
            'status_insert' => $insert, 
            'primary_key' => $get_primarykey
            );
    }

    // get data
    public function get_masterdetail($master_id) //ambil menu tersedia di outlet mana saja
    {
        $this->db->where('ingridient_fkid', $master_id);
        $this->db->where('ingridient_detail_data_status', 'on');
        $this->db->order_by('outlet_name', 'asc');
        return $this->db->get($this->table_viewmaster_detail)->result();
    }
    public function get_masterdetail_on_outlet($master_id, $outlet_id)
    {
        $this->db->where('ingridient_fkid', $master_id);
        $this->db->where('outlet_fkid', $outlet_id);
        $get = $this->db->get($this->table_masterdetail);
        if ($get->num_rows()==1) {
            $return = array(
                'status'=> true,
                'count_data' => $get->num_rows(),
                'data' => $get->row()
            );
        }
        elseif ($get->num_rows>1) {
            $return = array(
                'status' => false,
                'count_data' => $get->num_rows(),
                'message' => 'Duplicate Data'
            );
        }
        else{
            $return = array(
                'status' => false,
                'count_data' => $get->num_rows(),
                'message' => 'Record Not Found'
            );
        }

        return $return;
    }

    // update data
    public function update_master($master_id, $data)
    {
        $object = array(
            'name' => $data['name'],
            'product_type_fkid' => $data['product_type_fkid'],
            'ingridient_category_fkid' => $data['ingridient_category_fkid'],
            'ingridient_subcategory_fkid' => $data['ingridient_subcategory_fkid'],
            'purchase_report_category_fkid' => $data['purchase_report_category_fkid'],
            'unit_fkid' => $data['unit_fkid'],
            'barcode' => $data['barcode'],
            'sku' => $data['sku']
        );
        if (!empty($data['photo'])) {
            $object['photo'] = $data['photo'];
        }


        $this->db->where('ingridient_id', $master_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //owner id
        return $this->db->update($this->table_master, $object);
    }
    public function update_masterdetail($masterdetail_id, $data)
    {
        $object = array(
            'price_update' => $data['price_update'],
            'price_sell' => $data['price_sell'],
            'voucher' => $data['voucher'],
            'discount' => $data['discount'],
            'active' => $data['menu_active'],
            'data_modified' => date('Y-m-d H:i:s')
            );
        $this->db->where('ingridient_detail_id', $masterdetail_id);
        $this->db->where('data_status', 'on');
        return $this->db->update($this->table_masterdetail, $object);
    }
    public function update_masterdetail_by_masterid($master_id, $data)
    {
        $this->db->where('ingridient_fkid', $master_id);
        return $this->db->update($this->table_masterdetail, $data);
    }
    public function update_masterdetail_datastatus_by_outlet($outlet_id, $data_status_value)
    {
        $object = array(
            'data_status' => $data_status_value
        );

        $this->db->where('outlet_fkid', $outlet_id);
        return $this->db->update($this->table_masterdetail, $object);
    }



    /* TAX GRATUITY START */
    // function dipindah ke Ingridients_catalogue_taxgratuity_model
    /* TAX GRATUITY END */


    /* MULTIPLE PRICE START */
    // function dipindah ke Ingridients_catalogue_multipleprice_model
    /* MULTIPLE PRICE END */


    /* LINK MENU START */
    // function dipindah ke Ingridients_catalogue_link_model
    /* LINK MENU END */




    public function json_by_outlet($outlet_id)
    {
        $this->datatables->select('
            ingridients_catalogue.ingridients_catalogue_id,
            ingridients_catalogue.name,
            ingridients_catalogue.outlet_fkid,
            ingridients_catalogue.products_type_fkid,
            ingridients_catalogue.ingridients_category_fkid,
            ingridients_catalogue.ingridients_subcategory_fkid,
            ingridients_catalogue.purchase_report_category_fkid,
            ingridients_catalogue.price,
            ingridients_catalogue.price_update,
            ingridients_catalogue.price_sell,
            ingridients_catalogue.unit_fkid,
            ingridients_catalogue.photo,
            ingridients_catalogue.data_created,
            ingridients_catalogue.data_status,

            outlets.name AS outlet_name,
            outlets.admin_fkid,
            products_type.name AS product_type_name,
            ingridients_category.name AS ingridient_category_name,
            ingridients_subcategory.name AS ingridient_subcategory_name,
            purchase_report_category.name AS prc_name,
            unit.name AS unit_name
            
            ');
        //add this line for join
        //$this->datatables->join('table2', 'ingridients_catalogue.field = table2.field');
        $this->datatables->from('ingridients_catalogue');
        $this->datatables->join('outlets', 'outlets.outlet_id = ingridients_catalogue.outlet_fkid', 'left');
        $this->datatables->join('products_type', 'ingridients_catalogue.products_type_fkid = products_type.products_type_id', 'left');
        $this->datatables->join('ingridients_category', 'ingridients_catalogue.ingridients_category_fkid = ingridients_category.ingridients_category_id', 'left');
        $this->datatables->join('ingridients_subcategory', 'ingridients_catalogue.ingridients_subcategory_fkid = ingridients_subcategory.ingridients_subcategory_id', 'left');
        $this->datatables->join('purchase_report_category', 'ingridients_catalogue.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->join('unit', 'ingridients_catalogue.unit_fkid = unit.unit_id', 'left');



        //$this->datatables->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.outlet_id', $outlet_id); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        $this->datatables->add_column('action', anchor(site_url('ingridients_catalogue/read/$1'),'Read')." | ".anchor(site_url('ingridients_catalogue/update/$1'),'Update')." | ".anchor(site_url('ingridients_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'ingridients_catalogue_id');
        return $this->datatables->generate();
    }

    // datatables
    function json2() {
        $this->datatables->select('
            ingridients_catalogue.ingridients_catalogue_id,
            ingridients_catalogue.name,
            ingridients_catalogue.outlet_fkid,
            ingridients_catalogue.products_type_fkid,
            ingridients_catalogue.ingridients_category_fkid,
            ingridients_catalogue.ingridients_subcategory_fkid,
            ingridients_catalogue.purchase_report_category_fkid,
            ingridients_catalogue.price,
            ingridients_catalogue.price_update,
            ingridients_catalogue.price_sell,
            ingridients_catalogue.unit_fkid,
            ingridients_catalogue.photo,
            ingridients_catalogue.data_created,
            ingridients_catalogue.data_status,

            outlets.name AS outlet_name,
            outlets.admin_fkid,
            products_type.name AS product_type_name,
            ingridients_category.name AS ingridient_category_name,
            ingridients_subcategory.name AS ingridient_subcategory_name,
            purchase_report_category.name AS prc_name,
            unit.name AS unit_name
            
            ');
        //add this line for join
        //$this->datatables->join('table2', 'ingridients_catalogue.field = table2.field');
        $this->datatables->from('ingridients_catalogue');
        $this->datatables->join('outlets', 'outlets.outlet_id = ingridients_catalogue.outlet_fkid', 'left');
        $this->datatables->join('products_type', 'ingridients_catalogue.products_type_fkid = products_type.products_type_id', 'left');
        $this->datatables->join('ingridients_category', 'ingridients_catalogue.ingridients_category_fkid = ingridients_category.ingridients_category_id', 'left');
        $this->datatables->join('ingridients_subcategory', 'ingridients_catalogue.ingridients_subcategory_fkid = ingridients_subcategory.ingridients_subcategory_id', 'left');
        $this->datatables->join('purchase_report_category', 'ingridients_catalogue.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->join('unit', 'ingridients_catalogue.unit_fkid = unit.unit_id', 'left');


        //$this->datatables->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        $this->datatables->add_column('action', anchor(site_url('ingridients_catalogue/read/$1'),'Read')." | ".anchor(site_url('ingridients_catalogue/update/$1'),'Update')." | ".anchor(site_url('ingridients_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'ingridients_catalogue_id');
        return $this->datatables->generate();
    }

    function jsonSpoil($outlet_id) {
        $this->datatables->select('
            ingridients_catalogue.ingridients_catalogue_id,
            ingridients_catalogue.name,
            ingridients_catalogue.price,
            ingridients_catalogue.price_update,
            ingridients_catalogue.price_sell,
            unit.name AS unit_name,
            ingridients_catalogue.data_status,
            ingridients_catalogue.data_created

            
            ');
        //add this line for join
        $this->datatables->from('view_ingridients');
        
        $this->datatables->join('outlets', 'ingridients_catalogue.outlet_fkid = outlets.outlet_id');
        $this->datatables->join('unit', 'ingridients_catalogue.unit_fkid = unit.unit_id');
        
        //$this->datatables->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan 
        $this->datatables->where('ingridients_catalogue.outlet_fkid', $outlet_id); //ambil 
        $this->datatables->add_column('action', anchor(site_url('ingridients_catalogue/read/$1'),'Read')." | ".anchor(site_url('ingridients_catalogue/update/$1'),'Update')." | ".anchor(site_url('ingridients_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'ingridients_catalogue_id');
        return $this->datatables->generate();
    }

    


    

    /* custom cek data */
    function _cekDataID($id)
    {
        //cek apakah data merupakan milik yang login
        $this->db->select('ingridients_catalogue.ingridients_catalogue_id,
                            ingridients_catalogue.outlet_fkid,
                            outlets.admin_fkid');
        $this->db->from('ingridients_catalogue');
        $this->db->join('outlets', 'ingridients_catalogue.outlet_fkid = outlets.outlet_id', 'left');
        $this->db->where('ingridients_catalogue.ingridients_catalogue_id', $id);
        $this->db->where('outlets.admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        //$this->db->where('ingridients_catalogue.data_status', 'on'); //cari hanya data aktif
        return $cekData = $this->db->get();
    }
    function _cekDataOutlet($id_outlet)
    {
        $this->db->where('outlet_id', $id_outlet);
        $this->db->where('admin_fkid', $this->session->userdata('user_id')); //ambil berdasarkan 
        //$this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get('outlets');
    }
    /* custom cek data end */
    

    // get data by id old version
    function get_by_id__old($id)
    {
        //inisialisasi
        $hasilCekData = $this->_cekDataID($id)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            //eksekusi ambil data
            $this->db->where($this->id, $id);
            //$this->db->where('data_status', 'on'); //cari hanya data aktif
            return $this->db->get($this->table)->row();
        }
    }

    
    


    // insert data
    function insert($data)
    {
        //inisialisasi
        $id_outlet = $data['outlet_fkid'];
        $hasilCekData = $this->_cekDataOutlet($id_outlet)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            $data[$this->id] = null;
            $data['price_update'] = $data['price'];
            //$data['data_status'] = 'on';
            $data['data_created'] = date('Y-m-d H:i:s');
            //return $this->db->insert($this->table, $data);
            $insert = $this->db->insert($this->table, $data);
            $get_primarykey = $this->db->insert_id(); // ambil primary key

            return $return = array(
                'status_insert' => $insert, 
                'primary_key' => $get_primarykey
                );
        }
    }

    // update data
    function update($id, $data)
    {
        //inisialisasi
        $id_outlet = $data['outlet_fkid'];
        $hasilCekData = $this->_cekDataOutlet($id_outlet)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            $this->db->where($this->id, $id);
            //$data['price_update'] = $data['price'];
            //$this->db->where('data_status', 'on'); //cari hanya data aktif
            return $this->db->update($this->table, $data);
        }
    }

    // delete data
    function delete($id)
    {
        //inisialisasi
        $hasilCekData = $this->_cekDataID($id)->num_rows();
        $dataValid = ($hasilCekData==1) ? TRUE : FALSE;
        
        //ambil data hasil cek
        if ($dataValid===TRUE) {
            $this->db->where($this->id, $id);
            //$this->db->where('data_status', 'on'); //cari hanya data aktif
            return $this->db->delete($this->table);
        }
    }

    /* multiprice */
    /*function dipindahkan */

    
    /* TAX GRATUITY START */
    /* function dipindahkan */
    /* TAX GRATUITY END */


    /* custom form ingridient list for breakdown */
    function form_select_ingridient($outlet_id)
    {
        $this->db->where('outlet_fkid', $outlet_id);
        $this->db->where('data_status', 'on');
        $this->db->order_by('name', 'asc');
        return $this->db->get($this->table_masterdetail);
    }

    

}

/* End of file Ingridients_catalogue_model.php */
/* Location: ./application/models/Ingridients_catalogue_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-24 13:21:33 */
/* http://harviacode.com */