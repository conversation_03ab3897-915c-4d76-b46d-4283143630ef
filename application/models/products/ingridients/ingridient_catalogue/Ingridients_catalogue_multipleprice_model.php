<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ingridients_catalogue_multipleprice_model extends CI_Model {

    public $table = 'ingridients_detail_multipleprice';

    /* MULTIPLE PRICE START */
    //get multiple price
    public function getMultiplePrice($masterdetail_id)
    {
        $this->db->select('*');
        $this->db->from($this->table); //table
        $this->db->where('ingridient_detail_fkid', $masterdetail_id);
        $this->db->where('data_status', 'on');
        $this->db->order_by('qty', 'asc');
        return $this->db->get()->result();
    }
    public function get_multiprice_by_masterdetailid($masterdetail_id)
    {
        $this->db->where('ingridient_detail_fkid', $masterdetail_id);
        $data = $this->db->get($this->table);
        return $return = array(
            'data' => $data->result(),
            'num_rows' => $data->num_rows()
        );
    }

    public function insert_multiprice($data)
    {
        $data['multipleprice_id'] = null;
        $data['data_created'] = date('Y-m-d H:i:s');
        $data['data_status'] = 'on';
        $insertMultiprice = $this->db->insert($this->table, $data); //table
        //echo $this->db->last_query();
        return $insertMultiprice;
    }
    public function update_multiprice($multipleprice_id, $data)
    {
        $this->db->where('multipleprice_id', $multipleprice_id);
        return $result = $this->db->update($this->table, $data);
    }
    public function update_multiprice_datastatus_by_masterdetailid($masterdetail_id, $value)
    {
        $object = array('data_status' => $value);
        $this->db->where('ingridient_detail_fkid', $masterdetail_id);
        return $this->db->update($this->table, $object);
    }
    public function delete_multipleprice($masterdetail_id)
    {
        $this->db->where('ingridient_detail_fkid', $productdetail_id);
        return $this->db->delete($this->table);
    }
    /* MULTIPLE PRICE END */

}

/* End of file Ingridients_catalogue_multipleprice_model.php */
/* Location: ./application/models/products/ingridients/ingridient_catalogue/Ingridients_catalogue_multipleprice_model.php */