<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Ingridients_subcategory_model extends CI_Model
{

    public $table = 'ingridients_subcategory';
    public $id = 'ingridients_subcategory_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json() {
        $this->datatables->select('ingridients_subcategory.ingridients_subcategory_id, ingridients_subcategory.name, ingridients_subcategory.data_created, ingridients_subcategory.data_status, purchase_report_category.name AS prc_name');
        $this->datatables->from('purchase_report_category');
        //add this line for join
        //$this->datatables->join('table2', 'ingridients_subcategory.field = table2.field');
        $this->datatables->join('ingridients_subcategory', 'ingridients_subcategory.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->where('ingridients_subcategory.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('ingridients_subcategory.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner

        $this->datatables->add_column('action', anchor(site_url('ingridients_subcategory/read/$1'),'Read')." | ".anchor(site_url('ingridients_subcategory/update/$1'),'Update')." | ".anchor(site_url('ingridients_subcategory/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'ingridients_subcategory_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('ingridients_subcategory_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('purchase_report_category_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_status', $q);
	$this->db->from($this->table);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('ingridients_subcategory_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('purchase_report_category_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_status', $q);
	$this->db->limit($limit, $start);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        $data['admin_fkid'] = $this->session->userdata('user_id'); //ambil berdasarkan yang login
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->delete($this->table);
    }



    function form_select()
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->order_by('name', 'asc');
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

}

/* End of file Ingridients_subcategory_model.php */
/* Location: ./application/models/Ingridients_subcategory_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-24 07:25:55 */
/* http://harviacode.com */