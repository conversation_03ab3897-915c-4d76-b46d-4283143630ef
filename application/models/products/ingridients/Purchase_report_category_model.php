<?php
if (!defined('BASEPATH')) exit('No direct script access allowed');

class Purchase_report_category_model extends CI_Model
{

    public $table = 'purchase_report_category';
    public $id = 'purchase_report_category_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json() {
        $this->datatables->select('
            purchase_report_category_id AS id,
            purchase_report_category_id,
            name,
            if(is_operationalcost=1, "Yes","No") AS operationalcost
        ');
        $this->datatables->from('purchase_report_category');
        $this->datatables->where('data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //add this line for join
        //$this->datatables->join('table2', 'purchase_report_category.field = table2.field');
        //$this->datatables->add_column('action', anchor(site_url('purchase_report_category/read/$1'),'Read')." | ".anchor(site_url('purchase_report_category/update/$1'),'Update')." | ".anchor(site_url('purchase_report_category/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'purchase_report_category_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }

    // get data by name
    function get_by_name($name)
    {
        $this->db->where('name', $name);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('purchase_report_category_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_status', $q);
	$this->db->from($this->table);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('purchase_report_category_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_status', $q);
	$this->db->limit($limit, $start);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //ambil berdasarkan owner
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->db_debug = FALSE;
        
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->delete($this->table);
    }


    //form select purchase report category
    function form_prc()
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->order_by('name', 'asc');
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get('purchase_report_category')->result();
    }

}

/* End of file Purchase_report_category_model.php */
/* Location: ./application/models/Purchase_report_category_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-23 07:53:24 */
/* http://harviacode.com */