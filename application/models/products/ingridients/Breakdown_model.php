<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Breakdown_model extends CI_Model
{

    public $table = 'breakdown';
    public $table_detail = 'breakdown_detail';
    public $id = 'breakdown_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json() {
        $this->datatables->select('breakdown_id,product_fkid,ingridient_fkid,qty,hpp,data_created,data_modified');
        $this->datatables->from('breakdown');
        //add this line for join
        //$this->datatables->join('table2', 'breakdown.field = table2.field');
        $this->datatables->add_column('action', anchor(site_url('breakdown/read/$1'),'Read')." | ".anchor(site_url('breakdown/update/$1'),'Update')." | ".anchor(site_url('breakdown/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'breakdown_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        //ingridients_catalogue.price_update,
        $this->db->select('breakdown.*,
            ingridients.name AS ingridient_name,');
        $this->db->from('breakdown');
        $this->db->join('ingridients', 'breakdown.ingridient_fkid = ingridients.ingridient_id', 'left');
        $this->db->where($this->id, $id);
        return $this->db->get()->row();
    }

    function get_by_product_id($product_id)
    {
        $this->db->select('*');
        $this->db->from('breakdown');
        $this->db->where('product_fkid', $product_id);
        return $this->db->get()->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('breakdown_id', $q);
	$this->db->or_like('product_fkid', $q);
	$this->db->or_like('ingridient_fkid', $q);
	$this->db->or_like('qty', $q);
	$this->db->or_like('hpp', $q);
	$this->db->or_like('data_created', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('breakdown_id', $q);
	$this->db->or_like('product_fkid', $q);
	$this->db->or_like('ingridient_fkid', $q);
	$this->db->or_like('qty', $q);
	$this->db->or_like('hpp', $q);
	$this->db->or_like('data_created', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $object = array(
            $this->id => null,
            'product_fkid' => $data['product_fkid'],
            'data_created' => date('Y-m-d H:i:s'),
            'data_modified' => date('Y-m-d H:i:s')
        );
        return $this->db->insert($this->table, $object);
    }

    // insert data detail
    function insert_detail($data)
    {
        $object = array(
            'breakdowndetail_id' => null,
            'breakdown_fkid' => $data['breakdown_id'],
            'outlet_fkid' => $data['outlet_id'],
            'product_fkid' => $data['product_id'],
            'qty' => $data['qty'],
            'data_created' => date('Y-m-d H:i:s'),
            'data_modified' => date('Y-m-d H:i:s')
        );
        return $this->db->insert($this->table_detail, $object);
    }

    // delete
    function delete_detail_by_breakdown_outlet($breakdown_id, $outlet_id)
    {
        $this->db->where('breakdown_fkid', $breakdown_id);
        $this->db->where('outlet_fkid', $outlet_id);
        return $this->db->delete($this->table_detail);
    }


    /* custom */
    function get_breakdowndetail($breakdowndetail_id)
    {
        $this->db->select('breakdowndetail_id,product_fkid,product_name,qty,price_buy,hpp,unit_name');
        $this->db->where('product_adminid', $this->session->userdata('admin_id'));
        $this->db->where('outlet_adminid', $this->session->userdata('admin_id'));
        $this->db->where('breakdowndetail_id', $breakdowndetail_id);
        return $this->db->get('view_breakdown_detail')->row();
    }
    function update_breakdowndetail($breakdowndetail_id, $data)
    {
        $data['data_modified'] = date('Y-m-d H:i:s');

        $this->db->where('breakdowndetail_id', $breakdowndetail_id);
        return $this->db->update('breakdown_detail', $data);
    }
    function delete_breakdowndetail($breakdowndetail_id)
    {
        //cek apakah data ada
        $this->db->select('breakdowndetail_id');
        $this->db->where('breakdowndetail_id', $breakdowndetail_id);
        $this->db->where('product_adminid', $this->session->userdata('admin_id'));
        $this->db->where('outlet_adminid', $this->session->userdata('admin_id'));
        $result_cek = $this->db->get('view_breakdown_detail');

        if ($result_cek) {
            $this->db->where('breakdowndetail_id', $breakdowndetail_id);
            return $this->db->delete($this->table_detail);
        }
        else{
            return false;
        }
    }
}

/* End of file Breakdown_model.php */
/* Location: ./application/models/Breakdown_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-08-08 03:24:48 */
/* http://harviacode.com */