<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Gratuity_model extends CI_Model
{

    public $table = 'gratuity';
    public $id = 'gratuity_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json() {
        $this->datatables->select('gratuity_id,name,tax_category,tax_status,tax_type,jumlah,data_created,data_modified,data_status');
        $this->datatables->from('gratuity');
        $this->datatables->where('data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //add this line for join
        //$this->datatables->join('table2', 'unit.field = table2.field');
        $this->datatables->add_column('action', anchor(site_url('gratuity/read/$1'),'Read')." | ".anchor(site_url('gratuity/update/$1'),'Update')." | ".anchor(site_url('gratuity/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'gratuity_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('gratuity_id', $q);
    	$this->db->or_like('name', $q);
        $this->db->or_like('tax_category', $q);
        $this->db->or_like('tax_status', $q);
    	$this->db->or_like('tax_type', $q);
        $this->db->or_like('jumlah',$q);
    	$this->db->or_like('data_created', $q);
        $this->db->or_like('data_modified', $q);
    	$this->db->or_like('data_status', $q);
    	$this->db->from($this->table);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('gratuity_id', $q);
    	$this->db->or_like('name', $q);
        $this->db->or_like('tax_category', $q);
        $this->db->or_like('tax_status', $q);
    	$this->db->or_like('tax_type', $q);
        $this->db->or_like('jumlah', $q);
    	$this->db->or_like('data_created', $q);
        $this->db->or_like('data_modified', $q);
    	$this->db->or_like('data_status', $q);
    	$this->db->limit($limit, $start);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //ambil berdasarkan owner
        $data['data_status'] = 'on';
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $data['data_modified'] = current_millis();
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->db_debug = FALSE;
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->delete($this->table);
    }



    function form_select()
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->order_by('name', 'asc');
        return $this->db->get($this->table)->result();
    }

}

/* End of file Unit_model.php */
/* Location: ./application/models/Unit_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-24 08:13:51 */
/* http://harviacode.com */