<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Operationalcost_equipment_catalogue_model extends CI_Model
{

    public $table = 'equipment_catalogue';
    public $id = '  equipment_catalogue_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

      public function json_by_outlet($outlet_id)
    {
        $this->datatables->select('
            equipment_catalogue.equipment_catalogue_id,
            equipment_catalogue.name,
            equipment_catalogue.outlet_fkid,
            equipment_catalogue.products_type_fkid,
            equipment_catalogue.opcost_category_fkid,
            equipment_catalogue.opcost_subcategory_fkid,
            equipment_catalogue.purchase_report_category_fkid,
            equipment_catalogue.price,
            equipment_catalogue.price_update,
            equipment_catalogue.unit_fkid,
            equipment_catalogue.data_created,
            equipment_catalogue.data_status,

            outlets.name AS outlet_name, 
            outlets.admin_fkid,
            products_type.name AS products_type_name,
            operationalcost_category.name AS opcost_category_name,
            operationalcost_subcategory.name AS opcost_subcategory_name,
            purchase_report_category.name AS prc_name,
            unit.name AS unit_name
            
            ');
        $this->datatables->from('equipment_catalogue');
        $this->datatables->join('outlets', 'outlets.outlet_id = equipment_catalogue.outlet_fkid', 'left');
        $this->datatables->join('products_type', 'equipment_catalogue.products_type_fkid = products_type.products_type_id', 'left');
        $this->datatables->join('operationalcost_category', 'equipment_catalogue.opcost_category_fkid = operationalcost_category.opcost_category_id', 'left');
        $this->datatables->join('operationalcost_subcategory', 'equipment_catalogue.opcost_subcategory_fkid = operationalcost_subcategory.opcost_subcategory_id', 'left');
        $this->datatables->join('purchase_report_category', 'equipment_catalogue.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->join('unit', 'equipment_catalogue.unit_fkid = unit.unit_id', 'left');


        $this->datatables->where('equipment_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->datatables->add_column('action', anchor(site_url('equipment_catalogue/read/$1'),'Read')." | ".anchor(site_url('equipment_catalogue/update/$1'),'Update')." | ".anchor(site_url('equipment_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'equipment_catalogue_id');
        return $this->datatables->generate();
    }

    // datatables
    function json() {
        $this->datatables->select('
            equipment_catalogue.equipment_catalogue_id,
            equipment_catalogue.name,
            equipment_catalogue.outlet_fkid,
            equipment_catalogue.products_type_fkid,
            equipment_catalogue.opcost_category_fkid,
            equipment_catalogue.opcost_subcategory_fkid,
            equipment_catalogue.purchase_report_category_fkid,
            equipment_catalogue.price,
            equipment_catalogue.price_update,
            equipment_catalogue.unit_fkid,
            equipment_catalogue.data_created,
            equipment_catalogue.data_status,

            outlets.name AS outlet_name, 
            outlets.admin_fkid,
            products_type.name AS products_type_name,
            operationalcost_category.name AS opcost_category_name,
            operationalcost_subcategory.name AS opcost_subcategory_name,
            purchase_report_category.name AS prc_name,
            unit.name AS unit_name
            
            ');
        //add this line for join
        //$this->datatables->join('table2', 'equipment_catalogue.field = table2.field');
        $this->datatables->from('equipment_catalogue');
        $this->datatables->join('outlets', 'outlets.outlet_id = equipment_catalogue.outlet_fkid', 'left');
        $this->datatables->join('products_type', 'equipment_catalogue.products_type_fkid = products_type.products_type_id', 'left');
        $this->datatables->join('operationalcost_category', 'equipment_catalogue.opcost_category_fkid = operationalcost_category.opcost_category_id', 'left');
        $this->datatables->join('operationalcost_subcategory', 'equipment_catalogue.opcost_subcategory_fkid = operationalcost_subcategory.opcost_subcategory_id', 'left');
        $this->datatables->join('purchase_report_category', 'equipment_catalogue.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->join('unit', 'equipment_catalogue.unit_fkid = unit.unit_id', 'left');


        $this->datatables->where('equipment_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->datatables->add_column('action', anchor(site_url('equipment_catalogue/read/$1'),'Read')." | ".anchor(site_url('equipment_catalogue/update/$1'),'Update')." | ".anchor(site_url('equipment_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'equipment_catalogue_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('equipment_catalogue_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('products_type_fkid', $q);
    $this->db->or_like('ingridients_category_fkid', $q);
    $this->db->or_like('ingridients_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->from($this->table);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('equipment_catalogue_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('products_type_fkid', $q);
    $this->db->or_like('ingridients_category_fkid', $q);
    $this->db->or_like('ingridients_subcategory_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->limit($limit, $start);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['price_update'] = $data['price'];
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        //$data['price_update'] = $data['price'];
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->delete($this->table);
    }

}

/* End of file equipment_catalogue_model.php */
/* Location: ./application/models/equipment_catalogue_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-24 13:21:33 */
/* http://harviacode.com */