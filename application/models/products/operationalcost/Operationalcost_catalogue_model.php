<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Operationalcost_catalogue_model extends CI_Model {

    public $table = 'operationalcost_catalogue';
        public $id = '  operationalcost_catalogue_id';
        public $order = 'DESC';

    public function __construct()
    {
        parent::__construct();
        //Do your magic here
    }

    // datatables
    function json() {
        $this->datatables->select('
            operationalcost_catalogue.operationalcost_catalogue_id,
            operationalcost_catalogue.name,
            operationalcost_catalogue.outlet_fkid,
            operationalcost_catalogue.purchase_report_category_fkid,
            operationalcost_catalogue.price,
            operationalcost_catalogue.unit_fkid,
            operationalcost_catalogue.data_created,
            operationalcost_catalogue.data_status,

            outlets.name AS outlet_name,
            outlets.admin_fkid,
            purchase_report_category.name AS prc_name,
            unit.name AS unit_name
            
            ');
        //add this line for join
        //$this->datatables->join('table2', 'operationalcost_catalogue.field = table2.field');
        $this->datatables->from('operationalcost_catalogue');
        $this->datatables->join('outlets', 'outlets.outlet_id = operationalcost_catalogue.outlet_fkid');
        $this->datatables->join('purchase_report_category', 'operationalcost_catalogue.purchase_report_category_fkid = purchase_report_category.purchase_report_category_id', 'left');
        $this->datatables->join('unit', 'operationalcost_catalogue.unit_fkid = unit.unit_id', 'left');


        $this->datatables->where('operationalcost_catalogue.data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->datatables->add_column('action', anchor(site_url('operationalcost_catalogue/read/$1'),'Read')." | ".anchor(site_url('operationalcost_catalogue/update/$1'),'Update')." | ".anchor(site_url('operationalcost_catalogue/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'operationalcost_catalogue_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('operationalcost_catalogue_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->from($this->table);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('operationalcost_catalogue_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('outlet_fkid', $q);;
    $this->db->or_like('purchase_report_category_fkid', $q);
    $this->db->or_like('price', $q);
    $this->db->or_like('unit_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_status', $q);
    $this->db->limit($limit, $start);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        //$data['price_update'] = $data['price'];
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        return $this->db->delete($this->table);
    }

}

/* End of file Operationalcost_catalogue_model.php */
/* Location: ./application/models/products/operationalcost/Operationalcost_catalogue_model.php */