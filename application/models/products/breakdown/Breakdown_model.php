<?php
/* NEW BREAKDOWN MODEL */
defined('BASEPATH') OR exit('No direct script access allowed');

class Breakdown_model extends CI_Model {

	public $table = 'breakdown';
	public $id = 'breakdown_id';

	//get data by breakdown_id
	function get_by_id($breakdown_id)
	{
		$this->db->select('
			b.breakdown_id,
			b.product_fkid AS product_id,
			b.outlet_fkid AS outlet_id,
			b.item_product_fkid AS item_product_id,
			b.item_product_detail_fkid AS item_product_detail_id,
			b.qty,
			pi.name AS item_product_name,
			u.name AS unit_name,
			pdi.price_buy,
			(b.qty * pdi.price_buy) AS hpp,

			pi.admin_fkid
		');
		$this->db->from($this->table.' b');
		$this->db->join('products p', 'b.product_fkid = p.product_id', 'left');
		$this->db->join('products pi', 'b.item_product_fkid = pi.product_id', 'left');
		$this->db->join('products_detail pdi', 'b.item_product_detail_fkid = pdi.product_detail_id', 'left');
		$this->db->join('unit u', 'pi.unit_fkid = u.unit_id', 'left');
		$this->db->where($this->id, $breakdown_id);
		$this->db->where('pi.admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get()->row();
	}

	//get breakdown by product_id and outlet_id
	function get_by_product_and_outlet($product_id, $outlet_id)
	{
		$this->db->select('
			b.breakdown_id,
			b.product_fkid AS product_id,
			b.outlet_fkid AS outlet_id,
			b.item_product_fkid AS item_product_id,
			b.item_product_detail_fkid AS item_product_detail_id,
			b.qty,
			pi.name AS item_product_name,
			u.name AS unit_name,
			pdi.price_buy,
			(b.qty * pdi.price_buy) AS hpp,
			o.admin_fkid
		');
		$this->db->from($this->table.' b');
		$this->db->join('products p', 'b.product_fkid = p.product_id', 'left');
		$this->db->join('products pi', 'b.item_product_fkid = pi.product_id', 'left');
		$this->db->join('products_detail pdi', 'b.item_product_detail_fkid = pdi.product_detail_id', 'left');
		$this->db->join('unit u', 'pi.unit_fkid = u.unit_id', 'left');
		$this->db->join('outlets o', 'pdi.outlet_fkid = o.outlet_id', 'left');
		$this->db->where('b.product_fkid', $product_id);
		$this->db->where('b.outlet_fkid', $outlet_id);
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->order_by('item_product_name', 'asc');
		return $this->db->get()->result();
	}

	function get_by_productdetail($product_detail_id) //new breakdown
	{
		$this->db->select('
			b.breakdown_id,
			b.item_product_detail_fkid AS item_product_detail_id,
			ip.name AS item_product_name,
			ipv.variant_name AS item_variant_name,
			u.name AS unit_name,
			b.qty,
			ipd.price_buy,
			(ipd.price_buy * b.qty) AS hpp,
			ip.admin_fkid
		');
		$this->db->from($this->table.' b');
		$this->db->join('products_detail ipd', 'b.item_product_detail_fkid = ipd.product_detail_id', 'left');
		$this->db->join('products_detail_variant ipv', 'ipd.variant_fkid = ipv.variant_id', 'left');
		$this->db->join('products ip', 'ipd.product_fkid = ip.product_id', 'left');
		$this->db->join('unit u', 'ip.unit_fkid = u.unit_id', 'left');
		$this->db->where('b.product_detail_fkid', $product_detail_id);
		$this->db->where('ip.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('ip.data_status', 'on');
		$this->db->where('ipd.data_status', 'on');
		$this->db->order_by('item_product_name', 'asc');
		return $this->db->get()->result();
	}

	public function get_by_outlet($outlet_id) //new breakdown
	{
		$this->db->select('
			b.product_detail_fkid AS product_detail_id,
			pd.outlet_fkid AS outlet_id,
			pd.product_fkid AS product_id,
			pd.variant_fkid AS variant_id,
			p.name AS product_name,
			pdv.variant_name,
			qty
		');
		$this->db->from('breakdown b');
		$this->db->join('products_detail pd', 'b.product_detail_fkid = pd.product_detail_id', 'left');
		$this->db->join('products p', 'pd.product_fkid = p.product_id', 'left');
		$this->db->join('products_detail_variant pdv', 'pd.variant_fkid = pdv.variant_id', 'left');


	}

	function insert($data)
	{
		$data['data_created'] = current_millis();
		$data['data_modified'] = current_millis();
		return $this->db->insert($this->table, $data);
	}

	function insertbatch($data)
	{
		return $this->db->insert_batch($this->table, $data);
	}

	function update($breakdown_id, $data)
	{
		$data['data_modified'] = current_millis();
		
		$this->db->where($this->id, $breakdown_id);
		return $this->db->update($this->table, $data);
	}

	function delete_by_id($breakdown_id)
	{
		$this->db->where($this->id, $breakdown_id);
		return $this->db->delete($this->table);
	}

	function delete_by_product_and_outlet($product_id, $outlet_id)
	{
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->delete($this->table);
	}

	function delete_by_product_outlet_productdetail($product_id, $outlet_id, $product_detail_id)
	{
		$this->db->where('product_detail_fkid', $product_detail_id);
		$this->db->where('product_fkid', $product_id);
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->delete($this->table);
	}

}

/* End of file Breakdown_model.php */
/* Location: ./application/models/products/breakdown/Breakdown_model.php */