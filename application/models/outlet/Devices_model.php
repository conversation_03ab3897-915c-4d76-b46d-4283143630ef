<?php
if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Devices_model extends CI_Model
{

    public $table = 'devices';
    public $id = 'device_id';
    public $order = 'DESC';
    public $subscribe_device = 'view_subscribtion';

    // datatables
    function json() {
        $this->datatables->select('
            device_id,
            devices.name AS device_name,
            imei,
            outlet_fkid,
            outlets.name AS outlet_name,
            devices.admin_fkid,
            devices.data_created,
            devices.data_modified,
            devices.data_status');
        $this->datatables->from('devices');
        //add this line for join
        $this->datatables->join('outlets', 'devices.outlet_fkid = outlets.outlet_id', 'left');
        $this->datatables->join('admin', 'devices.admin_fkid = admin.admin_id', 'left');
        //$this->datatables->join('table2', 'devices.field = table2.field');
        $this->datatables->where('devices.admin_fkid', $this->session->userdata('admin_id'));
        $this->datatables->where('outlets.admin_fkid', $this->session->userdata('admin_id'));

        //action
        //$this->datatables->add_column('action', anchor(site_url('devices/read/$1'),'Read')." | ".anchor(site_url('devices/update/$1'),'Update')." | ".anchor(site_url('devices/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'device_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('device_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('imei', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_modified', $q);
    $this->db->or_like('data_status', $q);
    $this->db->from($this->table);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('device_id', $q);
    $this->db->or_like('name', $q);
    $this->db->or_like('imei', $q);
    $this->db->or_like('outlet_fkid', $q);
    $this->db->or_like('admin_fkid', $q);
    $this->db->or_like('data_created', $q);
    $this->db->or_like('data_modified', $q);
    $this->db->or_like('data_status', $q);
    $this->db->limit($limit, $start);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->get($this->table)->result();
    }

    // insert data
    // update data
    function update($id, $data)
    {
        $data['data_modified'] = current_millis();
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->delete($this->table);
    }



    function form_select()
    {
        //$this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->order_by('name', 'asc');
        return $this->db->get($this->table)->result();
    }

    function get_count_subscribe_device()
    {
        // $this->db->select('sum(qty) AS slot');
        // $this->db->from($this->subscribe_device);
        // $this->db->where('billing_status', 'success');
        // $this->db->where('service_feature', 'device');
        // $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        // $this->db->where('service_time_start<=', current_millis());
        // $this->db->where('service_time_expired>=', current_millis());
        // return $this->db->get()->row();
        return array();
    }

    function get_active_device()
    {
        $this->db->select('d.*, o.name AS outlet_name');
        $this->db->from($this->table.' d');
        $this->db->join('outlets o', 'd.outlet_fkid = o.outlet_id');
        $this->db->where(array(
            'd.admin_fkid' => $this->session->userdata('admin_id'),
            'd.device_status' => 'on'
        ));
        return $this->db->get()->result();
    }

}

/* End of file Devices_model.php */
/* Location: ./application/models/Devices_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-11-21 12:12:25 */
/* http://harviacode.com */