<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Printer_closingshift_model extends CI_Model {

	public $table = 'setting_printer_closingshift';
	public $id = 'closingshift_id';

	function get_all($printer_setting_id)
	{
		$this->db->where('printer_setting_fkid', $printer_setting_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->get($this->table)->result();
	}

	public function get_by_id($closingshift_id)
	{
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where($this->id, $closingshift_id);
		return $this->db->get($this->table)->row();
	}

	public function delete($closingshift_id)
	{
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where($this->id, $closingshift_id);
		return $this->db->delete($this->table);
	}

	public function delete_by_printerid($printer_id)
	{
		$this->db->where('printer_setting_fkid', $printer_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->delete($this->table);
	}

	public function update($closingshift_id, $data)
	{
		$data['data_modified'] = current_millis();
		$this->db->where('closingshift_id', $closingshift_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $data);
	}

	public function insert($data)
	{
		$data['closingshift_id'] = null;
		$data['admin_fkid'] = $this->session->userdata('admin_id');
		$data['data_modified'] = current_millis();
		return $this->db->insert($this->table, $data);
	}

}

/* End of file Printer_closingshift_model.php */
/* Location: ./application/models/outlet/printer/Printer_closingshift_model.php */