<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Printer_model extends CI_Model
{

    public $table = 'view_printer';
    public $table_ori = 'setting_printer';
    public $id = 'printer_setting_id';
    public $order = 'DESC';

    //datatables (new)
    public function datatables()
    {
        $this->datatables->select('
            p.printer_setting_id,
            p.printer_name,
            p.mac_address,
            o.name AS outlet_name
        ');
        $this->datatables->from($this->table_ori.' p');
        $this->datatables->join('outlets o', 'p.outlet_fkid = o.outlet_id', 'left');
        $this->datatables->where('p.admin_fkid', $this->session->userdata('admin_id'));
        $this->datatables->where('o.admin_fkid', $this->session->userdata('admin_id'));
        if (!empty($this->input->post('advancefilter_outlet'))) {
            $this->datatables->where('p.outlet_fkid', $this->input->post('advancefilter_outlet'));
        }
        return $this->datatables->generate();
    }

    // datatables
    function json() {
        $this->datatables->select('
            printer_setting_id,
            printer_name,
            mac_address,
            setting_closingshift,
            outlet_fkid,
            admin_fkid,
            date_created,
            outlet_name');
        $this->datatables->from($this->table);
        //add this line for join
        //$this->datatables->join('table2', 'view_printer.field = table2.field');
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id'));
        //$this->datatables->add_column('action', anchor(site_url('printer/read/$1'),'Read')." | ".anchor(site_url('printer/update/$1'),'Update')." | ".anchor(site_url('printer/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), '');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('p.admin_fkid', $this->session->userdata('admin_id'));
        // return $this->db->get($this->table.' p')->row();

        $this->db->select('p.*, o.name AS outlet_name');
        $this->db->from($this->table_ori.' p');
        $this->db->join('outlets o', 'o.outlet_id = p.outlet_fkid', 'left');
        return $this->db->get()->row();

    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('', $q);
        $this->db->or_like('printer_setting_id', $q);
        $this->db->or_like('printer_name', $q);
        $this->db->or_like('mac_address', $q);
        $this->db->or_like('setting_closingshift', $q);
        $this->db->or_like('setting_closingshift_rules', $q);
        $this->db->or_like('outlet_fkid', $q);
        $this->db->or_like('admin_fkid', $q);
        $this->db->or_like('date_created', $q);
        $this->db->or_like('outlet_name', $q);
        $this->db->from($this->table);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('', $q);
        $this->db->or_like('printer_setting_id', $q);
        $this->db->or_like('printer_name', $q);
        $this->db->or_like('mac_address', $q);
        $this->db->or_like('setting_closingshift', $q);
        $this->db->or_like('setting_closingshift_rules', $q);
        $this->db->or_like('outlet_fkid', $q);
        $this->db->or_like('admin_fkid', $q);
        $this->db->or_like('date_created', $q);
        $this->db->or_like('outlet_name', $q);
        $this->db->limit($limit, $start);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get($this->table)->result();
    }

    // insert data
    // function insert($data)
    // {
    //     $this->db->insert($this->table, $data);
    // }

    // update data
    function update($id, $data)
    {
        $data['time_modified'] = current_millis();
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where($this->id, $id);
        return $this->db->update($this->table_ori, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where($this->id, $id);
        return $this->db->delete($this->table_ori);
    }




    //device
    public function get_setting_device($printer_setting_id)
    {
        $this->db->where('printer_setting_fkid', $printer_setting_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get('setting_printer_device')->result();
    }

    public function delete_setting_device_by_printer($printer_setting_id)
    {
        $this->db->where('printer_setting_fkid', $printer_setting_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        $this->db->order_by('name', 'asc');
        return $this->db->delete('setting_printer_device');
    }
    public function insert_setting_device($data)
    {
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['data_modified'] = current_millis();
        return $this->db->insert('setting_printer_device', $data);
    }
    public function insertbatch_setting_device($data)
    {
        //remake data
        $data_new = array();
        foreach ($data as $value) {
            $data_new[] = array_merge($value, array(
                'admin_fkid' => $this->session->userdata('admin_id'),
                'data_modified' => current_millis(),
            ));
        }
        return $this->db->insert_batch('setting_printer_device', $data_new);
    }



    //print ticket kategori product
    public function get_print_ticket($printer_setting_id, $type)
    {
        $this->db->where('setting_type', $type);
        $this->db->where('printer_setting_fkid', $printer_setting_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get('setting_printer_ticket')->result();
    }

    public function get_print_ticket_by_id($ticket_id)
    {
        $this->db->where('printersetting_ticket_id', $ticket_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get('setting_printer_ticket')->row();
    }
    public function delete_print_ticket($ticket_id)
    {
        $this->db->where('printersetting_ticket_id', $ticket_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->delete('setting_printer_ticket');
    }
    public function delete_print_ticket_by_printerid($printer_id)
    {
        $this->db->where('printer_setting_fkid', $printer_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->delete('setting_printer_ticket');
    }

    public function insert_print_ticket($data)
    {
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        return $this->db->insert('setting_printer_ticket', $data);
    }

    public function insert_print_ticket_category($data)
    {
        $data['data_modified'] = current_millis();
        return $this->db->insert('setting_printer_ticket_category', $data);
    }

    //ticket detail
    public function get_ticket_category($ticket_id)
    {
        $this->db->where('printersetting_ticket_fkid', $ticket_id);
        return $this->db->get('setting_printer_ticket_category')->result();
    }


    /* TICKET NEW ON SUBCATEGORY */
    public function insert_print_ticket_subcategory($data)
    {
        $data['data_modified'] = current_millis();
        return $this->db->insert('setting_printer_ticket_subcategory', $data);
    }
    public function insertbatch_print_ticket_subcategory($data)
    {
        return $this->db->insert_batch('setting_printer_ticket_subcategory', $data);
    }
    public function get_ticket_subcategory($ticket_id)
    {
        $this->db->where('printersetting_ticket_fkid', $ticket_id);
        return $this->db->get('setting_printer_ticket_subcategory')->result();
    }
    

}

/* End of file Printer_model.php */
/* Location: ./application/models/Printer_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-11-24 23:15:42 */
/* http://harviacode.com */