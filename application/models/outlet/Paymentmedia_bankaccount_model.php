<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Paymentmedia_bankaccount_model extends CI_Model {

	public $table = 'payment_media_bank';
    public $id = 'bank_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json() {
        // admin_fkid,data_created,data_modified,data_status
        $this->datatables->select('bank_id,name,owner,no_rekening,provider');
        $this->datatables->from('payment_media_bank');
        //add this line for join
        //$this->datatables->join('table2', 'payment_media_bank.field = table2.field');
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        $this->datatables->where('data_status', 'on');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('bank_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('no_rekening', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->or_like('data_status', $q);
	$this->db->from($this->table);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('bank_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('no_rekening', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->or_like('data_status', $q);
	$this->db->limit($limit, $start);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
    	$data['bank_id'] = null;
    	$data['data_created'] = date('Y-m-d H:i:s');
        $data['data_modified'] = date('Y-m-d H:i:s');
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //ambil berdasarkan owner
        $data['data_status'] = 'on';
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
    	$data['data_modified'] = date('Y-m-d H:i:s');
    	$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        $this->db->where($this->id, $id);
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
    	$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        $this->db->where($this->id, $id);
        return $this->db->delete($this->table);
    }


    //active on outlet crud
    public function insert_activeoutlet($data)
    {
    	$object = array(
    		'bankdetail_id' => null,
    		'bank_fkid' => $data['bank_fkid'],
    		'outlet_fkid' => $data['outlet_fkid'],
            'active_on_pos' => $data['active_on_pos'],
            'active_on_crm' => $data['active_on_crm'],
    		'data_modified' => date('Y-m-d H:i:s'),
    		'data_status' => 'on'
    	);
    	return $this->db->insert('payment_media_bank_detail', $object);
    }

    public function update_disableoutlet($data)
    {
    	$object['data_modified'] = date('Y-m-d H:i:s');
    	$object['data_status'] = 'off';

    	$this->db->where('bank_fkid', $data['bank_fkid']);
    	$this->db->where('outlet_fkid', $data['outlet_fkid']);
    	return $this->db->update('payment_media_bank_detail', $object);
    }

    public function update_activeoutlet($data)
    {
        $object['active_on_pos'] = $data['active_on_pos'] ?? 0;
        $object['active_on_crm'] = $data['active_on_crm'] ?? 0;
    	$object['data_modified'] = date('Y-m-d H:i:s');
    	$object['data_status'] = 'on';

    	$this->db->where('bank_fkid', $data['bank_fkid']);
    	$this->db->where('outlet_fkid', $data['outlet_fkid']);
    	return $this->db->update('payment_media_bank_detail', $object);
    }

    public function cek_outlet($data)
    {
    	$this->db->where('bank_fkid', $data['bank_fkid']);
    	$this->db->where('outlet_fkid', $data['outlet_fkid']);
    	return $this->db->get('payment_media_bank_detail');
    }

    public function get_activeoutlet($bank_id)
    {
    	$this->db->where('bank_fkid', $bank_id);
    	$this->db->where('data_status', 'on');
    	return $this->db->get('payment_media_bank_detail');
    }

    function form_select()
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->order_by('name', 'asc');
        return $this->db->get($this->table)->result();
    }

}

/* End of file Paymentmedia_bankaccount_model.php */
/* Location: ./application/models/outlet/Paymentmedia_bankaccount_model.php */