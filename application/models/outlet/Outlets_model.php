<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Outlets_model extends CI_Model {

    public $table = 'outlets';
    public $id = 'outlet_id';
    public $order = 'DESC';
    public $multiaccess;

    function __construct()
    {
        parent::__construct();
        /* akses multi outlet start */
        $this->load->library('Employee_multioutlet_access');
        $this->multiaccess = new Employee_multioutlet_access();
        $this->multiaccess->cek_akses_outlet();
        /* akses multi outlet end */
    }

    // datatables
    function json() {
        $this->datatables->select('outlet_id,name,address,phone,country,province,city,postal_code,expired_date,data_status,data_created');
        $this->datatables->from('outlets');
        $this->datatables->where('data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner

        //akses multi outlet
        $this->multiaccess->datatables();
        
        //add this line for join
        //$this->datatables->join('table2', 'outlets.field = table2.field');
        $this->datatables->add_column('action', anchor(site_url('outlets/read/$1'),'Read')." | ".anchor(site_url('outlets/update/$1'),'Update')." | ".anchor(site_url('outlets/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'outlet_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('outlet_id', $q);
        $this->db->or_like('name', $q);
        $this->db->or_like('address', $q);
        $this->db->or_like('phone', $q);
        $this->db->or_like('country', $q);
        $this->db->or_like('province', $q);
        $this->db->or_like('city', $q);
        $this->db->or_like('postal_code', $q);
        $this->db->or_like('expired_date', $q);
        $this->db->or_like('data_status', $q);
        $this->db->or_like('data_created', $q);
        $this->db->from($this->table);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('outlet_id', $q);
        $this->db->or_like('name', $q);
        $this->db->or_like('address', $q);
        $this->db->or_like('phone', $q);
        $this->db->or_like('country', $q);
        $this->db->or_like('province', $q);
        $this->db->or_like('city', $q);
        $this->db->or_like('postal_code', $q);
        $this->db->or_like('expired_date', $q);
        $this->db->or_like('data_status', $q);
        $this->db->or_like('data_created', $q);
        $this->db->limit($limit, $start);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        //hitung expired
        $dateexpired = time()+(60*60*24*30);
        $dateexpired = date('Y-m-d H:i:s', $dateexpired);

        $data['outlet_id'] = null;
        $data['data_status'] = 'on';
        $data['expired_date'] = $dateexpired;
        $data['data_created'] = date('Y-m-d H:i:s');
        $data['data_modified'] = date('Y-m-d H:i:s');
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //ambil berdasarkan owner
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        if (!empty($data['receipt_logo'])) {
            $data['receipt_logo'] = $data['receipt_logo'];
        }
        $data['data_modified'] = date('Y-m-d H:i:s');
        
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->delete($this->table);
    }



    function form_select()
    {
        if ($this->session->userdata('user_type')=='admin') {
            $this->db->where('data_status', 'on'); //cari hanya data aktif
            $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
            $this->db->order_by('name', 'asc');
            return $this->db->get($this->table)->result();
        }else{
            $this->db->select('o.*');
            $this->db->from('employee_outlet eo');
            $this->db->join('outlets o', 'o.outlet_id = eo.outlet_fkid', 'left');
            $this->db->where('o.data_status', 'on');
            $this->db->where('eo.employee_fkid', $this->session->userdata('user_id'));
            $this->db->order_by('o.name', 'asc');
            return $this->db->get()->result();
        }
    }

    // form select outlet employee
    function outlet_employe()
    {
        $this->db->select("o.name as name,o.outlet_id as outlet_id");
        $this->db->from("outlets o");
        $this->db->join('employee_outlet eo', 'o.outlet_id = eo.outlet_fkid', 'left');
        $this->db->where('o.data_status', 'on'); //cari hanya data aktif
        $this->db->where('o.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        if ($this->session->userdata('user_type')== "employee") {
            $this->db->where("eo.employee_fkid",$this->session->userdata('user_id'));            
        }
        $this->db->order_by('o.name', 'asc');
        $this->db->group_by('o.outlet_id');
        return $this->db->get()->result();
    }

}

/* End of file Outlets_model.php */
/* Location: ./application/models/outlet/Outlets_model.php */