<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Shift_model extends CI_Model
{
    public $table = 'shift'; 
    public $id = 'shift_id';
    public $order = 'DESC';


    // datatables
    function json() {
        $this->datatables->select('shift_id,name,admin_fkid,data_created,data_modified,data_status');
        $this->datatables->from('shift');
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id'));
        //add this line for join
        //$this->datatables->join('table2', 'shift.field = table2.field');
        $this->datatables->add_column('action', anchor(site_url('shift/read/$1'),'Read')." | ".anchor(site_url('shift/update/$1'),'Update')." | ".anchor(site_url('shift/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'shift_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get($this->table)->row();
    }

    public function get_detail($shift_id)
    {
        $this->db->where('shift_fkid', $shift_id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get($this->table.'_outlet')->result();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('shift_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->or_like('data_status', $q);
	$this->db->from($this->table);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('shift_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('admin_fkid', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_modified', $q);
	$this->db->or_like('data_status', $q);
	$this->db->limit($limit, $start);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $data['shift_id'] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['data_created'] = current_millis(); //date('Y-m-d H:i:s');
        $data['data_modified'] = current_millis(); //date('Y-m-d H:i:s');
        $data['data_status'] = 'on';
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $data['data_modified'] = current_millis(); //date('Y-m-d H:i:s');
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        return $this->db->delete($this->table);
    }


    //ambil available outlet
    function available_outlet($shift_id)
    {
        $this->db->select('outlets.name AS outlet_name');
        $this->db->from('shift_outlet');
        $this->db->join('outlets', 'outlets.outlet_id = shift_outlet.outlet_fkid', 'left');
        $this->db->where('shift_outlet.shift_fkid', $shift_id);
        $this->db->where('shift_outlet.admin_fkid', $this->session->userdata('admin_id'));
        $this->db->order_by('outlet_name', 'asc');
        return $this->db->get()->result();
    }

    function available_outlet_insert($data)
    {
        $object = array(
            'shift_fkid' => $data['shift_id'],
            'outlet_fkid' => $data['outlet_id'],
            'admin_fkid' => $this->session->userdata('admin_id'),
            'data_modified' => current_millis()
        );
        return $this->db->insert('shift_outlet', $object);
    }

    public function available_outlet_delete($shift_id)
    {
        if ($this->session->userdata('user_type')=='employee') {
            $this->db->where_in('outlet_fkid', $this->session->userdata('outlet_access'));
        }
        $this->db->where('admin_fkid', $this->session->userdata('admin_id'));
        $this->db->where('shift_fkid', $shift_id);
        return $this->db->delete('shift_outlet');
    }

    function form_select()
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->order_by('name', 'asc');
        return $this->db->get($this->table)->result();
    }

    public function get_by_outlet($outlet_id)
    {
        $this->db->select('*');
        $this->db->from('shift');
        $this->db->join('shift_outlet so', 'so.shift_fkid = shift.shift_id', 'left');
        $this->db->where('so.outlet_fkid', $outlet_id);
        return $this->db->get()->result_array();
    }

}

/* End of file Shift_model.php */
/* Location: ./application/models/Shift_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-10-26 15:02:00 */
/* http://harviacode.com */