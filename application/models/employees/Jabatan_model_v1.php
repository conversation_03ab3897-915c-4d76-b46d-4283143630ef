<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Jabatan_model extends CI_Model
{

    public $table = 'employees_jabatan';
    public $id = 'jabatan_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // datatables
    function json() {
        $this->datatables->select('jabatan_id,name,level,data_created,data_status');
        $this->datatables->from('employees_jabatan');
        $this->datatables->where('data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //add this line for join
        //$this->datatables->join('table2', 'employees_jabatan.field = table2.field');
        $this->datatables->add_column('action', anchor(site_url('jabatan/read/$1'),'Read')." | ".anchor(site_url('jabatan/update/$1'),'Update')." | ".anchor(site_url('jabatan/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'jabatan_id');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('jabatan_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('level', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_status', $q);
	$this->db->from($this->table);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('jabatan_id', $q);
	$this->db->or_like('name', $q);
	$this->db->or_like('level', $q);
	$this->db->or_like('data_created', $q);
	$this->db->or_like('data_status', $q);
	$this->db->limit($limit, $start);
    $this->db->where('data_status', 'on'); //cari hanya data aktif
    $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //ambil berdasarkan owner
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->delete($this->table);
    }



    function form_select()
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->order_by('level', 'desc');
        return $this->db->get($this->table)->result();
    }

}

/* End of file Jabatan_model.php */
/* Location: ./application/models/Jabatan_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-19 16:08:55 */
/* http://harviacode.com */