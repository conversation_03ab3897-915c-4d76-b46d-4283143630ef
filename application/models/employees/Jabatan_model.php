<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Jabatan_model extends CI_Model {

    public $table = 'employees_jabatan';
    public $id = 'jabatan_id';
    public $order = 'DESC';

    // datatables
    public function datatables()
    {
        $this->datatables->select('jabatan_id, name, level');
        $this->datatables->from($this->table);
        $this->datatables->where('data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->datatables->generate();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->get($this->table)->row();
    }

    function get_role($jabatan_id)
    {
        $this->db->where('jabatan_fkid', $jabatan_id);
        return $this->db->get('employees_jabatan_role')->result();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['admin_fkid'] = $this->session->userdata('admin_id'); //ambil berdasarkan owner
        $data['data_status'] = 'on';
        $data['data_created'] = date('Y-m-d H:i:s');
        return $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        //set debug
        $this->db->db_debug = FALSE;
        
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->delete($this->table);
    }

    public function delete_role($jabatan_id)
    {
        $this->db->where('jabatan_fkid', $jabatan_id);
        return $this->db->delete('employees_jabatan_role');
    }



    function form_select()
    {
        if ($this->session->userdata('user_type')=='employee') {
            //current employee level
            $this->db->select('level');
            $this->db->from('employee e');
            $this->db->where('employee_id', $this->session->userdata('user_id'));
            $level = $this->db->get()->row()->level;
            $this->db->where('level <=', $level);
        }

        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->order_by('level', 'desc');
        return $this->db->get($this->table)->result();
    }



    /* UNUSED */
    function json() {
        $this->datatables->select('jabatan_id,name,level,data_created,data_status');
        $this->datatables->from('employees_jabatan');
        $this->datatables->where('data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        //add this line for join
        //$this->datatables->join('table2', 'employees_jabatan.field = table2.field');
        $this->datatables->add_column('action', anchor(site_url('jabatan/read/$1'),'Read')." | ".anchor(site_url('jabatan/update/$1'),'Update')." | ".anchor(site_url('jabatan/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), 'jabatan_id');
        return $this->datatables->generate();
    }

}

/* End of file Jabatan_model.php */
/* Location: ./application/models/employees/Jabatan_model.php */