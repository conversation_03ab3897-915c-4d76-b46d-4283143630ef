<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Employee_model extends CI_Model
{

    // public $table = 'employee';
    // public $id = 'employee_id';
    // public $order = 'DESC';

    public $table = 'view_employee';
    public $table_ori = 'employee';
    public $id = 'employee_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
        $this->load->helper('password');
    }

    // datatables
    function json() {
        $this->datatables->select('employee_id,name,address,phone,jabatan_fkid,level,email,password,pin,role,access_mode,access_status_web,access_status_mobile,user_activation_expired,date_join,admin_fkid,data_created,data_modified,data_status,jabatan_name,jabatan_adminid,employee_adminid');
        $this->datatables->from('view_employee');
        //add this line for join
        //$this->datatables->join('table2', 'view_employee.field = table2.field');
        $this->datatables->where('data_status', 'on'); //cari hanya data aktif
        $this->datatables->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner 
        // $this->datatables->where('jabatan_adminid', $this->session->userdata('admin_id')); //ambil berdasarkan owner jabatan
        if ($this->session->userdata('user_type')=='employee') {
            $this->datatables->where($this->id.' !=', $this->session->userdata('user_id'));
        }
        $this->datatables->add_column('action', anchor(site_url('view_employee/read/$1'),'Read')." | ".anchor(site_url('view_employee/update/$1'),'Update')." | ".anchor(site_url('view_employee/delete/$1'),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'), '');
        return $this->datatables->generate();
    }

    // get all
    function get_all()
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        $this->db->where('jabatan_adminid', $this->session->userdata('admin_id')); //ambil berdasarkan owner jabatan
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        // $this->db->where('jabatan_adminid', $this->session->userdata('admin_id')); //ambil berdasarkan owner jabatan
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }

    // insert data
    function insert($data)
    {
        $data[$this->id] = null;
        $data['email'] = (!empty($data['email'])) ? $data['email'] : null; //enkripsi pin
        //$data['pin'] = (!empty($data['pin'])) ? password_encrypt($data['pin']) : null; //enkripsi pin
        $data['pin'] =(!empty($data['pin'])) ? $data['pin'] : null;
        $data['user_activation_expired'] = date('Y-m-d'); //pakai menitan [NEXT]
        $data['date_join'] = current_millis();
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['data_created'] = current_millis();
        $data['data_modified'] = current_millis();
        $data['data_status'] = 'on';
        return $this->db->insert($this->table_ori, $data);
    }

    // update data
    function update($id, $data)
    {
        //value
        if (!empty($data['pin'])) {
            //$data['pin'] = password_encrypt($data['pin']); //enkripsi pin
            $data['pin'] = $data['pin'];
        }
        $data['user_activation_expired'] = date('Y-m-d H:i:s');//tambahan waktu
        $data['data_modified'] = current_millis();

        //kondisi
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table_ori, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner

        //mencegah hapus diri sendiri
        if ($this->session->userdata('user_type')=='employee') {
            $this->db->where($this->id.' !=', $this->session->userdata('user_id'));
        }
        return $this->db->delete($this->table_ori);
    }



    /* custom */
    //cek email sudah terdaftar atau belum
    function check_registered_email($action, $table, $data)
    {
        $user_id = $data['id'];
        $email = $data['email'];

        switch ($table) {
            case 'admin':
                $this->db->select('email');
                $this->db->where('email', $email);
                if ($action=='update') {
                    $this->db->where('admin_id !=', $user_id);
                }
                $result = $this->db->get('admin');
                break;
            case 'employee':
                $this->db->select('email');
                $this->db->where('email', $email);
                if ($action=='update') {
                    $this->db->where('employee_id !=', $user_id);
                }
                $result = $this->db->get('employee');
                break;
            
            default:
                # code...
                break;
        }

        return $return = array(
            'data_count' => $result->num_rows()
        );
    }

    function add_access_outlet($data)
    {
        $object = array(
            'employee_fkid' => $data['employee_fkid'],
            'outlet_fkid' => $data['outlet_fkid'],
            'data_modified' => current_millis()
        );
        return $this->db->insert('employee_outlet', $object);
    }

    function get_access_outlet($employee_id)
    {
        $this->db->where('employee_fkid', $employee_id);
        return $this->db->get('employee_outlet');
    }

    function delete_access_outlet($employee_id)
    {
        $this->db->where('employee_fkid', $employee_id);
        return $this->db->delete('employee_outlet');
    }

}

/* End of file Employee_model.php */
/* Location: ./application/models/Employee_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2017-07-26 09:34:08 */
/* http://harviacode.com */