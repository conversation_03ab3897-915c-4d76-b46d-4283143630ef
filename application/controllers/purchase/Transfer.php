<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Transfer extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->load->model('outlet/Outlets_model');
        $this->load->model('purchase/transfer/Transfer_model');
        $this->load->model('purchase/transfer/Transfer_detail_model');
        $this->load->model('purchase/transfer/Transfer_confirm_model');
        $this->load->model('products/ingridients/Ingridients_catalogue_model');
        $this->load->model('products/ingridients/Unit_model');
        $this->load->model('outlet/Shift_model', 'Shift_model');
        $this->load->model('outlet/Paymentmedia_bankaccount_model', 'Bankaccount_model');
        $this->load->library('export');
	}

	public function index()
	{
        // print_r($this->session->userdata());
		$link = site_url('purchase/transfer/'); //URL dengan slash
        $data = array(
            'kolomID' => 'transfer_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'update/',
            'ajaxActiongetDataEdit' => $link.'edit/', //ambil data yang akan diedit
            'ajaxActionTrasferDetail' => $link.'json_transfer_detail/', //ambil data yang akan diedit
            'ajaxActionTrasferConfirm' => $link.'jsonConfirm/',
            'ajaxActionTrasferBalance' => $link.'balance/',
            );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_unit_model'] = $this->Unit_model->form_select();
        $data['form_select_shift'] = $this->Shift_model->form_select();
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();

		// $this->load->view('purchase/transfer/transfer_v', $data);
        $data['page_title'] = 'Transfer';
        $this->template->view('purchase/transfer/transfer_v2', $data);
	}

	public function json($startDate,$endDate)
    {
        header('Content-Type: application/json');

        /* custom json output  start */
        $jsondata = $this->Transfer_model->json($startDate,$endDate); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        $dataOutlet= $this->Outlets_model->outlet_employe();
        foreach ($json_decode->data as $a) {
            $id = $a->transfer_id;
            $date_transfer = millis_to_localtime('Y-m-d',$a->date_transfer); //encoding string
            $outlet_origin_fkid = htmlentities($a->outlet_origin); //encoding string
            $outlet_destination_fkid = $a->destination_fkid;
            $keterangan = htmlentities($a->keterangan); //encoding string
            $user_fkid = htmlentities($a->admin_name); //encoding string
            // print_r($outlet_destination_fkid);die();

            //check id outlet origin

            // print_r($dataOutlet);die();
            $temp_data = array(
                'transfer_id' => $id,
                'date_transfer' => $date_transfer,
                'date_created' => millis_to_localtime('Y-m-d H:i:s',$a->date_created),
                'outlet_origin' => $outlet_origin_fkid,
                'outlet_destination' => $a->outlet_destination,
                'keterangan' => $keterangan,
                'admin_name' => $user_fkid,
                'total_markUp' => formatUang($a->total_markUp),
                'grand_total' => formatUang($a->grand_total),
                'operator' => $a->operator,
                // 'action' => $action
                );

            foreach ($dataOutlet as $key => $value) {
                if ($value->outlet_id == $outlet_destination_fkid || $this->session->userdata('admin_id')==$this->session->userdata('user_id')){
                    $action = '<div class="btn-group dropleft">
                  <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                    <span class=""><i class="fa fa-bars"></i></span>
                    <span class="sr-only">Toggle Dropdown</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-right" role="menu">
                    <li><a href="javascript:void(0)" onclick="confrim('.$id.')"><i class="fa fa-check"></i> Confirm</a></li>
                    <li class="cetak" data-param="'.$this->encrypt_url($id).'"><a href="javascript:void(0)"><i class="fa fa-print"></i> Cetak</a></li>
                  </ul>
                </div>';
                    // $action  = "<button class='btn-primary' style='border-color: transparent;' onclick='confrim({$id})'><span style='color:#000' class='edit'>Confrim</span>";
                    $temp_data['action'] = $action;
                    break;
                }else{
                    $action='<p style="color:#ffff">No Access</p>';
                    $temp_data['action'] = $action;
                }
            }

            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    //json transfer detail
    public function json_transfer_detail($transfer_id)
    {
        header('Content-Type: application/json');

        $data = $this->Transfer_detail_model->json_detail_transfer($transfer_id);
        $json_decode = json_decode($data);
        $data_array = array();
        foreach ($json_decode ->data as $a) {
            $id = $a->transfer_product_id;
            $qty = "<input type='hidden' id='qtyTransfer' name='qtyTransfer[]' value='$a->qty_trasfer_detail'/><span id='qty_transfer_text'>".$a->qty_trasfer_detail."</span>";
            $price = "<input type='hidden' id='priceConfirm' name='priceConfirm[]' value='$a->price'/><span id='price_confirm_text'>".$a->price."</span>";
            $markup = "<input type='hidden' id='markupTransfer' name='markupTransfer[]' value='$a->markup_detail'/><span id='markup_text'>".$a->markup_detail."</span>";
            $markup_type = "<input type='hidden' id='markupTypeTransfer' name='markupTypeTransfer[]' value='$a->markup_type'/><span id='markup_type_text'>".$a->markup_type."</span>";
            $discount = "<input type='hidden' id='discountConfirm' name='discountConfirm[]' value='$a->discount_detail'/><span id='discount_text'>".$a->discount_detail."</span>";
            $total = "<input type='hidden' class='totalTransfer' name='totalTransfer[]' value='$a->total_transfer_detail'/><span id='total_text'>".$a->total_transfer_detail."</span>";
            $price_transfer = "<input type='hidden' id='priceTransfer' name='priceTransfer[]' value='$a->price_transfer'/><span id='price_transfer_text'>".$a->price_transfer."</span>";

            $exsits = $this->Transfer_detail_model->exsits($id);
            if ($exsits) {
                $form = "<p><b>Confirmed</b></p>";
            }else{
                 $form = "<input type='number' id='qty_confrim' name='qty_confrim[]' step='0.01' min='0' max='$a->qty_trasfer_detail' onchange='jumlah_retur(this)' style='width:75px;' class='uniq_qty form-control'/><input type='hidden' id='transferProductId' name='transferProductId[]' value='$id'/>";
                $form .= "<input type='hidden' id='productId' name='productId[]' value='$a->product_id'/>";
                $form .= "<input type='hidden' id='productDetailId' name='productDetailId[]' value='$a->product_detail_id'/>";
                $form .= "<input type='hidden' id='variant' name='variant[]' value='$a->variant_fkid'/>";
            };


            $temp_data = array(
                'transfer_product_id' =>$id,
                'transfer_fkid' => $a->transfer_fkid,
                'qty_detail' => $qty,
                'total_detail' => $total,
                'price' => $price,
                'markup_detail' => $markup,
                'markup_type' => $markup_type,
                'discount_detail' => $discount,
                'price_transfer' => $price_transfer,
                'product_name' =>$a->product_name." ".$a->variant,
                'category' => $a->category,
                'sub_category' =>$a->sub_category,
                'unit_name' => $a->unit_name,
                'type' => $a->type,
                'action' => $form,
            );
             array_push($data_array, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $data_array,
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    //select product by outlet
    public function json_by_outlet($outlet_id=null)
    {
        header('Content-Type: application/json');

        /* custom json output  start */
        $jsondata = $this->Transfer_model->json_by_outlet($outlet_id); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->product_detail_id;
            $product_name = htmlentities($a->product_name); //encoding string
            $outlet_name = htmlentities($a->outlet_name);
            $price_sell = $a->price_sell;
            $products_type_id = $a->products_type_id;
            $type = $a->type;
            $product_category_id = $a->product_category_id;
            $category = $a->category;
            $product_subcategory_id = $a->product_subcategory_id;
            $sub_category = $a->sub_category;
            $unit_name = htmlentities($a->unit_name);
            $unit_id = htmlentities($a->unit_id);
            $transfer_markup = htmlentities($a->transfer_markup);
            $transfer_markup_type = htmlentities($a->transfer_markup_type);
            $price_buy = htmlentities($a->price_buy);

            $temp_data = array(
                'product_detail_id' => $id,
                'product_name' => $product_name." ".$a->variant,
                'outlet_name' => $outlet_name,
                'price_sell' => $price_sell,
                'products_type_id' => $products_type_id,
                'type' => $type,
                'product_category_id' => $product_category_id,
                'category' => $category,
                'unit_name' => $unit_name,
                'product_subcategory_id' => $product_subcategory_id,
                'sub_category' => $sub_category,
                'unit_name' => $unit_name,
                'unit_id' => $unit_id,
                'transfer_markup' => $transfer_markup,
                'transfer_markup_type' => $transfer_markup_type,
                'price_buy' => $price_buy,
                'product_id' => $a->product_id,
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }


    //insert transfer
    public function insert()
    {
        $this->db->trans_start();//jika gagal di jalan maka akan di roll back
        //proses post
        // print_r(count($this->input->post('ingridient_id[]')));die();
        $data_transfer = array(
            'date_transfer' => $this->input->post('date_milis'),
            'outlet_origin_fkid' => $this->input->post('outlet_id'),
            'outlet_destination_fkid' => $this->input->post('outlet_destination'),
            'keterangan' => $this->input->post('keterangan'),
            'shift_fkid' => $this->input->post('shift'),
            'sub_total' => $this->input->post('sub_total'),
            'grand_total' => $this->input->post('grand_total'),
            'total_markUp' => $this->input->post('markup-total'),
            'type_total_markUp' => $this->input->post('markup-type'),
            'discount_total' => $this->input->post('discount_total'),
            'discount_type' => $this->input->post('type_discount'),
            'payment' => $this->input->post('pay_type'),
        );
        if ($this->input->post('pay_type')=="card") {
            $data_transfer['bank_origin'] = $this->input->post('bankOrigin');
            $data_transfer['bank_destination'] = $this->input->post('bankDestination');
        }


				file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." transfer: $data_transfer \n");

        $this->Transfer_model->insert($data_transfer); //insert ke table transfer
        $primary_key = $this->db->insert_id(); //id terakhir setelah insert
        $jml_row= count($this->input->post('nomor[]'));
        $dataArray = array();
        for ($i=0; $i < $jml_row ; $i++) {
            $product_id = $this->input->post('product_id['.$i.']');
            $data_detail=array(
                'transfer_fkid' => $primary_key,
                'qty' => $this->input->post('qty['.$i.']'),
                'price' => $this->input->post('price['.$i.']'),
                'markup' => $this->input->post('markUp['.$i.']'),
                'markup_type' => $this->input->post('markup_type['.$i.']'),
                'discount' => $this->input->post('discount['.$i.']'),
                'total' => $this->input->post('total['.$i.']'),
                'price_transfer' => $this->input->post('price_transfer['.$i.']'),
                'product_detail_fkid' => $this->input->post('ingridient_id['.$i.']'),
                'data_status' => 'on',
                'date_created' => current_millis(),
                'date_updated' => current_millis(),
            );
            // print_r($data_detail)
            array_push($dataArray, $data_detail);
            // $response = $this->Transfer_detail_model->insert($data_detail); //insert ke table transfer_product
        }
        $this->db->insert_batch('transfer_products', $dataArray);
        $this->db->trans_complete();//juka gagal di jalan maka akan di rolback
        //buat output dalam format json
        return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output(json_encode($jml_row));
    }

    //update and confirm transfer
    public function update($id)
    {
        $data_edit = $this->Transfer_model->get_by_id($id);//data edit
        // $productId = $this->input->post('productId');
        // print_r($this->input->post());die();

        $count = count($this->input->post('qty_confrim'));
        $qty = $this->input->post('qty_confrim[]');
        $this->db->trans_start();//jika gagal di jalan maka akan di roll back
        foreach ($qty as $i => $value) {
            if (!empty($qty[$i])) {//insert hanya yang di atas 0

                $productId = $this->input->post('productId['.$i.']');
                $productDetailId = $this->input->post('productDetailId['.$i.']');
                $data_transfer_confirm = array(
                    'transfer_product_fkid' => $this->input->post('transferProductId['.$i.']'),
                    'qty_confirm' => $this->input->post('qty_confrim['.$i.']'),
                    'total' => $this->input->post('totalTransfer['.$i.']'),
                    'sub_total' => $this->input->post('grand_total_confirm'),
                    'price_transfer' => $this->input->post('priceTransfer['.$i.']'),
                    'grand_total' => $this->input->post('grand_total_confirm'),
                );
                if ($this->session->userdata('user_type')=='employee') {
                    $data_transfer_confirm['employe_fkid'] = $this->session->userdata('user_id');
                }
                $response = $this->Transfer_confirm_model->insert($data_transfer_confirm); //insert ke table transfer_confirm
                // cek apakah product_detail dan outlet sudah tersedia di product_dtail
                $product_detail = $this->Transfer_detail_model -> cek_id($productId,$data_edit->outlet_destination_id,$this->input->post('variant['.$i.']'));
                // jika produk sudah ada maka update harga
                if ($product_detail==TRUE) {
                    $data_updateProduct=array(
                        'price_buy' => $this->input->post('priceTransfer['.$i.']'),
                    );
                    //fungsi untuk update price buy di product_detail
                    $update_product = $this->Transfer_detail_model -> update_product($productId,$data_edit->outlet_destination_id,$data_updateProduct);

                    $data_detail = array(
                        'product_detail_des_fkid' => $product_detail[0]->product_detail_id,
                    );

                    $updateTransferProduct = $this->Transfer_detail_model->update_transfer_detail($this->input->post('transferProductId['.$i.']'),$data_detail); //update tabel transfer product

                    if ($product_detail[0]->data_status == "off") {
                        $this->db->where('product_detail_id', $product_detail[0]->product_detail_id);
                        $this->db->update('products_detail', ['data_status'=>'on']);
                    }

                }else{
                    $data_product_insert = $this->Transfer_detail_model->select_product($productDetailId,$productId);
                    $data_insert = array(
                        'product_fkid' => $data_product_insert[0]['product_fkid'],
                        'outlet_fkid' => $data_edit->outlet_destination_id,
                        'price_buy_start' => $data_product_insert[0]['price_buy_start'],
                        'price_buy' => $this->input->post('priceTransfer['.$i.']'),
                        'price_sell' => $data_product_insert[0]['price_sell'],
                        'voucher' => $data_product_insert[0]['voucher'],
                        'discount' => $data_product_insert[0]['discount'],
                        'active' => $data_product_insert[0]['active'],
                        'transfer_markup_type' => $data_edit->type_total_markUp,
                        'transfer_markup' => $data_edit->total_markUp,
                        'stock' => $data_product_insert[0]['stock'],
                        'variant_fkid' => $data_product_insert[0]['variant_fkid']
                    );
                    $insert_product = $this->Transfer_detail_model-> insert_product($data_insert); //insert ke tabel product_detail
                    $primary_key = array(
                        'product_detail_des_fkid' => $this->db->insert_id(), //id terakhir setelah insert product
                    );

                    $updateTransferProduct = $this->Transfer_detail_model->update_transfer_detail($this->input->post('transferProductId['.$i.']'),$primary_key); //update tabel transfer product
                }
            }

        };
        $this->db->trans_complete();//juka gagal di jalan maka akan di rolback
        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }


    //fungsi transfer confirm
    public function jsonConfirm($id)
    {
        header('Content-Type: application/json');

        $data = $this->Transfer_confirm_model->json_confirm($id);
        $json_decode = json_decode($data); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {

            $temp_data = array(
                'transfer_product_id' =>$a->transfer_product_id,
                'transfer_fkid' =>$a->transfer_fkid,
                'qty_confirm' =>$a->qty_confirm,
                'total_confirm' => formatUang($a->total_confirm),
                'price_confirm' => formatUang($a->price_confirm),
                'markup_detail' =>$a->markup_detail,
                'markup_type' =>$a->markup_type,
                'discount_detail' =>$a->discount_detail,
                'price_transfer' => formatUang($a->price_transfer),
                'product_name' =>$a->product_name." ".$a->variant,
                'category' =>$a->category,
                'sub_category' =>$a->sub_category,
                'unit_name' =>$a->unit_name,
                'type'=>$a->type,
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }



    public function edit($id=null)
    {
        $row = $this->Transfer_model->get_by_id($id);

        if ($row) {
            $data = array(
                'transfer_id' => $row->transfer_id,
                'date_transfer' => millis_to_localtime('Y-m-d',$row->date_transfer),
                'date_created' => millis_to_localtime('Y-m-d H:i:s',$row->date_created),
                'total_markUp' => $row->total_markUp,
                'grand_total' => $row->grand_total,
                'outlet_origin' => $row->outlet_origin,
                'outlet_origin_id' => $row->outlet_origin_id,
                'outlet_destination_id' =>$row->outlet_destination_id,
                'outlet_destination' =>$row->outlet_destination,
                'keterangan' => $row->keterangan,
                'shift_fkid' => $row->shift_fkid,
                'sub_total' => $row->sub_total,
                'discount_total' => $row->discount_total,
                'discount_type' => $row->discount_type,
                'type_total_markUp' => $row->type_total_markUp,

            );
            $dataArray = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo json_encode($dataArray);
    }

    public function balance($id)
    {
        header('Content-Type: application/json');

        $data = $this->Transfer_detail_model->jsonBalanace($id);
        $json_decode = json_decode($data);
        $data_array = array();
        foreach ($json_decode ->data as $a) {
            $qty_transfer = $a->qty_trasfer_detail;
            $qty_confirm = $a->qty_confirm;
            $balance = $qty_confirm-$qty_transfer;
            $dataArray = array(
                'transfer_product_id' =>$a->transfer_product_id,
                'product_name' =>$a->product_name." ".$a->variant,
                'category' => $a->category,
                'sub_category' => $a->sub_category,
                'unit_name' => $a->unit_name,
                'type' => $a->type,
                'qty_transfer' => $qty_transfer,
                'qty_confirm' => $qty_confirm,
                'balance' => $balance,
            );
            array_push($data_array, $dataArray);
        }
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $data_array,
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }


    public function _rules()
    {
        $this->form_validation->set_rules('opcost_name', 'opcost_name', 'required|max_length[50]');
        $this->form_validation->set_rules('qty', 'qty', 'required|integer|max_length[100]');
        $this->form_validation->set_rules('harga', 'harga', 'required|integer|max_length[100]');
        $this->form_validation->set_rules('keterangan', 'keterangan', 'required|max_length[50]');
        $this->form_validation->set_rules('supplier_id', 'supplier_id', 'required|max_length[50]');
        $this->form_validation->set_rules('outlet', 'outlet', 'required|max_length[15]');
        $this->form_validation->set_rules('id', 'opcost_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

    function encrypt_url($string) {

        $output = false;
        $secret_key     = "1111111111111111";
        $secret_iv      = "2456378494765431";
        $encrypt_method = "aes-256-cbc";

        // hash
        $key    = hash("sha256", $secret_key);

        // iv – encrypt method AES-256-CBC expects 16 bytes – else you will get a warning
        $iv     = substr(hash("sha256", $secret_iv), 0, 16);

        //do the encryption given text/string/number
        $result = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($result);
        return $output;
    }

    function decrypt_url($string) {

        $output = false;
        $secret_key     = "1111111111111111";
        $secret_iv      = "2456378494765431";
        $encrypt_method = "aes-256-cbc";

        // hash
        $key    = hash("sha256", $secret_key);

        // iv – encrypt method AES-256-CBC expects 16 bytes – else you will get a warning
        $iv = substr(hash("sha256", $secret_iv), 0, 16);

        //do the decryption given text/string/number

        $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
        return $output;
    }

    public function cetak($param)
    {
        $id = $this->decrypt_url($param);
        $dataDetail = $this->Transfer_detail_model->json_detail_transfer($id);
        $json = json_decode($dataDetail);
        // print_r($json->data);die();
        $dataHeder = $this->Transfer_model->get_by_id($id);
        $date= millis_to_localtime('d-m-Y',$dataHeder->date_transfer);
        // print_r($dataHeder);die();

        $table = '<table style="border-collapse:collapse;" width="100%" align="center" border="0" cellspacing="0" cellpadding="4">
                <tr>
                     <td rowspan="1" align="center"><img src="'.$this->session->userdata('photo_url').'"width="100" height="105"></td>
                     <td width="100%" align="center" style="border-bottom:none;border-left:none;"><font size="12"><b>TRANSFER PRODUK</b></font>
                     </td>
                </tr>
                <tr>
                     <td align="center" colspan="5"><hr style="height:3px;margin-bottom: 0px;"><hr style="margin-top: 1px;"></td>
                </tr>
            </table>';

        $table .= '<table style="border-collapse:collapse;font-size:12px;" width="100%" align="center" border="0" cellspacing="0" cellpadding="4">
                <tr>
                     <td width="15%"><b>Outlet Asal</b></td>
                     <td >:</td>
                     <td >'.$dataHeder->outlet_origin.'</td>
                     <td width="8%"><b>Shift</b></td>
                     <td width="1%">:</td>
                     <td >'.$dataHeder->shift_name.'</td>
                </tr>
                 <tr>
                     <td ><b>Outlet Tujuan</b></td>
                     <td width="1%">:</td>
                     <td >'.$dataHeder->outlet_destination.'</td>
                     <td ><b>Tanggal</b></td>
                     <td width="1%">:</td>
                     <td >'.$date.'</td>
                </tr>
                <tr>
                     <td align="center" colspan="6"><hr style="height:3px;margin-bottom: 0px;"><hr style="margin-top: 1px;"></td>
                </tr>
            </table> ';

        $table .= '<table style="border-collapse:collapse;font-size:10px;" width="100%" align="center" border="1" cellspacing="0" cellpadding="4">
            <thead>
                <tr>
                    <th bgcolor="#CCCCCC">No</th>
                    <th bgcolor="#CCCCCC">Produk</th>
                    <th bgcolor="#CCCCCC">Tipe</th>
                    <th bgcolor="#CCCCCC">Kategori</th>
                    <th bgcolor="#CCCCCC">Sub-Kategori</th>
                    <th bgcolor="#CCCCCC">Unit</th>
                    <th bgcolor="#CCCCCC">Qty</th>
                    <th bgcolor="#CCCCCC">Harga</th>
                    <th bgcolor="#CCCCCC">Mark Up</th>
                    <th bgcolor="#CCCCCC">Mark Type</th>
                    <th bgcolor="#CCCCCC">Diskon</th>
                    <th bgcolor="#CCCCCC">Total</th>
                    <th bgcolor="#CCCCCC">Harga Transfer</th>
                </tr>
            </thead>';
            $no = 1;
            foreach ($json->data as $key) {
                $table .= '<tr>
                <td>'.$no++.'</td>
                <td>'.$key->product_name.' '.$key->variant.'</td>
                <td>'.$key->type.'</td>
                <td>'.$key->category.'</td>
                <td>'.$key->sub_category.'</td>
                <td>'.$key->unit_name.'</td>
                <td>'.$key->qty_trasfer_detail.'</td>
                <td>'.formatUang($key->price).'</td>
                <td>'.$key->markup_detail.'</td>
                <td>'.$key->markup_type.'</td>
                <td>'.$key->discount_detail.'</td>
                <td>'.formatUang($key->total_transfer_detail).'</td>
                <td>'.formatUang($key->price_transfer).'</td>
                </tr>';
            };
        $table .= '</table> <br>';
        $table .= '<table style="border-collapse:collapse;font-size:12px;" width="100%" align="center" border="0" cellspacing="0" cellpadding="4">
                <tr>
                     <td width="15%"><b>Sub Total</b></td>
                     <td >:</td>
                     <td width="50%">'.formatUang($dataHeder->sub_total).'</td>
                     <td bgcolor="#CCCCCC" style="border-top: 1px solid;border-left: 1px solid;border-right: 1px solid;text-align:center"><b style="font-size:13px">Grand Total</b></td>
                </tr>
                 <tr>
                     <td ><b>Diskon Total</b></td>
                     <td width="1%">:</td>
                     <td >'.$dataHeder->discount_total.' '.$dataHeder->discount_type.'</td>
                     <td bgcolor="#CCCCCC" width="50%" rowspan="3" style="border-bottom: 1px solid;border-left: 1px solid;border-right: 1px solid;text-align:center"><b><font style="font-size:23px;">'.formatUang($dataHeder->grand_total).'</font></b></td>
                </tr>
                 <tr>
                     <td ><b>Mark-Up total</b></td>
                     <td width="1%">:</td>
                     <td >'.$dataHeder->total_markUp.' '.$dataHeder->type_total_markUp.'</td>
                </tr>
                 <tr>
                     <td ><b>Keterangan</b></td>
                     <td width="1%">:</td>
                     <td >'.$dataHeder->keterangan.'</td>
                </tr>
                <tr>
                     <td align="center" colspan="6"><hr style="height:3px;margin-bottom: 0px;"><hr style="margin-top: 1px;"></td>
                </tr>
            </table> ';
        // print_r($table);die();
        $judul = 'Transfer Produk Outlet ';
        // echo $table;die();
        $this->export->data($table)
            ->setMargin(10, 10)
            ->setTitle($judul)
            ->setFileName($judul)
            ->setOrientation('P');
        switch ($format="pdf") {
            case 'html';
                echo $this->export->html();
                break;
            case 'pdf';
                $this->export->pdf();
                break;
            case 'excel';
                $this->export->excel();
                break;
        }
    }

}

/* End of file Transfer.php */
/* Location: ./application/controllers/purchase/Transfer.php */
