<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Purchase_menu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		$this->user_role->purchase('read_access'); //employee role access page (menu)
	}

	public function index()
	{
		if ($this->session->userdata('user_type')=='admin') {
			redirect('purchase/supplier');
			die();
		}


		//setting untuk employee
		$role = $this->user_role;
		$redirek = 'purchase/';
		$valid = false;

		if ($role->purchase_supplier('read')===TRUE && $valid===FALSE) {
			$redirek .= 'supplier';
			$valid = true;
		}
		if ($role->purchase_purchase('read')===TRUE  && $valid===FALSE) {
			$redirek .= 'purchase';
			$valid = true;
		}
		if ($role->purchase_debt('read')===TRUE  && $valid===FALSE) {
			$redirek .= 'debt';
			$valid = true;
		}
		if ($role->purchase_transfer('read')===TRUE  && $valid===FALSE) {
			$redirek .= 'transfer';
			$valid = true;
		}
		

		//pindahkan halaman
		if ($valid===false) {
			//$this->user_role->purchase_transfer('read') = false;
			$this->user_role->purchase_transfer('read_access'); //employee role access page
		}
		else{
			redirect(site_url($redirek));
		}
	}

	public function index2()
	{
		//pindahkan halaman
		redirect(site_url('purchase/supplier'));
	}

}

/* End of file Purchase_menu.php */
/* Location: ./application/controllers/purchase/Purchase_menu.php */