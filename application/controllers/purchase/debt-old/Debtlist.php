<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Debtlist extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('purchase/Purchase_model'); 
        $this->load->model('outlet/Outlets_model'); //form
        $this->load->model('purchase/supplier/Supplier_model');
        $this->load->model('purchase/purchase_products/Purchase_products_model');
        $this->load->model('products/ingridients/Unit_model');
        $this->load->model('purchasing/retur/Retur_products_model');
        $this->load->model('purchase/debt/Debt_model');
        $this->load->model('outlet/Paymentmedia_bankaccount_model', 'Bankaccount_model');
        $this->load->model('outlet/Shift_model', 'Shift_model');
        $this->load->model('finance/M_set_jurnal_umum','set_jurnal');
    }

	public function index()
	{
        $link = site_url('purchase/debt/debtlist/'); //URL dengan slash
        $data = array(
            'kolomID' => 'purchase_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataRetur' => $link.'payment/' //ambil data yang akan diedit
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_supplier'] = $this->Supplier_model->form_select();
        $data['form_select_unit_model'] = $this->Unit_model->form_select();
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();
        $data['form_select_shift'] = $this->Shift_model->form_select();

		// $this->load->view('purchase/debt/debt_list_v',$data);
        $data['page_title'] = 'Debt List';
        $this->template->view('purchase/debt/debt_list/debt_list_v2',$data);
	}

    

    public function json($outlet,$start,$end)
    {
        $jsondata = $this->Purchase_model->debt($outlet,$start,$end); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->purchase_id;
            $invoice = htmlentities($a->invoice); //encoding string
            $keterangan = htmlentities($a->keterangan); //encoding string
            $bayar = htmlentities($a->bayar); //encoding string
            $grand_total = htmlentities($a->grand_total); //encoding string
            $hutang = htmlentities($a->hutang); //encoding string
            $status_lunas = htmlentities($a->status_lunas); //encoding string
            $outlet_name = htmlentities($a->outlet_name); //encoding string
            $supplier_name = htmlentities($a->supplier_name); //encoding string
            $employee_fkid = htmlentities($a->employee_fkid); //encoding string
            $admin_name = htmlentities($a->admin_name); //encoding string
            $jatuh_tempo = htmlentities($a->jatuh_tempo); //encoding string
            $data_created = millis_to_localtime('Y-m-d H:i', $a->data_created); //encoding string
            $data_status = htmlentities($a->data_status); //encoding string
            $action  = '<button class="btn btn-sm btn-primary" data-toggle="tooltip" onclick="payment('.$id.')" data-placement="top" title="Bayar"><i class="fa fa-money"></i></button>';
            
            
            $temp_data = array(
                'purchase_id' => $id,
                'invoice' => $invoice,
                'keterangan' => $keterangan,
                'bayar' => formatUang($bayar),
                'grand_total' => formatUang($grand_total),
                'hutang' => formatUang(abs($hutang)), 
                'status_lunas' => $status_lunas,
                'outlet_name' => $outlet_name,
                'supplier_name' => $supplier_name,
                'employee_fkid' => $employee_fkid,
                'jatuh_tempo' => $jatuh_tempo,
                'admin_name' => $admin_name,
                'data_created' => $data_created,
                'data_status' => $data_status,
                'total_pajak' => formatUang($a->total_pajak),
                'debt_payment' =>formatUang($a->debt_payment),
                'action' => $action
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

    public function payment($id=null)
    {
        $row = $this->Purchase_model->get_by_id($id);

        if ($row) {
            $data = array(
                    'id' => set_value('purchase_id', $row->purchase_id),
                    'invoice' => set_value('invoice', $row->invoice),
                    'keterangan' => set_value('keterangan', html_entity_decode($row->keterangan)),
                    'supplier' => set_value('supplier_fkid', $row->supplier_fkid),
                    'bayar' => set_value('bayar', $row->bayar),
                    'outlet' => set_value('outlet_fkid', $row->outlet_fkid),
                    'grand_total' => set_value('grand_total', $row->grand_total),
                    'hutang' => set_value('hutang', $row->hutang),
                    'discount_total' => set_value('discount_total', $row->discount_total),
                    'total_pajak' => set_value('total_pajak', $row->total_pajak),   
                    'jatuh_tempo' => set_value('jatuh_tempo', $row->jatuh_tempo),   
                    'sub_total' => set_value('sub_total', $row->sub_total),     
                    'outlet_name' => set_value('outlet_name', $row->outlet_name),     
                    'shift_name' => set_value('shift_name', $row->shift_name),     
                    'supplier_name' => set_value('supplier_name', $row->supplier_name),     
                    'jumlah_product' => set_value('jumlah_product', $row->jumlah_product),     
                    'jumlah_retur' => set_value('jumlah_retur', $row->jumlah_retur),     
                    'total_bayar' => set_value('jumlah_retur', $row->total_bayar),     
                          
                );
            $dataArray = array(
                    'status' => 'success',
                    'data' => $data
                );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo json_encode($dataArray);
    }

    public function action()
    {
         $i=$this->input->post('id_purchase_retur');
         $id_purchase= array(
            'hutang'=> $this->input->post('hutang_hiden'),
            'status_lunas'=> $this->input->post('status_lunas')
        );
         $response = $this->Purchase_model->update($i,$id_purchase);
        
        if ($response==TRUE) {
            $data=array(
                'nominal'=> preg_replace("/[A-Za-z.,]/","", $this->input->post('bayar_debt')),
                'purchase_fkid'=>$i,
                'keterangan_payment'=>$this->input->post('keterangan_debt'),
                'payment' => $this->input->post('pay_type'),
                'shift_fkid' => $this->input->post('shift_fkid'),
            );
            if ($this->input->post('pay_type')=="card") {
                $data['payment_bank_fkid'] = $this->input->post('bank');
            }
            $response=$this->Debt_model->insert($data);
            $id = $this->db->insert_id();

            // insert jurnal
            if(getenv('ENABLE_FINANCE') == 'true' || (getenv('CI_ENV') !== 'production')){
                $this->set_jurnal->jurnal_debit_pay($id);
            }            
            
        }

         if ($response==TRUE) {
            $messageUpdateSuccess = $this->session->set_flashdata('message', ' Success');
            $response = array(
                'status' => 'success',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            );
        }
        else{
            $messageUpdateSuccess = $this->session->set_flashdata('message', ' fail'); 
        }


       header('Content-Type: application/json');
       echo json_encode($response);
   }

   public function json_retur($id)
   {
        header('Content-Type: application/json');
        $jsondata = $this->Retur_products_model->json_retur($id); //ambil data json
        // $json_decode = json_encode($jsondata);
        // echo $this->db->last_query();;die();

        $dataArray = array();
        foreach ($jsondata as $a) { 
            $tax = $a['tax_type'] != "percentage" ? $a['tax'] : $a['tax']."%";
            $temp_data = array(
                'date_created' => millis_to_localtime('Y-m-d H:i:s',$a['data_created']),
                'qty_nota' => $a['qty_nota'],
                'tot_dis' => formatUang($a['tot_dis']),
                'price_nota' => formatUang($a['price_nota']),
                'qty_stok' => $a['qty_stok'],
                'price_stok' => formatUang($a['price_stok']),
                'total' => formatUang($a['total']),
                'discount' => $a['discount'],
                'retur' => $a['retur'],
                'invoice' => $a['invoice'],
                'hutang' => formatUang($a['hutang']),
                'unit_name' => $a['unit_name'],
                'unit_description' => $a['unit_description'],
                'products_name' => $a['products_name']." ".$a['variant'],
                'qty_retur' => $a['qty_retur'],
                'qtynota_retur' => $a['qtynota_retur'],
                'qtyStok_retur' => $a['qtyStok_retur'],
                'totdis_retur' => formatUang($a['totdis_retur']),
                'total_retur' => formatUang($a['total_retur']),
                'harga_retur' => formatUang($a['harga_retur']),
                'qty_confrimRetur' => $a['qty_confrim'],
                'keterangan' => $a['keterangan'],
                'pajak' => $a['tax_name']." ".$tax,
                'depreciation' => $a['depreciation'].'/Month',
            );
            array_push($dataArray, $temp_data);
        }
        $draw_json = array(
            'data' => $dataArray
        );
        return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output(json_encode($draw_json));
   }

   public function json_purchase($id)
   {
        header('Content-Type: application/json');
        $jsondata = $this->Purchase_products_model->json($id); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $tax = $a->tax_type != "percentage" ? $a->tax : $a->tax."%";
            $temp_data = array(
                'qty_nota' => $a->qty_nota,
                'tot_dis' => formatUang($a->tot_dis),
                'price_nota' => formatUang($a->price_nota),
                'qty_stok' => $a->qty_stok,
                'price_stok' => formatUang($a->price_stok),
                'total' => formatUang($a->total),
                'discount' => $a->discount,
                'retur' => $a->retur,
                'invoice' => $a->invoice,
                'hutang' => formatUang($a->hutang),
                'unit_name' => $a->unit_name,
                'unit_description' => $a->unit_description,
                'products_name' => $a->products_name." ".$a->variant,
                'retur' => $a->retur,
                'qty_confrim' => $a->qty_confrim,
                'keterangan' => $a->keterangan,
                'pajak' => $a->tax_name." ".$tax,
                'depreciation' => $a->depreciation."/ M",
                'expired'=>$a->expired
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );
        return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output(json_encode($draw_json));

   }

}

/* End of file Debtlist.php */
/* Location: ./application/controllers/purchase/debt/Debtlist.php */