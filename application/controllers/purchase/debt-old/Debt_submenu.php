<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Debt_submenu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		$this->user_role->purchase_debt('read_access'); //employee role access page | submenu debt
		$this->user_role->purchase('read_access'); //employee role access page | menu purchase
	}

	public function index()
	{
		//pindahkan halaman
		redirect(site_url('purchase/debt/debtlist'));
	}

}

/* End of file Debt_submenu.php */
/* Location: ./application/controllers/purchase/debt/Debt_submenu.php */