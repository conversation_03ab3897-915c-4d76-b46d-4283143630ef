<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Debtpayment extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('purchase/debt/Debt_model');
        $this->load->model('purchase/Purchase_model'); 
        $this->load->model('outlet/Outlets_model'); //form
        $this->load->model('purchase/supplier/Supplier_model');
        $this->load->model('purchase/purchase_products/Purchase_products_model');
        $this->load->model('products/ingridients/Unit_model');
        $this->load->model('purchasing/retur/Retur_products_model');
        $this->load->model('purchase/debt/Debt_model');
    }

	public function index()
	{

        $link = site_url('purchase/debt/debtpayment/'); //URL dengan slash
        $data = array(
            'kolomID' => 'debt_payment_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataRetur' => $link.'payment/', //ambil data yang akan diedit
            'ajaxActiongetDataDetail' => $link.'detail/' //ambil data yang akan diedit
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_supplier'] = $this->Supplier_model->form_select();
        $data['form_select_unit_model'] = $this->Unit_model->form_select();
		// $this->load->view('purchase/debt/debt_payment_v',$data);
        //template
        $data['page_title'] = 'Debt Payment';
        $this->template->view('purchase/debt/debt_payment/debt_payment_v2',$data);
	}

    public function json($outlet,$startDate,$endDate)
    {
        /* custom json output  start */
        $jsondata = $this->Debt_model->json($outlet,$startDate,$endDate); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->debt_payment_id;
            $time_created = millis_to_localtime('Y-m-d H:i:s', $a->time_created);
            $invoice = htmlentities($a->invoice); //encoding string
            $outlet_name = htmlentities($a->outlet_name); //encoding string
            $supplier_name = htmlentities($a->supplier_name); //encoding string
            $purchase_fkid = htmlentities($a->purchase_fkid); //encoding string
            $nominal = htmlentities($a->nominal); //encoding string
            $keterangan_payment = htmlentities($a->keterangan_payment); //encoding string

            $action  = '<button class="btn btn-sm btn-primary" data-toggle="tooltip" onclick="payment('.$purchase_fkid.','.$id.')" data-placement="top" title="Detail"><i class="fa fa-list-alt"></i></button>';
            
            
            $temp_data = array(
                'debt_payment_id' => $id,
                'time_created' => $time_created,
                'invoice' => $invoice,
                'outlet_name' => $outlet_name,
                'supplier_name' => $supplier_name,
                'nominal' => formatUang($nominal),
                'keterangan_payment' => $keterangan_payment,
                'purchase_fkid' => $purchase_fkid,
                'operator' => $a->operator,
                'action' => $action,
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

     public function delete($id=null)
    {
        $row = $this->Debt_model->get_by_id($id);

        if ($row) {
            $response = $this->Debt_model->delete($id);
            if ($response==true) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Record Deleted'
                );
            }
            else {
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        }
        else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //response output
        echo format_json($response);
    }

    public function payment($id=null)
    {
        $row = $this->Purchase_model->get_by_id_detail($id);

        if ($row) {
            $data = array(
                'id' => set_value('purchase_id', $row->purchase_id),
                'invoice' => set_value('invoice', $row->invoice),
                'keterangan' => set_value('keterangan', html_entity_decode($row->keterangan)),
                'supplier' => set_value('supplier_fkid', $row->supplier_fkid),
                'bayar' => set_value('bayar', $row->bayar),
                'outlet' => set_value('outlet_fkid', $row->outlet_fkid),
                'grand_total' => set_value('grand_total', $row->grand_total),
                'hutang' => set_value('hutang', $row->hutang),
                'discount_total' => set_value('discount_total', $row->discount_total),
                'total_pajak' => set_value('total_pajak', $row->total_pajak),   
                'jatuh_tempo' => set_value('jatuh_tempo', $row->jatuh_tempo),   
                'sub_total' => set_value('sub_total', $row->sub_total),     
                'outlet_name' => set_value('outlet_name', $row->outlet_name),     
                'shift_name' => set_value('shift_name', $row->shift_name),     
                'supplier_name' => set_value('supplier_name', $row->supplier_name),     
                'jumlah_product' => set_value('jumlah_product', $row->jumlah_product),     
                'jumlah_retur' => set_value('jumlah_retur', $row->jumlah_retur),     
                'bank' => set_value('bank', $row->bank),     
                'payment' => set_value('payment', $row->payment),   
                'purchase_fkid'=>$row->purchase_id, 
                'tglPurchase'=>millis_to_localtime('Y-m-d h:i',$row->date_purchase),
                'tglBayar'=> millis_to_localtime('Y-m-d h:i',$row->date_debt),
                'keteranganDebt'=>$row->keteranganDebt,
            );

            $dataArray = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $dataArray = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        echo format_json($dataArray);
    }

    public function detail($id=null)
    {
        $row = $this->Debt_model->get_detail($id);
        return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output($row);
    }


}

/* End of file Debtpayment.php */
/* Location: ./application/controllers/purchase/debt/Debtpayment.php */