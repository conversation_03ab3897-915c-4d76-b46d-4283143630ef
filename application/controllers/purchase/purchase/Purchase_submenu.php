<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Purchase_submenu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		$this->user_role->purchase_purchase('read_access'); //employee role access page | submenu purchase
		$this->user_role->purchase('read_access'); //employee role access page | menu
	}

	public function index()
	{
		//pindahkan halaman
		redirect(site_url('purchase/purchase/purchase_products'));
	}

}

/* End of file Purchase_submenu.php */
/* Location: ./application/controllers/purchase/purchase/Purchase_submenu.php */