<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Equipment_detail extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
        $this->load->model('purchase/Purchase_model'); 
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('purchase/supplier/Supplier_model');
		$this->load->model('purchase/purchase_products/Purchase_products_model');
        $this->load->model('products/ingridients/Unit_model');
        $this->load->model('purchasing/retur/Retur_products_model');
	}

	public function index()
	{
		$link = current_url().'/';//site_url('purchase/purchase/detail/equipment_detail/'); //URL dengan slash
        $data = array(
            'kolomID' => 'purchase_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json'
            );
        $data['page_title'] = 'Equipment Detail';
        // $data['form_select_outlet'] = $this->Outlets_model->form_select();
        // outlet employee by employee
        $data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
        $this->template->view('purchase/purchase/detail/v2/equipment_detail_v', $data);
	}

	public function json($outlet,$startDate,$endDate)
	{
        header('Content-Type: application/json');
        $jsondata= $this->Purchase_products_model->detail_equipment($outlet,$startDate,$endDate); //default json output
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $purchase_id = $a->purchase_id;
            $invoice = $a->invoice;
            $keterangan = $a->keterangan;
            $status_lunas = $a->status_lunas;
            $data_created = millis_to_localtime('Y-m-d H:i',$a->data_created);
            $jatuh_tempo = $a->jatuh_tempo;
            $outlet_name = $a->outlet_name;
            $supplier_name = $a->supplier_name;
            $unit_nota = $a->unit_nota;
            $qty_nota = formatDesimal($a->qty_nota,2);
            $harga_nota = formatUang($a->harga_nota);
            $unit_stok = $a->unit_stok;
            $qty_stok = formatDesimal($a->qty_stok,2);
            $harga_stok = formatUang($a->harga_stok);
            $total = formatUang($a->total);
            $dis = formatAngka($a->dis);
            $tot_dis = formatUang($a->tot_dis);
            $retur = $a->retur;
            $invoice = $a->invoice;
            $hutang = $a->hutang;
            $purchase_products_id = $a->purchase_products_id;
            $unit_name = $a->unit_name;
            $unit_description = $a->unit_description;
            $nama_barang = $a->nama_barang;
            $products_type = $a->products_type;
            $admin_name = $a->admin_name;
            $data_status = $a->data_status;
            
            
            $temp_data = array(
                'purchase_id' => $purchase_id,
                'invoice' => $invoice,
                'keterangan' => $keterangan,
                'status_lunas' => $status_lunas,
                'data_created' => $data_created,
                'jatuh_tempo' => $jatuh_tempo,
                'outlet_name' => $outlet_name,
                'supplier_name' => $supplier_name,
                'unit_nota' => $unit_nota,
                'qty_nota' => $qty_nota,
                'harga_nota' => $harga_nota,
                'unit_stok' => $unit_stok,
                'qty_stok' => $qty_stok,
                'harga_stok' => $harga_stok,
                'total' => $total,
                'dis' => $dis,
                'tot_dis' => $tot_dis,
                'retur' => $retur,
                'invoice' => $invoice,
                'hutang' => $hutang,
                'purchase_products_id' => $purchase_products_id,
                'unit_name' => $unit_name,
                'unit_description' => $unit_description,
                'nama_barang' => $nama_barang." ".$a->variant,
                'products_type' => $products_type,
                'admin_name' => $admin_name,
                'data_status' => $data_status,
                'depreciation' => $a->depreciation."/Month",
				'expired' => $a->expired
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json); 
        /* custom json output  end */

	}

}

/* End of file Equipment_detail.php */
/* Location: ./application/controllers/purchase/purchase/detail/Equipment_detail.php */
