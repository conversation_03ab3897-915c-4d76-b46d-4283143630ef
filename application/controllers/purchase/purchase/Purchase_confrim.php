<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Purchase_confrim extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('purchase/Purchase_model');
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('purchase/supplier/Supplier_model');
        $this->load->model('purchase/purchase_products/Purchase_products_model');
		$this->load->model('purchase/purchase_confrim/Purchase_confrim_model','purchase_confrim');
        $this->load->model('products/ingridients/Unit_model');
        $this->load->model('purchasing/retur/Retur_products_model');
        $this->load->model('products/products_catalogue/Products_catalogue_model');
        $this->load->model('finance/M_set_jurnal_umum');
        

        $this->load->library('google/Pubsub');
    }

	public function index()
	{
		$link = current_url().'/'; //URL dengan slash
        $data = array(
            'kolomID' => 'purchase_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'jsonConfrim',
            'ajaxActionJsonInConfrim' => $link.'jsonInConfrim',
            'ajaxActiongetDataEdit' => $link.'edit/',
            'ajaxActiongetDataInsert' => $link.'insert/',
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_supplier'] = $this->Supplier_model->form_select();
        $data['form_select_unit_model'] = $this->Unit_model->form_select();
        // $data['select_product'] = $this->Purchase_products_model->json_by_outlet(16);


		// $this->load->view('purchase/purchase/confrim/confrim_v',$data);
        $data['page_title'] = 'Purchase Confirm';
        $this->template->view('purchase/purchase/confirm/confirm_v2',$data);
	}

    public function jsonConfrim()
    {
        header('Content-Type: application/json');
        $data = $this->purchase_confrim->jsonConfrim();
        $json_decode = json_decode($data); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id=$a->id;
            $qty_stock=$a->qty_stok;
            $qty_confrim=$a->qty_confrim;
            $qty_notCOnfrim=$a->qty_notConfrim;

            $action ="<a href='javascript:void(0)' class='confrim' ><button class='btn-primary' style='border-color: transparent; color:#000' onclick='confrim({$id})'>Confirm</button> </a>";
            $temp_data=0;
            if ($qty_notCOnfrim !== '0') {
                 $temp_data = array(
                    'id' => $id,
                    'invoice' => $a->invoice,
                    'suplier' => $a->suplier,
                    'product_name' => $a->product_name,
                    'qty_stock' => formatDesimal($a->qty_stok,1),
                    'price_stock' => formatUang($a->price_stok),
                    'qty_confrim' => formatDesimal($a->qty_confrim,1),
                    'qty_notCOnfrim' =>formatDesimal($qty_notCOnfrim,1),
                    'total' => formatUang($a->total),
                    'outlet' => $a->outlet,
                    'action' =>$action,
                    'variant' => $a->variant
                );

                array_push($dataArray, $temp_data);
            }

        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */


    }

    public function jsonInConfrim()
    {
        header('Content-Type: application/json');
        $data = $this->purchase_confrim->jsonInConfrim();
        $json_decode = json_decode($data); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $temp_data = array(
                'id' => $a->id,
                'date' => millis_to_localtime('Y-m-d H:i',$a->date),
                'invoice' => $a->invoice,
                'suplier' => $a->suplier,
                'product_name' => $a->product_name,
                'qty_stock' => formatDesimal($a->qty_stok,1),
                'price_stock' => formatUang($a->price_stok),
                'qty_confrim' => formatDesimal($a->qty_confrim,1),
                'qty_notConfrim' => formatDesimal($a->qty_notConfrim,1),
                'qty_arive' => formatDesimal($a->qty_arive,1),
                'total' => formatUang($a->total),
                'retur' => formatDesimal($a->retur,1),
                'outlet' => $a->outlet,
                'date_purchase'=>millis_to_localtime('Y-m-d H:i',$a->date_purchase),
                'admin_name'=>$a->admin_name,
                'variant' => $a->variant
            );
            array_push($dataArray, $temp_data);

        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function edit($id)
    {
        $row = $this->purchase_confrim->get_by_id($id);
        if ($row) {
            $data = array(
                'id' => $row->id,
                'qty_notConfrim' => $row->qty_notConfrim,
                'purchase_product_fkid' => $row->purchase_product_fkid,
                'qty_notConfrim' => $row->qty_notConfrim,
                'product_name' => $row->product_name,
            );

            $draw_json = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }
        echo format_json($draw_json);
    }

    public function insert()
    {
        $dataConfrim = array(
            'purchase_product_fkid' => $this->input->post('purchaseProductFkid'),
            'qty' => $this->input->post('qtyConfrim'),
            'qty_notConfrim' => $this->input->post('qtyNotConfrim')-$this->input->post('qtyConfrim'),
            'qty_arive' => $this->input->post('qtyConfrim'),
            'user' => $this->session->userdata('user_name'),
            'date_created' =>current_millis(),
            'date_updated' =>current_millis(),
        );
        if ($this->session->userdata('user_type')=='employee') {
            $dataConfrim['employe_fkid'] = $this->session->userdata('user_id');
        }
        $confrim = $this->purchase_confrim->insert($dataConfrim);//inseet ke tabel purchase_confrim
		$confrimId = $this->purchase_confrim->fetch_last_insert_id();
        //jika insert berhasil data yang di insert lalu di upadate

        if ($confrim==true) {
            $confrim = array(
                'status' => 'success',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            );

            $env = 'development';
            if (!empty($_ENV['CI_ENV'])) {
                $env = $_ENV['CI_ENV'];
            }
            $log = " CI_ENV ".$_ENV['CI_ENV']." | getenv ".getenv('CI_ENV')."  \n";
            $topic = 'purchase_'.$env;
            $this->pubsub->publish($topic, json_encode(array('purchase_product_fkid' => $dataConfrim['purchase_product_fkid'], 'log' => $log, 'qty' => $dataConfrim['qty'], 'purchase_confirm_id' => $confrimId)));

            if(getenv('ENABLE_FINANCE') == 'true' || (getenv('CI_ENV') !== 'production' && getenv('CI_ENV') !== 'staging')){
                $this->M_set_jurnal_umum->jurnal_confirm($confrimId);
            }            
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($confrim);
    }

    public function export_data($id)
    {
        // select purchase detail
        $data = $this->Purchase_products_model->json_export($id);
        // print_r($data[0]->purchase_fkid);die();
        // print_r($data);die();
        //$i=0;//index baris
        $jumlah_data=count($data);
        for ($i=0; $i < $jumlah_data; $i++) {
            $data_purchase=array(
                'purchase_product_fkid' => $data[$i]->purchase_products_id,
                'qty' => $data[$i]->qty_stok,
                'qty_notConfrim' =>'0',
                'qty_arive' =>$data[$i]->qty_stok,
                'retur' =>'null',
                'user' =>$data[$i]->user,
                'date_updated' =>$data[$i]->date_purchase,
                'date_created' =>$data[$i]->date_purchase,
            );
            $response = $this->Purchase_products_model->insert_confirm($data_purchase); //insert ke table purchse_product
        }

    }

}

/* End of file Purchase_confrim.php */
/* Location: ./application/controllers/purchase/purchase/Purchase_confrim.php */
?>
