<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Purchase_products extends Auth_Controller {

    public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('purchase/Purchase_model');
        $this->load->model('outlet/Outlets_model'); //form
        $this->load->model('purchase/supplier/Supplier_model');
        $this->load->model('purchase/purchase_products/Purchase_products_model');
        $this->load->model('purchase/purchase_confrim/Purchase_confrim_model','purchase_confrim');
        $this->load->model('products/ingridients/Unit_model');
        $this->load->model('purchase/retur/Retur_products_model');
        $this->load->model('products/products_catalogue/Products_catalogue_model');
        $this->load->model('outlet/Shift_model', 'Shift_model');
        $this->load->model('products/gratuity/Tax_gratuity_model', 'tax_gratuity');
        $this->load->model('outlet/Paymentmedia_bankaccount_model', 'Bankaccount_model');

        if(getenv('ENABLE_FINANCE') == 'true' || (getenv('CI_ENV') !== 'production' && getenv('CI_ENV') !== 'staging')){
            $this->load->model('finance/M_set_jurnal_umum','set_jurnal');
        }                        
        
        $this->load->library('google/Pubsub');
    }

    public function index()
    {
        $link = current_url().'/'; //URL dengan slash
        $data = array(
            'kolomID' => 'purchase_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataRetur' => $link.'retur/', //ambil data yang akan diedit
            'saveSession' => $link.'saveSession',
            'getDetail' => $link.'getDetail/'
        );


        // $data['form_select_outlet'] = $this->Outlets_model->form_select();
        // outlet employee by employee
        $data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
        $data['form_select_supplier'] = $this->Supplier_model->form_select();
        $data['form_select_unit_model'] = $this->Unit_model->form_select();
        $data['form_select_shift'] = $this->Shift_model->form_select();
        $data['form_select_tax'] = $this->tax_gratuity->form_select();
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();


        // $this->load->view('purchase/purchase/products/purchase_products_v',$data);
        //template
        $data['page_title'] = 'Purchase Products';
        $this->template->view('purchase/purchase/products/purchase_products_v2', $data);
    }

    public function json_by_outlet($outlet_id=null)
    {
        header('Content-Type: application/json');
       // $data['select_product'] =$this->Purchase_products_model->json_by_outlet($outlet_id);
        echo $this->Purchase_products_model->json_by_outlet($outlet_id); //default json output

    }


    public function json($outlet,$startDate,$endDate)
    {
        header('Content-Type: application/json');

        /* custom json output  start */
        $jsondata = $this->Purchase_model->json($outlet,$startDate,$endDate); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->purchase_id;
            $invoice = htmlentities($a->invoice); //encoding string
            $keterangan = htmlentities($a->keterangan); //encoding string
            $bayar = htmlentities($a->bayar); //encoding string
            $grand_total = htmlentities($a->grand_total); //encoding string
            $hutang = $a->hutang; //encoding string
            $status_lunas = htmlentities($a->status_lunas); //encoding string
            $outlet_name = htmlentities($a->outlet_name); //encoding string
            $supplier_name = htmlentities($a->supplier_name); //encoding string
            $employee_fkid = htmlentities($a->employee_fkid); //encoding string
            $admin_name = htmlentities($a->admin_name); //encoding string
            $jatuh_tempo = htmlentities($a->jatuh_tempo); //encoding string
            $data_created =  millis_to_localtime('Y-m-d H:i',$a->data_created);
            $data_status = htmlentities($a->data_status); //encoding string
            $saldo=0;
            if ($hutang>=0) {
                $saldo=$hutang;
                $hutang=0;
            }

            // hidden sementara
            $action  = '<button class="btn btn-sm btn-primary" onclick="retur('.$id.')" data-toggle="tooltip" data-placement="top" title="Retur"><i class="fa fa-undo"></i></button>';


            $action .= '<button class="btn btn-sm btn-primary" onclick="detail('.$id.')" data-toggle="tooltip" data-placement="top" title="Detail"><i class="fa fa-list-alt"></i></button>';


            $temp_data = array(
                'purchase_id' => $id,
                'invoice' => $invoice,
                'keterangan' => $keterangan,
                'bayar' => formatUang($bayar),
                'grand_total' => formatUang($grand_total),
                'hutang' => formatUang(abs($hutang)),
                'status_lunas' => $status_lunas,
                'outlet_name' => $outlet_name,
                'supplier_name' => $supplier_name,
                'employee_fkid' => $employee_fkid,
                'jatuh_tempo' => $jatuh_tempo,
                'admin_name' => $admin_name,
                'data_created' => $data_created,
                'data_status' => $data_status,
                'operator' => $a->operator,
                'debt_payment' => formatUang($a->debt_payment),
                'action' => $action,
                'saldo' =>formatUang($saldo),
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function json_purchase($type=null)
    {
        header('Content-Type: application/json');
        $jsondata = $this->Purchase_products_model->json($type); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {

            $id = $a->purchase_products_id;
            $purchase_fkid = htmlentities($a->purchase_fkid); //encoding string
            $unit_fkid_nota = htmlentities($a->unit_fkid_nota); //encoding string
            $qty_nota ="<input type='hidden' id='qty_notaretur' class='qty_notaretur' name='qty_notaretur[$id]' value='$a->qty_nota'/><span id='qtynota1'>".$a->qty_nota."</span>"; //encoding string
            $price_nota = "<input type='hidden' id='pricenotaretur' name='pricenotaretur[$id]' value='$a->price_nota'/><span id='pricenota1'>".$a->price_nota."</span>"; //encoding string
            $unit_fkid_stok = htmlentities($a->unit_fkid_stok); //encoding string
            $qty_stok = "<input type='hidden' id='qty_stockToretur' class='qty_stockToretur' name='qty_stockToretur[$id]' value='$a->qty_stok' /><span id='qtystok1'>".$a->qty_stok."</span>";
            $price_stok ="<input type='hidden' id='harga_stok' name='harga_stok[$id]' value='$a->price_stok' /><span id='pricestok1'>".$a->price_stok."</span>";
            $data_status = htmlentities($a->data_status); //encoding string
            $products_fkid = htmlentities($a->products_fkid); //encoding string
            $total = "<input type='hidden' id='totalretur' name='totalretur[$id]' value='$a->total'/><span id='totalretur1'>".$a->total."</span>";//encoding string
            $disType = $a->disc_type != "percent" ? $a->discount : $a->discount."%";
            $discount ="<input type='hidden' id='discountretur' data-disType='".$a->disc_type."' data-disVal='".$a->discount."' name='discountretur[$id]' value='$a->discount'/>".$disType; //encoding string
            $tot_dis = "<input type='hidden' class='tot_disretur' name='tot_disretur[$id]' value='$a->tot_dis' /><input type='hidden' class='produk' name='produk[$id]' value='".$id."' /><span id='totdis1'>".$a->tot_dis."</span>";//encoding string
            $tax = $a->tax_type != "percentage" ? $a->tax : $a->tax."%";
            $total_pajak =" <span id='pajak_retur' data-type='".$a->tax_type."' data-val='".$a->tax."'>".$a->tax_name." ".$tax."</span>";
            $retur = htmlentities($a->retur); //encoding string
            $invoice = htmlentities($a->invoice); //encoding string
            $hutang = htmlentities($a->hutang); //encoding string
            $unit_name = htmlentities($a->unit_name); //encoding string
            $unit_description = htmlentities($a->unit_description); //encoding string
            $products_name = htmlentities($a->products_name); //encoding string


            $button = "<input type='hidden' id='id_purchaseProduct$id' name='id_purchaseProduct' value='".$id."' /><button type='button' class='retur btn-primary' style='border-color: transparent; color:#000'>Retur</button>";
            $temp_data = array(
                'purchase_products_id' => $id,
                'purchase_fkid' => $purchase_fkid,
                'unit_fkid_nota' => $unit_fkid_nota,
                'qty_nota' => $qty_nota,
                'tot_dis' => $tot_dis,
                'price_nota' => $price_nota,
                'unit_fkid_stok' => $unit_fkid_stok,
                'qty_stok' => $qty_stok,
                'price_stok' => $price_stok,
                'data_status' => $data_status,
                'products_fkid' => $products_fkid,
                'total' => $total,
                'discount' => $discount,
                'retur' => $retur,
                'invoice' => $invoice,
                'hutang' => $hutang,
                'unit_name' => $unit_name,
                'unit_description' => $unit_description,
                'products_name' => $products_name." ".$a->variant,
                'retur' => $retur,
                'qty_confrim' => $a->qty_confrim,
                'button' => $button,
                'keterangan' => $a->keterangan,
                'pajak' => $total_pajak,
                'depreciation' => $a->depreciation."/ M",
                'expired'=>$a->expired
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function detail($id)
    {
        header('Content-Type: application/json');
        $jsondata = $this->Purchase_products_model->json($id); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {

            $id = $a->purchase_products_id;
            $purchase_fkid = htmlentities($a->purchase_fkid); //encoding string
            $unit_fkid_nota = htmlentities($a->unit_fkid_nota); //encoding string
            $qty_nota =formatDesimal($a->qty_nota,1); //encoding string
            $price_nota = $a->price_nota; //encoding string
            $unit_fkid_stok = htmlentities($a->unit_fkid_stok); //encoding string
            $qty_stok = formatDesimal($a->qty_stok,1);
            $price_stok = $a->price_stok;
            $data_status = htmlentities($a->data_status); //encoding string
            $products_fkid = htmlentities($a->products_fkid); //encoding string
            $total = $a->total;//encoding string
            $disType = $a->disc_type != "percent" ? $a->discount : $a->discount."%";
            $discount =$disType; //encoding string
            $tot_dis = $a->tot_dis;//encoding string
            $tax = $a->tax_type != "percentage" ? $a->tax : $a->tax."%";
            $total_pajak =" <span id='pajak_retur' data-type='".$a->tax_type."' data-val='".$a->tax."'>".$a->tax_name." ".$tax."</span>";
            $retur = htmlentities($a->retur); //encoding string
            $invoice = htmlentities($a->invoice); //encoding string
            $hutang = htmlentities($a->hutang); //encoding string
            $unit_name = htmlentities($a->unit_name); //encoding string
            $unit_description = htmlentities($a->unit_description); //encoding string
            $products_name = htmlentities($a->products_name); //encoding string


            $button = "<input type='hidden' id='id_purchaseProduct$id' name='id_purchaseProduct' value='".$id."' /><button type='button' class='retur btn-primary' style='border-color: transparent; color:#000'>Retur</button>";
            $temp_data = array(
                'purchase_products_id' => $id,
                'purchase_fkid' => $purchase_fkid,
                'unit_fkid_nota' => $unit_fkid_nota,
                'qty_nota' => $qty_nota,
                'tot_dis' => formatUang($tot_dis),
                'price_nota' => formatUang($price_nota),
                'unit_fkid_stok' => $unit_fkid_stok,
                'qty_stok' => $qty_stok,
                'price_stok' => formatUang($price_stok),
                'data_status' => $data_status,
                'products_fkid' => $products_fkid,
                'total' => formatUang($total),
                'discount' => $discount,
                'retur' => $retur,
                'invoice' => $invoice,
                'hutang' => formatUang($hutang),
                'unit_name' => $unit_name,
                'unit_description' => $unit_description,
                'products_name' => $products_name." ".$a->variant,
                'retur' => $retur,
                'qty_confrim' => formatDesimal($a->qty_confrim,1),
                'button' => $button,
                'keterangan' => $a->keterangan,
                'pajak' => $total_pajak,
                'depreciation' => $a->depreciation."/ M",
                'expired'=>$a->expired
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }


    //fungsi untuk detail tabel
    public function getDetail($id)
    {
    //ambil purchase confrim where id
        header('Content-Type: application/json');
        $data = $this->purchase_confrim->get_by_id_retur($id);
        $dataConfrim = json_decode($data);
        $confrimData = array();
        foreach ($dataConfrim as $b) {
            $qty= $b->qty;
            $id_conf= $b->id;
            if ($qty>0) {
                $temp_data= array(
                    'qty' => "<input type='hidden' value=".$qty." id='qty_confrimRetur' name='qty_confrimRetur[".$id."][".$id_conf."]'/><span>".$qty."</span><br>",

                    'date_created' => millis_to_localtime('Y-m-d H:i:s',$b->date_created),

                    'retur' => "<input type='number' step='0.01' onchange='jumlah_retur(this)' min='0' max='$qty' style='width:75px;' name='jumlah[".$id."][".$id_conf."]' class='inputJumlah$id form-control'/><input type='hidden' value=".$id_conf." id='confrim_id' name='confrim_id[".$id_conf."]'/><input type='hidden' value=".$id_conf." id='purchase_product_id' name='purchase_product_id[$id]'/><span>*Input Qty Retur</span>"
                );
            }else{
                $temp_data= array(
                    'qty' => "<span>".$qty."</span><br>",
                    'date_created' => millis_to_localtime('Y-m-d H:i:s',$b->date_created),
                    'retur' => "All Returned"
                );
            }
            array_push($confrimData, $temp_data);
        };

        $draw_json = array(
            'data' => $confrimData
        );
        echo json_encode($draw_json);
    }


    //insert purchase
    public function insert()
    {
      $env = getenv('CI_ENV');
      if (!empty($_ENV['CI_ENV'])) {
        $env = $_ENV['CI_ENV'];
      }
      if($env === '') {
        $env = 'development';
      }
      $topic = 'purchase_'.$env;
      //$log = " CI_ENV ".$_ENV['CI_ENV']." | getenv ".getenv('CI_ENV')."  \n";
      //file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." environment => $log  \n");

        //proses post
        $this->db->trans_start();//jika gagal di jalan maka akan di roll back
        $data = array(
            'invoice' => $this->input->post('invoice'),
            'keterangan' => $this->input->post('keterangan'),
            'bayar' => preg_replace("/[A-Za-z.,]/", "",$this->input->post('bayar')),
            'outlet_fkid' => $this->input->post('select_outlet'),
            'supplier_fkid' => $this->input->post('supplier'),
            'grand_total' => $this->input->post('grand_total'),
            'hutang' => $this->input->post('hutang'),
            'discount_total' => $this->input->post('discount_total'),
            'total_pajak' => $this->input->post('total_pajak'),
            'sub_total' => $this->input->post('sub_total'),
            'status_lunas' => $this->input->post('status_lunas'),
            'jatuh_tempo' => $this->input->post('jatuh_tempo'),
            'type_discount' => $this->input->post('type_discount'),
            'pay_type' => $this->input->post('pay_type'),
            'payment_media_bank_fkid' => $this->input->post('bank'),
            'shift_fkid' => $this->input->post('shift_fkid'),
            'data_created' => current_millis()
        );
        $response = $this->Purchase_model->insert($data); //insert ke table purchse
        $last_id = $this->db->insert_id();
        // update invoice jika invoicenya kosong
        // PRC/[3 digit nama suplier]/[bulan]+[id]
        if (!$data['invoice']) {
            $supplier = $this->db->get_where('supplier',['supplier_id' => $data['supplier_fkid']])->row();
            $month = millis_to_localtime('m',$data['data_created']);
            $invoice = "PCR-".substr(strtoupper($supplier->name), 0, 3).$month.$last_id;
            $data['invoice'] = $invoice;
            $this->db->where('purchase_id', $last_id);
            $this->db->update('purchase', ['invoice'=>$invoice]);
        }

        if ($response['status_insert']===true) {
            $primary_key = $response['primary_key']; //id terakhir setelah insert
            $i=0;//index baris
            $jml_row= $this->input->post('nomor[]');
            $data['detail']=[];
            // $data['detail']['diterima']='';
            foreach ($jml_row as $row) {
                $data_detail=array(
                    'purchase_fkid' => $primary_key,
                    'unit_fkid_nota' => $this->input->post('unit_nota['.$i.']'),
                    'qty_nota' => $this->input->post('qtydi_nota['.$i.']'),
                    'price_nota' => $this->input->post('harga_nota['.$i.']'),
                    'unit_fkid_stok' => $this->input->post('unit_stock['.$i.']'),
                    'qty_stok' => $this->input->post('qtydi_stock['.$i.']'),
                    'price_stok' => $this->input->post('hargadi_stock['.$i.']'),
                    'products_fkid' => $this->input->post('name['.$i.']'),
                    'discount' => $this->input->post('discount['.$i.']'),
                    'total' => $this->input->post('total['.$i.']'),
                    'tot_dis' => $this->input->post('tot_dis['.$i.']'),
                    'retur' => $this->input->post('retur['.$i.']'),
                    'depreciation' => $this->input->post('depreciation['.$i.']'),
                    'expired' => $this->input->post('expired['.$i.']'),
                    'gratuity_fkid' => $this->input->post('gratuity_fkid['.$i.']'),
                    'tax' => $this->input->post('tax['.$i.']'),
                    'tax_type' => $this->input->post('tax_type['.$i.']'),
                    'disc_type' => $this->input->post('discount_type['.$i.']'),
                    );
                $response = $this->Purchase_products_model->insert($data_detail); //insert ke table purchse_product
                $purchase_product_id = $this->db->insert_id();
                $dataConfrim = array(
                    'purchase_product_fkid' =>$purchase_product_id,
                    'qty' => $this->input->post('diterima['.$i.']'),
                    'qty_notConfrim' => $this->input->post('qtydi_stock['.$i.']') - $this->input->post('diterima['.$i.']') ,
                    'qty_arive' => /*$this->input->post('qtydi_stock['.$i.']') - */ $this->input->post('diterima['.$i.']') ,
                    'user' => $this->session->userdata('user_name'),
                    'date_created' =>current_millis(),
                    'date_updated' =>current_millis(),
                );
                if ($this->session->userdata('user_type')=='employee') {
                    $dataConfrim['employe_fkid'] = $this->session->userdata('user_id');
                }
                $confrim = $this->purchase_confrim->insert($dataConfrim);//inseet ke tabel purchase_confrim
                $confrimId = $this->purchase_confrim->fetch_last_insert_id();

                $this->db->trans_complete();
                $i++;

                //CALL PUB SUB
                $this->pubsub->publish($topic, json_encode(array('purchase_product_fkid' => $purchase_product_id, 'qty' => $dataConfrim['qty_arive'], 'purchase_confirm_id' => $confrimId)));

                // SEND DATA INTO FINANCE_JURNAL_UMUM
                $data_detail['diterima']=$dataConfrim['qty_arive'];
                array_push($data['detail'],$data_detail);
                
            }
            if(getenv('CI_ENV') !== 'production' && getenv('CI_ENV') !== 'staging'){
                $this->set_jurnal->add_to_jurnal_purchase($data);
            }            
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    //Get data retur
    public function retur($id)
    {
        $row = $this->Purchase_model->get_by_id($id);

        if ($row) {
            $data = array(
                    'id' => set_value('purchase_id', $row->purchase_id),
                    'invoice' => set_value('invoice', $row->invoice),
                    'keterangan' => set_value('keterangan', html_entity_decode($row->keterangan)),
                    'supplier' => set_value('supplier_fkid', $row->supplier_fkid),
                    'bayar' => set_value('bayar', $row->bayar),
                    'total_bayar' => set_value('total_bayar', $row->total_bayar+$row->bayar),
                    'outlet' => set_value('outlet_fkid', $row->outlet_fkid),
                    'grand_total' => set_value('grand_total', $row->grand_total),
                    'hutang' => set_value('hutang', $row->hutang *-1),
                    'discount_total' => set_value('discount_total', $row->discount_total),
                    'total_pajak' => set_value('total_pajak', $row->total_pajak),
                    'jatuh_tempo' => set_value('jatuh_tempo', $row->jatuh_tempo),
                    'sub_total' => set_value('sub_total', $row->sub_total),
                    'jenis' => set_value('type_discount', $row->type_discount),
                    'payment_media_bank_fkid' => set_value('payment_media_bank_fkid', $row->payment_media_bank_fkid),
                    'pay_type' => set_value('pay_type', $row->pay_type." ".$row->bank_name),
                    'date' => set_value('data_created', millis_to_localtime('Y-m-d H:i:s',$row->data_created)),
                    'status_lunas' => set_value('status_lunas',$row->status_lunas),
                    'outlet_name'=>$row->outlet_name,
                    'supplier_name'=>$row->supplier_name,
                    'bank_name'=>$row->bank_name,

                );
            $dataArray = array(
                    'status' => 'success',
                    'data' => $data
                );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo json_encode($dataArray);
    }

    //insert data retur
    public function action()
    {
      $env = getenv('CI_ENV');
      if (!empty($_ENV['CI_ENV'])) {
        $env = $_ENV['CI_ENV'];
      }
      $topic = 'purchase_retur_'.$env;
      $pubsub_data = [];

        $this->db->trans_start();//jika gagal di jalan maka akan di roll back
        $i=$this->input->post('id_purchase_retur');
        $id_confrim = $this->input->post('confrim_id[]');
        $purchase_product_id = $this->input->post('purchase_product_id[]');
        //action untuk update data
        foreach ($purchase_product_id as $index => $value) {
            $dataPurchaseFromRetur = $this->Purchase_products_model->get_by_id($index);//ambil data to insert retur
            $data = array(
                'retur_product_id'=>'',
                'qty_retur'=> array_sum($this->input->post('jumlah['.$index.'][]')),
                'purchase_product_fkid' => $index , //purchase_ingridient
                'keterangan_retur' => $_POST ['keterangan_retur'],
                'qty_nota'=> $dataPurchaseFromRetur->qty_nota,//$this->input->post('qty_notaretur['.$index.']'),
                'qty_stok'=> $dataPurchaseFromRetur->qty_stok,//$this->input->post('qty_stockToretur['.$index.']'),
                'total'=> $dataPurchaseFromRetur->total,  //$this->input->post('totalretur['.$index.']'),
                'tot_dis'=> $dataPurchaseFromRetur->tot_dis, //$this->input->post('tot_disretur['.$index.']'),
                'harga_stok'=> $dataPurchaseFromRetur->price_stok, //$this->input->post('harga_stok['.$index.']'),
            );
            $response = $this->Retur_products_model->insert($data); //insert into tabel retur product
            $returId = $this->Retur_products_model->fetch_last_insert_id();

            if ($response==TRUE) {
                $id_barang = array(
                    'retur'=>'yes',
                    'qty_stok'=> $this->input->post('qty_stockToretur['.$index.']'),
                    'price_stok'=> $this->input->post('harga_stok['.$index.']'),
                    'qty_nota'=> $this->input->post('qty_notaretur['.$index.']'),
                    'total'=> $this->input->post('totalretur['.$index.']'),
                    'tot_dis'=> $this->input->post('tot_disretur['.$index.']'),
                    'discount'=> $this->input->post('discountretur['.$index.']'),
                );
                // print_r($id_barang);die();
                $response = $this->Purchase_products_model->update($index, $id_barang);//update ke tabel purchase product

                $id = $this->input->post('jumlah['.$index.'][]');
                foreach ($id as $a =>$val) {
                       //data update confrim
                    if (!empty($val) && is_numeric($val)) {
                        $qty_retur = $this->input->post('jumlah['.$index.']['.$a.']');
                        $qty_confrim = $this->input->post('qty_confrimRetur['.$index.']['.$a.']');
                        $retur = $qty_confrim - $qty_retur;
                            if ($retur<0) {
                                $retur=0;
                            }
                        $data_confrim = array(
                            'qty' => $retur,
                            'retur' => $qty_retur,
                        );
                        $response = $this->purchase_confrim->update($a,$data_confrim);//update table confrim

                        array_push($pubsub_data, array('purchase_product_fkid' => $index, 'qty' => $data['qty_retur'], 'retur_product_id' => $returId, 'purchase_confirm_id' => $a));
                    }
                }
            }
        }
        //input tiable purchase start
        if ($response==TRUE) {
           $id_purchase= array(
                'sub_total'=> $this->input->post('sub_totall_retur'),
                'grand_total'=> $this->input->post('grand_totall_retur'),
                'hutang'=> $this->input->post('hutang_retur'),
                'status_lunas'=> $this->input->post('status_lunass_retur'),
                'discount_total'=> $this->input->post('discount_totall'),
            );
           if ($id_purchase['hutang']>=0) {
               $id_purchase['jatuh_tempo']=null;
           }
           $response = $this->Purchase_model->update($i,$id_purchase);
        }//input purchase end
        $this->db->trans_complete();

        if ($response==TRUE) {
            $messageUpdateSuccess = $this->session->set_flashdata('message', ' Success');
            $response = array(
                'status' => 'success',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            );

            foreach ($pubsub_data as $data) {
              $this->pubsub->publish($topic, json_encode($data));
            }
        }
        else{
            $messageUpdateSuccess = $this->session->set_flashdata('message', ' fail');
        }

       header('Content-Type: application/json');
       echo json_encode($response);
    }

    public function saveSession($key, $value){
        $this->session->set_userdata($key,$value);
    }

    public function last_product_purchase()
    {
        $id = $this->input->get('id');
        $data = $this->db->order_by('purchase_products_id', 'desc')
        ->limit(1)
        ->get_where('purchase_products',['products_fkid'=>$id])
        ->row();
        return $this->response_json($data);
    }

}

/* End of file Ingridients.php
/* Location: ./application/controllers/purchase/purchase/Ingridients.php */
