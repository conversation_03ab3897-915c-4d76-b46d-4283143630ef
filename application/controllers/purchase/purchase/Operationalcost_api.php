<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Operationalcost_api extends CI_Controller {

    
    public function __construct()
    {
        parent::__construct();
        
        $this->load->model('finance/M_set_jurnal_umum');
        $this->load->model('purchase/Operationalcost_model');
    }

    public function jurnal_opcost()
    { 
        $jsonBody = json_decode($this->input->raw_input_stream, true);
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> receive pubsub operationalcost :".json_encode($jsonBody)." \n"); 

        $data = $jsonBody['message']['data'];
        $body = json_decode(base64_decode($data));                
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> data :".($data)." | body: ".json_encode($body)." \n"); 

        $id = $body->operationalcost_id;
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> id : '$id' \n"); 
        
        // validate the data (if exist)
        $operational = $this->Operationalcost_model->get_by_id($id);
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> data : ".json_encode($operational)." \n"); 

        if(is_null($operational)){
            file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> [ERROR OCCURED] receiving pubsub operationa cost, but no data found! id: '$id' \n"); 
            return;
        }

        $adminId = $operational->user_fkid;
        $newdata = array(
            'admin_id'  => $operational->user_fkid,
        );    
        $this->session->set_userdata($newdata);
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> adminId: '".$this->session->userdata('admin_id')."' \n"); 

        // $id = $this->input->post('id');
        $this->M_set_jurnal_umum->jurnal_oprasional($id);        
    }
    

}

/* End of file Controllername.php */
