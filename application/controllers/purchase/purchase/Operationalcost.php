<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Operationalcost extends Auth_Controller {


	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('purchase/Operationalcost_model');
        $this->load->model('purchase/supplier/Supplier_model');
        $this->load->model('outlet/Outlets_model');
        $this->load->model('products/ingridients/Purchase_report_category_model','purchase_category');
        $this->load->model('outlet/Shift_model', 'Shift_model');
        $this->load->model('outlet/Paymentmedia_bankaccount_model', 'Bankaccount_model');
        $this->load->model('products/products_subcategory/Products_subcategory_model', 'sub_category');
        $this->load->model('finance/M_set_jurnal_umum');
        
    }

	public function index()
	{//print_r($this->session->userdata());die();

		$link = current_url(); //URL dengan slash
        $data = array(
            'kolomID' => 'operationalcost_id', //nama kolom primary key pada tabel
            'currentURL' => $link, //link yang sedang diakses
            'ajaxActionDatatableList' => $link.'/json',
            'ajaxActionDelete' => $link.'/delete/',
            'ajaxActionCreate' => $link.'/action/create',
            'ajaxActionUpdate' => $link.'/action/update',
            'ajaxActiongetDataEdit' => $link.'/edit/' //ambil data yang akan diedit
        );
        $data['form_select_supplier'] = $this->Supplier_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_category'] = $this->purchase_category->form_prc();
        $data['form_select_shift'] = $this->Shift_model->form_select();
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();
        $data['form_select_sub_category'] = $this->sub_category->form_select();

		// $data['form_select_outlet'] = $this->Outlets_model->form_select();
        // outlet employee by employee
        $data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
        $data['page_title'] = 'Operational Cost';
        $this->template->view('purchase/purchase/oprationalcost/operationalcost_v2', $data);
	}

	public function json($outlet,$startDate,$endDate)
	{
		header('Content-Type: application/json');        
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__."\n\n----> ENABLE_FINANCE: ".getenv('ENABLE_FINANCE')." | on env: ".getenv('CI_ENV')."\n");

		/* custom json output  start */
        $jsondata = $this->Operationalcost_model->json($outlet,$startDate,$endDate); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->operationalcost_id;
        	$opcost_name = htmlentities($a->opcost_name); //encoding string
        	$data_created = $a->data_created; //encode string
        	$qty = htmlentities($a->qty); //encode string
        	$harga = htmlentities($a->harga); //encode string
        	$total = htmlentities($a->total); //encode string
            $keterangan = htmlentities($a->keterangan); //encode string
            $admin_name = htmlentities($a->admin_name); //encode string
        	$outlets_name = htmlentities($a->outlets_name); //encode string
        	$data_status = htmlentities($a->data_status);//encode
            $data_created = htmlentities($a->data_created);//encode
            $category = htmlentities($a->category);//encode
        	$shift = htmlentities($a->shift);//encode

            $supplier_name = $a->supplier_name;

        	$action  = '<button class="btn btn-xs btn-warning" onclick="actionEdit('.$id.')" data-toggle="tooltip" data-placement="top" title="Edit"><i class="fa fa-edit"></i></button>';
            $action .= "&nbsp;";
            $action .= '<button class="btn btn-xs btn-danger" onclick="actionDelete('.$id.')" data-toggle="tooltip" data-placement="top" title="Hapus"><i class="fa fa-trash"></i></button>';

        	$temp_data = array(
        		'operationalcost_id' => $id,
        		'opcost_name' => $opcost_name,
        		'data_created' => $data_created,
        		'qty' => formatAngka($qty),
        		'harga' => formatUang($harga),
                'total' => formatUang($total),
        		'supplier_name' => $supplier_name,
                'keterangan' => $keterangan,
                'admin_name' => $admin_name,
                'outlets_name' => $outlets_name,
                'category' => $category,
        		'shift' => $shift,
                'action' => $action,
                'subcategory' => $a->subcategory,
        		'depreciation' => $a->depreciation,
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
	}

    public function api(){
        echo "success";
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> receive pubsub operationalcost :".json_encode($this->input->post())." \n"); 
    }

    public function action($actionType=null) //actionType = 'create / update'
    {

         $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'opcost_name' => form_error('opcost_name'),
                        'qty' => form_error('qty'),
                        'harga' => form_error('harga'),
                        'total' => form_error('total'),
                        'keterangan' => form_error('keterangan'),
                        'supplier_name' => form_error('supplier'),
                        'outlet_name' => form_error('outlet'),
                        'category' => form_error('category'),
                        'shift_id' => form_error('shift_id'),
                        'payment' => form_error('payment'),
                        'bank' => form_error('bank'),
                        'sub_category_fkid' => form_error('sub_category_fkid'),
                        'depreciation' => form_error('depreciation'),
                    )
                );
        } else {
            //data yang dikirim
            $timeZone = $this->input->post('timeZone');
            
            $data = array(
                'opcost_name' => $this->input->post('opcost_name',TRUE),
                'data_created' => $this->input->post('dateform',TRUE),
                'qty' => $this->input->post('qty',TRUE),
                'harga' => preg_replace('/[A-Za-z,.]+/','',$this->input->post('harga',TRUE)),
                'total' => $this->input->post('total', TRUE),
                'keterangan' => $this->input->post('keterangan',TRUE),
                'supplier_fkid' => $this->input->post('supplier_id',TRUE),
                'outlet_fkid' => $this->input->post('outlet',TRUE),
                'prc_category_fkid' => $this->input->post('category',TRUE),
                'shift_fkid' => $this->input->post('shift_id',TRUE),
                'payment' => $this->input->post('payment',TRUE),
                'sub_category_fkid' => $this->input->post('sub_category_fkid',TRUE),
                'depreciation' => $this->input->post('depreciation',TRUE),
            );
            if ($data['payment']=='cash') {
                $data['payment_bank_fkid'] = null;
            }else{
                $data['payment_bank_fkid'] = $this->input->post('bank',TRUE);
            }


            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Operationalcost_model->insert($data);
                $id = $this->db->insert_id();
                if(getenv('ENABLE_FINANCE') == 'true' || (getenv('CI_ENV') !== 'production' && getenv('CI_ENV') !== 'staging')){
                    $this->M_set_jurnal_umum->jurnal_oprasional($id,$timeZone);
                }  
                
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->Operationalcost_model->update($this->input->post('opcost_id', TRUE), $data);
                // delete jurnal umum
                $id = $this->input->post('opcost_id');
                $this->db->where('trans_type', 13);
                $this->db->where("SUBSTRING_INDEX(trans_id,'.', -1) = ".$id);
                $this->db->where('admin_fkid', $this->session->userdata('admin_id')); 
                $this->db->delete('finance_jurnal_umum');

                // insert jurnal baru
                if(getenv('ENABLE_FINANCE') == 'true' || (getenv('CI_ENV') !== 'production' && getenv('CI_ENV') !== 'staging')){
                    $this->M_set_jurnal_umum->jurnal_oprasional($id,$timeZone);                
                }
                
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
        $row = $this->Operationalcost_model->get_by_id($id);

        if ($row) {
            $data = array(
                'operationalcost_id' => set_value('operationalcost_id', $row->operationalcost_id),
                'data_created' => set_value('dateform', $row->data_created),
                'opcost_name' => set_value('opcost_name', $row->opcost_name),
                'qty' => set_value('qty', $row->qty),
                'harga' => set_value('harga', $row->harga),
                'total' => set_value('total', $row->total),
                'supplier_fkid' => set_value('supplier_fkid', $row->supplier_fkid),
                'keterangan' => set_value('keterangan', $row->keterangan),
                'user_fkid' => set_value('user_fkid', $row->user_fkid),
                'outlet_fkid' => set_value('outlet_fkid', $row->outlet_fkid),
                'category' => set_value('prc_category_fkid', $row->prc_category_fkid),
                'shift_id' => set_value('shift_id', $row->shift_fkid),
                'payment' => set_value('payment', $row->payment),
                'bank' => set_value('bank', $row->payment_bank_fkid),
                'sub_category_fkid' => set_value('sub_category_fkid', $row->sub_category_fkid),
                'depreciation' => set_value('depreciation', $row->depreciation),
            );
            $dataArray = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo json_encode($dataArray);
    }

    public function delete($id=null)
    {
        $row = $this->Operationalcost_model->get_by_id($id);

        if ($row) {
            $response = $this->Operationalcost_model->delete($id);

            // delete jurnal umum
            $id = $this->input->post('opcost_id');
            $this->db->where('trans_type', 13);
            $this->db->where("SUBSTRING_INDEX(trans_id,'.', -1) = ".$id);
            $this->db->where('admin_fkid', $this->session->userdata('admin_id')); 
            $this->db->delete('finance_jurnal_umum');

            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
        $this->form_validation->set_rules('opcost_name', 'opcost_name', 'required|max_length[50]');
        $this->form_validation->set_rules('qty', 'qty', 'required|integer|max_length[100]');
        // $this->form_validation->set_rules('harga', 'harga', 'required|integer|max_length[100]');
        $this->form_validation->set_rules('keterangan', 'keterangan', 'required|max_length[50]');
        $this->form_validation->set_rules('supplier_id', 'supplier_id', 'required|max_length[50]');
        $this->form_validation->set_rules('category', 'category', 'required');
        $this->form_validation->set_rules('shift_id', 'shift_id', 'required');
        $this->form_validation->set_rules('outlet', 'outlet', 'required|max_length[15]'); //masih bug.. cari validation date
        $this->form_validation->set_rules('payment', 'payment', 'required'); //masih bug.. cari validation date
        $this->form_validation->set_rules('bank', 'payment_bank_fkid', 'required'); //masih bug.. cari validation date
        $this->form_validation->set_rules('sub_category_fkid', 'sub_category_fkid', 'integer|required');
        $this->form_validation->set_rules('depreciation', 'depreciation', 'integer|max[10]');

        $this->form_validation->set_rules('id', 'opcost_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }



}

/* End of file Operationalcost.php */
/* Location: ./application/controllers/purchase/purchase/Operationalcost.php */
