<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Supplier extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
        //proteksi halaman
		$this->load->model('purchase/supplier/Supplier_model');
	}

	public function index()
	{
		$link = site_url('purchase/supplier/'); //URL dengan slash
        $data = array(
            'kolomID' => 'supplier_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
        );

		// $this->load->view('purchase/supplier/supplier_v', $data);
        //template
        $data['page_title'] = 'Supplier';
        $this->template->view('purchase/supplier/supplier_v2', $data);
	}

	public function json()
    {
        /* custom json output  start */
        $jsondata = $this->Supplier_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->supplier_id;
        	$name = htmlentities($a->name); //encoding string
        	$address = htmlentities($a->address); //encoding string
        	$city = htmlentities($a->city); //encoding string
        	$phone = htmlentities($a->phone); //encoding string
        	$fax = htmlentities($a->fax); //encoding string
        	$email = htmlentities($a->email); //encoding string
        	$npwp= htmlentities($a->npwp); //encoding string
            $tempo = htmlentities($a->tempo); //encoding string
        	$type = htmlentities($a->type); //encoding string
        	$action  = '<button class="btn btn-xs btn-warning btn-edit" onclick="actionEdit('.$id.')" data-toggle="tooltip" data-placement="top" title="Edit"><i class="fa fa-edit"></i></button>';
        	$action .= "&nbsp";
        	$action .= '<button class="btn btn-xs btn-danger btn-edit" onclick="actionDelete('.$id.')" data-toggle="tooltip" data-placement="top" title="Hapus"><i class="fa fa-trash"></i></button>';
        	
        	
        	$temp_data = array(
        		'supplier_id' => $id,
        		'name' => $name,
        		'address' => $address,
        		'city' => $city,
        		'phone' => $phone,
        		'fax' => $fax,
        		'email' => $email,
        		'npwp' => $npwp,
                'tempo' => $tempo." ".$type,
        		'type' => $type,
        		'action' => $action
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                    'name' => form_error('name'),
                    'address' => form_error('address'),
					'city' => form_error('city'),
					'phone' => form_error('phone'),
					'fax' => form_error('fax'),
					'email' => form_error('email'),
					'npwp' => form_error('npwp'),
                    'keterangan' => form_error('keterangan'),
					'tempo' => form_error('tempo')
                )
            );
        } else {
            //data yang dikirim
            $data = array(
                'name' => $this->input->post('name',TRUE),
				'address' => $this->input->post('address',TRUE),
				'city' => $this->input->post('city',TRUE),
				'phone' => $this->input->post('phone',TRUE),
				'fax' => $this->input->post('fax',TRUE),
				'email' => $this->input->post('email',TRUE),
				'npwp' => $this->input->post('npwp',TRUE),
                'keterangan' => $this->input->post('keterangan', TRUE),
                'tempo' => $this->input->post('tempo',TRUE),
				'type' => $this->input->post('type',TRUE),
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Supplier_model->insert($data);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->Supplier_model->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
    	$row = $this->Supplier_model->get_by_id($id);
        if ($row) {
            $data = array(
        		'id' => $row->supplier_id,
        		'name' => htmlentities($row->name),
        		'address' => htmlentities($row->address),
        		'city' => htmlentities($row->city),
        		'phone' => $row->phone,
        		'fax' => $row->fax,
        		'email' => $row->email,
        		'npwp' => htmlentities($row->npwp),
                'keterangan' => htmlentities($row->keterangan),
        		'tempo' => set_value('tempo', html_entity_decode($row->tempo)),  
                'type' => $row->type                  
        	);

            $draw_json = array(
            	'status' => 'success',
            	'data' => $data
            );
        } else {
        	$draw_json = array(
        		'status' => 'error',
        		'message' => 'Record Not Found'
    		);
        }
        echo format_json($draw_json);
    }

    public function delete($id=null)
    {
    	$row = $this->Supplier_model->get_by_id($id);

        if ($row) {
            $response = $this->Supplier_model->delete($id);
            if ($response==true) {
            	$draw_json = array(
            		'status' => 'success',
            		'message' => 'Record Deleted'
            	);
            } else {
            	$draw_json = array(
            		'status' => 'error',
            		'message' => 'Delete Record Failed'
            	);
            }
        } else {
            $draw_json = array(
            	'status' => 'error',
            	'message' => 'Record Not Found'
            );
        }

        //response output
        echo format_json($draw_json);
    }

    public function _rules() 
    {
    	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[50]');
    	$this->form_validation->set_rules('address', 'address', 'trim|required|max_length[100]');
    	$this->form_validation->set_rules('city', 'city', 'trim|required|max_length[50]');
    	$this->form_validation->set_rules('phone', 'phone', 'trim|required|max_length[15]|is_numeric');
    	$this->form_validation->set_rules('fax', 'fax', 'trim|max_length[15]|is_numeric');
    	$this->form_validation->set_rules('npwp', 'npwp', 'trim|max_length[30]');
    	$this->form_validation->set_rules('tempo', 'tempo', 'trim|required'); //date verifikasi (belum nemu)
    	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Supplier.php */
/* Location: ./application/controllers/purchase/Supplier.php */