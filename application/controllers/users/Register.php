<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Register extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->protect_page->userlogged(FALSE); //halaman hanya bisa diakses bila tidak login

		$this->load->model('account/Register_model');
		$this->load->model('outlet/Outlets_model');
		$this->load->helper('sendemail');
		$this->load->library('user_key/User_key_lib');
	}

	public function _index()
	{
		//redirect ke home (sementara)
		$site = site_url();
		$site = str_replace('client', 'www', $site);
		redirect($site);die();

		$this->load->view('users/register_v');
	}

	public function _proses()
	{
		//inisialisasi
		$registered_email = false;


		$datapost = array(
			'name' => $this->input->post('name'),
			'email' => $this->input->post('email'),
			'phone' => $this->input->post('phone'),
			'password' => $this->input->post('password'),
			'outlet' => $this->input->post('outlet'),
			'address' => $this->input->post('address'),
			'postal_code' => $this->input->post('postal_code'),
			//'timezone' => $this->input->post('timezone')
		);
		$email = $datapost['email'];

		//validasi input CI
		$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
		$this->form_validation->set_rules('phone', 'phone', 'trim|required|max_length[15]|is_numeric');
		$this->form_validation->set_rules('password', 'password', 'trim|required|min_length[5]');

		$this->form_validation->set_rules('outlet', 'outlet name', 'trim|required|max_length[50]');
		$this->form_validation->set_rules('address', 'address', 'trim|required|max_length[100]');
		$this->form_validation->set_rules('postal_code', 'postal code', 'trim|required|max_length[6]');
		
		//$this->form_validation->set_rules('timezone', 'timezone', 'trim|required');
		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

		//cek email sudah dipakai atau belum
		$cek_email = $this->Register_model->cek_email($email,'admin');
		$registered_email = ($cek_email) ? true : false;
		if ($registered_email==false) {
			$cek_email = $this->Register_model->cek_email($email,'employee');
			$registered_email = ($cek_email) ? true : false;
		}

		//cek timezone benar atau tidak
		//$data_timezone_valid = isValidTimezoneId($datapost['timezone']);

		//validasi
		if ($this->form_validation->run() == FALSE || $registered_email==true) {
			$error_email = (!empty(form_error('email'))) ? form_error('email') : '';
			$error_email = (empty($error_email) && $registered_email==true) ? '<span class="text-danger">Email already registered.</span>' : $error_email;

			$draw_json = array(
				'status' => 'error',
				'message' => 'Registration Failed',
				'data' =>  array(
					'name' => form_error('name'),
					'email' => $error_email, //form_error('email'),
					'phone' => form_error('phone'),
					'password' => form_error('password'),
					'outlet' => form_error('outlet'),
					'address' => form_error('address'),
					'postal_code' => form_error('postal_code'),
					//'timezone' => ($data_timezone_valid==false) ? 'Invalid Time Zone' : form_error('timezone'),
				)
			);
		} else {
			//insert DB
			$data_insert = array(
				'name' => $datapost['name'],
				'email' => $datapost['email'],
				'password' => $datapost['password'],
				'phone' => $datapost['phone'],
			);
			$response = $this->Register_model->insert_admin($data_insert);
			if ($response) {
				$admin_id = $this->db->insert_id();

				//kirim email
				// $data_content['link_aktivasi'] = 'link aktivasi: ';
				// $kirim_email_content = $this->load->view('email_template', $data_content, TRUE);
				// $kirim_email = sendMail($data_insert['email'], 'Account Activation', $kirim_email_content);
				$kirim_email = $this->user_key_lib->create_activation_key($data_insert['email'],'admin'); //kirim aktivasi email

				//buat session
				$create_session_register = array(
					'user_logged' => TRUE,
					'user_type' => 'admin',
					'admin_id' => $admin_id,
					'user_id' => $admin_id,
					'user_name' => $data_insert['name'],
					'email' => $data_insert['email'],
					'phone' => $data_insert['phone'],
					'photo' => null,
					'photo_url' => nouserphoto(),
					'join_date' => current_millis(),
					//'timezone' => $datapost['timezone'],
					'lastlogin' => current_millis(),
					'activation_status' => 'pending'
				);
				$this->session->set_userdata( $create_session_register );

				//insert outlet (buat outlet)
				$data_insert_outlet = array(
					'name' => $datapost['name'],
					'address' => $datapost['address'],
					'postal_code' => $datapost['postal_code'],
					'phone' => '-',
					'country' => '-',
					'province' => '-',
					'city' => '-',
					//'admin_fkid' => $admin_id
				);
				$this->Outlets_model->insert($data_insert_outlet);

				//buat output json
				$draw_json = array(
					'status' => 'success',
					'message' => 'Registration Success.' .(($kirim_email==true) ? ' Activation link have been sent to your e-mail.' : '')
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Create Record Error'
				);
			}
		}

		echo format_json($draw_json);
	}

	public function pending()
	{
		//datapost
		$datapost = array(
			'name' => $this->input->post('name'),
			'email' => $this->input->post('email'),
			'phone' => $this->input->post('phone'),
			'company' => $this->input->post('company')
		);

		//validasi input
		$this->form_validation->set_rules('name', 'name', 'trim|required');
		$this->form_validation->set_rules('email', 'email', 'trim|required|valid_email|is_unique[quote.email]',
			array(
				'is_unique'     => 'This %s already exists.'
			)
		);
		$this->form_validation->set_rules('phone', 'phone', 'trim|required|numeric');
		$this->form_validation->set_rules('company', 'company', 'trim|required');
		$this->form_validation->set_error_delimiters('','');

		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Invalid Data!',
				'data' => array(
					'name' => form_error('name'),
					'email' => form_error('email'),
					'phone' => form_error('phone'),
					'company' => form_error('company')
				)
			);
		} else {
			//kirim ke api
			$curl = curl_init();
			curl_setopt_array($curl, array(
				CURLOPT_URL => "http://go.uniq.id/join?name=&phone=&email=&company=",
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => "",
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 30,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => "POST",
				CURLOPT_POSTFIELDS => 'name='.$datapost['name'].'&email='.$datapost['email'].'&phone='.$datapost['phone'].'&company='.$datapost['company'],
			));

			$response = curl_exec($curl);
			$err = curl_error($curl);

			curl_close($curl);

			if ($err) {
				// echo "cURL Error #:" . $err;
				$draw_json = array(
					'status' => 'error',
					'message' => $err
				);
			} else {
				$json = json_decode($response);
				if ($json->status==true) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Register Success!'
					);
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Register Failed'
					);
				}
			}
		}

		echo format_json($draw_json);
	}

}

/* End of file Register.php */
/* Location: ./application/controllers/core/Register.php */