<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_activation extends Unauth_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->helper('password');
		$this->load->model('account/Userkey_model');
		$this->load->model('user/Admin_model');
		$this->load->library('google/Pubsub');
	}

	public function index($user_type=null, $key=null, $email=null)
	{
		//init
		$action_link = current_url().'/activation_process';
		$email = urldecode(urldecode($email));

		//cek key valid atau tidak
		$response = $this->_check_key($user_type, $key, $email);
		if ($response===true) {
			$data = array(
				'key'		=> $key,
				'email'		=> $email,
				'user_type' => $user_type,
			);
			$page_content = $this->load->view('users/activation/activation_html_v', $data, TRUE);

			//templating
			$data_template = array(
				'page_title' => 'Account Activation',
				'form_action' => $action_link,
				'page_content' => $page_content
			);
			$this->load->view('template/homepage_v', $data_template, FALSE);
		}
		else{
			$this->session->set_flashdata('message_error', 'Sorry, The Activation Link is Invalid!');
			redirect(site_url());
		}
	}

	public function activation_process()
	{
		//get data post
		$datapost = array(
			'key' => $this->input->post('key'),
			'email' => $this->input->post('email'),
			'user_type' => $this->input->post('user_type'),
			'password' => $this->input->post('password'),
			'password_confirm' => $this->input->post('password_confirm'),
		);

		//cek link aktivasi apakah valid
		$response = $this->_check_key($datapost['user_type'], $datapost['key'], $datapost['email']);
		if ($response===true) {
			//validasi
			$this->form_validation->set_rules('key', 'key', 'trim|required');
			$this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
			$this->form_validation->set_rules('user_type', 'user type', 'trim|required|in_list[admin,employee,agent]');
			$this->form_validation->set_rules('password', 'password', 'trim|required|min_length[8]');
			$this->form_validation->set_rules('password_confirm', 'password confirm', 'trim|required|matches[password]');
			$this->form_validation->set_error_delimiters('<p class="text-danger">', '</p>');

			if ($this->form_validation->run() == FALSE) {
				$this->session->set_flashdata('message_error', validation_errors());
				$link_before = str_replace('/'.$this->uri->segment(5), '', current_url());
				redirect($link_before);
			} else {
				//proses aktivasi
				$response_activation = $this->_activation_process($datapost);
				if ($response_activation['status']=='success') {
					//publish pubsub
					$env = "development";
					if (!empty($_ENV["CI_ENV"])) {
					    $env = $_ENV["CI_ENV"];
					}
					$topic = "account_activation_" . $env;
					$this->pubsub->publish(
					    $topic,
					    json_encode([
					        "email" => $datapost['email'],
					        "user_type" => $datapost['user_type'],
					    ])
					);


					$this->session->set_flashdata('message_success', $response_activation['message']);
				}
				else{
					$this->session->set_flashdata('message_error', $response_activation['message']);
				}
				redirect(site_url());
			}
		}
		else{
			$this->session->set_flashdata('message_error', 'Sorry, Direct Access is not Allowed!');
			redirect(site_url());
		}
	}

	public function _check_key($user_type=null, $key=null, $email=null)
	{
		//init
		$email = urldecode($email);
		$valid = false;
		$i_array=0;
		$i_data=1;
		$user_level=null;
		$datenow = date('Y-m-d H:i:s'); //waktu sekarang


		//ambil key berdasarkan email dan untuk aktivasi
		$this->db->where('key_type', 'activation');
		$this->db->where('user_level', $user_type);
		$this->db->where('email', $email);
		$this->db->where('status', true);
		$this->db->where('data_expired >=', $datenow);
		$result = $this->db->get('users_key');
		$result_count = $result->num_rows();

		$no=1;
		while ($valid==false && $i_data<=$result_count) {
			$a = $result->row($i_array);
			$user_level = $a->user_level;

			//cek key valid atau tidak
			$valid = password_validation($key, $a->secret_key);

			$i_array++;
			$i_data++;
			$no++;
		}

		return $valid;
	}

	public function _activation_process($datapost)
	{
		//init
		$user_type = $datapost['user_type'];
		$email = $datapost['email'];
		$password = $datapost['password'];
		$response = false;

		//cari user
		switch ($user_type) {
			case 'admin':
				//get user admin
				$row = $this->Admin_model->get_by_email($email);

				// cek apakah akun sudah aktif? kalau belum lanjutkan proses aktivasi
				if ($row->activation_status!='activated') {
					//update
					$response = $this->Userkey_model->activation_admin($email, password_encrypt($password));
				}
				break;

			default:
				# code...
				break;
		}//endswitch


		//kirim email
		if ($response) {
			//kirim email
			$data_email = array(
				'email' => $email,
				'password' => $password
			);
			$email_content = $this->load->view('email_template/user_activation_success_v', $data_email, TRUE);
			$response_sendemail = sendEmail($email, 'Activation Success', $email_content);
			if ($response_sendemail['status'] == 'success') {
				$message = '<br>Login Information have been sent to your email.';
			}
			else{
				$message = '';
			}

			//publish pubsub
			$env = "development";
			if (!empty($_ENV["CI_ENV"])) {
					$env = $_ENV["CI_ENV"];
			}
			$topic = "account_activation_" . $env;
			$this->pubsub->publish(
					$topic,
					json_encode([
							"email" => $email,
							"user_type" => $user_type,
					])
			);

			//remove all valid key
			$response_disable = $this->Userkey_model->disabled_key($email, 'activation');

			return array(
				'status' => 'success',
				'message' => 'Activation Success! '.$message
			);
		}
		else{
			return array(
				'status' => 'error',
				'message' => 'Activation Failed!'
			);
		}
	}



}

/* End of file User_activation.php */
/* Location: ./application/controllers/users/User_activation.php */
