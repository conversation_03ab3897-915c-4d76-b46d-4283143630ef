<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Forgot extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->protect_page->userlogged(FALSE); //halaman hanya bisa diakses bila tidak login

		$this->load->model('account/Login_model');
		$this->load->model('account/Userkey_model');
		$this->load->helper('password');
		$this->load->helper('sendemail');
		$this->load->helper('encryption/base64');
	}

	public function index()
	{
		# v1
		// $data = array(
		// 	'form_action' => site_url('forgot_process')
		// );
		// $this->load->view('users/forgot/forgot_v', $data, FALSE);

		# v2
		// $this->load->view('users/forgot/forgot_v2');

		#v3
		$page_content = $this->load->view('users/forgot/forgot_v2_html', '',TRUE);
		$data_template = array(
			'page_title' => 'Forgot Password',
			'form_action' => site_url('forgot_process'),
			'page_content' => $page_content
		);
		$this->load->view('template/homepage_v', $data_template, FALSE);
	}

	public function forgot_process2()
	{
		//inisialisasi
		$draw_json = array();
		$found_user = false;
		$user_level = null;

		//data yang dikirim melalui post
		$datapost_email = $this->input->post('email');

		//form validation rules
		$this->form_validation->set_rules('email', 'e-mail', 'trim|required|valid_email');

		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => form_error('email')
			);
		} else {
			//get user by email untuk admin
			$get_user = $this->Login_model->get_by_email('admin', $datapost_email);
			$found_user = ($get_user) ? true : false;
			$user_level = 'admin';

			//get user by email untuk employee kalau admin invalid
			if ($found_user==false) {
				$get_user = $this->Login_model->get_by_email('employee', $datapost_email);
				$found_user = ($get_user) ? true : false;
				$user_level = 'employee';
			}

			//output kalau user ketemu
			if ($found_user==false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'User not found'
				);
			}
			else{
				$draw_json = array(
					'status' => 'success',
					'message' => 'Reset Success'
				);

				//kirim email
				$kirim_email_resetpassword = $this->_kirim_link_reset($datapost_email,$user_level);
				if ($kirim_email_resetpassword) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Reset Password link confirmation have been sent to your e-mail'
					);
				}
			}
		}

		if (!$this->input->post()) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'access forbidden'
			);
		}

		echo format_json($draw_json);
	}

	public function forgot_process()
	{
		//init
		$found_user = false;
		$user_type = '';

		//datapost
		$email = $this->input->post('email',true);

		//validation
		$this->form_validation->set_rules('email', 'email', 'required|callback_valid_email|trim');
		$this->form_validation->set_error_delimiters('', '');
		if ($this->form_validation->run() == FALSE) {
			$this->session->set_flashdata('message', 'Reset Password Failed!');
			$this->session->set_flashdata('error_email', form_error('email'));
		} else {
			//get user by email untuk admin
			if ($found_user==false) {
				$get_user = $this->Login_model->get_by_email('admin', $email);
				$found_user = ($get_user) ? true : false;
				$user_type = 'admin';
			}
			
			//get user by email untuk employee kalau admin invalid
			if ($found_user==false) {
				$get_user = $this->Login_model->get_by_email('employee', $email);
				$found_user = ($get_user) ? true : false;
				$user_type = 'employee';
			}

			//output kalau user ketemu
			if ($found_user) {
				//kirim email
				$kirim_email_resetpassword = $this->_kirim_link_reset($email, $user_type);
				if ($kirim_email_resetpassword) {
					$this->session->set_flashdata('message', 'Reset Password link have been sent to your email!');
				}
				else{
					$this->session->set_flashdata('message', 'Sorry, we failed to sent Reset Password link to your email!');
				}
			}
			else{
				$this->session->set_flashdata('message', 'Sorry, User not Found!');
			}
		}

		redirect(site_url('forgot'));
	}

	//function untuk generate key
	public function _kodeRandom($panjang_key)
	{
		$string		= 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		$string		.= '1234567890';

		$karakter = '';
		$karakter .= $string;

		$string = '';
		for ($i=0; $i < $panjang_key; $i++) { 
			$pos = rand(0, strlen($karakter)-1);
			$string .= $karakter{$pos};
		}
		return $string;
	}

	//kirim link reset password
	public function _kirim_link_reset($email=null, $user_type=null)
	{
		/* inisialisasi
		* user type = admin / employee
		*/

		//buat key reset penggantian email
		$key = $this->_kodeRandom(10);
		$key_md5 = md5($key); //simpan hasil generate key ke MD5
		//$key_DB = $plain_datakey.'&key='.$key_md5; //plain text yang akan dikirim ke user
		$key_DB = $key_md5; //plain text yang akan dikirim ke user
		$key_encrypt = password_encrypt($key_DB); //HASH KEY untuk dimasukkan DB user_key
		
		//masukkan data ke DB users_key
		$data_insert = array(
			'user_level' => $user_type,
			'email' => $email,
			'secret_key' => $key_encrypt,
			'key_type' => 'forgot'
		);

		// memasukkan data ke database
		$response = $this->Userkey_model->create_key($data_insert, 7200); //7200 = key expired dalam 2 jam

		//konten untuk isi email
		if ($response===true) {
			//https://stackoverflow.com/questions/12093050/encode-the-url-including-hyphen-and-dot-in-php
			$url_email = base64_url_encode($email);
			$link_konfirmasi = site_url('confirmation/resetpass/'.urlencode($key_DB).'/'.$url_email);

			$data_message['link_konfirmasi'] = $link_konfirmasi;
			$message = $this->load->view('email_template/user_forgot_v', $data_message, true);

			return sendMail($email, 'Reset Password Confirmation', $message);
		}
	}


	//custom validation with callback
    function valid_email($str)
    {
        return ( ! preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $str)) ? FALSE : TRUE;
    }

}

/* End of file Forgot.php */
/* Location: ./application/controllers/users/Forgot.php */