<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_key extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		if ($this->session->userdata('user_logged')===true) {
			if ($this->session->userdata('user_type')!='admin') {
				die("You don't allowed to access this page.");
			}
		}

		$this->load->helper('password');
		$this->load->helper('sendemail');

		$this->load->model('account/Userkey_model');
		$this->load->library('user_key/User_key_lib');
	}

	public function _kodeAcak($panjang=null)
	{
		$karakter = '';
		$karakter .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; // karakter alfabet
		$karakter .= '**********'; // karakter numerik
		//$karakter .= '@#$^*()_+=/?'; // karakter simbol

		$string = '';
		for ($i=0; $i < $panjang; $i++) { 
			$pos = rand(0, strlen($karakter)-1);
			$string .= $karakter{$pos};
		}
		return $string;
	}

	public function create_activation_key($email=null)
	{
		//disable semua aktivasi sebelumnya
		$this->Userkey_model->disable_activation_link($email);

		//buat data untuk dimasukkan ke database userkey
		$key = $this->_kodeAcak(10);
		$key_md5 = md5($key);
		$key_encrypt = password_encrypt($key_md5);
		$email = urldecode($email);

		$data_insert = array(
			'user_level' => 'employee',
			'email' => $email,
			'secret_key' => $key_encrypt,
		);

		$time_expired = 60 * 60 * 24;
		$response = $this->Userkey_model->create_activation_key($data_insert, $time_expired); // memasukkan data ke database

		//konten untuk isi email
		if ($response===true) {
			$email_url = urlencode($email);
			$email_url = str_replace('.', '%2E', $email_url);

			$link_aktivasi = site_url('activation/'.$key_md5.'/' . $email_url);
			$data_message['link_aktivasi'] = $link_aktivasi;
			$message = $this->load->view('email_template/user_activation_v', $data_message, true);

			$response_sendmail = sendMail($email, 'Activation', $message); //kirim email
		}
	}

	public function account_verify($key=null, $email=null)
	{
		$email = urldecode($email);
		$datenow = date('Y-m-d H:i:s');


		$this->db->where('key_type', 'activation');
		$this->db->where('email', $email);
		$this->db->where('status', true);
		$this->db->where('data_expired >=', $datenow);
		$result = $this->db->get('users_key');
		$result_count = $result->num_rows();


		//inisialisai
		$valid = false;
		$i_array=0;
		$i_data=1;
		$user_level=null;

		$no=1;
		while ($valid==false && $i_data<=$result_count) {
			$a = $result->row($i_array);
			/*
			echo $no.$br;
			echo 'ID='.$a->key_id .$br;
			echo 'Key='.$a->secret_key. $br;
			echo $br.$br;
			*/
			$user_level = $a->user_level;

			//cek key valid atau tidak
			$valid = password_validation($key, $a->secret_key);

			$i_array++;
			$i_data++;
			$no++;
		}

		if ($valid===true) {
			//update db employee
			if ($user_level=='employee') {
				//echo "ketemu";
				$this->load->view('_homepage/account_activation_v');
				//aktivasi akun
				//$response1 = $this->user_key_lib->create_password($email);
				

				//matikan semua key activation
				//$response2 = $this->Userkey_model->disable_activation_link($email);
			}
		}
		else{
			echo 'Error: Key maybe expired. <a href="'.site_url('').'">[ Home ]</a>';
		}
	}

	public function activation($key=null, $email=null)
	{
		//inisialisasi
		$email = urldecode($email);
		$br = '<br>';
		$datenow = date('Y-m-d H:i:s'); //waktu sekarang
		#inisialisasi 2
		$valid = false;
		$i_array=0;
		$i_data=1;
		$user_level=null;

		//ambil key berdasarkan email dan untuk aktivasi
		$this->db->where('key_type', 'activation');
		$this->db->where('email', $email);
		$this->db->where('status', true);
		$this->db->where('data_expired >=', $datenow);
		$result = $this->db->get('users_key');
		$result_count = $result->num_rows();
		

		$no=1;
		while ($valid==false && $i_data<=$result_count) {
			$a = $result->row($i_array);
			/*
			echo $no.$br;
			echo 'ID='.$a->key_id .$br;
			echo 'Key='.$a->secret_key. $br;
			echo $br.$br;
			*/
			$user_level = $a->user_level;

			//cek key valid atau tidak
			$valid = password_validation($key, $a->secret_key);

			$i_array++;
			$i_data++;
			$no++;
		}

		if ($valid===true) {
			//update db aktivasi akun
			switch ($user_level) {
				case 'employee':
					//aktivasi akun
					$response1 = $this->user_key_lib->create_password($email);

					//matikan semua key activation
					$response2 = $this->Userkey_model->disable_activation_link($email);
					break;

				case 'admin':
					$response1 = $this->Userkey_model->admin_web_activation($email);
					if ($response1) {
						//matikan semua key activation
						$response2 = $this->Userkey_model->disable_activation_link($email);

						//buat session
						$update_session['activation_status'] = 'activated';
						$this->session->set_userdata($update_session);
						echo "Activation Success. <a href=\"".site_url('')."\">[ Home ]</a>";
					}
					else{
						echo "Activation Failed";
					}
					break;
				
				default: break;
			}
		}
		else{
			echo 'Error: Key maybe expired. <a href="'.site_url('').'">[ Home ]</a>';
		}
	}

	public function _add()
	{
		$datenow = date('Y-m-d H:i:s');
		$dateexpired = time()+600;
		$dateexpired = date('Y-m-d H:i:s', $dateexpired);

		$user_type = 'employee';
		$key = 'coeksekali';
		$key_md5 = md5($key);
		$key_encrypt = password_encrypt($key_md5);
		$email = '<EMAIL>';

		$object = array(
			'key_id' => null,
			'key_type' => 'activation',
			'user_level' => 'employee',
			'email' => $email,
			'secret_key' => $key_encrypt,
			'status' => true,
			'data_expired' => $dateexpired,
			'data_created' => $datenow
		);

		$result = $this->db->insert('users_key', $object);
		echo $result.' '.site_url('activation/'.$key_md5.'/'.$email);
	}

}

/* End of file User_key.php */
/* Location: ./application/controllers/_homepage/User_key.php */