<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Reset_data extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//fitur hanya untuk owner
		if ($this->session->userdata('user_type')!='admin') {
			redirect(site_url());
			die();
		}

		$this->load->helper('password');
		$this->load->helper('sendemail');

		$this->load->model('account/Userkey_model');
		$this->load->library('user_key/User_key_lib');

		$this->load->model('outlet/Outlets_model');
	}

	public function index($datakey=null, $email=null)
	{
		//inisialisasi
		$outlet_selected = array();
		$plain_datakey = urldecode($datakey); //ambil key di url
		$plain_email = urldecode($email);

		//validasi key
		$validkey = $this->_carikey('resetdata', $plain_datakey, $plain_email);
		if ($validkey==true) {
			//get selected outlet
			$outlet_get = explode('outlet=', $plain_datakey);
			$outlet_get = explode('&', $outlet_get[1]);
			$outlet = $outlet_get[0];

			//olah data outlet yang dipilih
			$outlet = explode('_', $outlet);
			foreach ($outlet as $index => $value) {
				$temp_outlet = str_replace('_', '', $value);
				array_push($outlet_selected, $temp_outlet);
			}


			//get reset data
			//reset transaction
			$reset_trx1 = explode('reset_transaction=', $plain_datakey);
			$reset_trx2 = explode('&', $reset_trx1[1]);
			$reset_trx = $reset_trx2[0];
			$reset_trx_tgl = explode('_', $reset_trx);
			$reset_trx_tgl_from = $reset_trx_tgl[0];
			$reset_trx_tgl_till = $reset_trx_tgl[1];

			//echo "reset tgl trx: dari=".$reset_trx_tgl_from." sampai=".$reset_trx_tgl_till;

			//get outlet
			foreach ($outlet_selected as $outlet_id) {
				//echo "selected: $value";

				//validasi outlet
				$cek_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($cek_outlet) {
					//hapus transaksi
					$result = $this->_reset_transaksi($outlet_id, $reset_trx_tgl_from, $reset_trx_tgl_till);
					if ($result) {
						echo "Transaction from ".$reset_trx_tgl_from." till ".$reset_trx_tgl_till." on outlet ".$cek_outlet->name." have been deleted.<br>";
					}
				}
			}

			//disable key
			$this->Userkey_model->disabled_key($plain_email, 'resetdata');
		}
		else{
			echo 'Error: Reset Data Link Confirmation is Invalid. <a href="'.site_url('').'">[ Home ]</a>';
		}
	}

	public function _carikey($key_type=null, $datakey=null, $email=null)
	{
		//inisialisasi
		$datenow = current_millis(); //waktu sekarang
		$valid = false;
		$i_array=0;
		$i_data=1;
		$user_level=null;
		$br = '<br>';
		$email = ($email);
		$plain_datakey = ($datakey);

		//get change_email key
		$this->db->where('key_type', 'resetdata');
		$this->db->where('email', $email);
		$this->db->where('status', true);
		$this->db->where('data_expired >=', $datenow);
		$result = $this->db->get('users_key');
		$result_count = $result->num_rows();

		//pencocokan key START
		$no=1;
		while ($valid==false && $i_data<=$result_count) {
			$a = $result->row($i_array);
			/*
			echo $no.$br;
			echo 'ID='.$a->key_id .$br;
			echo 'Key='.$a->secret_key. $br;
			echo $br.$br;
			*/
			$user_level = $a->user_level;

			//cek key valid atau tidak
			$valid = password_validation($plain_datakey, $a->secret_key);

			$i_array++;
			$i_data++;
			$no++;
		}
		//pencocokan key END

		return $valid;
		die();

		//hasil pencocokan key
		if ($valid==false) {
			echo 'Error: This link confirmation is not valid. <a href="'.site_url('').'">[ Home ]</a>';
			die();
		}
		else{
			//ambil new email
			$get_newemail1 = explode('newemail=', $plain_datakey);
			$get_newemail2 = explode('&key=', $get_newemail1[1]);
			$newemail = $get_newemail2[0];

			
			//cek email apakah sudah dipakai atau belum
			$this->db->where('email', $newemail);
			switch ($user_level) {
				case 'admin':
					$result_cekemail = $this->db->get('admin')->row();
					break;
				case 'employee':
					$result_cekemail = $this->db->get('employee')->row();
					break;
				default: break;
			}

			if ($result_cekemail) {
				//disabled key
				$this->Userkey_model->disable_change_email_link($oldemail);
				echo $newemail.' already registered.';
			}
			else{
				//update data email user
				$object = array(
					'email' => $newemail,
					'data_modified' => current_millis()
				);
				$this->db->where('email', $oldemail);
				switch ($user_level) {
					case 'admin':
						$result_updateemail = $this->db->update('admin', $object);
						$this->db->last_query();
						break;
					case 'employee':
						$result_updateemail = $this->db->update('employee', $object);
						break;
					default: break;
				}

				if ($result_updateemail) {
					//$this->session->flashdata('message','Your e-mail have been updated.');
					echo "Your e-mail have been updated. <a href='".site_url('settings')."'>Click here to continue.</a>";
					$this->Userkey_model->disable_change_email_link($oldemail);
					
					//kirim email notifikasi
					$this->_notif_change_email($newemail);
				}
				else{
					echo "Fail to update your e-mail.";
				}
			}
		}
	}

	public function _reset_transaksi($outlet_id, $tgl_dari, $tgl_sampai)
	{
		/* get data */
		// $this->db->select("*, FROM_UNIXTIME((time_created)/1000, '%d-%m-%Y') AS waktu_transaksi");
		// $this->db->from('sales');
		// $this->db->where(
		// 	array(
		// 		'outlet_fkid' => $outlet_id,
		// 		"FROM_UNIXTIME((time_created)/1000, '%d-%m-%Y') >=" => $tgl_dari,
		// 		"FROM_UNIXTIME((time_created)/1000, '%d-%m-%Y') <=" => $tgl_sampai
		// 	)
		// );
		// $result = $this->db->get()->result();
		// //echo $this->db->last_query();
		// return $result;

		/* delete data */
		// $this->db->where(
		// 	array(
		// 		'outlet_fkid' => $outlet_id,
		// 		"FROM_UNIXTIME((time_created)/1000, '%d-%m-%Y') >=" => $tgl_dari,
		// 		"FROM_UNIXTIME((time_created)/1000, '%d-%m-%Y') <=" => $tgl_sampai
		// 	)
		// );
		// return $this->db->delete('sales');

		/* ganti status transaksi */
		$this->db->where(
			array(
				'outlet_fkid' => $outlet_id,
				"FROM_UNIXTIME((time_created)/1000, '%d-%m-%Y') >=" => $tgl_dari,
				"FROM_UNIXTIME((time_created)/1000, '%d-%m-%Y') <=" => $tgl_sampai
			)
		);
		$object['data_status'] = 'off';
		$object['time_modified'] = current_millis();
		return $this->db->update('sales', $object);
	}

}

/* End of file Reset_data.php */
/* Location: ./application/controllers/welcome/confirmation/Reset_data.php */