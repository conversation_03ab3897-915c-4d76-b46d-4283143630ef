<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Reset_password extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(FALSE); //halaman hanya bisa diakses bila tidak login

		$this->load->helper('password');
		$this->load->helper('sendemail');

		$this->load->model('account/Userkey_model');
		$this->load->model('settings/Profile_model');
		$this->load->helper('encryption/base64');
	}

	//kirim link reset password
	//verifikasi link reset password
	public function verifikasi($datakey=null, $email=null)
	{
		//inisialisasi
		$datenow = current_millis(); //waktu sekarang
		$valid = false;
		$i_array=0;
		$i_data=1;
		$user_level=null;
		$br = '<br>';
		$email = base64_url_decode($email);
		$plain_datakey = urldecode($datakey);
		$plain_newpassword = null;

		//get change_email key
		$this->db->where('key_type', 'forgot');
		$this->db->where('email', $email);
		$this->db->where('status', true);
		$this->db->where('data_expired >=', $datenow);
		$result = $this->db->get('users_key');
		$result_count = $result->num_rows();

		//pencocokan key START
		$no=1;
		while ($valid==false && $i_data<=$result_count) {
			$a = $result->row($i_array);
			/*
			echo $no.$br;
			echo 'ID='.$a->key_id .$br;
			echo 'Key='.$a->secret_key. $br;
			echo $br.$br;
			*/
			$user_level = $a->user_level;

			//cek key valid atau tidak
			$valid = password_validation($plain_datakey, $a->secret_key);

			$i_array++;
			$i_data++;
			$no++;
		}
		//pencocokan key END

		//hasil pencocokan key
		if ($valid==false) {
			echo 'Error: This reset password link is not valid. <a href="'.site_url('').'">[ Home ]</a>';
			die();
		}
		else{
			if ($this->input->post()) {
				//init
				$valid_input = true;
				$msg_error = array();

				//validasi input forgot
				$this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
				$this->form_validation->set_rules('key', 'key', 'trim|required');
				$this->form_validation->set_rules('password', 'password', 'trim|required|min_length[8]');
				$this->form_validation->set_rules('passconf', 'confirm password', 'trim|required|matches[password]');

				// $this->form_validation->set_error_delimiters('', '');

				//validasi manual
				if ($email != $this->input->post('email')) {
					$valid_input = false;
					$msg_error['email'] = 'Invalid Email.';
				}
				if ($datakey != $this->input->post('key')) {
					$valid_input = false;
					$msg_error['key'] = 'Invalid Key.';
				}

				if ($this->form_validation->run() == FALSE || $valid_input==false) {
					$this->session->set_flashdata('validation_errors', validation_errors());
					redirect(current_url());
				} else {
					//update password akun
					$plain_newpassword = $this->input->post('password');
					$datareset['password'] = password_encrypt($plain_newpassword);

					//change password where use forgot password
					//v1
					//$response = $this->Profile_model->update_user($user_level, $email, $datareset);
					//v2
					$newpassword = $datareset['password'];
					$response = $this->db->query("CALL user_change_password('".$email."', '".$newpassword."', '".ENVIRONMENT."')");
					if ($response) {
						echo "Password have been reset. ";
						//kirim password baru
						$this->_notif_resetpassword($email, $plain_newpassword);
						//matikan all link
						$this->Userkey_model->disabled_key($email, 'forgot');

						echo '<br><br> <a href="'.site_url('').'">Login here!</a>';
					}
				}
			}
			else{
				$data = array(
					'key' => $datakey,
					'email' => $email,
					'action' => current_url()
				);
				// $this->load->view('users/forgot/forgot_confirmation_v', $data, FALSE);
				$page_content = $this->load->view('users/forgot/forgot_confirmation_v2_html', $data, TRUE);

				$data_template = array(
					'page_title' => 'Reset Password Confirmation',
					'form_action' => '',
					'page_content' => $page_content
				);
				$this->load->view('template/homepage_v', $data_template, FALSE);
			}
			
		}
	}
	//kirim password baru

	private function _notif_resetpassword($email=null, $password=null)
	{
		$data['email'] = $email;
		$data['password'] = $password;
		$message = $this->load->view('email_template/user_forgot_newpassword_v', $data, TRUE);
		$kirim_email = sendMail($email, 'Reset Password Success', $message);
		if ($kirim_email) {
			echo "New Login information have been sent to your email.";
		}
		else{
			echo "Failed to send your login information.";
		}
	}

}

/* End of file Reset_password.php */
/* Location: ./application/controllers/welcome/confirmation/Reset_password.php */