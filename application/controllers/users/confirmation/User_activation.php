<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_activation extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(FALSE); //halaman hanya bisa diakses bila login

		$this->load->helper('password');
		$this->load->helper('sendemail');
		$this->load->helper('encryption/base64');

		$this->load->model('account/Userkey_model');
		$this->load->library('user_key/User_key_lib');
		$this->load->library('user_agent');
	}

	//cek aktivasi akun
	private function _key_verify($key=null, $email=null)
	{
		//inisialisasi
		$email = base64_url_decode($email);
		$br = '<br>';
		$datenow = date('Y-m-d H:i:s'); //waktu sekarang
		#inisialisasi 2
		$valid = false;
		$i_array=0;
		$i_data=1;
		$user_level=null;

		//ambil key berdasarkan email dan untuk aktivasi
		$this->db->where('key_type', 'activation');
		$this->db->where('email', $email);
		$this->db->where('status', true);
		$this->db->where('data_expired >=', $datenow);
		$result = $this->db->get('users_key');
		$result_count = $result->num_rows();
		

		$no=1;
		while ($valid==false && $i_data<=$result_count) {
			$a = $result->row($i_array);
			/*
			echo $no.$br;
			echo 'ID='.$a->key_id .$br;
			echo 'Key='.$a->secret_key. $br;
			echo $br.$br;
			*/
			$user_level = $a->user_level;

			//cek key valid atau tidak
			$valid = password_validation($key, $a->secret_key);

			$i_array++;
			$i_data++;
			$no++;
		}

		$data_return = array(
			'valid' => $valid,
			'user_level' => ($valid===true) ? $a->user_level : null
		);

		return $data_return;

		/*
		if ($valid===true) {
			//update db aktivasi akun
			switch ($user_level) {
				case 'employee':
					//aktivasi akun
					$response1 = $this->user_key_lib->create_password($email);

					//matikan semua key activation
					$response2 = $this->Userkey_model->disable_activation_link($email);
					break;

				case 'admin':
					$response1 = $this->Userkey_model->admin_web_activation($email);
					if ($response1) {
						//matikan semua key activation
						$response2 = $this->Userkey_model->disable_activation_link($email);

						//buat session
						$update_session['activation_status'] = 'activated';
						$this->session->set_userdata($update_session);
						echo "Activation Success. <a href=\"".site_url('')."\">[ Home ]</a>";
					}
					else{
						echo "Activation Failed";
					}
					break;
				
				default: break;
			}
		}
		else{
			echo 'Error: Key maybe expired. <a href="'.site_url('').'">[ Home ]</a>';
		}
		*/
	}

	public function invitation($key=null, $email_encrypt=null)
	{
		$valid = $this->_key_verify($key, $email_encrypt);
		$email = base64_url_decode($email_encrypt);
		if ($valid['valid']===true) {
			$data = array(
				'action' => site_url('confirmation/user-activation/key-verify/'.$key.'/'.$email_encrypt.'/activation_process'),
				'key' => $key,
				'email' => $email
			);
			$this->load->view('users/activation_v',$data);
		}
		else{
			$this->session->set_flashdata('message', 'Sorry, The activation link is invalid!');
			redirect(site_url());
		}
	}

	public function activation_process()
	{
		$datapost = array(
			'email' => $this->input->post('email',true),
			'key' => $this->input->post('key'),
			'password' => $this->input->post('password'),
			'passconf' => $this->input->post('passconf')
		);
		$email = $datapost['email'];
		$key = $datapost['key'];
		$email_encrypt = base64_url_encode($email);

		$this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
		$this->form_validation->set_rules('key', 'key', 'trim|required');
		$this->form_validation->set_rules('password', 'password', 'trim|required|min_length[8]');
		$this->form_validation->set_rules('passconf', 'confirm password', 'trim|required|matches[password]');
		$this->form_validation->set_error_delimiters('<p class="text-danger">', '</p>');

		if ($this->form_validation->run() == FALSE) {
			// echo "input gak valid";
			$this->session->set_flashdata('msg_activation_err', validation_errors());
			redirect($this->agent->referrer());
		} else {
			$valid = $this->_key_verify($key, $email_encrypt);
			if ($valid['valid']===true) {
				$object['password'] = password_encrypt($datapost['password']);
				switch ($valid['user_level']) {
					case 'admin':
						$object['activation_status'] = 'activated';
						$this->db->where('email', $email);
						$result = $this->db->update('admin', $object);
						break;
					case 'employee':
						$object['access_status_web'] = 'activated';
						$this->db->where('email', $email);
						$result = $this->db->update('employee', $object);
						break;
					default:
						$this->session->set_flashdata('message', 'Something error!');
						redirect(site_url());die();
						break;
				}
				if ($result) {
					//matikan semua key activation
					$this->Userkey_model->disable_activation_link($email);

					/* kirim email aktivasi sukses */
					$this->sendemail_lib->user_activation_success($email, $datapost['password']);
					$this->session->set_flashdata('message', 'Activation Success!');
					// echo "Activation Success..";
					redirect('','refresh');
				}
				else{
					$this->session->set_flashdata('message', 'Activation Failed!');
					redirect(site_url());die();
				}
			}
			else{
				$this->session->set_flashdata('message', 'Sorry, The activation link is invalid!');
				redirect(site_url());
			}
		}
	}

}

/* End of file User_activation.php */
/* Location: ./application/controllers/welcome/confirmation/User_activation.php */