<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Change_email extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->helper('password');
		$this->load->helper('sendemail');

		$this->load->model('account/Userkey_model');
		$this->load->library('user_key/User_key_lib');
	}



	public function confirm($datakey=null, $oldemail=null)
	{
		//inisialisasi
		$datenow = current_millis(); //waktu sekarang
		$valid = false;
		$i_array=0;
		$i_data=1;
		$user_level=null;
		$br = '<br>';
		$oldemail = urldecode($oldemail);
		$plain_datakey = urldecode($datakey);

		//get change_email key
		$this->db->where('key_type', 'change_email');
		$this->db->where('email', $oldemail);
		$this->db->where('status', true);
		$this->db->where('data_expired >=', $datenow);
		$result = $this->db->get('users_key');
		$result_count = $result->num_rows();

		//pencocokan key START
		$no=1;
		while ($valid==false && $i_data<=$result_count) {
			$a = $result->row($i_array);
			/*
			echo $no.$br;
			echo 'ID='.$a->key_id .$br;
			echo 'Key='.$a->secret_key. $br;
			echo $br.$br;
			*/
			$user_level = $a->user_level;

			//cek key valid atau tidak
			$valid = password_validation($plain_datakey, $a->secret_key);

			$i_array++;
			$i_data++;
			$no++;
		}
		//pencocokan key END

		//hasil pencocokan key
		if ($valid==false) {
			echo 'Error: This link confirmation is not valid. <a href="'.site_url('').'">[ Home ]</a>';
			die();
		}
		else{
			//ambil new email
			$get_newemail1 = explode('newemail=', $plain_datakey);
			$get_newemail2 = explode('&key=', $get_newemail1[1]);
			$newemail = $get_newemail2[0];

			
			//cek email apakah sudah dipakai atau belum
			$this->db->where('email', $newemail);
			switch ($user_level) {
				case 'admin':
					$result_cekemail = $this->db->get('admin')->row();
					break;
				case 'employee':
					$result_cekemail = $this->db->get('employee')->row();
					break;
				default: break;
			}

			if ($result_cekemail) {
				//disabled key
				$this->Userkey_model->disable_change_email_link($oldemail);
				echo $newemail.' already registered.';
			}
			else{
				//update data email user
				$object = array(
					'email' => $newemail,
					'data_modified' => current_millis()
				);
				$this->db->where('email', $oldemail);
				switch ($user_level) {
					case 'admin':
						$result_updateemail = $this->db->update('admin', $object);
						$this->db->last_query();
						break;
					case 'employee':
						$result_updateemail = $this->db->update('employee', $object);
						break;
					default: break;
				}

				if ($result_updateemail) {
					//$this->session->flashdata('message','Your e-mail have been updated.');
					echo "Your e-mail have been updated. <a href='".site_url('settings')."'>Click here to continue.</a>";
					$this->Userkey_model->disable_change_email_link($oldemail);
					
					//kirim email notifikasi
					$this->_notif_change_email($newemail);
				}
				else{
					echo "Fail to update your e-mail.";
				}
			}
		}
	}

	public function _notif_change_email($email=null)
	{
		$data['newemail'] = $email;
		$message = $this->load->view('email_template/user_change_email_success_v', $data, TRUE);
		sendMail($email, 'Change E-mail Success', $message);
	}

}

/* End of file Change_email.php */
/* Location: ./application/controllers/welcome/confirmation/Change_email.php */