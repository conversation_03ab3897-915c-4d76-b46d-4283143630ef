<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {

	public $draw_json = array();

	public function __construct()
	{
		parent::__construct();
		$this->load->model('account/Login_model');
		$this->load->helper('password');
		$this->load->library('auth/Authlib');
	}

	public function login_ajax()
	{
		//inisialisasi
		$valid_access = true;

		//kalau sudah login bisa akses halaman?
		if ($this->session->userdata('user_logged')==TRUE) {
			$valid_access = false;
			$draw_json = array(
				'status' => 'error',
				'message' => "You're already logged."
			);
		}

		//kalau langsung buka halaman bisa akses?
		if (!$this->input->post()) {
			$valid_access = false;
			$draw_json = array(
				'status' => 'error',
				'message' => "Direct access to this page dont allowed."
			);
		}
		

		if ($valid_access==true) {
			//inisialisasi variabel
			$user_found = false;
			$user_level = null;
			

			//data post (data yang dikirim oleh form)
			$datapost = array(
				'email' => $this->input->post('email', true),
				'password' => $this->input->post('password', true),
				'mode' => $this->input->post('mode', true),
				'timezone' => $this->input->post('timezone', true),
			);
			$data_email = $datapost['email'];
			$data_password = $datapost['password'];

			//cek input login mode
			if ($datapost['mode']=='front' || $datapost['mode']=='back') {
				$data_mode_valid = true;
				$data_mode_errorinput = '';
			}
			else{
				$data_mode_valid = false;
				$data_mode_errorinput = 'Invalid Input on Login Mode';
			}

			//cek timezone benar atau tidak
			$error_timezone = '';
			if (!empty($datapost['timezone'])) {
				//konversi timezone offset ke nama timezone
				// This is just an example. In application this will come from Javascript (via an AJAX or something)
				$timezone_offset_minutes = $datapost['timezone'];  // $_GET['timezone_offset_minutes']

				// Convert minutes to seconds
				$timezone_name = timezone_name_from_abbr("", $timezone_offset_minutes*60, false);
				$datapost['timezone'] = $timezone_name;

				// Asia/Kolkata
				// echo $timezone_name;
				
				
				$data_timezone_valid = isValidTimezoneId($datapost['timezone']);
				$error_timezone = ($data_timezone_valid==false) ? 'Invalid Timezone' : '';
			}
			

			//set validasi
			$this->_rules();

			//validasi
			if ($this->form_validation->run() == FALSE || $data_mode_valid===FALSE || $data_timezone_valid==false) {
				//buat response
				$draw_json = array(
					'status' => 'error',
					'message'=> 'Login Failed',
					'data' => array(
						'email' => form_error('email'),
						'password' => form_error('password'),
						'mode' => (form_error('mode') ? form_error('mode') : $data_mode_errorinput),
						'timezone' => (empty($error_timezone)) ? form_error('timezone') : $error_timezone,
					)
				);
			} else {
				//cek user admin
				$response = $this->Login_model->get_by_email('admin', $data_email); //cek user ada atau tidak
				if ($response) {
					$user_found = true;
					$user_level = 'admin';
				}

				//kalau admin tidak ketemu, cek user employee
				if ($user_found==false) {
					$response = $this->Login_model->get_by_email('employee', $data_email); //cek user ada atau tidak
					if ($response) {
						$user_found = true;
						$user_level = 'employee';
					}
				}


				//ambil data user kalau user ditemukan
				if ($user_found == true) {
					//kalau user ditemukan
					$datarow = $response;
					$valid_password = password_validation($data_password, $datarow->password); //cek validasi password
					
					//action kalau password valid
					if ($valid_password===TRUE) {
						//buat session START
						$buatsession = array(
							'user_logged' => TRUE,
							'user_type' => $user_level,
							'admin_id' => ($user_level=='admin') ? $datarow->admin_id : $datarow->admin_fkid,
							'user_id' => ($user_level=='admin') ? $datarow->admin_id : $datarow->employee_id,
							'user_name' => $datarow->name,
							'email' => $datarow->email,
							'phone' => $datarow->phone,
							'photo' => $datarow->photo,
							'join_date' => $datarow->date_join,
							'timezone' => $datapost['timezone'],
							'lastlogin' => current_millis()
						);

						if ($user_level=='admin') {
							$buatsession['activation_status'] = $datarow->activation_status;
							//cek gambar ada atau tidak
							$photopath1 = photo_url().$datarow->admin_id.'/'.$datarow->photo;
						}
						elseif ($user_level=='employee') {
							$photopath1 = photo_url().$datarow->admin_fkid.'/'.$datarow->photo;
						}

						//cek apakah foto user ada
						$photopath2 = str_replace(base_url(), '', $photopath1);
						if (file_exists($photopath2) && !empty($datarow->photo)) {
							$buatsession['photo_url'] = $photopath1;
						}
						else{
							$buatsession['photo_url'] = nouserphoto();
						}
						
						$this->session->set_userdata( $buatsession ); //function buat session
						//buat session END



						//buat response output login
						$draw_json = array(
							'status' => 'success',
							'message' => 'Login Success'
						);
						$this->Login_model->update_lastlogin();
					}
					else{
						//password tidak valid
						$draw_json = array(
							'status' => 'error',
							'message' => 'E-mail and Password didn\'t match',
							'data' => array(
								'email' => form_error('email'),
								'password' => form_error('password')
							)
						);
					}
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'User not found!'
					);
				}
			}//end validasi
		}
		echo format_json($draw_json);
	}

	public function login_proses()
	{
		$valid_access = true;
		//kalau sudah login bisa akses halaman?
		if ($this->session->userdata('user_logged')==TRUE) {
			$valid_access = false;
		}

		//kalau langsung buka halaman bisa akses?
		if (!$this->input->post()) {
			$valid_access = false;
		}

		if ($valid_access==false) {
			redirect(site_url());
			die();
		}




		//inisialisasi variabel
		$user_found = false;
		$user_level = null;
		

		//data post (data yang dikirim oleh form)
		$datapost = array(
			'email' => $this->input->post('email', true),
			'password' => $this->input->post('password', true),
			'mode' => $this->input->post('mode', true),
			'timezone' => $this->input->post('timezone', true),
		);
		$rememberme = (bool)$this->input->post('rememberme',true);
		$data_email = $datapost['email'];
		$data_password = $datapost['password'];


		//cek timezone benar atau tidak
		$error_timezone = '';
		if (!empty($datapost['timezone'])) {
			//konversi timezone offset ke nama timezone
			// This is just an example. In application this will come from Javascript (via an AJAX or something)
			$timezone_offset_minutes = $datapost['timezone'];  // $_GET['timezone_offset_minutes']

			// Convert minutes to seconds
			$timezone_name = timezone_name_from_abbr("", $timezone_offset_minutes*60, false);
			$datapost['timezone'] = $timezone_name;

			// Asia/Kolkata
			// echo $timezone_name;
			
			
			$data_timezone_valid = isValidTimezoneId($datapost['timezone']);
			$error_timezone = ($data_timezone_valid==false) ? 'Invalid Timezone' : '';
		}
		

		//set validasi
		$this->_rules();

		//validasi
		if ($this->form_validation->run() == FALSE || $data_timezone_valid==false) {
			$this->session->set_flashdata(
				array(
					'message_error' => 'Login Failed',
					'error_email' => form_error('email'),
					'error_password' => form_error('password'),
					'error_mode' => form_error('mode'),
					'error_timezone' => (empty($error_timezone)) ? form_error('timezone') : $error_timezone,
				)
			); //error message
		} else {
			//cek user admin
			$response = $this->Login_model->get_by_email('admin', $data_email); //cek user ada atau tidak
			if ($response) {
				$user_found = true;
				$user_level = 'admin';
			}

			//kalau admin tidak ketemu, cek user employee
			if ($user_found==false) {
				$response = $this->Login_model->get_by_email('employee', $data_email); //cek user ada atau tidak
				if ($response) {
					$user_found = true;
					$user_level = 'employee';
				}
			}


			//ambil data user kalau user ditemukan
			if ($user_found == true) {
				//kalau user ditemukan
				$datarow = $response;
				$valid_password = password_validation($data_password, $datarow->password); //cek validasi password
				
				//action kalau password valid
				if ($valid_password===TRUE) {
					//API REPORT
					$this->load->library('app/Api_report');
					$this->api_report->auth($data_email, $data_password);

					
					//v1
					/*
					//buat session START
					$buatsession = array(
						'user_logged' => TRUE,
						'user_type' => $user_level,
						'admin_id' => ($user_level=='admin') ? $datarow->admin_id : $datarow->admin_fkid,
						'user_id' => ($user_level=='admin') ? $datarow->admin_id : $datarow->employee_id,
						'user_name' => $datarow->name,
						'email' => $datarow->email,
						'phone' => $datarow->phone,
						'photo' => $datarow->photo,
						'activation_status' => '',
						'developer_mode' => $datarow->developer_mode,
						'timezone' => $datapost['timezone'],
					);

					if ($user_level=='admin') {
						$buatsession['activation_status'] = $datarow->activation_status;
						//cek gambar ada atau tidak
						$photopath1 = photo_url().$datarow->admin_id.'/'.$datarow->photo;
					}
					elseif ($user_level=='employee') {
						$photopath1 = photo_url().$datarow->admin_fkid.'/'.$datarow->photo;
						$buatsession['role'] = json_decode($datarow->role);
						$buatsession['lastupdate'] = current_millis();
					}

					//currency setting
					if (is_json($datarow->settings)) {
						$data_settings = json_decode($datarow->settings);
						$buatsession['currency_symbol'] = $data_settings->currency_symbol;
						$buatsession['currency_position'] = $data_settings->currency_position;
						$buatsession['currency_separator'] = $data_settings->currency_separator;
					}

					//cek apakah foto user ada
					$photopath2 = str_replace(base_url(), '', $photopath1);
					if (file_exists($photopath2) && !empty($datarow->photo)) {
						$buatsession['photo_url'] = $photopath1;
					}
					else{
						$buatsession['photo_url'] = nouserphoto();
					}
					
					$this->session->set_userdata( $buatsession ); //function buat session
					//buat session END
					*/
					//v2
					$email = $data_email;
					$this->authlib->create_user_session_by_email($email);


					//create remember me
					if ($rememberme) {
						$this->authlib->create_remember($user_level, $buatsession['user_id']);
					}




					//buat response output login
					$this->session->set_flashdata(
						array(
							'message_success' => 'Login Success',
						)
					); //error message
					$this->Login_model->update_lastlogin();
					

					//redirect
					if ($this->input->post('continue')) {
						$continue_url = $this->input->post('continue');
						$continue_url = html_entity_decode($continue_url);
						if (filter_var($continue_url, FILTER_VALIDATE_URL)) {
							$continue_hosturl = parse_url($continue_url, PHP_URL_HOST);
							$current_hosturl = parse_url(site_url(), PHP_URL_HOST);
							if ($current_hosturl==$continue_hosturl) {
								redirect($continue_url);
							}
						}
					}
					redirect(site_url());
					die();
				}
				else{
					//password tidak valid
					$this->session->set_flashdata(
						array(
							'message_error' => 'Incorrect e-mail or password!',
						)
					); //error message
				}
			}
			else{
				$this->session->set_flashdata(
					array(
						'message_error' => 'User not Found',
					)
				); //error message
			}
		}//end validasi

		redirect(site_url());
	}

	public function logout()
	{
		$this->session->sess_destroy();
		delete_cookie('uniq_pos_cookie');
		redirect(site_url());
		die();
	}

	function _rules()
	{
		$this->form_validation->set_rules('email', 'E-mail', 'trim|required|max_length[100]|valid_email');
		$this->form_validation->set_rules('password', 'password', 'trim|required|min_length[5]');
		// $this->form_validation->set_rules('mode', 'Login Mode', 'trim|required|in_list[front,back]');
		$this->form_validation->set_rules('timezone', 'Timezone', 'trim|required');

		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
	}

}

/* End of file Login.php */
/* Location: ./application/controllers/core/Login.php */