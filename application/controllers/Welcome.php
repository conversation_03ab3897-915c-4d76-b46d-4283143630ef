<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Welcome extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//cek remember me
		$this->load->helper(array('cookie','string','password'));
		$this->load->library('auth/Authlib');

		//cek remember me cookie
		$cookie = get_cookie($this->config->item('remember_cookie_name','auth'));
		if ($this->session->userdata('user_logged')==false && $cookie<>"") {
			$this->authlib->create_user_session_by_cookie($cookie);
		}

		//proteksi halaman
		$this->protect_page->userlogged(FALSE); //halaman hanya bisa diakses bila tidak login
	}

	public function index()
	{
		//halaman login
		// $this->load->view('users/login/login_v2');

		//new
		$page_content = $this->load->view('users/login/login_v2_html', '',TRUE);

		$data_template = array(
			'page_title' => 'Client Login Area',
			'form_action' => site_url('login_process'),
			'page_content' => $page_content
		);
		$this->load->view('template/homepage_v', $data_template, FALSE);
	}

	public function forgot()
	{
		$page_content = $this->load->view('users/forgot/forgot_v2_html', '',TRUE);

		$data_template = array(
			'page_title' => 'Forgot Password',
			'form_action' => site_url('forgot_process'),
			'page_content' => $page_content
		);
		$this->load->view('template/homepage_v', $data_template, FALSE);
	}

}

/* End of file Welcome.php */
/* Location: ./application/controllers/Welcome.php */