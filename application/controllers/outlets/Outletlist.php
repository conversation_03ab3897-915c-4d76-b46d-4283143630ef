<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Outletlist extends Auth_Controller {

	protected $outlet_photo_path; //path logo: receipt logo outlet

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->model('outlet/Outlets_model');

		//inisialisasi variabel
		$outlet_path = outlet_url();//'assets/outlets/'.$this->session->userdata('admin_id').'/'; //path 
		$outlet_path = str_replace(base_url(), '', $outlet_path).'';
		$this->outlet_photo_path = $outlet_path.$this->session->userdata('admin_id').'/';
	}

	public function index()
	{
		$data['featurelist'] = $this->outlet_feature->featurelist('mainfeature');
		$data['featurelist_authorization'] = $this->outlet_feature->featurelist('authorization');

		//feature access / permission access
		$data['permission'] = array(
			'add' => $this->privilege->check(uri_string(),'add'),
			'edit' => $this->privilege->check(uri_string(),'edit'),
			'delete' => $this->privilege->check(uri_string(),'delete'),
		);		

		//templating
		$data['page_title'] = 'Outlet List';
		$data['view_path'] = 'outlets/outlet/v2/';
		$this->template->view('outlets/outlet/v2/outletlist_v', $data);
	}

	public function datatables()
	{
		/* custom json output  start */
		$jsondata = $this->Outlets_model->json(); //ambil data json
		$json_decode = json_decode($jsondata); //pecah data json
		$dataArray = array();
		foreach ($json_decode->data as $a) {
			$dataArray[] = array(
				'outlet_id' => $a->outlet_id,
				'name' => htmlentities($a->name), //encoding string
				'address' => htmlentities($a->address),
				'phone' => htmlentities($a->phone),
				'country' => htmlentities($a->country),
				'province' => htmlentities($a->province),
				'city' => htmlentities($a->city),
				'postal_code' => htmlentities($a->postal_code),
				'expired_date' => $a->expired_date,
			);
		}

		//remake json data (JANGAN DIEDIT)
		$draw_json = array(
			'draw' => $json_decode->draw,
			'recordsTotal' => $json_decode->recordsTotal,
			'recordsFiltered' => $json_decode->recordsFiltered,
			'data' => $dataArray
		);

		//tampilkan data json
		echo format_json($draw_json);
		/* custom json output  end */
	}

	public function get($id=null)
	{
		$row = $this->Outlets_model->get_by_id($id);
		if ($row) {
			/* cek gambar START */
			$photo_name = $row->receipt_logo;
			$photo_path = (!empty($photo_name) ? $this->outlet_photo_path.$photo_name : '');
			$photo_path = str_replace(base_url(), '', $photo_path); //ambil path
			if (file_exists($photo_path)) {
				$photo_link = base_url($photo_path);
			}
			else{
				$photo_link = noimage(); //default pict
			}
			/* cek gambar END */

			$data = array(
				'outlet_id' => ($row->outlet_id),
				'name' => html_entity_decode($row->name),
				'address' => html_entity_decode($row->address),
				'phone' => html_entity_decode($row->phone),
				'country' => html_entity_decode($row->country),
				'province' => html_entity_decode($row->province),
				'city' => html_entity_decode($row->city),
				'postal_code' => html_entity_decode($row->postal_code),
				'feature' => json_decode($row->feature),
				//'expired_date' => set_value('expired_date', $row->expired_date)

				//receipt
				'receiptnotes' => html_entity_decode($row->receipt_note),
				'receiptphone' => html_entity_decode($row->receipt_phone),
				'receiptaddress' => html_entity_decode($row->receipt_address),
				'receiptsocmed' => html_entity_decode($row->receipt_socialmedia),
				'receiptlogo' => $photo_link,
			);

			$draw_json = array(
				'status' => 'success',
				'data' => $data
			);
		}
		else {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function action($actionType=null) //actionType = 'create / update'
	{
		$this->_rules(); //validasi rules
		$current_photo = '';

		/* PROSES OLAH GAMBAR START */
		$photo = $this->_upload_photo(); // PROSES OLAH GAMBAR
		$photo_status = $photo['status_upload'];    // upload foto berhasil atau tidak
		$photo_available = $photo['available'];     // submit form memiliki gambar atau tidak
		$error_imageupload = $photo['error_msg'];   // pesan error saat upload foto
		$photo_path = $photo['path'];               // path direktori foto
		$photo_name = $photo['name'];               // nama foto yang diupload
		/* PROSES OLAH GAMBAR END   */

		if ($this->form_validation->run() == FALSE || 
			($photo_status===FALSE && $photo_available===TRUE) //verifikasi kalau action create tidak ada foto
			) {

			//hapus foto yang baru diupload
			if ($photo_status===true) {
				$path = $photo_path.'/'.$photo_name;
				unlink($path); //hapus gambar yang baru saja diupload saat input gagal
			}

			//error response
			$message_feature = (!empty(form_error('mainfeature[]'))) ? form_error('mainfeature[]') : form_error('feature_authorization[]');

			$draw_json = array(
				'status' => 'error',
				'message'=> ucfirst($actionType).' Record Failed',
				'data'   => array(
					'name' => form_error('name'),
					'address' => form_error('address'),
					'phone' => form_error('phone'),
					'country' => form_error('country'),
					'province' => form_error('province'),
					'city' => form_error('city'),
					'postal_code' => form_error('postal_code'),
					'expired_date' => form_error('expired_date'),

					//receipt error
					'receiptnotes' => form_error('receiptnotes'),
					'receiptphone' => form_error('receiptphone'),
					'receiptaddress' => form_error('receiptaddress'),
					'receiptsocialmedia' => form_error('receiptsocmed'),
					'receiptlogo' => $error_imageupload
				)
			);
		}
		else {
			//data yang dikirim
			$data_post = array(
				'name' => $this->input->post('name',TRUE),
				'address' => $this->input->post('address',TRUE),
				'phone' => $this->input->post('phone',TRUE),
				'country' => $this->input->post('country',TRUE),
				'province' => $this->input->post('province',TRUE),
				'city' => $this->input->post('city',TRUE),
				'postal_code' => $this->input->post('postal_code',TRUE),
				//'expired_date' => $this->input->post('expired_date',TRUE),
				'receipt_note' => $this->input->post('receiptnotes', true),
				'receipt_phone' => $this->input->post('receiptphone', true),
				'receipt_address' => $this->input->post('receiptaddress', true),
				'receipt_socialmedia' => $this->input->post('receiptsocmed', true),
			);

			//mengetahui feature yang dicentang
			//MAIN FEATURE
			$checked_feature = array();
			$featurelist = $this->outlet_feature->featurelist('mainfeature');
			foreach ($featurelist as $list) {
				$checked = ($this->input->post('mainfeature['.$list[0].']')=='on') ? true : false;
				$checked_feature[$list[0]] = $checked;
			}
			//--FEATURE AUTHORIZATION
			$checked_feature['authorization'] = array();
			$featurelist_authorization = $this->outlet_feature->featurelist('authorization');
			foreach ($featurelist_authorization as $list) {
				$checked = ($this->input->post('feature_authorization['.$list[0].']')=='on') ? true : false;
				if ($checked===true) {
					$checked_feature['authorization'][$list[0]] = true;
				}
			}
			$data_post['feature'] = format_json($checked_feature);



			//deteksi actionType (create/update)
			if ($actionType=='create') {
				$data_post['receipt_logo'] = $photo_name;
				$response = $this->Outlets_model->insert($data_post);

				//insert employee outlet access
				if ($response && $this->session->userdata('user_type')=='employee') {
					$outlet_id = $this->db->insert_id();
					$this->db->insert('employee_outlet', array(
						'employee_fkid' => $this->session->userdata('user_id'),
						'outlet_fkid' => $outlet_id
					));
				}
			}
			elseif ($actionType=='update') {
				//get data untuk menghapus receipt logo lama
				$id = $this->input->post('id', true);
				$row = $this->Outlets_model->get_by_id($id);
				if ($row) {
					//action untuk update data
					if ($photo_available==true) {
						$data_post['receipt_logo'] = $photo_name;
					}
					$response = $this->Outlets_model->update($id, $data_post);
					if ($response) {
						//hapus gambar lama bila update gambar
						$filename = $photo_path.$row->receipt_logo;
						if ($photo_status===TRUE && $photo_available===TRUE) {
							//cek apakah file ada
							if (file_exists($filename) && !empty($row->receipt_logo)) {
								unlink($filename); //echo "The file $filename exists";
							}

							$current_photo = base_url($photo_path.'/'.$photo_name);
						}
						else{
							$current_photo = base_url($filename);
						}
					}
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Record Not Found'
					);
				}
			}

			//buat array untuk output json
			if ($response==true) {
				$draw_json = array(
					'status' => 'success',
					'message' => ucfirst($actionType).' Record Success',
					'data' => array(
						'receiptlogo' => $current_photo
					)
				);
			}
		}

		//buat output dalam format json
		echo format_json($draw_json);
	}

	public function delete($id=null)
	{
		//init
		$draw_json = array();

		//cek data
		$row = $this->Outlets_model->get_by_id($id);
		if ($row) {
			$response = $this->Outlets_model->delete($id); //hapus
			if ($response) {
				$filename = $this->outlet_photo_path.$row->receipt_logo;

				//hapus gambar dari direktori
				if (file_exists($filename) && (!empty($row->receipt_logo))) {
					unlink($filename); //echo "The file $filename exists";
				}

				//menambah notifikasi dari hapus data
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Record Success'
				);
			}
			else {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Delete Record Failed'
				);
			}
		}
		else {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//response output
		echo format_json($draw_json);
	}

	public function _rules() 
    {
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[50]|alpha_numeric_spaces');
        $this->form_validation->set_rules('address', 'address', 'trim|required|max_length[100]');
        $this->form_validation->set_rules('phone', 'phone', 'trim|required|is_numeric|max_length[15]');
        $this->form_validation->set_rules('country', 'country', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('province', 'province', 'trim|required|max_length[50]');
        $this->form_validation->set_rules('city', 'city', 'trim|required|max_length[50]');
        $this->form_validation->set_rules('postal_code', 'postal code', 'trim|required|max_length[6]');
        //$this->form_validation->set_rules('expired_date', 'expired date', 'trim|required'); //masih bug.. cari validation date
        $this->form_validation->set_rules('mainfeature[]', 'main feature', 'trim');
        $this->form_validation->set_rules('featurelist_authorization[]', 'feature authorization', 'trim');

        //receipt validation
        $this->form_validation->set_rules('receiptnotes', 'notes', 'trim');
        $this->form_validation->set_rules('receiptphone', 'phone', 'trim|max_length[15]|is_numeric');
        $this->form_validation->set_rules('receiptaddress', 'address', 'trim|max_length[100]');
        $this->form_validation->set_rules('receiptsocmed', 'social media', 'trim|max_length[100]');
        //$this->form_validation->set_rules('receiptlogo', 'logo', 'trim|min_length[5]|max_length[12]');

        $this->form_validation->set_rules('id', 'outlet_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

    public function _upload_photo()
    {
        /* PROSES OLAH GAMBAR by aziz */

        //inisialisasi variabel
        $error_imageupload = '';
        $photo_available = false;   //form ada submit foto atau tidak
        $photo_upload = false;      //foto berhasil diupload atau tidak

        /*konfigurasi upload gambar start*/
        //$photo_path = outlet_url(); //(ambil dari assets_helper)
        //$photo_path = str_replace(base_url(), '', $photo_path); //ambil path dari assets/images/outlets
        //$photo_path = str_replace(base_url(), '', $photo_path.'/'.$this->session->userdata('admin_id')); //ambil path dari assets/images/outlets
        $photo_path = $this->outlet_photo_path;

        $photo_name = $this->session->userdata('admin_id').'_'.$this->input->post('name').'_'.date('YmdHis'); //temporary name
        $photo_name = strtolower($photo_name); //mengubah nama file menjadi kecil semua
        $photo_name = str_replace(' ', '', $photo_name); //fungsi untuk menghilangkan spasi pada nama
        $config['upload_path']          = $photo_path; //'./_tes_upload/'.$album;
        $config['allowed_types']        = 'gif|jpg|png|jpeg'; //ekstensi yang diizinkan
        $config['max_size']             = 100; //maksimal size
        $config['file_name']            = $photo_name; //nama gambar akan diganti saat diupload
        //$config['max_width']  = '1024'; //atur lebar maksimal gambar
        //$config['max_height']  = '768'; //atur tinggi maksimal gambar
        $config['remove_spaces'] = true;
        /*konfigurasi upload gambar end*/
        
        /*proses upload gambar start*/
        //buat direktori berdasarkan owner
        $dir_exist = true;
        if (!is_dir($photo_path)) {
            /* kalau mkdir() disable, comment dari sini */
            $old = umask(0);
            mkdir($photo_path, 0775, true); //buat direktori image produk untuk per-owner
            umask($old);
            /* kalau mkdir() disable, comment sampai sini */
            $dir_exist = false; // dir not exist
            $draw_json = array(
                'status' => 'error',
                'message' => 'Dir not Exist'
            );
        }

        //upload gambar
        $this->load->library('upload', $config);
        if (!empty($_FILES['photo']['name']) && $_FILES['photo']['name']!="") { //bila upload foto
            $photo_available = true;

            if ( ! $this->upload->do_upload('photo')){
                $error_imageupload = array('error' => $this->upload->display_errors());
                $error_imageupload = $error_imageupload['error'];
                $photo_upload = false; //status upload
                //echo "upload gagal";
            }
            else{
                $data = array('upload_data' => $this->upload->data());
                $photo_name = $data['upload_data']['file_name'];
                $photo_upload = true; //status upload
                //echo "upload success";
            }
        }
        else{
            $photo_available = false;//custom
            $photo_name = '';
            //echo "tidak ada gambar";
        }
        /* PROSES OLAH GAMBAR END*/

        return $photo = array(
            'status_upload' => $photo_upload,
            'available' => $photo_available,
            'error_msg' => $error_imageupload,
            'path' => $photo_path,
            'name' => $photo_name,
        );
    }

    public function _employee_role($action='')
    {
    	$valid = true;
    }

}

/* End of file Outletlist.php */
/* Location: ./application/controllers/outlets/Outletlist.php */