<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Printer_ticket extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->outlets_printer('read_access'); //employee role access page
		$this->user_role->outlets('read_access'); //employee role access page

		//model
		$this->load->model('outlet/printer/Printer_model');
		$this->load->model('outlet/Devices_model'); //form
		#$this->load->model('products/products_category/Products_category_model');
		$this->load->model('products/products_subcategory/Products_subcategory_model');
	}

	public function create($element=null)
	{
		$data_post = array(
			'printer_id' => $this->input->post('id',true),
			'name' => $this->input->post('name',true), //ticket name
			'category' => $this->input->post('productcategory[]',true),
			'print_type' => $element
		);


		//cek ID setting printer valid atau tidak
        $cek_valid_printer = $this->Printer_model->get_by_id($data_post['printer_id']);
        if (!$cek_valid_printer) {
            $draw_json = array('status' => 'error', 'message'=>'Invalid Printer');
            //echo format_json($draw_json);die();
        }
        else{
        	//kalau printer setting valid
			if ($element != 'printorder' && $element != 'printlabel') {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Ticket Type'
				);
			}
			else{
				//validasi
				$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
				$this->form_validation->set_rules('id', 'printer id', 'trim|required');
				$this->form_validation->set_rules('name', 'ticket name', 'trim|required|max_length[30]');
				$this->form_validation->set_rules('productcategory[]', 'category', 'trim|required');


				if ($this->form_validation->run() == FALSE) {
					$draw_json = array(
						'status' => 'error',
						'message' => 'Create Ticket Failed',
						'data' => array(
							'printer_id' => form_error('id'),
							'ticket_name' => form_error('name'),
							'category' => form_error('productcategory[]')
						)
					);
				} else {
					//insert ticket
					$data_create = array(
						'name' => $data_post['name'],
						'setting_type' => $data_post['print_type'],
						'printer_setting_fkid' => $data_post['printer_id']
					);
					$response = $this->Printer_model->insert_print_ticket($data_create);
					$primary_key = $this->db->insert_id();

					//insert ke tabel ticket product category
					/* inisialisasi daftar product category yang dicentang */
					$categorySubmitted = $data_post['category'];
					if ($categorySubmitted[0]=='all_category') {
					    #$categorylist = $this->Products_category_model->form_select();//OLD
					    $categorylist = $this->Products_subcategory_model->form_select();
					    $i=0;
					    foreach ($categorylist as $value) {
					        #$categorySubmitted[$i] = $value->product_category_id;//OLD
					        $categorySubmitted[$i] = $value->product_subcategory_id;
					        $i++;
					    }
					}
					//kirim data berdasarkan category yang dicentang
					$loop = 0;
					$success_insert = 0;
                    foreach ($categorySubmitted as $checked) { //data checkbox outlet yang dikirim
                        $category_id = $checked;
                        $data_create_category = array(
                            'printersetting_ticket_fkid' => $primary_key,
                            #'product_category_fkid' => $category_id //OLD
                            'product_subcategory_fkid' => $category_id
                        );

                        //$response_category = $this->Printer_model->insert_print_ticket_category($data_create_category);
                        $response_category = $this->Printer_model->insert_print_ticket_subcategory($data_create_category);
                        $loop++;
                        ($response_category) ? $success_insert++ : '';
                    } //end foreach

                    if ($response) {
                    	switch ($data_post['print_type']) {
                    		case 'printorder':
                    			$ticket_type = 'Print Order';
                    			break;
                    		case 'printlabel':
                    			$ticket_type = 'Print Label';
                    			break;
                    		default: break;
                    	}
                    	$draw_json = array(
                    		'status' => 'success',
                    		'message' => 'Create '.$ticket_type.' Ticket Success'
                    	);
                    }
				}
			}
		}

		echo format_json($draw_json);
	}

	public function delete($ticket_id=null)
	{
		//cek apakah data benar
		$row = $this->Printer_model->get_print_ticket_by_id($ticket_id);
		if ($row) {
			$response = $this->Printer_model->delete_print_ticket($ticket_id);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Ticket Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Delete Ticket Failed'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Data Not Found'
			);
		}

		echo format_json($draw_json);
	}

	public function get_ticket_by_printer($printersetting_id, $ticket_type) //$ticket_type = 'printorder' || 'printlabel'
	{
		$data = array(
			'ticket' => array()
		);

		//ambil print order ticket
        $get_ticket = $this->Printer_model->get_print_ticket($printersetting_id, $ticket_type);
        foreach ($get_ticket as $a) {
            $temp_data = array(
                'ticket_id' => $a->printersetting_ticket_id,
                'ticket_name' => htmlentities($a->name)
            );

            array_push($data['ticket'], $temp_data);
        }

    	$draw_json = array(
        	'status' => 'success',
        	'ticket_type' => $ticket_type,
        	'data' => $data
    	);

    	echo format_json($draw_json);
	}

	public function update($element=null)
	{
		$data_post = array(
			'printer_id' => $this->input->post('id',true),
			'ticket_id' => $this->input->post('ticket_id',true),
			'name' => $this->input->post('name',true), //ticket name
			'category' => $this->input->post('productcategory[]',true),
			'print_type' => $element
		);


		//cek ID setting printer valid atau tidak
        $cek_valid_printer = $this->Printer_model->get_by_id($data_post['printer_id']);
        if (!$cek_valid_printer) {
            $draw_json = array('status' => 'error', 'message'=>'Invalid Printer');
            //echo format_json($draw_json);die();
        }
        else{
        	//kalau printer setting valid
			if ($element != 'printorder' && $element != 'printlabel') {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Ticket Type'
				);
			}
			else{
				//validasi
				$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
				$this->form_validation->set_rules('id', 'printer id', 'trim|required');
				$this->form_validation->set_rules('name', 'ticket name', 'trim|required|max_length[30]');
				$this->form_validation->set_rules('productcategory[]', 'category', 'trim|required');


				if ($this->form_validation->run() == FALSE) {
					$draw_json = array(
						'status' => 'error',
						'message' => 'Update Ticket Failed',
						'data' => array(
							'printer_id' => form_error('id'),
							'ticket_name' => form_error('name'),
							'category' => form_error('productcategory[]')
						)
					);
				} else {
					//insert ticket
					$data_update = array(
						'name' => $data_post['name'],
						//'setting_type' => $data_post['print_type'],
						//'printer_setting_fkid' => $data_post['printer_id']
					);
					//query update
					$this->db->where('printersetting_ticket_id', $data_post['ticket_id']);
					$this->db->where('printer_setting_fkid', $data_post['printer_id']);
					$this->db->where('setting_type', $element);
					$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
					$response = $this->db->update('setting_printer_ticket', $data_update);
					//$response = $this->Printer_model->insert_print_ticket($data_create); update
					$primary_key = $data_post['ticket_id'];


					//hapus semua tabel ticket product category yang sudah ada
					// $this->db->where('printersetting_ticket_fkid', $data_post['ticket_id']);
					// $this->db->delete('setting_printer_ticket_category');
					$this->db->where('printersetting_ticket_fkid', $data_post['ticket_id']);
					$this->db->delete('setting_printer_ticket_subcategory');


					//insert ke tabel ticket product category
					/* inisialisasi daftar product category yang dicentang */
					$categorySubmitted = $data_post['category'];
					if ($categorySubmitted[0]=='all_category') {
					    #$categorylist = $this->Products_category_model->form_select();
					    $categorylist = $this->Products_subcategory_model->form_select();
					    $i=0;
					    foreach ($categorylist as $value) {
					        #$categorySubmitted[$i] = $value->product_category_id;
					        $categorySubmitted[$i] = $value->product_subcategory_id;
					        $i++;
					    }
					}
					//kirim data berdasarkan category yang dicentang
					$loop = 0;
					$success_insert = 0;
                    foreach ($categorySubmitted as $checked) { //data checkbox outlet yang dikirim
                        $category_id = $checked;
                        $data_create_category = array(
                            'printersetting_ticket_fkid' => $primary_key,
                            #'product_category_fkid' => $category_id
                            'product_subcategory_fkid' => $category_id
                        );

                        #$response_category = $this->Printer_model->insert_print_ticket_category($data_create_category);
                        $response_category = $this->Printer_model->insert_print_ticket_subcategory($data_create_category);
                        $loop++;
                        ($response_category) ? $success_insert++ : '';
                    } //end foreach

                    if ($response) {
                    	switch ($data_post['print_type']) {
                    		case 'printorder':
                    			$ticket_type = 'Print Order';
                    			break;
                    		case 'printlabel':
                    			$ticket_type = 'Print Label';
                    			break;
                    		default: break;
                    	}
                    	$draw_json = array(
                    		'status' => 'success',
                    		'message' => 'Update '.$ticket_type.' Ticket Success'
                    	);
                    }
				}
			}
		}

		echo format_json($draw_json);
	}

}

/* End of file Printer_ticket.php */
/* Location: ./application/controllers/outlets/printer/Printer_ticket.php */