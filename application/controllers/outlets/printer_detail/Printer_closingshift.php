<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Printer_closingshift extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //model
        $this->load->model('outlet/printer/Printer_closingshift_model');
        $this->load->model('outlet/printer/Printer_model');
	}

	public function get_by_printer($printersetting_id=null)
	{
		//cek printer setting
		$row_cek = $this->Printer_model->get_by_id($printersetting_id);
		if ($row_cek) {
			$draw_json = array(
				'status' => 'success',
				'data' => array()
			);
			//get closingshift
			$getclosing = $this->Printer_closingshift_model->get_all($printersetting_id);
			foreach ($getclosing as $a) {
                $temp_data = array(
                    'closingshift_id' => $a->closingshift_id,
                    'name' => htmlentities($a->name),
                    'rules' => json_decode($a->rules)
                );

                array_push($draw_json['data'], $temp_data);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Printer Setting Not Found'
			);
		}

		echo format_json($draw_json);
	}

	public function get_by_id($closingshift_id=null)
	{
		$row = $this->Printer_closingshift_model->get_by_id($closingshift_id);
		if ($row) {
			$draw_json = array(
				'status' => 'success',
				'data' => array(
					'closingshift_id' => $row->closingshift_id,
					'name' => htmlentities($row->name),
					'rules' => json_decode($row->rules)
				)
			);
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		echo format_json($draw_json);
	}

	public function delete($closingshift_id=null)
	{
		//cek data
		$row = $this->Printer_closingshift_model->get_by_id($closingshift_id);
		if ($row) {
			//hapus data
			$response = $this->Printer_closingshift_model->delete($closingshift_id);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Delete Record Error'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		echo format_json($draw_json);
	}

	public function update($printersetting_id=null, $closingshift_id=null)
	{
		$draw_json = array();

		$this->_rules();
		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Update Record Failed',
				'data' => array(
					'name' => form_error('name'),
					'closingshift' => form_error('closing[]')
				)
			);
		} else {
			$rules_closingshift = $this->_checkedclosingshift();
			$data_update = array(
				'name' => $this->input->post('name', true),
				'rules' => $rules_closingshift
			);

			$response = $this->Printer_closingshift_model->update($closingshift_id, $data_update);
			if ($response===true) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Update Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Failed'
				);
			}
			
		}
		
		echo format_json($draw_json);
	}

	public function create()
	{
		$this->_rules();
		$data_post = array(
			'printer_id' => $this->input->post('id', true),
			'name' => $this->input->post('name', true),
			'rules' => $this->_checkedclosingshift()
		);

		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Create Record Failed',
				'data' => array(
					'name' => form_error('name'),
					'closingshift' => form_error('closing[]')
				)
			);
		} else {
			//cek printer
			$cekprintersetting = $this->Printer_model->get_by_id($data_post['printer_id']);
			if ($cekprintersetting) {
				//insert setting
				$data_create = array(
					'name' => $data_post['name'],
					'rules' => $data_post['rules'],
					'printer_setting_fkid' => $data_post['printer_id']
				);
				$response = $this->Printer_closingshift_model->insert($data_create);
				if ($response) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Create Record Success'
					);
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Create Record Failed'
					);
				}
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Printer'
				);
			}
		}

		echo format_json($draw_json);
	}

	public function _checkedclosingshift()
	{
		//$checked_closingshift = array();
		$checked_closingshift = array(
            'time' => (!empty($this->input->post('closing[time]'))) ? true : false,
            'shiftname' => (!empty($this->input->post('closing[shiftname]'))) ? true : false,
            'sales' => (!empty($this->input->post('closing[sales]'))) ? true : false,
            'paymentmedia' => (!empty($this->input->post('closing[paymentmedia]'))) ? true : false,
            'tax' => (!empty($this->input->post('closing[tax]'))) ? true : false,
            'itemsales' => (!empty($this->input->post('closing[itemsales]'))) ? true : false,
            'groupsales' => (!empty($this->input->post('closing[groupsales]'))) ? true : false,
            'refund' => (!empty($this->input->post('closing[refund]'))) ? true : false,
            'fifo' => (!empty($this->input->post('closing[fifo]'))) ? true : false,
            'actual' => (!empty($this->input->post('closing[actual]'))) ? true : false,
            'purchase' => (!empty($this->input->post('closing[purchase]'))) ? true : false,
            'operationalcost_detail' => (!empty($this->input->post('closing[opcostdetail]'))) ? true : false,
            'debt' => (!empty($this->input->post('closing[debt]'))) ? true : false,
            'entertaint_income' => (!empty($this->input->post('closing[entertaintincome]'))) ? true : false,
            'compliment_detail' => (!empty($this->input->post('closing[complimentdetail]'))) ? true : false,
            'discount_detail' => (!empty($this->input->post('closing[discountdetail]'))) ? true : false,
            'free_detail' => (!empty($this->input->post('closing[freedetail]'))) ? true : false,
            'voucher_detail' => (!empty($this->input->post('closing[voucherdetail]'))) ? true : false,
            'dutymeals_detail' => (!empty($this->input->post('closing[dutydetail]'))) ? true : false,
            'promo_detail' => (!empty($this->input->post('closing[promodetail]'))) ? true : false,
            'piutang_detail' => (!empty($this->input->post('closing[piutangdetail]'))) ? true : false,
            'avgpaxbill' => (!empty($this->input->post('closing[avgpaxbill]'))) ? true : false,
        );

        if (!empty($this->input->post('closing[all]'))) {
        	$checked_closingshift = array(
	            'time' => true,
	            'shiftname' => true,
	            'sales' => true,
	            'paymentmedia' => true,
	            'tax' => true,
	            'itemsales' => true,
	            'groupsales' => true,
	            'refund' => true,
	            'fifo' => true,
	            'actual' => true,
	            'purchase' => true,
	            'operationalcost_detail' => true,
	            'debt' => true,
	            'entertaint_income' => true,
	            'compliment_detail' => true,
	            'discount_detail' => true,
	            'free_detail' => true,
	            'voucher_detail' => true,
	            'dutymeals_detail' => true,
	            'promo_detail' => true,
	            'piutang_detail' => true,
	            'avgpaxbill' => true,
	        );
        }

        return format_json($checked_closingshift);
	}

	public function _rules()
	{
		$this->form_validation->set_rules('id', 'printer id', 'trim|required');
		$this->form_validation->set_rules('closingshift_id', 'closingshift_id', 'trim');
		$this->form_validation->set_rules('action', 'action', 'trim|required');
		$this->form_validation->set_rules('name', 'closing shift name', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('closing[]', 'closing feature', 'trim|required');

		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
	}

}

/* End of file Printer_closingshift.php */
/* Location: ./application/controllers/outlets/printer_detail/Printer_closingshift.php */