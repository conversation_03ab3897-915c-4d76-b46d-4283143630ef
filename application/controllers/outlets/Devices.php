<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Devices extends Auth_Controller {

    public function __construct()
    {
        parent::__construct();
        //Do your magic here
        $this->load->model('outlet/Devices_model');
        $this->load->model('outlet/Outlets_model'); //form
    }

	public function index()
	{
		$link = site_url('outlets/devices/'); //URL dengan slash
        $data = array(
            'kolomID' => 'device_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'update',
            'ajaxActiongetDataEdit' => $link.'get_data/', //ambil data yang akan diedit
            'device_slot_list' => $this->Devices_model->get_count_subscribe_device(),
            'device_active_list' => $this->Devices_model->get_active_device(),
            'ajaxDeviceLogout' => $link.'logout/',
        );

        //form
        $data['form_select_outlet'] = $this->Outlets_model->form_select();

		// $this->load->view('outlets/device/device_v', $data);
        //templating
        $data['page_title'] = 'Devices';
        $data['view_path'] = 'outlets/device/v2/';
        $this->template->view('outlets/device/v2/device_v2', $data);
	}

	public function json()
	{
		$jsondata = $this->Devices_model->json();
		$json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->device_id;
            $name = htmlentities($a->device_name); //encoding string
            $outlet_name = htmlentities($a->outlet_name); //encode string


            $action  = '<button class="btn btn-xs btn-warning btn-edit" onclick="actionEdit('.$id.')"><i class="fa fa-edit"></i></button>';
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= '<button class="btn btn-xs btn-danger btn-hapus" onclick="actionDelete('.$id.')"><i class="fa fa-trash"></i></button>';
            
            
            $temp_data = array(
                'device_id' => $id,
                'device_name' => $name,
                'outlet_name' => $outlet_name,
                'action' => $action
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
	}

	public function get_data($device_id=null)
	{
		$row = $this->Devices_model->get_by_id($device_id);
        if ($row) {
            $data = array(
                'device_id' => ($row->device_id),
                'name' => html_entity_decode($row->name),
                'outlet' => html_entity_decode($row->outlet_fkid),
            );
            $draw_json = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }
        echo json_encode($draw_json);
	}

	public function update()
	{
		$this->_rules();

		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Update Record Failed',
				'data' => array(
					'id' => form_error('id'),
					'name' => form_error('name'),
					'outlet' => form_error('outlet')
				)
			);
		} else {
			//data yang dikirim
			$data_post = array(
				'id' => $this->input->post('id', TRUE),
				'name' => $this->input->post('name', TRUE),
				'outlet' => $this->input->post('outlet', TRUE)
			);

			//cek apakah outlet benar
			$cek_outlet = $this->Outlets_model->get_by_id($data_post['outlet']);
			if ($cek_outlet->outlet_id != $data_post['outlet']) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Outlet'
				);
			}
			else{
				//data yang akan di update
				$data_post_update = array(
					'name' => $data_post['name'],
					'outlet_fkid' => $data_post['outlet']
				);

				//update data
				$response = $this->Devices_model->update($this->input->post('id'), $data_post_update);
				if ($response) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Update Record Success'
					);
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Update Record Failed!'
					);
				}
			}
		}

		echo format_json($draw_json);
	}

	public function logout($device_id=null)
	{
		$row = $this->Devices_model->get_by_id($device_id);
        if ($row) {
        	$data_update = array(
        		'device_status' => 'off'
        	);
        	$response = $this->Devices_model->update($device_id, $data_update);
        	if ($response) {
        		//logout device
	            $draw_json = array(
	                'status' => 'success',
	                'message' => 'Logout Device Success'
	            );
        	}
        	else{
        		$draw_json = array(
        			'status' => 'error',
        			'message' => 'Logout Failed'
        		);
        	}
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }
        
        echo json_encode($draw_json);
	}

	public function delete($id=null)
    {
        $row = $this->Devices_model->get_by_id($id);
        if ($row) {
            //cek device sudah logout atau belum
            if ($row->device_status=='on') {
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Please logout this device'
                );
            }
            else{
                $response = $this->Devices_model->delete($id);
                if ($response==true) {
                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Record Deleted'
                    );
                } else {
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Delete Record Failed'
                    );
                }
            }
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record not Found'
            );
        }

        //response output
        echo format_json($draw_json);
    }

	public function _rules()
	{
		$this->form_validation->set_rules('id', 'id', 'trim|required');
		$this->form_validation->set_rules('name', 'device name', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('outlet', 'outlet', 'trim|required');

		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
	}

}

/* End of file Devices.php */
/* Location: ./application/controllers/outlets/Devices.php */