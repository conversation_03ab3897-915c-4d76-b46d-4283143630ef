<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Paymentmedia extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->model('outlet/Outlets_model');
	}

	public function index()
	{
		$link = site_url('outlets/paymentmedia/'); //URL dengan slash
		$data = array(
			'ajaxActionSave'	=> $link.'save',
			'ajaxActionGet'		=> $link.'get_data'
		);

		//form
		$data['form_select_outlet'] = $this->Outlets_model->form_select();

		//templating
		$data['page_title'] = 'Payment Media';
		$data['view_path'] = 'outlets/paymentmedia/v2/';
		$this->template->view('outlets/paymentmedia/v2/payment_media_v2', $data);
	}

	public function get_data($paymentmedia=null)
	{
		$payment = '';
		switch ($paymentmedia) {
			case 'cash':
				$payment = 'payment_cash';
				break;
			case 'card':
				$payment = 'payment_card';
				break;
			case 'compliment':
				$payment = 'payment_compliment';
				break;
			case 'piutang':
				$payment = 'payment_piutang';
				break;
			case 'duty':
				$payment = 'payment_duty';
				break;
			default:
				# code...
				break;
		}

		//ambil data
		$this->db->where($payment, true);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
		$result = $this->db->get('outlets')->result();

		$data_get = array(); //inisialisasi
		foreach ($result as $a) {
			$temp = array(
				'outlet_id' => $a->outlet_id,
				'outlet_name' => htmlentities($a->name)
			);

			array_push($data_get, $temp);
		}

	
		$draw_json = array(
			'status' => 'success',
			'data' => $data_get
		);	

		//tampilkan json
		echo format_json($draw_json);
	}

	public function save()
	{
		//data yang dikirim
		$data_post = array(
			'action' => $this->input->post('action', TRUE),
			'outlet' => $this->input->post('outlet[]', TRUE),
		);

		//CI validation
		$this->form_validation->set_rules('action', 'action', 'trim|required');
		$this->form_validation->set_rules('outlet[]', 'outlet', 'trim');
		$this->form_validation->set_error_delimiters('', '');

		
        //VALIDASI
		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message'=> 'Update Record Failed',
				'data' => array(
					'action' => form_error('action'),
					'outlet' => form_error('outlet[]'),
				)
			);
		} else {
			//inisialisasi daftar outlet yang dicentang
            $outletSubmitted = $data_post['outlet'];
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }


            //matikan semua payment media
			switch ($data_post['action']) {
				case 'cash':
					$payment = 'payment_cash';
					break;
				case 'card':
					$payment = 'payment_card';
					break;
				case 'compliment':
					$payment = 'payment_compliment';
					break;
				case 'piutang':
					$payment = 'payment_piutang';
					break;
				case 'duty':
					$payment = 'payment_duty';
					break;
				default:
					# code...
					break;
			}

			//matikan payment
			$object = array(
				$payment => false,
				'data_modified' => date('Y-m-d H:i:s'),
			);
			if ($this->session->userdata('user_type')=='employee') {
				$this->privilege->refresh_outlet_access(); //refresh outlet access
				$outlet_access = $this->session->userdata('outlet_access');
				if (isset($data_post['outlet'])) {
					$outletSubmitted = array_intersect($data_post['outlet'], $outlet_access);
				}
	            $this->db->where_in('outlet_id', $outlet_access);
	        }
			$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //berdasarkan yang login
			$result = $this->db->update('outlets', $object);


            //kirim data berdasarkan outlet yang dicentang
            if (!empty($outletSubmitted)) {
	            foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
	                $outlet_id = $checkedoutlet;
	                //cek outlet id
	                $cek_outlet = $this->Outlets_model->get_by_id($outlet_id);
	                if ($cek_outlet) {
						//aktifkan yang dicentang
						$object = array(
							$payment => true,
							'data_modified' => date('Y-m-d H:i:s'),
						);
						$this->db->where('outlet_id', $outlet_id);
						$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //berdasarkan yang login
						$result = $this->db->update('outlets', $object);
	                }

	            } //end foreach
            }


            $draw_json = array(
            	'status' => 'success',
            	'message' => 'Update Record Success'
            );
		}

		//tampilkan data json
		echo format_json($draw_json);
	}

}

/* End of file Payment_media.php */
/* Location: ./application/controllers/outlets/Payment_media.php */