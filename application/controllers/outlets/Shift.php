<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Shift extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
        $this->load->model('outlet/Shift_model');
        $this->load->model('outlet/Outlets_model'); //form
	}

	public function index()
	{
		$link = site_url('outlets/shift/'); //URL dengan slash
        $data = array(
            'kolomID' => 'shift_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );

        //form
        $data['form_select_outlet'] = $this->Outlets_model->form_select();

        //templating
        $data['page_title'] = 'Shift';
        $data['view_path'] = 'outlets/shift/v2/';
		$this->template->view('outlets/shift/v2/shift_v2', $data);
	}

	public function json()
	{
		/* custom json output  start */
        $jsondata = $this->Shift_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->shift_id;
        	$name = htmlentities($a->name); //encoding string
            $outlet_list = $this->Shift_model->available_outlet($id);
            $tombol_outlet_available = '';
            foreach ($outlet_list as $a) {
                $tombol_outlet_available .= '<br>';
                $tombol_outlet_available .= htmlentities($a->outlet_name);
            }
            $tombol_outlet = '<div class="btn btn-primary btn-xs tiptext">Outlet
                                <div class="description">'.$name.' available on: '.$tombol_outlet_available.'</div>
                                </div>';

        	$action  = "<a href='javascript:void(0)' class='btn btn-xs btn-warning' onclick='actionEdit({$id})'><span class='fa fa-edit'></a>";
            $action .= "&nbsp;";
            $action .= "<a href='javascript:void(0)' class='btn btn-xs btn-danger' onclick='actionDelete({$id})'><span class='fa fa-trash'></a>";
        	
        	
        	$temp_data = array(
        		'shift_id' => $id,
        		'name' => $name,
                'tombol_outlet' => $tombol_outlet,
        		'action' => $action
        	);
        	array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
	}

	public function action($actionType=null) //actionType = 'create / update'
    {
        $message = '';
        $draw_json = array();
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $message = 'Create Record Failed';
            }
            elseif ($actionType=='update') {
                $message = 'Update Record Failed';
            }

            $draw_json = array(
                'status' => 'error',
                'message'=> $message,
                'data'   => array(
                    'name' => form_error('name'),
                    'outlet' => form_error('outlet[]')
                )
            );
        } else {
            //data yang dikirim
            $data = array(
                'name' => $this->input->post('name',TRUE),
            );
            $data_post['outlet'] = $this->input->post('outlet[]', true);


            //inisialisasi daftar outlet yang dicentang START
            $outletSubmitted = $data_post['outlet'];
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }
            //inisialisasi daftar outlet yang dicentang END

            //deteksi actionType (create/update)
            $this->db->trans_start();
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Shift_model->insert($data);
                $primary_key = $this->db->insert_id();
                $message = 'Create Record Success';
            }
            elseif ($actionType=='update') {
                //action untuk update data
                if ($this->session->userdata('user_type')=='employee') {
                    $this->privilege->refresh_outlet_access(); //refresh outlet access
                    $outlet_access = $this->session->userdata('outlet_access');
                    $outletSubmitted = array_intersect($data_post['outlet'], $outlet_access);
                }
                $response = $this->Shift_model->update($this->input->post('id', TRUE), $data);
                $response_delete = $this->Shift_model->available_outlet_delete($this->input->post('id'));
                $message = 'Update Record Success';
            }

            //kirim data berdasarkan outlet yang dicentang
            foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                $outlet_id = $checkedoutlet;

                switch ($actionType) {
                    case 'create':
                        $data_outlet = array(
                            'shift_id' => $primary_key,
                            'outlet_id' => $outlet_id
                        );
                        
                        break;
                    case 'update':
                        $data_outlet = array(
                            'shift_id' => $this->input->post('id' ,true),
                            'outlet_id' => $outlet_id
                        );
                        break;
                }
                $response_insert = $this->Shift_model->available_outlet_insert($data_outlet);
            } //end foreach
            $response = $this->db->trans_complete();

            
            //buat array untuk output json
            if ($response==true) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => $message
                );
            }else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Process Failed'
                );
            }
        }

        //buat output dalam format json
        echo format_json($draw_json);
    }

    public function get_data($id=null)
	{
		$row = $this->Shift_model->get_by_id($id);
        if ($row) {
            $data = array(
				'shift_id' => $row->shift_id,
				'name' => $row->name,
                'outlet' => array()
    		);

            //get shft outlet
            $data_outlet = $this->Shift_model->get_detail($id);
            foreach ($data_outlet as $r) {
                $data['outlet'][] = array(
                    'outlet_id' => $r->outlet_fkid
                );
            }

            $draw_json = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Data Not Found'
            );
        }
        echo format_json($draw_json);
	}

    public function delete($id=null)
    {
    	$row = $this->Shift_model->get_by_id($id);

        if ($row) {
            $response = $this->Shift_model->delete($id);
            if ($response==true) {
            	$this->session->set_flashdata('message', 'Record Deleted');
            	$response = array(
            		'status' => 'success',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            } else {
            	$this->session->set_flashdata('message', 'Deleted Failed');
            	$response = array(
            		'status' => 'error',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
            	'status' => 'error',
            	'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            	);
        }

        //response output
        echo format_json($response);
    }

    // get shift by outlet
    public function get_by_outlet($outlet_id)
    {
        $data = $this->Shift_model->get_by_outlet($outlet_id);
        print_r(json_encode($data));
    }


    public function _rules() 
    {
		$this->form_validation->set_rules('name', 'name', 'required|max_length[30]');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');

		$this->form_validation->set_rules('id', 'outlet_id', 'trim');
		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Shift.php */
/* Location: ./application/controllers/outlets/Shift.php */