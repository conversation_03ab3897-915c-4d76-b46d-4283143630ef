<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Bankaccount extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //Do your magic here
        $this->load->model('outlet/Paymentmedia_bankaccount_model', 'Bankaccount_model');
        $this->load->model('outlet/Outlets_model'); //form
    }

	public function index()
	{
		$link = site_url('outlets/bankaccount/'); //URL dengan slash
		$data = array(
			'kolomID'					=> 'bank_id', //nama kolom primary key pada tabel
			'currentURL'				=> $link, //link yang sedang diakses
			'ajaxActionDatatableList'	=> $link.'datatable',
			'ajaxActionCreate'			=> $link.'action/create',
			'ajaxActionGet'				=> $link.'get_data/',
			'ajaxActionUpdate'		=> $link.'action/update/',
			'ajaxActionDelete'		=> $link.'delete/',
		);

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
		// $this->load->view('outlets/bankaccount/bank_v', $data);
        
        //templating
        $data['page_title'] = 'Bank Account';
        $data['view_path'] = 'outlets/bankaccount/v2/';
        $this->template->view('outlets/bankaccount/v2/bank_v2', $data);
	}

	public function datatable()
    {
        /* custom json output  start */
        $jsondata = $this->Bankaccount_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json        

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            //encoding string untuk mencegah XSS injection dengan htmlentities()
        	$id = $a->bank_id;
        	$name = htmlentities($a->name); //encoding string
            $norekening = htmlentities($a->no_rekening); //encoding string
            $owner = ($a->owner ?? '');
            
            //tombol action pada datatable
        	$action = "<a href='javascript:void(0)' onclick='tombolEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";


            //data yang akan ditampilkan dalam json
        	$temp_data = array(
        		'bank_id' => $id,
        		'name' => $name,
                'no_rekening' => $norekening,
                'owner' => $owner,
        		'action' => $action,
                'provider' => $a ->provider,
        	);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json); //function ambil dari helper
        /* custom json output  end */
    }

    public function action($actionType=null)
    {
    	$this->_rules(); //validasi input

        //verifikasi outlet sudah diisi atau belum multi outlet
        $status_checked_outlet = true;
        if ($this->input->post('outlet[]')==null && $this->input->post('outlet')==null) {
            $status_checked_outlet = false;
        }

    	//VALIDASI CI
    	if ($this->form_validation->run() == FALSE) {
    		//action salah
    		if ($actionType=='create') {
    			$error_message = $this->session->set_flashdata('message', 'Create Record Failed');
    		}
    		elseif ($actionType=='update') {
    			$error_message = $this->session->set_flashdata('message', 'Update Record Failed');
    		}

    		$draw_json = array(
				'status' => 'error',
				'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
				'data' => array(
					'name' => form_error('name'),
					'accountno' => form_error('accountno'),
                    'account_owner' => form_error('account_owner'),
                    'outlet' => form_error('outlet[]'),
				)
			);
    	} else {
            $this->db->trans_start();


            //data yang di-post (dikirim)
            $id = $this->input->post('id', true);
            $data_post = array(
                'bank_id' => $id,
                'name' => $this->input->post('name', true),
                'no_rekening' => $this->input->post('accountno', true),
                'outlet' => $this->input->post('outlet[]', true),
            );

            //inisialisasi daftar outlet yang dicentang
            $outletSubmitted = $data_post['outlet'];
            if (isset($outletSubmitted[0]) && $outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }


    		$message = '';
    		switch ($actionType) {
	    		case 'create':
                    $data_post_create = array(
                        'name' => $this->input->post('name', true),
                        'no_rekening' => $this->input->post('accountno', true),
                        'owner' => $this->input->post('account_owner', true),
                    );
	    			$result = $this->Bankaccount_model->insert($data_post_create);
                    $primary_key = $this->db->insert_id(); //bank id

                    //insert ke outlet yang aktif
                    //kirim data berdasarkan outlet yang dicentang
                    if (!empty($outletSubmitted)) {
                        foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                            $outlet_id = $checkedoutlet;

                            $active_on_pos = ($this->input->post('feature_pos['.$outlet_id.']')) ? '1' : '0';
                            $active_on_crm = ($this->input->post('feature_crm['.$outlet_id.']')) ? '1' : '0';

                            $databankdetail = array(
                                'bank_fkid' => $primary_key,
                                'outlet_fkid' => $outlet_id,
                                'active_on_pos' => $active_on_pos,
                                'active_on_crm' => $active_on_crm,
                            );

                            $this->Bankaccount_model->insert_activeoutlet($databankdetail);
                        } //end foreach
                    }

	    			if ($result===true) {
	    				$draw_json = array(
	    					'status' => 'success',
	    					'message' => 'Create Record Success'
	    				);
	    			}
	    			else{
	    				$draw_json = array(
	    					'status' => 'error',
	    					'message' => 'Create Record Failed'
	    				);
	    			}
	    			break;
	    		case 'update':
                    $data_post_update = array(
                        'bank_id' => $id,
                        'name' => $this->input->post('name', true),
                        'no_rekening' => $this->input->post('accountno', true),
                        'owner' => $this->input->post('account_owner', true),
                    );
	    			$result = $this->Bankaccount_model->update($id, $data_post_update);
                    $primary_key = $id;
                    //update ke outlet yang aktif
                    /* matikan outlet yang aktif */
                    $alloutlet = $this->Outlets_model->form_select();
                    foreach ($alloutlet as $a) {
                        $data_disable = array(
                            'bank_fkid' => $data_post_update['bank_id'],
                            'outlet_fkid' => $a->outlet_id,
                        );
                        $this->Bankaccount_model->update_disableoutlet($data_disable);
                    }

                    //kirim data berdasarkan outlet yang dicentang (mengaktifkan data)
                    if (!empty($outletSubmitted)) {
                        foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                            $outlet_id = $checkedoutlet;
                            
                            /*cek dulu apakah data sudah ada atau belum*/
                            $active_on_pos = ($this->input->post('feature_pos['.$outlet_id.']')) ? '1' : '0';
                            $active_on_crm = ($this->input->post('feature_crm['.$outlet_id.']')) ? '1' : '0';

                            $data_enable = array(
                                'bank_fkid' => $data_post_update['bank_id'],
                                'outlet_fkid' => $outlet_id,
                                'active_on_pos' => $active_on_pos,
                                'active_on_crm' => $active_on_crm,
                            );
                            $result_cek = $this->Bankaccount_model->cek_outlet($data_enable);
                            if ($result_cek->num_rows()<=0) {
                                //kalau data belum ada
                                $result = $this->Bankaccount_model->insert_activeoutlet($data_enable);
                            }
                            else{
                                //kalau data sudah ada, update data
                                $result = $this->Bankaccount_model->update_activeoutlet($data_enable);
                            };
                        } //end foreach
                    }
	    			if ($result===true) {
	    				$draw_json = array(
	    					'status' => 'success',
	    					'message' => 'Update Record Success'
	    				);
	    			}
	    			else{
	    				$draw_json = array(
	    					'status' => 'error',
	    					'message' => 'Update Record Failed'
	    				);
	    			}
	    			break;

	    		default: break;
	    	}

            $this->db->trans_complete();
    	}

    	echo format_json($draw_json);
    }

    public function get_data($bank_id=null)
    {
    	$id = $bank_id;
    	$row = $this->Bankaccount_model->get_by_id($id);
    	if ($row) {
            $data = array(
				'bank_id' => $row->bank_id,
				'name' => htmlentities($row->name),
				'accountno' => htmlentities($row->no_rekening),
                'account_owner'=>htmlentities($row->owner),
	    	);

            //get active outlet
            $result_activeoutlet = $this->Bankaccount_model->get_activeoutlet($bank_id);
            $data['outlet'] = array();
            foreach ($result_activeoutlet->result() as $a) {
                $temp = array(
                    'outlet_id' => $a->outlet_fkid,
                    'active_feature' => [
                        'pos' => $a->active_on_pos ?? '0',
                        'crm' => $a->active_on_crm ?? '0'
                    ]
                );

                array_push($data['outlet'], $temp);
            }

            //buat tampilan json
	    	$draw_json = array(
	    		'status' => 'success',
	    		'data'	 => $data
	    	);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $draw_json = array(
            	'status' => 'error',
            	'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            );
        }

        echo format_json($draw_json);
    }

    public function delete($bank_id=null)
    {
    	$id = $bank_id;
    	$response = $this->Bankaccount_model->delete($id);

    	if ($response===true) {
            $response = array(
                'status' => 'success',
                'message' => 'Delete Record Success'
                );
        }
        else{
            $response = array(
                'status' => 'error',
                'message' => 'Delete Record Failed'
                );
        }

        echo format_json($response);
    }

    public function _rules()
    {
    	$this->form_validation->set_rules('name', 'bank name', 'trim|required|max_length[15]');
    	$this->form_validation->set_rules('accountno', 'account no.', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('account_owner', 'account owner', 'trim|required|max_length[255]');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim');

    	$this->form_validation->set_rules('id', 'bank_id', 'trim');
		$this->form_validation->set_error_delimiters('', '');
    }

}

/* End of file Bankaccount.php */
/* Location: ./application/controllers/outlets/Bankaccount.php */