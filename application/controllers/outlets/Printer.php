<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Printer extends Auth_Controller {

    protected $ticketlist_closingshift;

    public function __construct()
    {
        parent::__construct();
        //Do your magic here
        $this->load->model('outlet/printer/Printer_model');
        $this->load->model('outlet/printer/Printer_closingshift_model');
        $this->load->model('outlet/Devices_model'); //form
        //$this->load->model('products/products_category/Products_category_model'); //old
        $this->load->model('products/products_subcategory/Products_subcategory_model');
        $this->load->model('outlet/Outlets_model'); //form

        $this->ticketlist_closingshift = array(
            'time' => 'Time',
            // 'shiftname' => 'Shift Name',
            // 'sales' => 'Sales',
            'paymentmedia' => 'Payment Media',
            'tax' => 'Tax',
            'itemsales' => 'Item Sales',
            'groupsales' => 'Group Sales',
            'refund' => 'Refund',
            'fifo' => 'FIFO',
            'actual' => 'Actual',
            // 'purchase' => 'Purchase',
            'opcostdetail' => 'Operational Cost Detail',
            // 'debt' => 'Debt',
            'entertainincome' => 'Entertaint Income',
            // 'complimentdetail' => 'Compliment Detail',
            // 'discountdetail' => 'Discount Detail',
            // 'freedetail' => 'Free Detail',
            // 'voucher' => 'Voucher',
            // 'dutydetail' => 'Duty Meals Detail',
            // 'promodetail' => 'Promo Detail',
            // 'piutangdetail' => 'Borrowed Detail',
            'avgpaxbill' => 'Average Pax & Bill</option>',
        );
        if (ENVIRONMENT != 'production') {
            $this->ticketlist_closingshift = array_merge($this->ticketlist_closingshift, array(
                'shiftname' => 'Shift Name',
                'sales' => 'Sales',
                'purchase' => 'Purchase',
                'debt' => 'Debt',
                'complimentdetail' => 'Compliment Detail',
                'discountdetail' => 'Discount Detail',
                'freedetail' => 'Free Detail',
                'voucher' => 'Voucher',
                'dutydetail' => 'Duty Meals Detail',
                'promodetail' => 'Promo Detail',
                'piutangdetail' => 'Borrowed Detail',
            ));
        }
    }

	public function index()
	{
        $link = current_url().'/';
        $data = array(
            'kolomID' => 'printer_setting_id', //nama kolom primary key pada tabel
            'currentURL' => $link,
            'ajaxActionDatatableList' => $link.'datatables',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionUpdate' => $link.'update/',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );

        $data['ajaxActionCreate_ticket'] = site_url('outlets/printer_detail/');
        $data['form_select_device'] = $this->Devices_model->form_select();
        //$data['form_select_productcategory'] = $this->Products_category_model->form_select(); //old
        $data['form_select_productsubcategory'] = $this->Products_subcategory_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();

        $data['ticketlist_closingshift'] = $this->ticketlist_closingshift;


		// $this->load->view('outlets/printer/printer_v', $data);
        $data['page_title'] = 'Printer';
        $this->template->view('outlets/printer/printer_v2', $data);
	}

    public function datatables()
    {
        $jsondata = $this->Printer_model->datatables();
        $json_decode = json_decode($jsondata);
        $data = array();
        foreach ($json_decode->data as $a) {
            $data[] = array(
                'printer_setting_id' => $a->printer_setting_id,
                'printer_name' => htmlentities($a->printer_name),
                'mac_address' => htmlentities($a->mac_address),
                'outlet_name' => htmlentities($a->outlet_name),
            );
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $data
        );

        //output
        echo format_json($draw_json);
    }

    public function get_data($printersetting_id=null)
    {
        $row = $this->Printer_model->get_by_id($printersetting_id);

        if ($row) {
            $data = array(
                'printer_setting_id' => htmlentities($row->printer_setting_id),
                'name' => htmlentities($row->printer_name),
                'papersize' => $row->setting_printpapersize,
                'max_print' => $row->max_print,
                'device' => array(),
                'print_receipt' => $row->setting_printreceipt,
                'print_receipt_jumlah' => $row->setting_printreceipt_jumlah,
                'print_receipt_orderid' => $row->setting_printreceiptorderid,
                'print_order' => $row->setting_printorder,
                'print_order_ticket' => array(),
                'print_label' => $row->setting_printlabel,
                'print_label_ticket' => array(),
                'closingshift' => json_decode($row->setting_closingshift),
                'closingshift_data' => array(),
                //'closingshift_rules' => json_decode($row->setting_closingshift_rules),
                'outlet_id' => $row->outlet_fkid,
            );

            //ambil device
            $get_device = $this->Printer_model->get_setting_device($printersetting_id);
            foreach ($get_device as $a) {
                $temp_data = array(
                    'device_id' => $a->device_fkid,
                );
                array_push($data['device'], $temp_data);
            }

            //ambil print order ticket
            $get_ticket = $this->Printer_model->get_print_ticket($printersetting_id, 'printorder');
            foreach ($get_ticket as $a) {
                $temp_data = array(
                    'ticket_id' => $a->printersetting_ticket_id,
                    'ticket_name' => htmlentities($a->name),
                    'list' => array()
                );

                //get list
                $list = $this->Printer_model->get_ticket_subcategory($a->printersetting_ticket_id);
                foreach ($list as $b) {
                    $temp_data['list'][] = array(
                        'subcategory_id' => $b->product_subcategory_fkid
                    );
                }

                array_push($data['print_order_ticket'], $temp_data);
            }

            //ambil print label ticket
            $get_ticket = $this->Printer_model->get_print_ticket($printersetting_id, 'printlabel');
            foreach ($get_ticket as $a) {
                $temp_data = array(
                    'ticket_id' => $a->printersetting_ticket_id,
                    'ticket_name' => htmlentities($a->name),
                    'list' => array()
                );

                //get list
                $list = $this->Printer_model->get_ticket_subcategory($a->printersetting_ticket_id);
                foreach ($list as $b) {
                    $temp_data['list'][] = array(
                        'subcategory_id' => $b->product_subcategory_fkid
                    );
                }

                array_push($data['print_label_ticket'], $temp_data);
            }

            //ambil data closingshift
            $get_ticket = $this->Printer_closingshift_model->get_all($printersetting_id);
            foreach ($get_ticket as $a) {
                $temp_data = array(
                    'closingshift_id' => $a->closingshift_id,
                    'name' => htmlentities($a->name),
                    'rules' => json_decode($a->rules)
                );

                array_push($data['closingshift_data'], $temp_data);
            }


            $draw_json = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $draw_json = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo format_json($draw_json);
    }

    public function save()
    {
        //init
        $draw_json = array();

        //cek data save
        $id = $this->input->post('id', true);


        //validasi
        $this->form_validation->set_rules('id', 'id', 'trim|required');
        $this->form_validation->set_rules('name', 'printer name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('papersize', 'paper size', 'trim|required|numeric');
        $this->form_validation->set_rules('maxprint', 'max print', 'trim|greater_than_equal_to[1]');
        // $this->form_validation->set_rules('device[]', 'device', 'trim|required');
        $this->form_validation->set_rules('printreceipt', 'print receipt', 'trim|required|in_list[0,1]', array(
            'in_list' => 'The %s must Yes or No.'
        ));
        if ($this->input->post('printreceipt')==1) {
            $this->form_validation->set_rules('jumlahprintreceipt', 'print receipt qty', 'trim|greater_than_equal_to[1]');
        }

        $this->form_validation->set_rules('printorder_select', 'print order', 'trim|required|in_list[0,1]', array(
            'in_list' => 'The %s must Yes or No.'
        ));
        if (!empty($this->input->post('printorder[name][]'))) {
            foreach ($this->input->post('printorder[name][]') as $key => $value) {
                // echo $key ."---". $value."#";
                $this->form_validation->set_rules('printorder[name]['.$key.']', 'print order name', 'trim|required|max_length[30]');
                $this->form_validation->set_rules('printorder[list]['.$key.']', 'print order list', 'trim');
            }
        }

        $this->form_validation->set_rules('printlabel_select', 'print label', 'trim|required|in_list[0,1]', array(
            'in_list' => 'The %s must Yes or No.'
        ));
        if (!empty($this->input->post('printlabel[name][]'))) {
            foreach ($this->input->post('printlabel[name][]') as $key => $value) {
                // echo $key ."---". $value."#";
                $this->form_validation->set_rules('printlabel[name]['.$key.']', 'print label name', 'trim|required|max_length[30]');
                $this->form_validation->set_rules('printlabel[list]['.$key.']', 'print label list', 'trim');
            }
        }

        $this->form_validation->set_rules('closingshift_select', 'closing shift', 'trim|required|in_list[0,1]', array(
            'in_list' => 'The %s must Yes or No.'
        ));
        if (!empty($this->input->post('closingshift[name][]'))) {
            foreach ($this->input->post('closingshift[name][]') as $key => $value) {
                // echo $key ."---". $value."#";
                $this->form_validation->set_rules('closingshift[name]['.$key.']', 'closing shift ticket name', 'trim|required|max_length[30]');
                $this->form_validation->set_rules('closingshift[list]['.$key.']', 'closing shift list', 'trim');
            }
        }
        // $this->form_validation->set_rules('closingshift[list]', 'closing shift list', 'trim');
        $this->form_validation->set_error_delimiters('','');



        //validasi run
        if ($this->form_validation->run() == FALSE) {
            $error_printorder_list = '';
            if (!empty($this->input->post('printorder[name][]'))) {
                foreach ($this->input->post('printorder[name][]') as $key => $value) {
                    if (!empty(form_error('printorder[name]['.$key.']'))) {
                        $error_printorder_list = form_error('printorder[name]['.$key.']');
                        break;
                    }
                }
            }

            $error_printlabel_list = '';
            if (!empty($this->input->post('printlabel[name][]'))) {
                foreach ($this->input->post('printlabel[name][]') as $key => $value) {
                    if (!empty(form_error('printlabel[name]['.$key.']'))) {
                        $error_printlabel_list = form_error('printlabel[name]['.$key.']');
                        break;
                    }
                }
            }

            $error_closingshift_list = '';
            if (!empty($this->input->post('closingshift[name][]'))) {
                foreach ($this->input->post('closingshift[name][]') as $key => $value) {
                    if (!empty(form_error('closingshift[name]['.$key.']'))) {
                        $error_closingshift_list = form_error('closingshift[name]['.$key.']');
                        break;
                    }
                }
            }


            //json data
            $draw_json = array(
                'status' => 'error',
                'message' => 'Update Record Failed',
                'data_error' => array(
                    'name' => form_error('name'),
                    'papersize' => form_error('papersize'),
                    'maxprint' => form_error('maxprint'),
                    'device' => form_error('device[]'),
                    'printreceipt' => form_error('printreceipt'),
                    'jumlahprintreceipt' => form_error('jumlahprintreceipt'),
                    'printorder_select' => form_error('printorder_select'),
                    'printorder_list' => $error_printorder_list,
                    'printlabel_select' => form_error('printlabel_select'),
                    'printlabel_list' => $error_printlabel_list,
                    'closingshift_select' => form_error('closingshift_select'),
                    'closingshift_list' => $error_closingshift_list,
                )
            );
        } else {
            //cek data
            $row = $this->Printer_model->get_by_id($id);
            if ($row) {
                //update main printer setting
                $data_main_printersetting = array(
                    'printer_name' => $this->input->post('name', true),
                    'setting_printpapersize' => $this->input->post('papersize'),
                    'max_print' => $this->input->post('maxprint'),
                    'setting_printreceipt' => $this->input->post('printreceipt'),
                    'setting_printreceipt_jumlah' => $this->input->post('jumlahprintreceipt'),
                    // 'setting_printreceiptorderid' =>,
                    'setting_printorder' => $this->input->post('printorder_select'),
                    'setting_printlabel' => $this->input->post('printlabel_select'),
                    'setting_closingshift' => $this->input->post('closingshift_select'),
                );
                $response = $this->Printer_model->update($id, $data_main_printersetting);
                if ($response) {
                    $draw_json = array('status'=>'success', 'message'=>'Update Record Success');
                    //hapus data ticket
                    $response_delete_printorder = $this->Printer_model->delete_print_ticket_by_printerid($id);



                    //get device
                    $data_device = $this->input->post('device[]');
                    if (!empty($data_device)) {
                        //delete device by printer id
                        $this->Printer_model->delete_setting_device_by_printer($id);


                        //get data device
                        $data_settingdevice = array();
                        foreach ($data_device as $key => $value) {
                            //check device
                            $row_device = $this->Devices_model->get_by_id($value);
                            if ($row_device) {
                                // echo "|data:okee";
                                $data_settingdevice[] = array(
                                    'printer_setting_fkid' => $id,
                                    'device_fkid' => $value,
                                );
                            }
                        }
                        //insert data device
                        $this->Printer_model->insertbatch_setting_device($data_settingdevice);
                    }//endif: device

                    
                    //get print order
                    $data_printorder = $this->input->post('printorder[name][]');
                    if (!empty($data_printorder)) {
                        //ambil data printorder
                        foreach ($data_printorder as $key => $value) {
                            $data_printerticket_printorder = array(
                                'name' => $value,
                                'setting_type' => 'printorder',
                                'printer_setting_fkid' => $id,
                            );
                            //insert printorder data
                            $response_insert_printorder = $this->Printer_model->insert_print_ticket($data_printerticket_printorder);
                            if ($response_insert_printorder) {
                                //init
                                $printorder_ticketid = $this->db->insert_id();
                                $printorder_listbatch = array();

                                //get checked printorder list
                                if (!empty($this->input->post('printorder[list]['.$key.']'))) {
                                    foreach ($this->input->post('printorder[list]['.$key.']') as $key2 => $value2) {
                                        //cek data
                                        $cek_subcategory = $this->Products_subcategory_model->get_by_id($value2);
                                        if ($cek_subcategory) {
                                            $printorder_listbatch[] = array(
                                                'printersetting_ticket_fkid' => $printorder_ticketid,
                                                'product_subcategory_fkid' => $value2,
                                                'data_modified' => current_millis()
                                            );
                                        }
                                    }//endforeach

                                    //insert batch
                                    $this->Printer_model->insertbatch_print_ticket_subcategory($printorder_listbatch);
                                }//endif: is have printorder list
                            }//endif: is success insert printorder?
                        }//endforeach: printorder ticket
                    }//endif: printorder


                    //get print label
                    $data_printlabel = $this->input->post('printlabel[name][]');
                    if (!empty($data_printlabel)) {
                        //ambil data printlabel
                        foreach ($data_printlabel as $key => $value) {
                            $data_printerticket_printlabel = array(
                                'name' => $value,
                                'setting_type' => 'printlabel',
                                'printer_setting_fkid' => $id,
                            );
                            //insert printlabel data
                            $response_insert_printlabel = $this->Printer_model->insert_print_ticket($data_printerticket_printlabel);
                            if ($response_insert_printlabel) {
                                //init
                                $printlabel_ticketid = $this->db->insert_id();
                                $printlabel_listbatch = array();

                                //get checked printlabel list
                                if (!empty($this->input->post('printlabel[list]['.$key.']'))) {
                                    foreach ($this->input->post('printlabel[list]['.$key.']') as $key2 => $value2) {
                                        //cek data
                                        $cek_subcategory = $this->Products_subcategory_model->get_by_id($value2);
                                        if ($cek_subcategory) {
                                            $printorder_listbatch[] = array(
                                                'printersetting_ticket_fkid' => $printlabel_ticketid,
                                                'product_subcategory_fkid' => $value2,
                                                'data_modified' => current_millis()
                                            );
                                        }
                                    }//endforeach

                                    //insert batch
                                    $this->Printer_model->insertbatch_print_ticket_subcategory($printorder_listbatch);
                                }//endif: is have printlabel list
                            }//endif: is success insert printlabel?
                        }//endforeach: printlabel ticket
                    }//endif: printlabel


                    //get closing shift
                    $data_closingshift = $this->input->post('closingshift[name][]');
                    if (!empty($data_closingshift)) {
                        //delete old data closingshift
                        $this->Printer_closingshift_model->delete_by_printerid($id);


                        //ambil data closingshift
                        foreach ($data_closingshift as $key => $value) {
                            //get closingshift rules
                            $closingshift_rules = array();
                            if (!empty($this->input->post('closingshift[list]['.$key.']'))) {
                                foreach ($this->input->post('closingshift[list]['.$key.']') as $key2 => $value2) {
                                    $closingshift_rules = array_merge($closingshift_rules, array($value2=>true));
                                }//endforeach
                            }

                            //data closingshift
                            $data_printerticket_closingshift = array(
                                'name' => $value,
                                'printer_setting_fkid' => $id,
                                'rules' => json_encode($closingshift_rules)
                            );
                            $this->Printer_closingshift_model->insert($data_printerticket_closingshift);
                        }
                    }
                }
                else{
                    $draw_json = array('status'=>'error', 'message'=>'Update Record Error');
                }
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Record Not Found'
                );
            }//endif: check row
        }//endif: formvalrun

        //output
        echo format_json($draw_json);
    }

    public function delete($id=null)
    {
        //cek
        $row = $this->Printer_model->get_by_id($id);
        if ($row) {
            $response = $this->Printer_model->delete($id);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //output
        echo format_json($draw_json);
    }




    //READY TO DELETE

    //untuk reload di dialog edit
    public function get_print_ticket($printersetting_ticket_id)
    {
        $row = $this->Printer_model->get_print_ticket_by_id($printersetting_ticket_id);

        if ($row) {
            $data = array(
                'ticket_id' => htmlentities($row->printersetting_ticket_id),
                'ticket_name' => htmlentities($row->name),
                'ticket_type' => htmlentities($row->setting_type),
                'category' => array()
            );

            //ambil category
            #$get_category = $this->Printer_model->get_ticket_category($printersetting_ticket_id);
            $get_category = $this->Printer_model->get_ticket_subcategory($printersetting_ticket_id);
            foreach ($get_category as $a) {
                $temp_data = array(
                    #'category_id' => $a->product_category_fkid,
                    'category_id' => $a->product_subcategory_fkid,
                );
                array_push($data['category'], $temp_data);
            }

            $draw_json = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $draw_json = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo format_json($draw_json);
    }

	public function update($element)
	{
        $this->_rules($element);//validasi

        //data yang dikirim
        $data_post = array(
            'id' => $this->input->post('id', true),
            'name' => $this->input->post('name', true),
            'papersize' => $this->input->post('papersize', true),
            'device' => $this->input->post('device[]', true),
            'printreceipt' => $this->input->post('printreceipt', true),
            'printreceipt_jumlah' => $this->input->post('printreceipt_jumlah', true),
            'printreceiptorderid_status' => $this->input->post('printreceiptorderid_status',true),
            'printorder_status' => $this->input->post('printorder_status', true),
            'printlabel_status' => $this->input->post('printlabel_status', true),
            'closingshift_status' => $this->input->post('closingshift_status', true),

            //ticket
            'action' => $this->input->post('action', true),
            'ticket_type' => $this->input->post('ticket_type', true),
            'ticket_name' => $this->input->post('ticket_name', true),
            'ticket_category' => $this->input->post('productcategory[]', true)
        );

        //cek ID setting printer valid atau tidak
        $cek_valid_printer = $this->Printer_model->get_by_id($data_post['id']);
        if (!$cek_valid_printer) {
            $draw_json = array('status' => 'error', 'message'=>'Invalid Printer');
            echo format_json($draw_json);die();
        }


        

        if ($this->form_validation->run() == FALSE) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Update Record Failed',
                'data' => array(
                    'name' => form_error('name'),
                    'papersize' => form_error('papersize'),
                    'device' => form_error('device[]'),
                    'printreceipt' => form_error('printreceipt'),
                    'printreceipt_jumlah' => form_error('printreceipt_jumlah'),
                    'printreceiptorderid' => form_error('printreceiptorderid_status'),
                    'printorder' => form_error('printorder_status'),
                    'printlabel' => form_error('printlabel_status'),
                    'closingshift' => form_error('closingshift_status'),
                )
            );
        } else {
            //update data
            $message = '';
            //$response = false;
            switch ($element) {
                case 'name':
                    $data_update = array(
                        'printer_name' => $data_post['name']
                    );
                    $response = $this->Printer_model->update($data_post['id'], $data_update);
                    $message = 'Update Printer Name Success';
                    break;
                case 'papersize':
                    $data_update = array(
                        'setting_printpapersize' => $data_post['papersize']
                    );
                    $response = $this->Printer_model->update($data_post['id'], $data_update);
                    $message = 'Update Printer Paper Size Success';
                break;
                case 'device':
                    //hapus semua data lama
                    $this->Printer_model->delete_setting_device_by_printer($data_post['id']);


                    //inisialisasi daftar device yang dicentang
                    $deviceSubmitted = $data_post['device'];
                    if ($deviceSubmitted[0]=='all_device') {
                        $devicelist = $this->Devices_model->form_select();
                        $i=0;
                        foreach ($devicelist as $value) {
                            $deviceSubmitted[$i] = $value->device_id;
                            $i++;
                        }
                    }

                    //kirim data berdasarkan device yang dicentang
                    foreach ($deviceSubmitted as $checked) { //data checkbox outlet yang dikirim
                        $device_id = $checked;
                        $data_update = array(
                            'printer_setting_fkid' => $data_post['id'],
                            'device_fkid' => $device_id
                        );

                        $response = $this->Printer_model->insert_setting_device($data_update);
                    } //end foreach
                    $message = 'Update Printer Setting on Device Success';
                    break;
                case 'printreceipt':
                    //echo $data_post['printreceipt'];
                    if ($data_post['printreceipt']=='on') {
                        $data_update['setting_printreceipt'] = true;
                        $data_update['setting_printreceipt_jumlah'] = $data_post['printreceipt_jumlah'];
                        //echo "benar";
                    }
                    else{
                        //echo "salah";
                        $data_update['setting_printreceipt'] = false;
                        $data_update['setting_printreceipt_jumlah'] = 0;
                    }
                    $response = $this->Printer_model->update($data_post['id'], $data_update);
                    $message = 'Update Print Receipt Success';
                    break;
                case 'printreceiptorderid_status':
                    $data_update['setting_printreceiptorderid'] = ($data_post['printreceiptorderid_status']=='on') ? true : false;
                    $response = $this->Printer_model->update($data_post['id'], $data_update);
                    $message = 'Update Print Receipt Order ID Success';
                    break;
                case 'printorder_status':
                    $data_update['setting_printorder'] = ($data_post['printorder_status']=='on') ? true : false;
                    $response = $this->Printer_model->update($data_post['id'], $data_update);
                    $message = 'Update Print Order Status Success';
                    break;
                case 'printlabel_status':
                    $data_update['setting_printlabel'] = ($data_post['printlabel_status']=='on') ? true : false;
                    $response = $this->Printer_model->update($data_post['id'], $data_update);
                    $message = 'Update Print Label Status Success';
                    break;
                case 'ticket_category':
                    //inisialisasi daftar category yang dicentang
                    $categorySubmitted = $data_post['ticket_category'];
                    if ($categorySubmitted[0]=='all_category') {
                        $categorylist = $this->Products_category_model->form_select();
                        $i=0;
                        foreach ($categorylist as $value) {
                            $categorySubmitted[$i] = $value->product_category_id;
                            $i++;
                        }
                    }

                    //kirim data berdasarkan device yang dicentang
                    foreach ($categorySubmitted as $checked) { //data checkbox outlet yang dikirim
                        $category_id = $checked;
                        $data_update = array(
                            //'printer_setting_fkid' => $data_post['id'],
                            'product_category_fkid' => $category_id
                        );

                        //$response = $this->Printer_model->insert_setting_device($data_update);
                    } //end foreach
                    $message = 'Update Ticket Success';
                    break;
                case 'closingshift_status':
                    $data_update['setting_closingshift'] = ($data_post['closingshift_status']=='on') ? true : false;
                    $response = $this->Printer_model->update($data_post['id'], $data_update);
                    $message = 'Update Closing Shift Status Success';
                    break;
                default:
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Undefined Element'
                    );
                break;
            }

            //$response = $this->Printer_model->update($data_post['id'], $data_update);
            if ($response===true) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => $message
                );
            }
            else{
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Update Record Failed'
                );
            }
            
        }

        echo format_json($draw_json);
	}

    public function _rules($element)
    {
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

        switch ($element) {
            case 'name':
                $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
                break;
            case 'papersize':
                $this->form_validation->set_rules('papersize', 'paper size', 'trim|required|is_numeric');
                break;
            case 'device':
                $this->form_validation->set_rules('device[]', 'device', 'trim|required');
                break;
            case 'printreceipt':
                $this->form_validation->set_rules('printreceipt', 'print receipt', 'trim|required');
                if ($this->input->post('printreceipt')!=null) {
                    $this->form_validation->set_rules('printreceipt_jumlah', 'qty print receipt', 'trim|is_numeric');
                }
                else{
                    $this->form_validation->set_rules('printreceipt_jumlah', 'qty print receipt', 'trim|required|is_numeric');
                }
                break;
            case 'printorder_status':
                $this->form_validation->set_rules('printorder_status', 'print order', 'trim|required');
                break;
            case 'printreceiptorderid_status':
                $this->form_validation->set_rules('printreceiptorderid_status', 'print receipt order ID', 'trim|required');
                break;
            case 'printlabel_status':
                $this->form_validation->set_rules('printlabel_status', 'print label', 'trim|required');
                break;
            case 'closingshift_status':
                $this->form_validation->set_rules('closingshift_status', 'closing shift', 'trim|required');
                break;
            default: break;
        }
    }

    public function json()
    {
        //header('Content-Type: application/json');
        //echo $this->Employee_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Printer_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->printer_setting_id;
            $name = htmlentities($a->printer_name); //encoding string
            $mac_address = htmlentities($a->mac_address);
            $outlet = htmlentities($a->outlet_name);
            
            $action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
            
            
            $temp_data = array(
                'printer_setting_id' => $id,
                'printer_name' => $name,
                'mac_address' => $mac_address,
                'outlet_name' => $outlet,
                'action' => $action
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function get_data2($printersetting_id=null)
    {
        $row = $this->Printer_model->get_by_id($printersetting_id);

        if ($row) {
            $data = array(
                'printer_setting_id' => htmlentities($row->printer_setting_id),
                'name' => htmlentities($row->printer_name),
                'papersize' => $row->setting_printpapersize,
                'device' => array(),
                'print_receipt' => $row->setting_printreceipt,
                'print_receipt_jumlah' => $row->setting_printreceipt_jumlah,
                'print_receipt_orderid' => $row->setting_printreceiptorderid,
                'print_order' => $row->setting_printorder,
                'print_order_ticket' => array(),
                'print_label' => $row->setting_printlabel,
                'print_label_ticket' => array(),
                'closingshift' => json_decode($row->setting_closingshift),
                'closingshift_data' => array(),
                //'closingshift_rules' => json_decode($row->setting_closingshift_rules),
                'outlet_id' => $row->outlet_fkid,
            );

            //ambil device
            $get_device = $this->Printer_model->get_setting_device($printersetting_id);
            foreach ($get_device as $a) {
                $temp_data = array(
                    'device_id' => $a->device_fkid,
                );
                array_push($data['device'], $temp_data);
            }

            //ambil print order ticket
            $get_ticket = $this->Printer_model->get_print_ticket($printersetting_id, 'printorder');
            foreach ($get_ticket as $a) {
                $temp_data = array(
                    'ticket_id' => $a->printersetting_ticket_id,
                    'ticket_name' => htmlentities($a->name)
                );

                array_push($data['print_order_ticket'], $temp_data);
            }

            //ambil print label ticket
            $get_ticket = $this->Printer_model->get_print_ticket($printersetting_id, 'printlabel');
            foreach ($get_ticket as $a) {
                $temp_data = array(
                    'ticket_id' => $a->printersetting_ticket_id,
                    'ticket_name' => htmlentities($a->name)
                );

                array_push($data['print_label_ticket'], $temp_data);
            }

            //ambil data closingshift
            $get_ticket = $this->Printer_closingshift_model->get_all($printersetting_id);
            foreach ($get_ticket as $a) {
                $temp_data = array(
                    'closingshift_id' => $a->closingshift_id,
                    'name' => htmlentities($a->name),
                    'rules' => json_decode($a->rules)
                );

                array_push($data['closingshift_data'], $temp_data);
            }


            $draw_json = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $draw_json = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo format_json($draw_json);
    }

}

/* End of file Printer.php */
/* Location: ./application/controllers/outlets/Printer.php */