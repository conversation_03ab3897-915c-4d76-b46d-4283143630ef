<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Subscribe_add extends Auth_Controller{

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->model('settings/Service_model');

		//library
		$this->load->library('cart');

		//akses hanya untuk developer UNIQ
		$this->protect_page->developerOnly('Add Service');

		if ($this->session->userdata('user_type')!='admin') {
			show_404();
		}
	}
	
	function index(){
		//custom
		$data['ajax_addcart'] = current_url().'/add_to_cart';	 // site_url('settings/service-add/add_to_cart');
		$data['ajax_loadcart'] = current_url().'/load_cart';	 //site_url('settings/service-add/load_cart');
		$data['ajax_deletecart'] = current_url().'/delete_cart'; //site_url('settings/service-add/delete_cart');

		//form select
		$data['form_service'] = $this->Service_model->form_select();
		$data['action_complete'] = site_url('outlets/subscribe/completeorder');//site_url('settings/service/completeorder');

		//template
		// $this->load->view('settings/service/service_add_v',$data);
		$data['page_title'] = 'Add Subscribe';
		$this->template->view('settings/service/v2/service_add_v2',$data);
	}

	function add_to_cart(){ //fungsi Add To Cart
		$datapost = array(
			'id' => $this->input->post('produk_id'), 
			'name' => $this->input->post('produk_nama'), //data tidak dipakai
			'price_ori' => $this->input->post('produk_harga'), //data diambil dari DB
			'qty' => $this->input->post('quantity'), 
			'period' => $this->input->post('period'),
			'service_length_day' => $this->input->post('service_length_day'), //data diambil dari DB
		);


		//get data
		$row = $this->Service_model->get_by_id($datapost['id']);
		if ($row) {
			$data = array(
				'id' => $row->sys_service_id,
				'name' => htmlentities($row->name),
				'price' => $row->price,
				'discount' => $row->discount,
				'service_length_day' => $row->service_length_day,
				'service_feature' => $row->service_feature, //fitur device, report,atau yang lainnya
				'service_type' => $row->service_type,
				'period' => ($datapost['period']>$row->service_period_max) ? $row->service_period_max : $datapost['period'],
				'qty' => $datapost['qty'],
			);
			$data['subtotal'] = ($row->price * $data['qty'] * $data['period']);

			//cart
			$this->cart->product_name_rules = "\.\:\-_\"\' () a-z0-9";
			$datacart = array(
				'id' => $data['id'],
				'name' => $data['name'],
				'service_type' => $data['service_type'],
				'service_feature' => $data['service_feature'],
				'qty' => $data['qty'],
				'period' => $data['period'],
				'price_ori' => $data['price'],
				'price' => $data['price'] * $data['period'],

				//data keterangan
				'service_length_day' => $data['service_length_day'],
			);
			
			$this->cart->insert($datacart);
			echo $this->show_cart(); //tampilkan cart setelah added
		}
	}

	function show_cart(){ //Fungsi untuk menampilkan Cart
		$output = '';
		$no = 0;

		//print_r($this->cart->contents());die();

		foreach ($this->cart->contents() as $items) {
			/* custom output start */
			switch ($items['service_feature']) {
				case 'device':
					$items['name'] = $items['name'].' ('.$items['qty'].' Device)';
					$items['price_output'] = $items['qty'].' x '.$items['period'].' x '.formatAngka($items['price_ori']);
					break;
				
				default:
					$items['price_output'] = $items['period'].' x '.formatAngka($items['price_ori']);
					break;
			}
			/* custom output end */

			$no++;
			$output .='
				<tr>
					<td>'.$items['name'].'</td>
					<td>'.$items['period'].' x '.$items['service_length_day'].' Days</td>
					<!--<td>'.$items['qty'].'</td>-->
					<!--<td>'.$items['qty'].' x '.$items['period'].' x '.formatAngka($items['price_ori']).'</td>-->
					<td>'.$items['price_output'].'</td>
					<td>'.formatAngka($items['subtotal']).'</td>
					<td><button type="button" id="'.$items['rowid'].'" class="hapus_cart btn btn-danger btn-xs"><span class="glyphicon glyphicon-trash" style="color:#fff"></span></button></td>
				</tr>
			';
		}
		if ($no==0) {
			$output .= '
				<tr>
					<td colspan="6" class="text-center">No Data</td>
				</tr>
			';
		}
		$output .= '
			<tr>
				<th colspan="4">Total</th>
				<th colspan="2">'.'IDR '.formatAngka($this->cart->total()).'</th>
			</tr>
		';
		return $output;
	}

	function load_cart(){ //load data cart
		echo $this->show_cart();
	}

	function delete_cart(){ //fungsi untuk menghapus item cart
		$data = array(
			'rowid' => $this->input->post('row_id'), 
			'qty' => 0, 
		);
		$this->cart->update($data);
		echo $this->show_cart();
	}
}