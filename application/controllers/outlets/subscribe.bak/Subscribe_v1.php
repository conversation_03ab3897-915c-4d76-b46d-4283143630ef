<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Subscribe extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//fitur hanya untuk owner
		if ($this->session->userdata('user_type')!='admin') {
			redirect(site_url());
			die();
		}

		//akses hanya untuk developer UNIQ
		$this->protect_page->developerOnly('Service');

		//load library model dll
		$this->load->model(
			array(
				'settings/Service_model',
				'settings/Billing_model'
			)
		);
		$this->load->library('cart');
	}

	public function index()
	{
		$data = array(
			'datatable_json' => current_url().'/json'
		);
		$this->load->view('settings/service/service_v', $data);
	}

	public function json()
	{
		$jsondata = $this->Service_model->json();
		$json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $temp_data = array(
                'billing_detail_id' => $a->billing_detail_id,
                'service_name' => htmlentities($a->service_name),
                'service_feature' => htmlentities($a->service_feature),
                'qty' => $a->qty,
                'service_time_start' => millis_to_localtime('Y-m-d H:i:s',$a->service_time_start),
                'service_time_expired' => millis_to_localtime('Y-m-d H:i:s',$a->service_time_expired)
            );

            switch ($a->service_feature) {
            	case 'device':
            		$temp_data['service_name'] = $temp_data['service_name'].' ('.$a->qty.' Device)';
            		break;
            	
            	default:
            		# code...
            		break;
            }
            array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
	}

	public function completeorder()
	{
		if (!$this->input->post()) {
			redirect(site_url('dashboard'));
			die();
		}
		if (empty($this->cart->contents())) {
			redirect(site_url('settings/service'));
			die();
		}


		//data yang dikirim
		$datapost = array(
			'agree' => $this->input->post('agree', TRUE)
		);

		//validasi
		$this->form_validation->set_rules('agree', 'agreement of terms &amp; conditions', 'trim|required');

		///formval
		if ($this->form_validation->run() == FALSE) {
			//buat error
			$this->session->set_flashdata(
				array(
					'status' => 'error',
					'error_agree' => form_error('agree'),
				)
			);
			redirect(site_url('settings/service-add'));
		} else {
			//insert data ke DB
			$data_invoice = array(
				'detail_total' => $this->cart->total(),
				//'discount' => 0,
				'kode_unik' => 0,
				'billing_notes' => null,
			);
			$addbilling = $this->Billing_model->insert_invoice($data_invoice);
			$billing_id = $this->db->insert_id();
			if ($addbilling) {
				$data_insertbatch = array();
				
				foreach ($this->cart->contents() as $items) {
					//data batch
					$temp = array(
						'billing_fkid' => $billing_id,
						'sys_service_fkid' => $items['id'],
						'qty' => $items['qty'],
						'service_length_day' => $items['service_length_day'],
						'service_period' => $items['period'],
						'service_type' => $items['service_type'],
						'price' => $items['price_ori'],
						'discount' => 0
					);

					if ($items['service_type']=='extend') {
						//kalau extend, service_time_start diambil dari expired terakhir service
						//$temp['service_time_start'] = 
					}

					array_push($data_insertbatch, $temp);
				}
				//insert detail
				$resp_insertbatch = $this->Billing_model->insert_invoice_detail_batch($data_insertbatch);
				if ($resp_insertbatch) {
					//destroy all data on cart
					$this->cart->destroy();

					//buat message success
					$this->session->set_flashdata(
						array(
							'status' => 'success',
							'message' => 'New billing have been added to your account!',
						)
					);

					//redirect to billing info
					redirect(site_url('settings/billing/detail/'.$billing_id));
				}
			}
			else{
				echo "something error";
			}
		}
	}

}

/* End of file Service.php */
/* Location: ./application/controllers/settings/Service.php */