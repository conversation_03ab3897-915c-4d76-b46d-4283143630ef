<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Outletlist extends CI_Controller {

    private $outlet_photo_path; //path logo: receipt logo outlet

    function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->outlets_outletlist('read_access'); //employee role access page
        $this->user_role->outlets('read_access'); //employee role access page
        
        $this->load->model('outlet/Outlets_model');

        //inisialisasi variabel
        $outlet_path = outlet_url();//'assets/outlets/'.$this->session->userdata('admin_id').'/'; //path 
        $outlet_path = str_replace(base_url(), '', $outlet_path);
        $this->outlet_photo_path = $outlet_path.$this->session->userdata('admin_id');
    }

    public function index()
    {
        $link = site_url('outlets/outletlist/'); //URL dengan slash
        $data = array(
            'kolomID' => 'outlet_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );
        $data['featurelist'] = $this->outlet_feature->featurelist('mainfeature');
        $data['featurelist_authorization'] = $this->outlet_feature->featurelist('authorization');

        $this->load->view('outlets/outlet/outlet_list_v', $data);
    }

    public function json()
    {
        /* custom json output  start */
        $jsondata = $this->Outlets_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->outlet_id;
            $name = htmlentities($a->name); //encoding string
            $address = htmlentities($a->address); //encode string
            $phone = htmlentities($a->phone); //encode string
            $country = htmlentities($a->country); //encode string
            $province = htmlentities($a->province); //encode string
            $city = htmlentities($a->city); //encode string
            $postal_code = htmlentities($a->postal_code);//encode
            $expired_date = $a->expired_date;

            $action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
            
            
            $temp_data = array(
                'outlet_id' => $id,
                'name' => $name,
                'address' => $address,
                'phone' => $phone,
                'country' => $country,
                'province' => $province,
                'city' => $city,
                'postal_code' => $postal_code,
                'expired_date' => $expired_date,
                'action' => $action
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        /* PROSES OLAH GAMBAR START */
        $photo = $this->_upload_photo(); // PROSES OLAH GAMBAR
        $photo_status = $photo['status_upload'];    // upload foto berhasil atau tidak
        $photo_available = $photo['available'];     // submit form memiliki gambar atau tidak
        $error_imageupload = $photo['error_msg'];   // pesan error saat upload foto
        $photo_path = $photo['path'];               // path direktori foto
        $photo_name = $photo['name'];               // nama foto yang diupload
        /* PROSES OLAH GAMBAR END   */

        if ($this->form_validation->run() == FALSE ||
            ($photo_status===FALSE && $photo_available===TRUE) //verifikasi kalau action create tidak ada foto
            ) {
            //hapus foto yang baru diupload
            if ($photo_status===true) {
                //$path = $photo_path.$photo_name;
                $path = $photo_path.'/'.$photo_name;
                unlink($path); //hapus gambar yang baru saja diupload saat input gagal
            }
            //error response
            if ($actionType=='create') {
                $message_error = 'Create Record Failed';
            }
            elseif ($actionType=='update') {
                $message_error = 'Update Record Failed';
            }

            $message_feature = (!empty(form_error('mainfeature[]'))) ? form_error('mainfeature[]') : form_error('feature_authorization[]');


            $response = array(
                'status' => 'error',
                'message'=> $message_error,
                'data'   => array(
                    'name' => form_error('name'),
                    'address' => form_error('address'),
                    'phone' => form_error('phone'),
                    'country' => form_error('country'),
                    'province' => form_error('province'),
                    'city' => form_error('city'),
                    'postal_code' => form_error('postal_code'),
                    'expired_date' => form_error('expired_date'),
                    //receipt error
                    'receiptnotes' => form_error('receiptnotes'),
                    'receiptphone' => form_error('receiptphone'),
                    'receiptaddress' => form_error('receiptaddress'),
                    'receiptsocialmedia' => form_error('receiptsocmed'),
                    'receiptlogo' => $error_imageupload
                )
            );
        } else {
            //data yang dikirim
            $data_post = array(
                'name' => $this->input->post('name',TRUE),
                'address' => $this->input->post('address',TRUE),
                'phone' => $this->input->post('phone',TRUE),
                'country' => $this->input->post('country',TRUE),
                'province' => $this->input->post('province',TRUE),
                'city' => $this->input->post('city',TRUE),
                'postal_code' => $this->input->post('postal_code',TRUE),
                //'expired_date' => $this->input->post('expired_date',TRUE),
                'receipt_note' => $this->input->post('receiptnotes', true),
                'receipt_phone' => $this->input->post('receiptphone', true),
                'receipt_address' => $this->input->post('receiptaddress', true),
                'receipt_socialmedia' => $this->input->post('receiptsocmed', true),
                'receipt_logo' => $photo_name
            );

            //mengetahui feature yang dicentang
            //MAIN FEATURE
            $checked_feature = array();
            $featurelist = $this->outlet_feature->featurelist('mainfeature');
            foreach ($featurelist as $list) {
                $checked = ($this->input->post('mainfeature['.$list[0].']')=='on') ? true : false;
                $checked_feature[$list[0]] = $checked;
            }
            //--FEATURE AUTHORIZATION
            $checked_feature['authorization'] = array();
            $featurelist_authorization = $this->outlet_feature->featurelist('authorization');
            foreach ($featurelist_authorization as $list) {
                $checked = ($this->input->post('feature_authorization['.$list[0].']')=='on') ? true : false;
                if ($checked===true) {
                    $checked_feature['authorization'][$list[0]] = true;
                }
            }
            $data_post['feature'] = format_json($checked_feature);

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Outlets_model->insert($data_post);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //get data untuk menghapus receipt logo lama
                $id = $this->input->post('id', true);
                $row_receiptlogo_old = '';
                $row = $this->Outlets_model->get_by_id($id);
                if ($row) {
                    $row_receiptlogo_old = $row->receipt_logo;
                }

                //action untuk update data
                $response = $this->Outlets_model->update($this->input->post('id', TRUE), $data_post);

                //hapus gambar lama bila update gambar
                if ($response==true) {
                    if ($photo_status===TRUE && $photo_available===TRUE) {
                        $filename = $photo_path.$row_receiptlogo_old;
                        //cek apakah file ada
                        if (file_exists($filename) && !empty($row_receiptlogo_old)) {
                            unlink($filename); //echo "The file $filename exists";
                        }
                    }
                }
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function get_data($id=null)
    {
        $photo_link = noimage(); //default pict
        $photo_path = $this->outlet_photo_path; //path receipt_logo outlet
        $row = $this->Outlets_model->get_by_id($id);

        if ($row) {
            $photo_name = $row->receipt_logo;
            $photo_name = (!empty($photo_name) ? $photo_path.'/'.$photo_name : '');
            $photo_name = str_replace(base_url(), '', $photo_name); //ambil path
            if (file_exists($photo_name)) {;
                $photo_link = base_url($photo_name);
            }

            $data = array(
                'outlet_id' => ($row->outlet_id),
                'name' => html_entity_decode($row->name),
                'address' => html_entity_decode($row->address),
                'phone' => html_entity_decode($row->phone),
                'country' => html_entity_decode($row->country),
                'province' => html_entity_decode($row->province),
                'city' => html_entity_decode($row->city),
                'postal_code' => html_entity_decode($row->postal_code),
                'feature' => json_decode($row->feature),
                //'expired_date' => set_value('expired_date', $row->expired_date)
                //receipt
                'receiptnotes' => html_entity_decode($row->receipt_note),
                'receiptphone' => html_entity_decode($row->receipt_phone),
                'receiptaddress' => html_entity_decode($row->receipt_address),
                'receiptsocmed' => html_entity_decode($row->receipt_socialmedia),
                'receiptlogo' => $photo_link,
            );
            $dataArray = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo json_encode($dataArray);
    }

    public function delete($id=null)
    {
        //inisialisasi
        $row_receiptlogo_old = '';
        $row = $this->Outlets_model->get_by_id($id);

        if ($row) {
            $row_receiptlogo_old = $row->receipt_logo;
            //$filename = $this->outlet_photo_path.$row_receiptlogo_old;
            //$filename = $this->outlet_photo_path.'/'.$this->session->userdata('admin_id').'/'.$row_receiptlogo_old;
            //$filename = str_replace(base_url(), '', $filename);
            // echo $filename;
            // die();
            $filename = $this->outlet_photo_path.'/'.$row_receiptlogo_old;
                
            $response = $this->Outlets_model->delete($id); //hapus
            if ($response==true) {
                //hapus gambar dari direktori
                if (file_exists($filename) && !empty($row_receiptlogo_old)) {
                    unlink($filename); //echo "The file $filename exists";
                }

                //menambah notifikasi dari hapus data
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Record Deleted'
                );
            }
            else {
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Failed'
                );
            }
        }
        else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //response output
        echo format_json($draw_json);
    }

    public function _rules() 
    {
        $this->form_validation->set_rules('name', 'name', 'required|max_length[50]');
        $this->form_validation->set_rules('address', 'address', 'required|max_length[100]');
        $this->form_validation->set_rules('phone', 'phone', 'trim|required|is_numeric|max_length[15]');
        $this->form_validation->set_rules('country', 'country', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('province', 'province', 'trim|required|max_length[50]');
        $this->form_validation->set_rules('city', 'city', 'trim|required|max_length[50]');
        $this->form_validation->set_rules('postal_code', 'postal code', 'trim|required|max_length[6]');
        //$this->form_validation->set_rules('expired_date', 'expired date', 'trim|required'); //masih bug.. cari validation date
        $this->form_validation->set_rules('mainfeature[]', 'main feature', 'trim');
        $this->form_validation->set_rules('featurelist_authorization[]', 'feature authorization', 'trim');

        //receipt validation
        $this->form_validation->set_rules('receiptnotes', 'notes', 'trim');
        $this->form_validation->set_rules('receiptphone', 'phone', 'trim|max_length[15]|is_numeric');
        $this->form_validation->set_rules('receiptaddress', 'address', 'trim|max_length[100]');
        $this->form_validation->set_rules('receiptsocmed', 'social media', 'trim|max_length[100]');
        //$this->form_validation->set_rules('receiptlogo', 'logo', 'trim|min_length[5]|max_length[12]');

        $this->form_validation->set_rules('id', 'outlet_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

    public function _upload_photo()
    {
        /* PROSES OLAH GAMBAR by aziz */

        //inisialisasi variabel
        $error_imageupload = '';
        $photo_available = false;   //form ada submit foto atau tidak
        $photo_upload = false;      //foto berhasil diupload atau tidak

        /*konfigurasi upload gambar start*/
        //$photo_path = outlet_url(); //(ambil dari assets_helper)
        //$photo_path = str_replace(base_url(), '', $photo_path); //ambil path dari assets/images/outlets
        //$photo_path = str_replace(base_url(), '', $photo_path.'/'.$this->session->userdata('admin_id')); //ambil path dari assets/images/outlets
        $photo_path = $this->outlet_photo_path;

        $photo_name = $this->session->userdata('admin_id').'_'.$this->input->post('name').'_'.date('YmdHis'); //temporary name
        $photo_name = strtolower($photo_name); //mengubah nama file menjadi kecil semua
        $photo_name = str_replace(' ', '', $photo_name); //fungsi untuk menghilangkan spasi pada nama
        $config['upload_path']          = $photo_path; //'./_tes_upload/'.$album;
        $config['allowed_types']        = 'gif|jpg|png|jpeg'; //ekstensi yang diizinkan
        $config['max_size']             = 100; //maksimal size
        $config['file_name']            = $photo_name; //nama gambar akan diganti saat diupload
        //$config['max_width']  = '1024'; //atur lebar maksimal gambar
        //$config['max_height']  = '768'; //atur tinggi maksimal gambar
        $config['remove_spaces'] = true;
        /*konfigurasi upload gambar end*/
        
        /*proses upload gambar start*/
        //buat direktori berdasarkan owner
        $dir_exist = true;
        if (!is_dir($photo_path)) {
            /* kalau mkdir() disable, comment dari sini */
            $old = umask(0);
            mkdir($photo_path, 0775, true); //buat direktori image produk untuk per-owner
            umask($old);
            /* kalau mkdir() disable, comment sampai sini */
            $dir_exist = false; // dir not exist
            $draw_json = array(
                'status' => 'error',
                'message' => 'Dir not Exist'
            );
        }

        //upload gambar
        $this->load->library('upload', $config);
        if (!empty($_FILES['photo']['name']) && $_FILES['photo']['name']!="") { //bila upload foto
            $photo_available = true;

            if ( ! $this->upload->do_upload('photo')){
                $error_imageupload = array('error' => $this->upload->display_errors());
                $error_imageupload = $error_imageupload['error'];
                $photo_upload = false; //status upload
                //echo "upload gagal";
            }
            else{
                $data = array('upload_data' => $this->upload->data());
                $photo_name = $data['upload_data']['file_name'];
                $photo_upload = true; //status upload
                //echo "upload success";
            }
        }
        else{
            $photo_available = false;//custom
            $photo_name = '';
            //echo "tidak ada gambar";
        }
        /* PROSES OLAH GAMBAR END*/

        return $photo = array(
            'status_upload' => $photo_upload,
            'available' => $photo_available,
            'error_msg' => $error_imageupload,
            'path' => $photo_path,
            'name' => $photo_name,
        );
    }

}

/* End of file Outlet_list.php */
/* Location: ./application/controllers/outlets/Outlet_list.php */