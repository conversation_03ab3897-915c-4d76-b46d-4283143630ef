<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Subscribtion extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		if (!$this->session->userdata('user_logged')) {
			http_response_code(401);
			return;
		}
	}

	public function index()
	{
		session_write_close();
		
		$sess_subscribe = $this->session->userdata('subscription');
		if (isset($sess_subscribe['device'])) {
			/* v1
			$business_id = $this->session->userdata('admin_id');
			$timeMillis = current_millis();

			$total_active = $this->db->query("SELECT COUNT(id) as total, feature, TIME_MILLIS() FROM `system_subscribe` WHERE admin_fkid={$business_id} AND 
				service_time_start <= {$timeMillis} AND 
				service_time_expired >= {$timeMillis} AND
				feature='device'
				GROUP by feature")->row()->total;

			$data = [
				'total_active' => (int) $total_active,
				'total_remain' => 1,
			];
			*/

			$day = 14;
			$data = $this->query($day);
			$sess_subscribe['device'] = array_merge($sess_subscribe['device'], $data);
		}
		
		echo format_json($sess_subscribe);
	}

	public function query($minday=0)
	{
		if (ENVIRONMENT != 'development') {
			show_404();
			return;
		}
		
		$timeMillis = current_millis();
		$timeMillisOneDay = 86400000; //time millis in one day

		$this->db->select("
			COUNT(id) AS total,
			feature,
			ROUND((service_time_expired - {$timeMillis}) / {$timeMillisOneDay}) AS dayleft
		");
		$this->db->from('system_subscribe');
		$this->db->where('service_time_start <=', $timeMillis);
		$this->db->where('service_time_expired >=', $timeMillis);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where_in('feature', ['device']);
		$this->db->group_by('feature, dayleft');
		$query1 = $this->db->get_compiled_select();

		//query check
		// $this->db->select('SUM(total) AS total_active, SUM(IF(dayleft<=14,total,0)) as total_remain, MIN(dayleft) AS min_dayleft');
		$this->db->select('IFNULL(SUM(total),0) AS total_active, IFNULL(SUM(IF(dayleft<='.$minday.',total,0)),0) as total_remain, IFNULL(MIN(dayleft),0) AS min_dayleft');
		$result = $this->db->get('('.$query1.') t');
		return $result->row_array();
	}


	public function query_raw()
	{
		/*
		// v1
		SELECT COUNT(id) as total, feature, service_time_expired, TIME_MILLIS(), 
		service_time_expired-TIME_MILLIS() AS timeleft_millis,
		(service_time_expired-TIME_MILLIS())/86400000 AS dayleft
		FROM `system_subscribe` WHERE 
		service_time_start <= TIME_MILLIS() AND 
		service_time_expired >= TIME_MILLIS()
		and admin_fkid=7
		GROUP by feature, service_time_expired

		// v2
		SELECT COUNT(id) as total, feature, service_time_expired, TIME_MILLIS(), 
		service_time_expired-TIME_MILLIS() AS timeleft_millis,
		ROUND((service_time_expired-TIME_MILLIS())/86400000) AS dayleft
		FROM `system_subscribe` WHERE 
		service_time_start <= TIME_MILLIS() AND 
		service_time_expired >= TIME_MILLIS()
		and admin_fkid=7
		GROUP by feature, service_time_expired


		//oncheck
		SELECT COUNT(id) as total, feature, 
		ROUND((service_time_expired-TIME_MILLIS())/86400000) AS dayleft
		FROM `system_subscribe` WHERE 
		service_time_start <= TIME_MILLIS() AND 
		service_time_expired >= TIME_MILLIS()
		and admin_fkid=7
		GROUP by feature, dayleft

		//query to use 1
		SELECT COUNT(id) as total, feature, 
		ROUND((service_time_expired-TIME_MILLIS())/86400000) AS dayleft
		FROM `system_subscribe` WHERE 
		service_time_start <= TIME_MILLIS() AND 
		service_time_expired >= TIME_MILLIS()
		and admin_fkid=7
		AND feature='device'
		GROUP by feature, dayleft

		//to final
		SELECT SUM(IF(dayleft<=14,total,0)) as total_left,
           SUM(total) AS total_active
           FROM
		(
		SELECT COUNT(id) as total, feature, 
		ROUND((service_time_expired-TIME_MILLIS())/86400000) AS dayleft
		FROM `system_subscribe` WHERE 
		service_time_start <= TIME_MILLIS() AND 
		service_time_expired >= TIME_MILLIS()
		and admin_fkid=7
		AND feature='device'
		GROUP by feature, dayleft
		) t
		*/
	}

}

/* End of file Subscribtion.php */
/* Location: ./application/controllers/Subscribtion.php */