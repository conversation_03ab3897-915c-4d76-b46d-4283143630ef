<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Account extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->load->helper('password');
		$this->load->library('user_key/User_key_change_email_lib');

		//model
		$this->load->model('settings/Change_email_model');
		$this->load->model('settings/Profile_model');

		//inisialisasi
		$photo_path = photo_url().$this->session->userdata('admin_id').'/'; //(ambil dari assets_helper)
        $this->photo_path = str_replace(base_url(), '', $photo_path); //ambil path dari assets/images/users
	}

	public function index()
	{
		//inisialisasi
		$link = site_url('settings/account/'); //URL dengan slash

		//ambil data user yang login
		$user = $this->Profile_model->get_mydata();
		$data = array(
            'ajaxActionUpdateProfile' => $link.'update-profile',
            'ajaxActionUpdateEmail' => $link.'change-email',
            'ajaxActionUpdatePassword' => $link.'change-password',

            //data dari yang login
            'data_name' => htmlentities($user->name),
            'data_address' => (!empty($user->address)) ? htmlentities($user->address) : '',
            'data_phone' => $user->phone,
            'data_email' => $user->email,
            'data_timezone' => $this->session->userdata('timezone'),
            'data_joindate' => millis_to_localtime('Y-m-d H:i:s', $user->date_join),
            'data_lastlogin' => millis_to_localtime('Y-m-d H:i:s', $user->last_login),
            'currency_symbol' => '',
            'currency_position' => '',
            'currency_separator' => '',
        );
        if (is_json($user->settings)) {
        	$settings = json_decode($user->settings);
        	$data['currency_symbol'] = (!empty($settings->currency_symbol)) ? htmlentities($settings->currency_symbol) : '';
        	$data['currency_position'] = (!empty($settings->currency_position)) ? $settings->currency_position : '';
        	$data['currency_separator'] = (!empty($settings->currency_separator)) ? $settings->currency_separator : '';
        }
		// $this->load->view('settings/account/account_v', $data);

		//template
		$data['page_title'] = 'Account';
		$this->load->view('themes/v2/header', $data);
		$this->load->view('settings/account/account_v2', $data);
		$this->load->view('themes/v2/footer', $data);
	}

	public function update_profile()
	{
		$datapost = array(
			'name' => $this->input->post('name', true),
			'address' => $this->input->post('address', true),
			'phone' => $this->input->post('phone', true),
		);
		if ($this->session->userdata('user_type')=='admin') {
			$datapost['settings'] = json_encode(array(
				'currency_symbol' => $this->input->post('currency_symbol',true),
				'currency_position' => $this->input->post('currency_position', true),
				'currency_separator' => $this->input->post('currency_separator', true),
			));
		}

		//upload foto
		$photo = $this->_upload_photo(); //response upload photo
		$photo_status = $photo['status_upload'];    // upload foto berhasil atau tidak
        $photo_available = $photo['available'];     // submit form memiliki gambar atau tidak
        $error_imageupload = $photo['error_msg'];   // pesan error saat upload foto
        $photo_path = $photo['path'];               // path direktori foto
        $photo_name = $photo['name'];               // nama foto yang diupload

		//validasi CI
		$this->_rules_profile();
		if ($this->form_validation->run() == FALSE || ($photo_status===FALSE && $photo_available===TRUE)) {
			//hapus gambar yang diupload bila validasi salah
            if ($photo_status===true) {
                $path = $photo_path.$photo_name;
                unlink($path); //hapus gambar yang baru saja diupload saat input gagal
            }

            //error currency
            $error_currency = (!empty(form_error('currency_symbol'))) ? form_error('currency_symbol') : form_error('currency_position');
            $error_currency = (!empty($error_currency)) ? $error_currency : form_error('currency_separator');
			$draw_json = array(
				'status' => 'error',
				'message' => 'Update Record Failed',
				'data' => array(
					'name' => form_error('name'),
					'address' => form_error('address'),
					'phone' => form_error('phone'),
					'photo' => $error_imageupload,
					'currency' => $error_currency
				)
			);
		} else {
			//setting session untuk foto
			if ($photo_available==true && $photo_status==true) {
				//kalau uppload sukses, hapus data lama
				if (!empty($this->session->userdata('photo'))) {
					$path = $photo_path.$this->session->userdata('photo');
					(file_exists($path) ? unlink($path): ''); //hapus foto lama
				}

        		//update session photo
				$updatesession['photo'] = $photo_name;
				$updatesession['photo_url'] = photo_url().$this->session->userdata('admin_id').'/'.$photo_name;
			}


			switch ($this->session->userdata('user_type')) {
				case 'admin':
					//update database admin
					$data_update_admin = array(
						'name' => $datapost['name'],
						'phone' => $datapost['phone'],
						'photo' => $photo_name,
						'settings' => $datapost['settings'],
					);
					$response = $this->Profile_model->update_admin($this->session->userdata('user_id'), $data_update_admin);
					break;

				case 'employee':
					//update database employee
					$data_update_employee = array(
						'name' => $datapost['name'],
						'phone' => $datapost['phone'],
						'address' => $datapost['address'],
						'photo' => $photo_name
					);
					$response = $this->Profile_model->update_employee($this->session->userdata('user_id'), $data_update_employee);
				break;

				default: break;
			}

			//update session
			$updatesession['user_name'] = $datapost['name'];
			$updatesession['phone'] = $datapost['phone'];

			//update currency
			$user = $this->Profile_model->get_mydata();
	        if (is_json($user->settings)) {
	        	$settings = json_decode($user->settings);
	        	$updatesession['currency_symbol'] = htmlentities($settings->currency_symbol);
	        	$updatesession['currency_position'] = $settings->currency_position;
	        	$updatesession['currency_separator'] = $settings->currency_separator;
	        }
			$this->session->set_userdata( $updatesession ); //update session

			//setting output
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Update Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Error'
				);
			}
			
		}

		echo format_json($draw_json);
	}//update

	public function change_email()
	{
		$this->_rules_email();

		//inisialisasi
		$draw_json = array();
		$valid_email = true;
		$available_email = true;

		$datapost = array(
			'oldemail' => $this->input->post('oldemail', true),
			'newemail' => $this->input->post('newemail', true),
		);

		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Change E-mail Failed',
				'data' => array(
					'oldemail' => form_error('oldemail'),
					'newemail' => form_error('newemail')
				)
			);
		} else {
			//cek email apakah merupakan pemilik data
			$user = $this->Profile_model->get_mydata();
			$current_email = $user->email;
			$valid_email = ($current_email != $datapost['oldemail']) ? false : true;

			//cek ketersediaan email START
			switch ($this->session->userdata('user_type')) {
				case 'admin':
					$cek_email = $this->Change_email_model->get_admin_by_email($datapost['newemail']);
					break;
				case 'employee':
					$cek_email = $this->Change_email_model->get_employee_by_email($datapost['newemail']);
					break;
				default: break;
			}
			$available_email = ($cek_email) ? false : true;
			//cek ketersediaan email END


			//validasi email yang digunakan
			if ($valid_email==false || $available_email==false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Change E-mail Failed',
					'data' => array(
						'oldemail' => ($valid_email==false) ? '<span class="text-danger">Invalid Current E-mail</span>' : '',
						'newemail' => ($available_email==false) ? '<span class="text-danger">E-mail already taken</span>' : ''
					),
				);
			}
			else{
				//kirim email konfirmasi pergantian email
				$response_kirimemail = $this->user_key_change_email_lib->create_confirm_change_email($current_email, $datapost['newemail'], $this->session->userdata('user_type'));

				//output
				$draw_json = array(
					'status' => 'success',
					'message' => 'Change E-mail Success! Please check your email to confirm your activity.'
				);
			}
		}

		echo format_json($draw_json);

	}

	public function change_password()
	{
		$this->_rules_password();

		//inisialisasi
		$valid_password = false;
		$draw_json = array();

		//data yang dikirim
		$data_post = array(
			'oldpass' => $this->input->post('oldpassword', true),
			'newpass' => $this->input->post('newpassword', true),
			'retypepass' => $this->input->post('retypepassword', true)
		);

		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Change Password Failed',
				'data' => array(
					'oldpassword' => form_error('oldpassword'),
					'newpassword' => form_error('newpassword'),
					'retypepassword' => form_error('retypepassword'),
				)
			);
		} else {
			//cek apakah oldpass benar
			$this->db->select('password');
			switch ($this->session->userdata('user_type')) {
				case 'admin':
					$this->db->where('admin_id', $this->session->userdata('admin_id'));
					$result = $this->db->get('admin');
					break;
				case 'employee':
					$this->db->where('employee_id', $this->session->userdata('user_id'));
					$result = $this->db->get('employee');
					break;
				default: break;
			}

			if ($result) {
				$current_password = $result->row()->password;
				$valid_password = password_validation($data_post['oldpass'], $current_password);
				if ($valid_password===false) {
					$draw_json = array(
						'status' => 'error',
						'message' => 'Invalid current password!'
					);
				}
				else{
					//cek new password dan retype sama atau tidak
					if ($data_post['newpass'] != $data_post['retypepass']) {
						$draw_json = array(
							'status' => 'error',
							'message' => 'New Password Password not match!',
							'data' => array()
						);
					}
					else{
						//update password
						$password_encrypt = password_encrypt($data_post['newpass']);
						$object['password'] = $password_encrypt;

						switch ($this->session->userdata('user_type')) {
							case 'admin':
								$this->db->where('admin_id', $this->session->userdata('admin_id'));
								$result_updatepass = $this->db->update('admin', $object);
								break;
							case 'employee':
								$this->db->where('employee_id', $this->session->userdata('user_id'));
								$result_updatepass = $this->db->update('employee', $object);
								break;
							default: break;
						}

						if ($result_updatepass === true) {
							$draw_json = array(
								'status' => 'success',
								'message' => 'Change Password Success!'
							);
						}
						else{
							$draw_json = array(
								'status' => 'error',
								'message' => 'Change Password Failed!'
							);
						}
					}
				}
			}
		}

		echo format_json($draw_json);
	}

	public function _upload_photo()
    {
        /* PROSES OLAH GAMBAR by aziz */

        //inisialisasi variabel
        $error_imageupload = '';
        $photo_available = false;   //form ada submit foto atau tidak
        $photo_upload = false;      //foto berhasil diupload atau tidak

        /*konfigurasi upload gambar start*/
        // $photo_path = product_url(); //(ambil dari assets_helper)
        // $photo_path = str_replace(base_url(), '', $photo_path); //ambil path dari assets/images/products

        /* photo path by private $photo_path */
        $photo_path = $this->photo_path;

        $this->load->helper('myfunction');
        $photo_name = current_millis().unique(3); //temporary name
        $config['upload_path']          = $photo_path; //'./_tes_upload/'.$album;
        $config['allowed_types']        = 'gif|jpg|png'; //ekstensi yang diizinkan
        $config['max_size']             = 100; //maksimal size
        $config['file_name']            = $photo_name; //nama gambar akan diganti saat diupload
        $config['max_width']  = '200'; //atur lebar maksimal gambar
        $config['max_height']  = '200'; //atur tinggi maksimal gambar
        $config['min_width']  = '200'; //atur lebar maksimal gambar
        $config['min_height']  = '200'; //atur tinggi maksimal gambar
        $config['remove_spaces'] = true;
        /*konfigurasi upload gambar end*/
        
        /*proses upload gambar start*/
        //buat direktori berdasarkan owner
        $dir_exist = true;
        if (!is_dir($photo_path)) {
            /* kalau mkdir() disable, comment dari sini */
            $old = umask(0);
            mkdir($photo_path, 0775, true); //buat direktori image produk untuk per-owner
            umask($old);
            /* kalau mkdir() disable, comment sampai sini */
            $dir_exist = false; // dir not exist
            $draw_json = array(
                'status' => 'error',
                'message' => 'Dir not Exist'
            );
        }

        //upload gambar
        $this->load->library('upload', $config);
        if (!empty($_FILES['photo']['name']) && $_FILES['photo']['name']!="") { //bila upload foto
            $photo_available = true;

            if ( ! $this->upload->do_upload('photo')){
                $error_imageupload = array('error' => $this->upload->display_errors());
                $error_imageupload = $error_imageupload['error'];
                $photo_upload = false; //status upload
                //echo "upload gagal";
            }
            else{
                $data = array('upload_data' => $this->upload->data());
                $photo_name = $data['upload_data']['file_name'];
                $photo_upload = true; //status upload
                //echo "upload success";
            }
        }
        else{
            $photo_available = false;//custom
            $photo_name = '';
            //echo "tidak ada gambar";
        }
        /* PROSES OLAH GAMBAR END*/

        return $photo = array(
            'status_upload' => $photo_upload,
            'available' => $photo_available,
            'error_msg' => $error_imageupload,
            'path' => $photo_path,
            'name' => $photo_name,
        );
    }

	public function _rules_profile()
	{
		$this->form_validation->set_rules('name', 'name', 'required|max_length[50]');
		$this->form_validation->set_rules('phone', 'phone', 'trim|required|is_numeric|max_length[15]');

		//validasi untuk employee
		if ($this->session->userdata('user_type')=='employee') {
			$this->form_validation->set_rules('address', 'address', 'required|max_length[100]');
		}
		else{
			$this->form_validation->set_rules('currency_symbol', 'currency symbol', 'trim|required|max_length[5]');
			$this->form_validation->set_rules('currency_position', 'currency position', 'trim|required|in_list[left,right]',array(
				'in_list' => 'The % must Left or Right.'
			));
			$this->form_validation->set_rules('currency_separator', 'currency separator', 'trim|required|in_list[point,comma]', array(
				'in_list' => 'The % must point or comma.'
			));
		}

		//$this->form_validation->set_rules('id', 'outlet_id', 'trim');
		$this->form_validation->set_error_delimiters('', '');
	}

	public function _rules_email()
	{
		$this->form_validation->set_rules('oldemail', 'old password', 'trim|required|valid_email');
		$this->form_validation->set_rules('newemail', 'new password', 'trim|required|valid_email');

		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
	}

	public function _rules_password()
	{
		$this->form_validation->set_rules('oldpassword', 'old password', 'trim|required');
		$this->form_validation->set_rules('newpassword', 'new password', 'trim|required|min_length[8]');
		$this->form_validation->set_rules('retypepassword', 're-type password', 'trim|required|min_length[8]');

		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
	}

}

/* End of file Account.php */
/* Location: ./application/controllers/settings/Account.php */