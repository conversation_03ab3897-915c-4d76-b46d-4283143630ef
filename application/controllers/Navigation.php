<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Navigation extends Auth_Controller {

	public function lv1()
	{
		$mainmenu = $this->uri->segment(1);
		$nav = $this->template->navigation();
		foreach ($nav[$mainmenu]['sub'] as $key => $value) {
			$this->check($value['url']);
		}

		$this->privilege->disable_page();
	}

	public function lv2()
	{
		$url = uri_string();

		$mainmenu = $this->uri->segment(1);
		$nav = $this->template->navigation();
		foreach ($nav[$mainmenu]['sub'] as $key => $value) {
			if (uri_string()==$value['url']) {
				if (!empty($value['sub'])) {
					foreach ($value['sub'] as $key2 => $value2) {
						if ($value['url']==uri_string()) {
							$this->check($value2['url']);
						}
						else{
							if ($_SESSION['role_web'][$value['url']]==true) {
								$this->check($value2['url']);
							}
						}
					}
				}
			}
		}
		$this->privilege->disable_page();
	}

	private function check($url='')
	{
		//init
		$role = $this->session->userdata('role_web');
		$is_admin = ($this->session->userdata('user_type')=='admin') ? true : false;
		if ((!empty($role[$url]['view']) && $role[$url]['view']==true) || $is_admin) {
			redirect($url);
			die();
		}
	}

}

/* End of file Navigation.php */
/* Location: ./application/controllers/Navigation.php */