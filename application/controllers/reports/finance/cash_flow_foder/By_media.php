<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class By_media extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//akses hanya untuk developer UNIQ
		$this->protect_page->developer<PERSON><PERSON><PERSON>('Finance');

		//role setting untuk user tipe employee
		$this->user_role->reports_finance('read_access'); //employee role access page
		$this->user_role->reports('read_access'); //employee role access page

		//load model
		$this->load->model('outlet/Outlets_model', 'Outlets_model');
		$this->load->model('outlet/Shift_model', 'Shift_model');
		$this->load->model('report/finance/Cash_flow_model_v2','cash_flow_model');
	}

	public function media($type,$outlet,$shift,$date,$timeZone)
	{
		header('Content-Type: application/json');
		$data=file_get_contents("http://localhost:2893/finance/cash_flow/media/".$type."/".$outlet."/".$shift."/".$date."/".$timeZone);		
		print_r($data);	
	}
}

/* End of file Finance.php */
/* Location: ./application/controllers/reports/Finance.php */