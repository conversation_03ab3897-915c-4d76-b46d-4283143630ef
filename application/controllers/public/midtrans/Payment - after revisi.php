<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Payment extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here

		$this->load->model('settings/Billing_model');
		$this->load->model('settings/Payment_model');
		$this->load->library('midtrans/Midtrans');
	}

	public function payment_notification()
	{
		/* for testing only */
		$object['json_result'] = file_get_contents('php://input');
		$object['result'] = 'dataget: '.json_encode($this->input->get());
		$object['error_log'] = error_log(print_r($this->input->get(),TRUE));
		$object['notif'] = 'notif tidak ada';
		$object['datapost'] = json_encode($this->input->post());
		$this->db->insert('testing', $object);
		/* for testing only */

		//INSERT KE TABLE PAYMENT
		$jsondata = file_get_contents('php://input');
		if (isJson($jsondata)==true) {
			//ubah array object
			$datamidtrans = json_decode($jsondata);

			//ambil Data dari JSON
			$billing_id = $datamidtrans->order_id;
			$payment_type = $datamidtrans->payment_type;
			$transaction_id = $datamidtrans->transaction_id;
			$transaction_time = $datamidtrans->transaction_time;
			$transaction_status = $datamidtrans->transaction_status;
			$status_code = $datamidtrans->status_code;
			$signature_key = $datamidtrans->signature_key;
			$fraud_status = (!empty($datamidtrans->fraud_status)) ? $datamidtrans->fraud_status : '';

			//cek Billing ID di server sendiri
			$databilling = $this->Billing_model->get_public_billing_by_id($billing_id);
			if ($databilling) {
				# STEP 1 //cek Billing ID dari midtrans notification di server dengan di MIDTRANS
				//ambil midtrans_notif lalu cek transaksi_status di MIDTRANS
				$midtrans_result_statusJson = $this->midtrans->status($billing_id); //output json
				$midtrans_result_statusArray = json_decode($midtrans_result_statusJson); //ubah jadi array object

				# STEP 2 //validasi data dari midtrans_notification dengan data di MIDTRANS
				// inisialisasi variabel
				$billing_id_valid = true;
				$message_error = '';
				//2.1. cocokkan berdasarkan order_id MIDTRANS
				if ($billing_id!=$midtrans_result_statusArray->order_id){
					$billing_id_valid = false;
					$message_error = 'Billing not valid';
				}
				//2.2. cocokkan berdasarkan transaction_id MIDTRANS
				if ($transaction_id!=$midtrans_result_statusArray->transaction_id) {
					$billing_id_valid = false;
					$message_error = 'transaction_id on midtrans server is not valid';
				}
				//2.3. cocokkan berdasarkan signature_key MIDTRANS
				if ($signature_key!=$midtrans_result_statusArray->signature_key) {
					$billing_id_valid = false;
					$message_error = 'signature_key is not valid';

					if (($transaction_status=='cancel') && ($transaction_status==$midtrans_result_statusArray->transaction_status)) {
						$billing_id_valid = true;
						echo $message_error = 'billiing cancel';
					}
					if (($transaction_status=='expire') && ($transaction_status==$midtrans_result_statusArray->transaction_status)) {
						$billing_id_valid = true;
						echo $message_error = 'billiing expired';
					}
				}


				//bila benar semua, insert ke payment history
				if ($billing_id_valid==true) {
					$payment_notification_response = $this->Payment_model->insert_payment_notification($billing_id, $jsondata);
					if ($payment_notification_response) { //bila insert ke DB success
						# MIDTRANS TRANSACTION STATUS
						// //credit cart payment:: https://api-docs.midtrans.com/#credit-card-charge
						// if ($transaction_status=='capture' && $fraud_status=='accept' ) { //payment success
						// 	//$update_billing_status = 'payment_success';
						// 	$update_billing_paidstatus = true;
						// }
						// if ($transaction_status=='capture' && $fraud_status=='challenge') { //payment butuh konfirmasi manual
						// 	//$update_billing_status = 'payment_challenge';
						// 	$update_billing_paidstatus = false;
						// }
						// if ($transaction_status=='settlement' && $fraud_status=='accept') {
						// 	//$update_billing_status = 'payment_success';
						// 	$update_billing_paidstatus = true;
						// }
						// if ($transaction_status=='deny') { //transaction is denied by the bank or FDS
						// 	//$update_billing_status = 'payment_reject';
						// 	$update_billing_paidstatus = true;
						// }
						// if ($transaction_status=='authorize' && $fraud_status=='accept') {
						// 	//$update_billing_status = 'payment_success_wait'; //payment menunggu menjadi settle/capture
						// 	$update_billing_paidstatus = false;
						// }

						// //transfer bank
						// if ($transaction_status=='expire' && $fraud_status=='accept') { //expire payment billing
						// 	//$update_billing_status = 'expire';
						// 	$update_billing_paidstatus = false;
						// }
						// if ($payment_type=='bank_transfer') {
						// 	if ($status_code=='200' && $transaction_status=='settlement') {
						// 		$update_billing_paidstatus = true;
						// 	}
						// }
						switch ($status_code) {
							case '200':
								switch ($payment_type) {
									case 'bank_transfer': //BANK TRANSFER
										if ($transaction_status=='settlement') {
											$update_billing_paidstatus = true;
										}
										break;
									
									default:
										# code...
										break;
								}
								break;
							case '201':
								# code...
								break;
							case '202':
								# code...
								break;
							default:
								# code...
								break;
						}
						

						//update current billing_status
						switch ($datamidtrans->transaction_status) {
							case 'pending':
								$update_billing_status = 'pending';
								break;
							case 'settlement':
								$update_billing_status = 'success';
								break;
							case 'capture':
								$update_billing_status = 'success';
								break;
							case 'deny':
								$update_billing_status = 'billing';
								break;
							case 'expire':
								$update_billing_status = 'expire';
								break;
							case 'cancel':
								$update_billing_status = 'cancel';
								break;
							default:
								$update_billing_status = 'undefined';
								break;
						}

						$billing_data['billing_status'] = $update_billing_status;
						if ($update_billing_status=='success') {
							/*
							//kalau billing sudah terbayarkan, aktifkan service
							*/
							$billing_data['time_confirm'] = current_millis();
						}
						if (!empty($update_billing_paidstatus)) {
							$billing_data['paid_status'] = $update_billing_paidstatus;
						}
						$this->Payment_model->update_billing_data($billing_id,$billing_data);
					}
				}
				else{
					echo $message_error;
				}
			}
			else{
				echo "Billing ID: ".$billing_id." is not found.";
			}
		}
	}

	public function payment_finish()
	{
		$dataget = $this->input->get();
		echo print_r($dataget);
		echo "<br><br><br>";
		echo "payment finish bot access";
		echo "<br><br><br>";
		$datapost = $this->input->post();
		print_r($datapost);
	}

	public function payment_unfinish()
	{
		$dataget = $this->input->get();
		echo print_r($dataget);
		echo "<br><br><br>";
		echo "payment unfinish bot access";
		echo "<br><br><br>";
		$datapost = $this->input->post();
		print_r($datapost);
	}

	public function payment_error()
	{
		$dataget = $this->input->get();
		echo print_r($dataget);
		echo "<br><br><br>";
		echo "payment error bot access";
		echo "<br><br><br>";
		$datapost = $this->input->post();
		print_r($datapost);
	}

}

/* End of file Payment.php */
/* Location: ./application/controllers/public_bot/Payment.php */