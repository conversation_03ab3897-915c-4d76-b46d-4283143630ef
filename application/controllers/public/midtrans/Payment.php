<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Payment extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here

		$this->load->model('settings/Billing_model');
		$this->load->model('settings/Payment_model');
		$this->load->library('midtrans/Midtrans');
		$this->load->library('Myservice_lib');
	}

	public function payment_notification()
	{
		/* for testing only */
		$object['json_result'] = file_get_contents('php://input');
		$object['result'] = 'dataget: '.json_encode($this->input->get());
		$object['error_log'] = error_log(print_r($this->input->get(),TRUE));
		$object['notif'] = 'notif tidak ada';
		$object['datapost'] = json_encode($this->input->post());
		$this->db->insert('testing', $object);
		/* for testing only */

		//INSERT KE TABLE PAYMENT
		$jsondata = file_get_contents('php://input');
		if (isJson($jsondata)==true) {
			//ubah array object
			$datamidtrans = json_decode($jsondata);

			//ambil Data dari JSON
			$billing_id = $datamidtrans->order_id;
			$payment_type = $datamidtrans->payment_type;
			$transaction_id = $datamidtrans->transaction_id;
			$transaction_time = $datamidtrans->transaction_time;
			$transaction_status = $datamidtrans->transaction_status;
			$status_code = $datamidtrans->status_code;
			$signature_key = $datamidtrans->signature_key;
			$fraud_status = (!empty($datamidtrans->fraud_status)) ? $datamidtrans->fraud_status : '';

			//cek Billing ID di server sendiri
			$databilling = $this->Billing_model->get_public_billing_by_id($billing_id);
			// print_r($databilling);
			// die();
			if ($databilling) {
				# STEP 1 //cek Billing ID dari midtrans notification di server dengan di MIDTRANS
				//ambil midtrans_notif lalu cek transaksi_status di MIDTRANS
				$midtrans_result_statusJson = $this->midtrans->status($billing_id); //output json
				$midtrans_result_statusArray = json_decode($midtrans_result_statusJson); //ubah jadi array object

				# STEP 2 //validasi data dari midtrans_notification dengan data di MIDTRANS
				// inisialisasi variabel
				$billing_id_valid = true;
				$message_error = '';
				//2.1. cocokkan berdasarkan order_id MIDTRANS
				if ($billing_id!=$midtrans_result_statusArray->order_id){
					$billing_id_valid = false;
					$message_error = 'Billing not valid';
				}
				//2.2. cocokkan berdasarkan transaction_id MIDTRANS
				if ($transaction_id!=$midtrans_result_statusArray->transaction_id) {
					$billing_id_valid = false;
					$message_error = 'transaction_id on midtrans server is not valid';
				}
				//2.3. cocokkan berdasarkan signature_key MIDTRANS
				if ($signature_key!=$midtrans_result_statusArray->signature_key) {
					$billing_id_valid = false;
					$message_error = 'signature_key is not valid';

					if (($transaction_status=='cancel') && ($transaction_status==$midtrans_result_statusArray->transaction_status)) {
						$billing_id_valid = true;
						echo $message_error = 'billiing cancel';
					}
					if (($transaction_status=='expire') && ($transaction_status==$midtrans_result_statusArray->transaction_status)) {
						$billing_id_valid = true;
						echo $message_error = 'billiing expired';
					}
				}


				//bila benar semua, insert ke payment history
				if ($billing_id_valid==true) {
					$payment_notification_response = $this->Payment_model->insert_payment_notification($billing_id, $jsondata);
					if ($payment_notification_response) { //bila insert ke DB success
						# MIDTRANS TRANSACTION STATUS
						// //credit cart payment:: https://api-docs.midtrans.com/#credit-card-charge
						//ambil status code
						if ($status_code=='200') {
							if ($transaction_status=='settlement') {
								$update_billing_paidstatus = true;
								$update_billing_status = 'success'; //update DB billing status
							}
							elseif ($transaction_status=='capture') {
								$update_billing_paidstatus = true;
								$update_billing_status = 'success'; //update DB billing status
							}

							/* //kalau billing sudah terbayarkan, aktifkan service */
							echo $this->myservice_lib->konfirmasi($billing_id);
						}
						elseif ($status_code=='201') {
							if ($transaction_status=='pending') {
								$update_billing_paidstatus = false;
								$update_billing_status = 'pending';
							}
							elseif ($transaction_status=='capture') {
								$update_billing_paidstatus = false;
								$update_billing_status = 'pending_challenge';
							}
						}
						elseif ($status_code=='202') {
							if ($transaction_status=='deny') {
								if ($databilling->billing_status=='billing') {
									$update_billing_status = 'billing';
								}
								elseif ($databilling->billing_status=='success') {
									$update_billing_status = 'expire';
									$billing_data['time_order_expired'] = current_millis();
								}
							}
							elseif ($transaction_status=='cancel') {
								$update_billing_status = 'cancel';
							}
							elseif ($transaction_status=='expire') {
								$update_billing_status = 'expire';
							}
						}

						
						//update billing on DB
						$billing_data['billing_status'] = $update_billing_status;
						if ($update_billing_status=='success') { //kalau billing sudah terbayarkan, aktifkan service
							$billing_data['time_order_expired'] = current_millis(); //nonaktifkan billing
							$billing_data['time_confirm'] = current_millis();
						}
						if (!empty($update_billing_paidstatus)) {
							$billing_data['paid_status'] = $update_billing_paidstatus;
						}
						//update billing
						$response = $this->Payment_model->update_billing_data($billing_id,$billing_data);
					}
				}
				else{
					echo $message_error;
				}
			}
			else{
				echo "Billing ID: ".$billing_id." is not found.";
			}
		}
	}

	public function payment_notification_new()
	{
		//inisialisasi variabel
		$billingID_valid = false;
		$billingID_valid_server = false;
		$billingID_valid_midtrans = false;
		$notification_valid = true;

		//INSERT KE TABLE PAYMENT
		$datapost_json = file_get_contents('php://input');
		if (isJson($datapost_json)==true) {
			//ubah array object
			$datapost_array = json_decode($datapost_json);
			$datapost_billing_id = $datapost_array->order_id;

			# CEK DATAPOST DENGAN DATA SERVER DAN MIDTRANS
			//cek data apakah ada di SERVER berdasarkan billing ID
			$dataserver = $this->Billing_model->get_public_billing_by_id($datapost_billing_id);
			$billingID_valid_server = ($dataserver) ? true : false;

			//cek data apakah ada di MIDTRANS berdasarkan billing ID
			$datamidtrans = $this->midtrans->status($datapost_billing_id);
			$billingID_valid_midtrans = ($datamidtrans) ? true : false;

			# BILA DATA ADA DI SERVER DAN MIDTRANS
			if ($billingID_valid_server==true && $billingID_valid_midtrans==true) {
				//data ada di SERVER dan MIDTRANS
				$billingID_valid = true;
			}
			elseif ($billingID_valid_server==true && $billingID_valid_midtrans==false) {
				$billingID_valid = true;
				echo "billing tidak ada di midtrans";
			}
			elseif ($billingID_valid_server==false && $billingID_valid_midtrans==true) {
				$billingID_valid = true;
				echo "billing tidak ada di server";
			}
			else{
				$billingID_valid = true;
				echo "billing tidak ada di server dan midtrans";
			}


			
			if ($billingID_valid==true) {
				# VALIDASI DATAPOST dengan di MIDTRANS
				$datamidtrans_array = json_decode($datamidtrans);

				//status_code
				if ($notification_valid==true) {
					$notification_valid = ($datapost_array->status_code==$datamidtrans_array->status_code) ? true : false;
					// echo "datapost: ".$datapost_array->status_code;
					// echo "\n";
					// echo "datamidtrans:".$datamidtrans_array->status_code;
					// echo "\n";
				}
				//transaction_id
				if ($notification_valid==true) {
					$notification_valid = ($datapost_array->transaction_id==$datamidtrans_array->transaction_id) ? true : false;
				}
				//signature_key
				if ($notification_valid==true) {
					$notification_valid = ($datapost_array->signature_key==$datamidtrans_array->signature_key) ? true : false;
				}
			}

			// echo "notification_valid: ".$notification_valid." \n";
			$payment_notification_response = $this->Payment_model->insert_payment_notification($datapost_billing_id, $datapost_json);
			if ($payment_notification_response) {
				# MIDTRANS TRANSACTION STATUS
				//credit cart payment:: https://api-docs.midtrans.com/#credit-card-charge
				switch ($datamidtrans_array->status_code) {
					case '200':
						$update_billing_paidstatus = true;
						$update_billing_status = 'success'; //update DB billing status

						/* //kalau billing sudah terbayarkan, aktifkan service */
						echo $this->myservice_lib->konfirmasi($datapost_billing_id);
						break;
					case '201':
						if ($datamidtrans_array->transaction_status=='pending') {
							$update_billing_paidstatus = false;
							$update_billing_status = 'pending';
						}
						elseif ($datamidtrans_array->transaction_status=='capture') {
							$update_billing_paidstatus = false;
							$update_billing_status = 'pending_challenge';
						}
						break;
					case '202':
						if ($datamidtrans_array->transaction_status=='deny') {
							if ($databilling->billing_status=='billing') {
								$update_billing_status = 'billing';
							}
							elseif ($databilling->billing_status=='success') {
								$update_billing_status = 'expire';
							}
						}
						elseif ($datamidtrans_array->transaction_status=='cancel') {
							$update_billing_status = 'cancel';
						}
						elseif ($datamidtrans_array->transaction_status=='expire') {
							$update_billing_status = 'expire';
						}
						break;
					
					default:
						$update_billing_paidstatus = false;
						$update_billing_status = 'undefined';
						break;
				}//end of switch


				//update billing on DB
				if (!empty($update_billing_status)) {
					$billing_data['billing_status'] = $update_billing_status;
				}
				$billing_data['paid_status'] = (!empty($update_billing_paidstatus) && $update_billing_paidstatus==true) ? true : false;
				//update billing
				$response = $this->Payment_model->update_billing_data($datapost_billing_id, $billing_data);
			}
		}
		else{
			echo "data bukan format json";
		}
	}

	public function payment_finish()
	{
		$dataget = $this->input->get();
		echo print_r($dataget);
		echo "<br><br><br>";
		echo "payment finish bot access";
		echo "<br><br><br>";
		$datapost = $this->input->post();
		print_r($datapost);
	}

	public function payment_unfinish()
	{
		$dataget = $this->input->get();
		echo print_r($dataget);
		echo "<br><br><br>";
		echo "payment unfinish bot access";
		echo "<br><br><br>";
		$datapost = $this->input->post();
		print_r($datapost);
	}

	public function payment_error()
	{
		$dataget = $this->input->get();
		echo print_r($dataget);
		echo "<br><br><br>";
		echo "payment error bot access";
		echo "<br><br><br>";
		$datapost = $this->input->post();
		print_r($datapost);
	}

}

/* End of file Payment.php */
/* Location: ./application/controllers/public_bot/Payment.php */