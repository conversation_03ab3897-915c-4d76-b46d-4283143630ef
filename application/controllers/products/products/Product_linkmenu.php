<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_linkmenu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        $this->user_role->products('read_access'); //employee role access page

        //model
        $this->load->model('products/products_catalogue/Products_linkmenu_model','Linkmenu_model');
        $this->load->model('outlet/Outlets_model'); //form
	}

	//LINKMENU
	public function get_linkmenu_by_outlet($outlet_id=null, $product_id=null)
	{
		//inisialisasi
		$draw_json = array();
		$data = array();


		if ($outlet_id!=null && $product_id!=null) {
			//ambil data linkmenu
			$data_linkmenu = array(
				'outlet_id' => $outlet_id,
				'product_id' => $product_id
			);
			$result_linkmenu = $this->Linkmenu_model->get_linkmenu_by_outlet($data_linkmenu);

			foreach ($result_linkmenu->result() as $a) {
				$id = $a->linkmenu_id;
				$name = htmlentities($a->name);
				$order = htmlentities($a->order_no);
				$temp = array(
					'linkmenu_id' => $id,
					'name' => $name,
					'description' => $a->description,
					'multiplechoice' => $a->is_multiplechoice,
					'order' => $a->order_no,
					'action' => '<span onclick="detailLinkmenu('.$id.')" class="btn btn-primary btn-xs">Detail</span>
							&nbsp;&nbsp;&nbsp; <span onclick="editLinkmenu('.$id.')" class="btn btn-primary btn-xs">Edit</span>
							&nbsp;&nbsp;&nbsp; <span onclick="deleteLinkmenu('.$id.')" class="glyphicon glyphicon-trash btn btn-primary btn-xs"></span>'
				);

				array_push($data, $temp);
			}
			$draw_json = array(
				'status' => 'success',
				'data' => $data
			);
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Invalid Outlet or Product'
			);
		}

		echo format_json($draw_json);
	}

	public function get_linkmenu_by_id($linkmenu_id=null, $product_id=null)
	{
		$response = $this->Linkmenu_model->get_linkmenu_by_id($linkmenu_id,$product_id)->row();
		if ($response) {
			$draw_json = array(
				'status' => 'success',
				'data' => array(
					'linkmenu_id'	=> $response->linkmenu_id,
					'name'			=> htmlentities($response->name),
					'description'	=> htmlentities($response->description),
					'multiplechoice' => $response->is_multiplechoice,
					'order'			=> $response->order_no,
				)
			);
		}
		else{
			$draw_json = array(
				'status' => 'success',
				'message' => 'Error Getting Data'
			);
		}

		echo format_json($draw_json);
	}

	public function create_linkmenu()
	{
		$this->_rules();
		$draw_json = array();

		if ($this->form_validation->run() === FALSE) {
			$messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed'); //setting pesan 
			$draw_json = array(
				'status' => 'error',
				'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
				'data' => array(
					'name' => form_error('name'),
					'description' => form_error('description'),
					'order' => form_error('order'),
					'multiplechoice' => form_error('multiple'),
					'outlet' => form_error('outlet[]'),
				)
			);
		} else {
			//data yang dikirim
			$data_post = array(
				'name' => $this->input->post('name', true),
				'description' => $this->input->post('description', true),
				'order' => $this->input->post('order', true),
				'multiple' => $this->input->post('multiple',true),
				'outlet' => $this->input->post('outlet[]', true),
				'product_id' => $this->input->post('productid', true),
			);

			//jika action create
			$data_post_create = array(
				'name' => $data_post['name'],
				'description' => $data_post['description'],
				'order_no' => $data_post['order'],
				'is_multiplechoice' => $data_post['multiple'],
				'product_fkid' => $data_post['product_id'],
			);

			//outlet yang dicentang
			/*re-format value outlet yang dicentang untuk all outlet*/
			$outletSubmitted = $data_post['outlet'];
			if ($outletSubmitted[0]=='all_outlet') {
			    $outletlist = $this->Outlets_model->form_select();
			    $i=0;
			    foreach ($outletlist as $value) {
			        $outletSubmitted[$i] = $value->outlet_id;
			        $i++;
			    }
			}

			//masukkan ke database berdasarkan outlet yang dicentang
			foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
				$data_post_create['outlet_fkid'] = $checkedoutlet;
				$response = $this->Linkmenu_model->insert_linkmenu($data_post_create);
			}

			if ($response===true) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Create Record Success'
				);
			}
		}

		echo format_json($draw_json);
	}

	public function update_linkmenu()
	{
		$draw_json = array();
		$this->_rules();
		/*custom rules start*/
		$this->form_validation->set_rules('outlet[]', 'outlet', 'trim');
		/*custom rules end*/
		

		if ($this->form_validation->run() === FALSE) {
			$messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed'); //setting pesan 
			$draw_json = array(
				'status' => 'error',
				'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
				'data' => array(
					'name' => form_error('name'),
					'description' => form_error('description'),
					'order' => form_error('order'),
					'multiplechoice' => form_error('multiple'),
					'outlet' => form_error('outlet[]'),
				)
			);
		} else {
			//data yang dikirim
			$data_post = array(
				'id' => $this->input->post('id',true),
				'name' => $this->input->post('name', true),
				'description' => $this->input->post('description', true),
				'order' => $this->input->post('order', true),
				'multiple' => $this->input->post('multiple',true),
				//'outlet' => $this->input->post('outlet[]', true),
				'product_id' => $this->input->post('productid', true),
			);

			//jika action update
			$data_post_update = array(
				'id' => $data_post['id'],
				'name' => $data_post['name'],
				'description' => $data_post['description'],
				'order_no' => $data_post['order'],
				'is_multiplechoice' => $data_post['multiple'],
				'product_fkid' => $data_post['product_id'],
			);

			$response = $this->Linkmenu_model->update_linkmenu($data_post_update['id'], $data_post_update);
			if ($response===true) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Update Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Failed'
				);
			}
		}

		echo format_json($draw_json);
	}

	public function delete_linkmenu($linkmenu_id=null, $product_id=null)
	{
		$draw_json = array();

		$response = $this->Linkmenu_model->delete_linkmenu($linkmenu_id, $product_id);
		if ($response===true) {
			$draw_json = array(
				'status' => 'success',
				'message'=> 'Delete Record Success'
			);
		}
		else {
			$draw_json = array(
				'status' => 'error',
				'message'=> 'Delete Record Failed'
			);
		}

		echo format_json($draw_json);
	}

	public function _rules() 
	{
		$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('description', 'description', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('order', 'order no', 'trim|required|is_numeric');
		$this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
		$this->form_validation->set_rules('productid', 'product id', 'trim|required');
		$this->form_validation->set_rules('multiple', 'multiple choice', 'trim|required|in_list[0,1]',array(
			'in_list' => 'The %s must Yes or No.'
		));

		$this->form_validation->set_rules('id', 'linkmenu_id', 'trim');
		$this->form_validation->set_error_delimiters('','');
	}

	//LINKMENU DETAIL
	public function get_linkmenu_detail($linkmenu_id=null)
	{
		$draw_json = array();
		$data = array();
		$result = $this->Linkmenu_model->get_detail_by_linkmenu_id($linkmenu_id);

		foreach ($result as $a) {
			$temp = array(
				'linkmenu_detail_id' => $a->linkmenu_detail_id,
				'product_detail_id' => $a->product_detail_fkid,
				'product_detail_name' => htmlentities($a->product_name_onlinkdetail),
				'price_sell' => $a->price_sell_onoutlet,
				'price_add' => $a->price_add,
			);

			array_push($data, $temp);
		}

		$draw_json = array(
			'status' => 'success',
			'data' => $data
		);

		echo format_json($draw_json);
	}

	public function update_linkmenu_detail($linkmenu_detail_id=null)
	{
		//rules validasi
		$this->form_validation->set_rules('price_add', 'additional price', 'trim|required|is_numeric');
		$this->form_validation->set_rules('linkmenu_id', 'linkmenu id', 'trim|required|is_numeric');

		//data yang dikirim
		$data_post = array(
			'linkmenu_detail_id' => $linkmenu_detail_id,
			'linkmenu_id' => $this->input->post('linkmenu_id', true),
			'price_add' => $this->input->post('price_add', true),
		);


		if (empty($linkmenu_detail_id) || !is_numeric($linkmenu_detail_id)) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Invalid ID'
			);
		}
		else{

			//CI validation run
			if ($this->form_validation->run() == FALSE) {
				$error = str_replace('<p>', '', form_error('price_add'));
				$error = str_replace('</p>', '', $error);

				$draw_json = array(
					'status' => 'error',
					'message' => $error,
					'data' => array(
						'linkmenu_id' => form_error('linkmenu_id'),
						'price_add' => form_error('price_add')
					)
				);
			}
			else {
				$response = $this->Linkmenu_model->update_linkmenudetail($linkmenu_detail_id, $data_post);
				if ($response===true) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Update Record Success'
					);
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Update Record Failed'
					);
				}
			}
		}
		
		echo format_json($draw_json);
	}

	public function delete_linkmenu_detail($linkmenu_detail_id=null)
	{
		$draw_json = array();

		//cek data
		$row = $this->Linkmenu_model->get_linkmenudetail_by_detail_id($linkmenu_detail_id);
		if ($row) {
			$response = $this->Linkmenu_model->delete_linkmenudetail($linkmenu_detail_id);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Delete Record Failed'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Data not found'
			);
		}

		echo format_json($draw_json);
	}

	//ADD PRODUCT TO LINKMENU
	public function datatables_productlink($outlet_id=null)
	{
		$jsondata = $this->Linkmenu_model->json_product_by_outlet($outlet_id); //ambil data json datatable
		$json_decode = json_decode($jsondata); //pecah data json

		$dataArray = array();
        foreach ($json_decode->data as $a) {
            //encoding string untuk mencegah XSS injection dengan htmlentities()
            $detail_id = $a->product_detail_id;
        	$id = $a->product_fkid;
        	$name = htmlentities($a->product_name); //encoding string
            $barcode = htmlentities($a->barcode);
            $sku = htmlentities($a->sku);
            $unit_name = htmlentities($a->unit_name);
            $price_sell = $a->price_sell;
        	
            //tombol action pada datatable
        	$action = "<a href='javascript:void(0)' class='actionAddToForm' var-id='{$detail_id}'><span class='btn btn-primary btn-xs'>Add</span></a>";

            //data yang akan ditampilkan dalam json
        	$temp_data = array(
        		'product_detail_id' => $detail_id,
        		'product_fkid' => $id,
        		'product_name' => $name,
                'barcode' => $barcode,
                'sku' => $sku,
                'unit_name' => $unit_name,
                'price_sell' => $price_sell,
                'action_link' => $action,
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
	}

	public function add_linkmenu_detail($linkmenu_id=null)
	{
		//inisialisasi variabel
		$draw_json = array();

		//validation rules
		$this->form_validation->set_rules('product_priceadd[]', 'additional price', 'trim|required|is_numeric');

		if ($this->form_validation->run() == FALSE || empty($linkmenu_id)) {
			$error_message = '';
			if ($linkmenu_id==null || !is_numeric($linkmenu_id)) {
				$error_message = 'Invalid Link Menu';
			}
			$error_message = (empty($error_message) ? form_error('product_priceadd[]') : $error_message);
			$error_message = str_replace('<p>', '', $error_message);
			$error_message = str_replace('</p>', '', $error_message);

			$draw_json = array(
				'status' => 'error',
				'message' => $error_message
			);
		} else {
			//echo "ini hasil true";
			$data_post = array(
				'price_add' => $this->input->post('product_priceadd[]', true),
				'outlet_id' => $this->input->post('outlet', true)
			);

			$data_no = 0;
			$success_insert = 0;
			foreach ($data_post['price_add'] as $index => $value) {
				$data_post_create = array(
					'linkmenu_id' => $linkmenu_id,
					'product_detail_id' => $index,
					'price_add' => $value
				);

				//cek apakah benar product ada di outlet
				$response = $this->Linkmenu_model->get_product_onoutlet($index)->row();
				if ($response) {
					//jika benar ada di outlet
					$outlet_id = $response->outlet_fkid;
					if ($outlet_id==$data_post['outlet_id']) {
						$response_insert = $this->Linkmenu_model->add_linkmenu_product($data_post_create);
						if ($response_insert) {
							$success_insert++;
						}
					}
				}

				$data_no++;
			}
			if ($data_no==$success_insert) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Create Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'success',
					'message' => 'Some Record not Added'
				);
			}
			
		}
		echo format_json($draw_json);
	}

}

/* End of file Product_catalogue_linkmenu.php */
/* Location: ./application/controllers/products/products/Product_linkmenu.php */