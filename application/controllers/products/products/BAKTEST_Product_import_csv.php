<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_import_csv extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		//$this->user_role->products_products('read_access'); //employee role access page | submenu ingridient
		$this->user_role->products('read_access'); //employee role access page

		//load
		$this->load->library('CSVReader');
		$this->load->model(array(
			'products/type/Type_model',
			'products/products_category/Products_category_model',
			'products/products_subcategory/Products_subcategory_model',
			'products/ingridients/Purchase_report_category_model',
			'products/ingridients/Unit_model',
			'products/products_catalogue/Products_catalogue_model',
			'products/products_catalogue/Products_import_model',
			'outlet/Outlets_model',
			'products/gratuity/Gratuity_model',

			'products/products_catalogue/Products_model',
			'products/products_catalogue/Products_detail_model',
			'products/products_catalogue/Products_catalogue_taxgratuity_model',
		));
	}

	public function index()
	{
		//echo "import data";
		$data = array(
			'link_action' => site_url('products/product-catalogue/import_process')
		);
		$this->load->view('products/products_import/import_csv/import_v', $data, FALSE);
	}

	public function _import5() //oke
	{
		$this->load->library('CSVReader');
		$file = $_FILES['csv_file']['tmp_name'];
		$result =   $this->csvreader->parse_file($file);//path to csv file

	    $data['csvData'] =  $result;
	    echo print_r($result);
	}

	public function import()
	{
		//init
		$draw_json = array();
		$valid = true; //untuk validasi input
		$valid_data = true; //untuk validasi isi CSV
		$success_import = 0;

		//data yang dikirim
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');
		$datapost_tax = $this->input->post('tax[]');

		//validasi
		if ($valid==true) { //cek CSV
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV not found'
				);
			}
		}
		if ($valid==true) { //cek outlet
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}

		//proses olah data
		if ($valid==true) {
			//cek outlet yang dicentang apakah valid atau tidak
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//cek tax yang dicentang apakah valid atau tidak
			$selected_tax = array();
			if (!empty($datapost_tax)) {
				foreach ($datapost_tax as $tax_id) {
					$valid_tax = $this->Gratuity_model->get_by_id($tax_id);
					if ($valid_tax) {
						$selected_tax[] = $tax_id;
					}
				}
			}


			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $row) {
				$temp = array(
					'name'						=> $row[0],
					'catalogue_type'			=> $row[1], 
					'type'						=> $row[2], //$products_type_id
					'category'					=> $row[3], //$products_category_id
					'subcategory' 				=> $row[4], //$products_subcategory_id
					'pure_category'				=> $row[5], //$purchase_report_category_id
					'unit'						=> $row[6], //$unit_id
					'unit_description'			=> $row[7], //varchar 30
					'stock_management'			=> $row[8], //boolean (y/n)
					'barcode'					=> $row[9], //varchar 30
					'sku'						=> $row[10], //varchar 30
					'price_buy'					=> $row[11], //numeric
					'price_sell'				=> $row[12], //numeric
					'voucher'					=> $row[13], //on|off
					'discount'					=> $row[14], //on|off
					'menu_active'				=> $row[15], //on_all|on_sales|on_link|off
					'active_outlet'				=> $row[16], //on|off (y/n)
				);

				//validasikan data (master)
				#cek catalogue type
				$cek_products_type = $this->Type_model->get_by_name($temp['type']); //cari berdasarkan nama
				if ($cek_products_type) {
					//kalau ditemukan, ambil id dari product type
					$products_type_id = $cek_products_type->products_type_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_type_insert = array('name' => $temp['type']);
					$insert_products_type = $this->Type_model->insert($products_type_insert);
					if ($insert_products_type) {
						$products_type_id = $this->db->insert_id();
					}
				}

				#cek products category
				$cek_products_category = $this->Products_category_model->get_by_name($temp['category']);
				if ($cek_products_category) {
					//kalau ditemukan, ambil id dari product category
					$products_category_id = $cek_products_category->product_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_category_insert = array('name' => $temp['category']);
					$insert_products_category = $this->Products_category_model->insert($products_category_insert);
					if ($insert_products_category) {
						$products_category_id = $this->db->insert_id();
					}
				}

				#cek products subcategory
				$cek_products_subcategory = $this->Products_subcategory_model->get_by_name($temp['subcategory']);
				if ($cek_products_subcategory) {
					//kalau ditemukan, ambil id dari product subcategory
					$products_subcategory_id = $cek_products_subcategory->product_subcategory_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_subcategory_insert = array('name' => $temp['subcategory']);
					$insert_products_subcategory = $this->Products_subcategory_model->insert($products_subcategory_insert);
					if ($insert_products_subcategory) {
						$products_subcategory_id = $this->db->insert_id();
					}
				}

				#cek purchase report category
				$cek_purchase_report_category = $this->Purchase_report_category_model->get_by_name($temp['pure_category']);
				if ($cek_purchase_report_category) {
					//kalau ditemukan, ambil id dari purchase report category
					$purchase_report_category_id = $cek_purchase_report_category->purchase_report_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$purchase_report_category_insert = array('name' => $temp['pure_category']);
					$insert_purecategory = $this->Purchase_report_category_model->insert($purchase_report_category_insert);
					if ($insert_purecategory) {
						$purchase_report_category_id = $this->db->insert_id();
					}
				}

				#cek unit
				$cek_unit = $this->Unit_model->get_by_name($temp['unit']);
				if ($cek_unit) {
					//kalau ditemukan, ambil id dari unit
					$unit_id = $cek_unit->unit_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$unit_insert = array('name' => $temp['unit'], 'description' => $temp['unit_description']);
					$insert_unit = $this->Unit_model->insert($unit_insert);
					if ($insert_unit) {
						$unit_id = $this->db->insert_id();
					}
				}

				#cek stock management
				$stock_management = (strtolower($temp['stock_management'])=='y') ? true : false;

				$price_buy = (is_numeric($temp['price_buy'])) ? $temp['price_buy'] : 0;
				$price_sell = (is_numeric($temp['price_sell'])) ? $temp['price_sell'] : 0;

				#cek voucher (statis)
				$voucher = (strtolower($temp['voucher'])=='y') ? 'on' : 'off';

				#cek discount (statis)
				$discount = (strtolower($temp['discount'])=='y') ? 'on' : 'off';

				$active_outlet = (strtolower($temp['active_outlet'])=='y') ? 'on' : 'off';

				#cek menu active
				$menu_active = strtolower($temp['menu_active']);
				switch ($menu_active) {
					case 'all':
						$menu_active = 'on_all';
						break;
					case 'sales':
						$menu_active = 'on_sales';
						break;
					case 'link':
						$menu_active = 'on_link';
						break;
					default:
						$menu_active = 'off';
						break;
				}


				//data yang akan diinsert (master)
				$data_import = array(
					'name' => $temp['name'],
					'catalogue_type' => $temp['catalogue_type'],
					'product_type_fkid' => $products_type_id,
					'product_category_fkid' => $products_category_id,
					'product_subcategory_fkid' => $products_subcategory_id,
					'purchase_report_category_fkid' => $purchase_report_category_id,
					'unit_fkid' => $unit_id,
					'stock_management' => $stock_management,
					'barcode' => (!empty($temp['barcode'])) ? $temp['barcode'] : null,
					'sku' => (!empty($temp['sku'])) ? $temp['sku'] : null,
					'data_status' => 'on',
				);


				//ambil data master products berdasarkan SKU
				$data_product = $this->Products_model->get_by_sku($temp['sku']);
				if ($data_product) {
					//bila data sudah ada
					$products_id = $data_product->product_id;

					//update data (master)
					$response = $this->Products_model->update($products_id, $data_import);
				}
				else{
					//bila data belum ada
					$response = $this->Products_model->insert($data_import);
					if ($response) {
						$products_id = $this->db->insert_id(); // ambil primary key
					}
				}


				//insert ke detail
				# matikan data di detail
				// $data_detaildata_to_off = array('data_status' => 'off');
				// $response = $this->Products_detail_model->update_by_parent_id($products_id, $data_detaildata_to_off); //matikan semua data

				# cek, product sudah ada di outlet atau belum
				foreach ($selected_outlet as $data_outlet_id) {
					$product_detail_id = null; //init var
					$cek_detail = $this->Products_detail_model->get_by_parent_and_outlet($products_id, $data_outlet_id);
					if ($cek_detail) {
						$product_detail_id = $cek_detail->product_detail_id;
						//hidupkan menu di outlet bila sudah ada
						$data_detaildata_to_on = array(
							'price_buy' => $price_buy,
							'price_sell' => $price_sell,
							'voucher' => $voucher,
							'discount' => $discount,
							'active' => $menu_active,
							'data_status' => $active_outlet,
						);
						$response = $this->Products_detail_model->update($cek_detail->product_detail_id, $data_detaildata_to_on);
					}
					else{
						//insert menu untuk outlet
						$data_insert_products_on_outlet = array(
							'product_fkid' => $products_id,
							'outlet_fkid' => $data_outlet_id,
							'price_buy_start' => $price_buy,
							'price_buy' => $price_buy,
							'price_sell' => $price_sell,
							'voucher' => $voucher,
							'discount' => $discount,
							'active' => $menu_active,
						);
						$response = $this->Products_detail_model->insert($data_insert_products_on_outlet);
						$product_detail_id = $this->db->insert_id();
					}

					
					#matikan data di detail tax berdasarkan product detail id
					$tax_detail_update_off = $this->Products_catalogue_taxgratuity_model->update_taxgratuity_datastatus_by_masterdetailid($product_detail_id, 'off');
					//insert ke detail tax
					if (!empty($selected_tax)) {
						foreach ($selected_tax as $tax_id) {
							//cek apakah data sudah ada
							$cek_tax_detail = $this->Products_catalogue_taxgratuity_model->get_taxdetail_by_tax_masterdetail($tax_id,$product_detail_id);
							if ($cek_tax_detail) {
								//jika tax sudah ada (update jadi on)
								$taxdetail_update_on = array('data_status' => 'on');
								$this->Products_catalogue_taxgratuity_model->update_taxdetail_by_tax_masterdetail($tax_id, $product_detail_id, $taxdetail_update_on);
							}
							else{
								//jika tax belum ada (maka insert)
								$taxdetail_insert = array(
									'tax_fkid' => $tax_id,
									'product_detail_fkid' => $product_detail_id,
								);
								$this->Products_catalogue_taxgratuity_model->insert_taxgratuity($taxdetail_insert);
							}
						}
					}//endif: selected tax
					$success_import++;
				}//endforeach: selected outlet
			}//endforeach: parsing data csv

			//response success
			$draw_json = array(
				'status' => 'success',
				'message' => $success_import/count($datapost_outlet).' of '.count($result_csv_parse).' data have been added to '.count($datapost_outlet).' outlets!'
			);
		}

		//output json
		echo format_json($draw_json);
	}

}

/* End of file Product_import_csv.php */
/* Location: ./application/controllers/products/products/Product_import_csv.php */