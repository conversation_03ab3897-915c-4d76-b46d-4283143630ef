<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_export extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		//$this->user_role->products_products('read_access'); //employee role access page
		$this->user_role->products('read_access'); //employee role access page

		//model
		$this->load->model('outlet/Outlets_model');
	}

	public function export()
	{
		//init
		$draw_json = array();
		$datapost = array(
			'format' => $this->input->post('format',true),
			'outlet[]' => $this->input->post('outlet[]',true),
		);

		//validasi
		$this->form_validation->set_rules('format', 'format', 'trim|required|in_list[xls]');

		if ($this->form_validation->run() == FALSE) {
			//invalid input
			$draw_json = array(
				'status' => 'error',
				'message' => 'Export Data Failed',
				'data' => array(
					'format' => form_error('format'),
					'outlet' => form_error('outlet[]'),
				)
			);
			//redirect
			$this->session->set_flashdata('msg_error', 'Export Data Failed!');
			redirect(site_url('products/product-catalogue'));
		} else {
			//validasi selected outlet
			$selectedOutlet = array();
			foreach ($datapost['outlet[]'] as $outlet_id) {
				$valid = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid) {
					$selectedOutlet[] = $outlet_id;
				}
			}
			if (empty($selectedOutlet)) {
				$this->session->set_flashdata('msg_error', 'Selected Outlet not Found!');
				redirect(site_url('products/product-catalogue'));
				die();
			}


			//ambil data
			$this->db->select('v.*,u.description AS unit_desc');
			$this->db->from('view_catalogue_detail v');
			$this->db->join('unit u', 'v.unit_fkid = u.unit_id', 'left');
			$this->db->where('v.product_adminid', $this->session->userdata('admin_id'));
			$this->db->where('v.data_status', 'on');
			$this->db->where_in('v.outlet_fkid', $selectedOutlet);
			$this->db->order_by('v.product_name', 'asc');
			$result = $this->db->get()->result();

			switch ($datapost['format']) {
				case 'xls':
					$data['export'] = $result;

					// Fungsi header dengan mengirimkan raw data excel
					header("Content-type: application/vnd-ms-excel");
		 
					// Mendefinisikan nama file ekspor "hasil-export.xls"
					header("Content-Disposition: attachment; filename=UNIQ-products.xls");
					$this->load->view('products/products/catalogue/export/export_format_v', $data);
					break;
				
				default:
					$this->session->set_flashdata('msg_error', 'Format Data not Found!');
					redirect(site_url('products/product-catalogue'));
					break;
			}
		}
	}

}

/* End of file Product_export.php */
/* Location: ./application/controllers/products/products/Product_export.php */