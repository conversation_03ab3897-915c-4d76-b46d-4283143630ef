<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Products_submenu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		//$this->user_role->products_products('read_access'); //employee role access page | submenu ingridient
		$this->user_role->products('read_access'); //employee role access page
	}

	public function index()
	{
		//pindahkan halaman
		redirect(site_url('products/products/categories'));
	}

}

/* End of file Products_submenu.php */
/* Location: ./application/controllers/products/products/Products_submenu.php */