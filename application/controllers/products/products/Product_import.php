<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_import extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login
		$this->user_role->products('read_access'); //employee role access page

		$this->load->library('CSVReader');
		$this->load->model('products/type/Type_model');
		$this->load->model('products/products_category/Products_category_model');
		$this->load->model('products/products_subcategory/Products_subcategory_model');
		$this->load->model('products/ingridients/Purchase_report_category_model');
		$this->load->model('products/ingridients/Unit_model');
		$this->load->model('products/gratuity/Gratuity_model');
		$this->load->model('products/products_catalogue/Products_import_model');
		$this->load->model('outlet/Outlets_model');

		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_catalogue_taxgratuity_model');
	}

	public function template()
	{
		//ambil semua data products
		$result = $this->Products_model->get_all_view();

		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=template_import_product-catalogue.csv");
		header("Content-Transfer-Encoding: binary");



		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output,array(
			"Product Name (*max 30char)",
			"Catalogue Type (equipment/product/ingridient)",
			"Product Type (*max 30 char)",
			"Product Category (*max 30 char)",
			"Product Subcategory (*max 30 char)",
			"Purchase Report Category (*max 30 char)",
			"Unit (*max 10 char)",
			"Unit Description (*max 30 char)",
			"Stock Management (y/n)",
			"Barcode (*max 30 char)",
			"SKU (*max 30 char)",
			"Buy Price (*numeric)",
			"Sell Price (*numeric)",
			"Voucher (y/n)",
			"Discount (y/n)",
			"Menu Active (all/sales/link/off)",
			"Active on Outlet (y/n)",
		));
		// fputcsv($handle, $fields, "\t");
		// foreach ($result as $data) {
			fputcsv($output, array(
				'My Product 1', 'equipment', 'My Type 1', 'My Category 1', 'My Sub-Category 1', 'Purchare Report 1', 'kg', 'Sample Unit Kilos', 'Y', 'BC00001', 'SKU00001', 10000, 15000, 'Y', 'N', 'all', 'y'
			));
			fputcsv($output, array(
				'My Product 2', 'product', 'My Type 1', 'My Category 2', 'My Sub-Category 1', 'Purchare Report 1', 'gr', 'Sample Unit Gram', 'N', 'BC00002', 'SKU00002', 1500, 2000, 'Y', 'N', 'sales', 'y'
			));
			fputcsv($output, array(
				'My Product 3', 'ingridient', 'My Type 2', 'My Category 1', 'My Sub-Category 2', 'Purchare Report 2', 'kg', 'Sample Unit Kilos', 'Y', 'BC00003', 'SKU00003', 0, 0, 'Y', 'N', 'link', 'y'
			));
		// }
	}

	public function import($value='')
	{
		# code...
	}

}

/* End of file Product_import.php */
/* Location: ./application/controllers/products/products/Product_import.php */