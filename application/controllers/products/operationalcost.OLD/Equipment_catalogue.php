<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Equipment_catalogue extends CI_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->products_operationalcost_...('read_access'); //employee role access page | sub-submenu thispage
        //$this->user_role->products_operationalcost('read_access'); //employee role access page | submenu opcost
        $this->user_role->products('read_access'); //employee role access page
    
        $this->load->model('products/operationalcost/Operationalcost_equipment_catalogue_model');
        $this->load->model('products/type/Type_model'); //form
        $this->load->model('products/operationalcost/Operationalcost_category_model'); //form
        $this->load->model('products/operationalcost/Operationalcost_subcategory_model'); //form
        $this->load->model('products/ingridients/Purchase_report_category_model'); //form
        $this->load->model('products/ingridients/Unit_model'); //form
        $this->load->model('outlet/Outlets_model'); //form
    }

    public function index()
    {
        $link = site_url('products/operationalcost/equipment_catalogue/'); //URL dengan slash
        $data = array(
            'kolomID' => 'equipment_catalogue_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
            );

        $data['form_select_products_type'] = $this->Type_model->form_select();
        $data['form_select_opcost_category'] = $this->Operationalcost_category_model->form_select();
        $data['form_select_opcost_subcategory'] = $this->Operationalcost_subcategory_model->form_select();
        $data['form_select_purchase_report_category'] = $this->Purchase_report_category_model->form_prc();
        $data['form_select_unit'] = $this->Unit_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        

        $this->load->view('products/operationalcost/equipment_catalogue/equipment_catalogue_v',$data);
    }

	public function json()
    {
        //header('Content-Type: application/json');
        //echo $this->Operationalcost_equipment_catalogue_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Operationalcost_equipment_catalogue_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->equipment_catalogue_id;
        	$name = htmlentities($a->name); //encoding string
        	$outlet_name = htmlentities($a->outlet_name);
        	$products_type_name = htmlentities($a->products_type_name);
        	$opcost_category_name = htmlentities($a->opcost_category_name);
        	$opcost_subcategory_name = htmlentities($a->opcost_subcategory_name);
        	$prc_name = htmlentities($a->prc_name);
        	$price = ($a->price);
            $price_update = ($a->price_update);
        	$unit_name = htmlentities($a->unit_name); 

        	$action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
        	
        	$temp_data = array(
        		'equipment_catalogue_id' => $id,
        		'name' => $name,
        		'outlet_name' => $outlet_name,
        		'products_type_name' => $products_type_name,
        		'opcost_category_name' => $opcost_category_name,
        		'opcost_subcategory_name' => $opcost_subcategory_name,
        		'prc_name' => $prc_name,
        		'price' => $price,
                'price_update' => $price_update,
        		'unit_name' => $unit_name,
        		'action' => $action
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    //datatable by outlet 
    public function json_by_outlet($outlet_id=null)
    {
        header('Content-Type: application/json');
        //echo $this->Operationalcost_equipment_catalogue_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Operationalcost_equipment_catalogue_model->json_by_outlet($outlet_id); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->equipment_catalogue_id;
            $name = htmlentities($a->name); //encoding string
            $outlet_name = htmlentities($a->outlet_name);    
            $unit_fkid = htmlentities($a->unit_fkid);
            $price = ($a->price);
            $price_update = ($a->price_update);
            $unit_name = htmlentities($a->unit_name);
            $active = htmlentities($a->data_status);

            $action  = "<button class='btn btn-primary btn-xs' onclick='actionAddToForm({$id})'>Add</button>";
            
            
            $temp_data = array(
                'equipment_catalogue_id' => $id,
                'name' => $name,
                'outlet_name' => $outlet_name,
                'price' => $price,
                'price_update' => $price_update,
                'unit_name' => $unit_name,
                'unit_fkid' => $unit_fkid,
                'data_status' => $active,
                'action' => $action
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules
        $status_submit = true;

        if ($this->input->post('outlet[]')==null && $this->input->post('outlet')==null) {
            $status_submit = false;
        }


        if ($this->form_validation->run() == FALSE || $status_submit==FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'outlet' => form_error('outlet[]'),
                        'product_type' => form_error('product_type'),
                        'opcost_category' => form_error('opcost_category'),
                        'opcost_subcategory' => form_error('opcost_subcategory'),
                        'purchase_report_category' => form_error('purchase_report_category'),
                        'price' => form_error('price'),
                        'unit' => form_error('unit')
                    )
                );
        } else {
            //data yang dikirim
            $data = array(
                'name' => $this->input->post('name',TRUE),
                //'outlet_fkid' => $this->input->post('outlet[]'),
                //'outlet_fkid' => 2,
                'products_type_fkid' => $this->input->post('product_type', TRUE),
                'opcost_category_fkid' => $this->input->post('opcost_category', TRUE),
                'opcost_subcategory_fkid' => $this->input->post('opcost_subcategory', TRUE),
                'purchase_report_category_fkid' => $this->input->post('purchase_report_category',TRUE),
                //'price' => $this->input->post('price', TRUE),
                'unit_fkid' => $this->input->post('unit', TRUE)
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                /* custom input */
                $outletSubmitted = $this->input->post('outlet[]');
                $data['price'] = $this->input->post('price', TRUE);

                foreach ($outletSubmitted as $a) {
                    if ($a=='all_outlet') {
                        //echo 'ada semua outlet';
                        $dataSemuaOutlet = $this->Outlets_model->form_select();
                        foreach ($dataSemuaOutlet as $b) {
                            $data['outlet_fkid'] = $b->outlet_id;
                            $response = $this->Operationalcost_equipment_catalogue_model->insert($data);
                        }
                    }
                    else{
                        $data['outlet_fkid'] = $a;
                        $response = $this->Operationalcost_equipment_catalogue_model->insert($data);
                    }
                }
                //die();
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                /* custom input */
                $data['outlet_fkid'] = $this->input->post('outlet', TRUE);
                $data['price_update'] = $this->input->post('price', TRUE);

                //action untuk update data
                $response = $this->Operationalcost_equipment_catalogue_model->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
        $row = $this->Operationalcost_equipment_catalogue_model->get_by_id($id);

        if ($row) {
            $data = array(
                    'id' => set_value('equipment_catalogue_id', $row->equipment_catalogue_id),
                    'name' => set_value('name', html_entity_decode($row->name)),
                    'outlet' => set_value('outlet_fkid', $row->outlet_fkid),
                    'product_type' => set_value('product_type', $row->products_type_fkid),
                    'opcost_category' => set_value('opcost_category', $row->opcost_category_fkid),
                    'opcost_subcategory' => set_value('opcost_subcategory', $row->opcost_subcategory_fkid),
                    'purchase_report_category' => set_value('purchase_report_category', $row->purchase_report_category_fkid),
                    'price' => set_value('price', $row->price),
                    'unit' => set_value('unit', $row->unit_fkid)
                );
            $dataArray = array(
                    'status' => 'success',
                    'data' => $data
                );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo json_encode($dataArray);
    }

    public function delete($id=null)
    {
        $row = $this->Operationalcost_equipment_catalogue_model->get_by_id($id);

        if ($row) {
            $response = $this->Operationalcost_equipment_catalogue_model->delete($id);
            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
    $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
    // $this->form_validation->set_rules('outlet[]', 'outlet', 'trim'); //multiple outlet
    $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required'); //all outlet
    $this->form_validation->set_rules('product_type', 'product type', 'trim|required');
    $this->form_validation->set_rules('opcost_category', 'operational cost category', 'trim|required');
    $this->form_validation->set_rules('opcost_subcategory', 'operational cost subcategory', 'trim|required');
    $this->form_validation->set_rules('purchase_report_category', 'purchase report category', 'trim|required');
    $this->form_validation->set_rules('price', 'price', 'trim|required|is_numeric');
    $this->form_validation->set_rules('unit', 'unit', 'trim|required');

    $this->form_validation->set_rules('id', 'equipment_catalogue_id', 'trim');
    $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }



}

/* End of file Equipment_catalogue.php */
/* Location: ./application/controllers/products/operationalcost/Equipment_catalogue.php */