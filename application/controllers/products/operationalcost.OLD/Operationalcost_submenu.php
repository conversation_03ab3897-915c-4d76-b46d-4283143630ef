<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Operationalcost_submenu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		//$this->user_role->products_operationalcost('read_access'); //employee role access page | submenu opcost
		$this->user_role->products('read_access'); //employee role access page
	}

	public function index()
	{
		//pindahkan halaman
		redirect(site_url('products/operationalcost/categories'));
	}

}

/* End of file Operationalcost_submenu.php */
/* Location: ./application/controllers/products/operationalcost/Operationalcost_submenu.php */