<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Catalogue extends Auth_Controller {

	protected $url;
	protected $url_path;
	public $photo_path;
	protected $tmp_upload_path;
	protected $google_storage_path; //path dari google storage
	public $max_upload_size=501; //maksimal upload size

	public function __construct()
	{
		parent::__construct();
		//LOAD MODEL
		$this->load->model('products/products_catalogue/Products_model','Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model','Products_detail_model');
		$this->load->model('products/products_catalogue/Products_catalogue_multipleprice_model', 'Multipleprice_model');
		$this->load->model('products/products_catalogue/Products_catalogue_taxgratuity_model','Taxdetail_model');
		$this->load->model('products/products_catalogue/Products_variant_model','Variant_model');
		$this->load->model('products/type/Type_model'); //form
		$this->load->model('products/products_category/Products_category_model'); //form
		$this->load->model('products/products_subcategory/Products_subcategory_model'); //form
		$this->load->model('products/ingridients/Purchase_report_category_model'); //form
		$this->load->model('products/ingridients/Unit_model'); //form
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('products/gratuity/Gratuity_model'); //form
		// $this->load->model('finance/Accounts_model'); //form

		//LOAD LIBRARY
		$this->load->library('google/Google_storage');
		$this->google_storage_path =  ENVIRONMENT.'/products/'.$this->session->userdata('admin_id').'/';

		//inisialisasi product photo path
		$this->photo_path = 'assets/images/products/' . $this->session->userdata('admin_id').'/';

		//init variable
		$this->url = site_url('products/catalogue/');
		$this->url_path = 'products/catalogue';

		//check
		$this->tmp_upload_path = APPPATH.'cache/uploads';
	}

	public function index()
	{
		$link = current_url().'/';// site_url('products/catalogue/');
		$data = array(
			//
			'max_upload_size' => '500KB',
			//form
			'formselect_type' => $this->Type_model->form_select(),
			'formselect_category' => $this->Products_category_model->form_select(),
			'formselect_subcategory' => $this->Products_subcategory_model->form_select(),
			'formselect_purchasereportcategory' => $this->Purchase_report_category_model->form_prc(),
			// 'formselect_accounts' => $this->Accounts_model->form_select(),
			'formselect_unit' => $this->Unit_model->form_select(),
			'formselect_outlet' => $this->Outlets_model->form_select(),
			'formselect_taxgratuity' => $this->Gratuity_model->form_select(),

			//action
			'ajaxActionExport' => $link.'export_process',
			'ajaxActionImportCSV_product' => $link.'import-product/import_process',
			'ajaxActionImportCSV_linkmenu' => $link.'linkmenu-import/import_process',
			'ajaxActionImportCSV_multipleprice' => $link.'multipleprice-import/import_process',
			'ajaxActionGetMaster' => $link.'get/',

			//link
			'link_multipleprice_template' => $link.'multipleprice-import/template',
			'link_getlist' => site_url('products/linkmenu/get_list/'),
			'link_copylink' => site_url('products/linkmenu/copylink'),
			'link_deletemass' => site_url('products/linkmenu/delete_mass'),

			//feature access / permission access
			'permission' => array(
				'add' => $this->privilege->check(uri_string(),'add'),
				'edit' => $this->privilege->check(uri_string(),'edit'),
				'delete' => $this->privilege->check(uri_string(),'delete'),
			)
		);

		$data['page_title'] = 'Catalogue';
		$this->template->view('products/product-catalogue/list_v2', $data);
	}

	public function datatables()
	{
		// if (empty($this->input->post('advancefilter_outlet'))) {
		// 	$json = $this->Products_model->json();
		// }
		// else{
		// 	$json = $this->Products_model->json_detail();
		// }

		$json = $this->Products_model->datatables();
		$json_decode = json_decode($json);
		$data_array = array();
		foreach ($json_decode->data as $r) {
			//deteksi gambar ada atau tidak
			// $photo_name = $r->photo;
			// $photo_path = $this->photo_path.$photo_name;
			// $photo = (file_exists($photo_path) && !empty($photo_name)) ? base_url($photo_path) : noimage();


			//cek gambar ada atau tidak
			$photo = noimage();
			if (!empty($r->photo)) {
				//-- cek local storage
				$photo_path = $this->photo_path.$r->photo;
				if (file_exists($photo_path) && !empty($r->photo)) {
					$photo = base_url($photo_path);
				}
				//-- cek di google storage
				else{
					if ($this->google_storage->file_exists($r->photo)) {
						$photo = $r->photo;
					}
				}
			}



			//tombol outlet
			/*v1
			$this->db->select('outlet_name,price_sell,variant_name');
			$this->db->where('product_fkid', $r->product_id);
			$this->db->where('product_detail_data_status', 'on');
			$this->db->where('data_status', 'on');
			$this->db->order_by('outlet_name', 'asc');
			$this->db->order_by('variant_name', 'asc');
			$result = $this->db->get('view_catalogue_detail')->result();
			*/
			$this->db->select('o.name AS outlet_name, pd.price_sell, v.variant_name');
			$this->db->from('products_detail pd');
			$this->db->join('outlets o', 'o.outlet_id = pd.outlet_fkid', 'left');
			$this->db->join('products_detail_variant v', 'v.variant_id = pd.variant_fkid', 'left');
			$this->db->where('pd.product_fkid', $r->product_id);
			$this->db->where('pd.data_status', 'on');
			$this->db->where('o.data_status', 'on');
			$this->db->order_by('outlet_name asc, variant_name asc');
			$result = $this->db->get()->result();
			$tombol_outlet_available = '';
			$no=0;
			foreach ($result as $a) {
				$variant = (!empty($a->variant_name)) ? '('.htmlentities($a->variant_name).')' : '';
				$tombol_outlet_available .= "\n";
				$tombol_outlet_available .= htmlentities($a->outlet_name).' '.$variant;
				$tombol_outlet_available .= ': '.formatAngka($a->price_sell); //menampilkan harga di tiap outlet
				$no++;
			}
			$keterangan = $tombol_outlet_available;

			$data_array[] = array(
				'product_id' => $r->product_id,
				'product_name' => htmlentities($r->product_name),
				'catalogue_type' => ucfirst(htmlentities($r->catalogue_type)),
				'product_type_name' => htmlentities($r->product_type_name),
				'product_category_name' => htmlentities($r->product_category_name),
				'product_subcategory_name' => htmlentities($r->product_subcategory_name),
				'prc_name' => htmlentities($r->prc_name),
				'unit_name' => htmlentities($r->unit_name),
				'barcode' => htmlentities($r->barcode),
				'sku' => htmlentities($r->sku),
				'photo' => $photo,
				'keterangan' => $keterangan
			);
		}

		//remake json data
		$draw_json = array(
			'draw' => $json_decode->draw,
			'recordsTotal' => $json_decode->recordsTotal,
			'recordsFiltered' => $json_decode->recordsFiltered,
			'data' => $data_array
		);

		//output
		echo format_json($draw_json);
	}

	public function create_v2()
	{
		//init
		$draw_json = array();
		$valid_input = true;
		$msg_err = array();

		//validasi
		$this->_rules('create');

		//cek type
		$msg_err['type'] = '';
		$row_type = $this->Type_model->get_by_id($this->input->post('type'));
		if (!$row_type) {
			$valid_input = false;
			$msg_err['type'] = 'Invalid Data Type';
		}

		//cek category
		$msg_err['category'] = '';
		$row_category = $this->Products_category_model->get_by_id($this->input->post('category'));
		if (!$row_category) {
			$valid_input = false;
			$msg_err['category'] = 'Invalid Data Category';
		}

		//cek subcategory
		$msg_err['subcategory'] = '';
		$row_subcategory = $this->Products_subcategory_model->get_by_id($this->input->post('subcategory'));
		if (!$row_subcategory) {
			$valid_input = false;
			$msg_err['subcategory'] = 'Invalid Data Sub-Category';
		}

		//cek unit
		$msg_err['unit'] = '';
		$row_unit = $this->Unit_model->get_by_id($this->input->post('unit'));
		if (!$row_unit) {
			$valid_input = false;
			$msg_err['unit'] = 'Invalid Data Unit';
		}

		//cek purchase report category
		$msg_err['purchasereportcategory'] = '';
		$row_purchasereportcategory = $this->Purchase_report_category_model->get_by_id($this->input->post('purchasereportcategory'));
		if (!$row_purchasereportcategory) {
			$valid_input = false;
			$msg_err['purchasereportcategory'] = 'Invalid Data Purchase Report Category';
		}

		//cek outlet
		$msg_err['outlet'] = '';
		$selected_outlet = array();
		if (!empty($this->input->post('outlet[]'))) {
			foreach ($this->input->post('outlet[]') as $outlet_id) {
				$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($row_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}
			if (count($selected_outlet)<=0) {
				$valid_input = false;
				$msg_err['outlet'] = 'Invalid Data Outlet';
			}
		}

		//cek account
		$msg_err['account_purchase'] = '';
		if (!empty($this->input->post('account_purchase'))) {
			$row_account_purchase = $this->Accounts_model->get_by_id($this->input->post('account_purchase'));
			if (!$row_account_purchase) {
				$valid_input = false;
				$msg_err['account_purchase'] = 'Invalid Data Account';
			}
		}
		$msg_err['account_sales'] = '';
		if (!empty($this->input->post('account_sales'))) {
			$row_account_sales = $this->Accounts_model->get_by_id($this->input->post('account_sales'));
			if (!$row_account_sales) {
				$valid_input = false;
				$msg_err['account_sales'] = 'Invalid Data Account';
			}
		}

		//cek barcode
		$msg_err['barcode'] = '';
		if (!empty($this->input->post('barcode'))) {
			$row_barcode = $this->Products_model->get_by_barcode($this->input->post('barcode'));
			if ($row_barcode) {
				$valid_input = false;
				$msg_err['barcode'] = 'Barcode is already in use.';
			}
		}

		//cek taxgratuity
		$selected_taxgratuity = array();
		$datapost['voucher'] = 'off';
		$datapost['discount'] = 'off';
		if (!empty($this->input->post('taxgratuity[]'))) {
			foreach ($this->input->post('taxgratuity[]') as $id) {
				switch ($id) {
					case 'voucher':
						$datapost['voucher'] = 'on';
						break;
					case 'discount':
						$datapost['discount'] = 'on';
						break;
					default:
						$row_taxgratuity = $this->Gratuity_model->get_by_id($id);
						if ($row_taxgratuity) {
							$selected_taxgratuity[] = $id;
						}
						break;
				}
			}
		}

		$msg_err['sku'] = '';
		//cek sku
		if (!empty($this->input->post('sku'))) {
			$row_sku = $this->Products_model->get_by_sku($this->input->post('sku'));
			if ($row_sku) {
				$valid_input = false;
				$msg_err['sku'] = 'SKU is already in use.';
			}
		}

		//cek multipleprice
		if ($this->input->post('variant')==false) {
			//cek multipleprice
			$multipleprice_qty_arr = array();
			$multipleprice_price_arr = array();
			if (($this->input->post('multipleprice_qty[]')) || ($this->input->post('multipleprice_price[]'))) {
				$i=0;
				$valid_mp = array();
				foreach ($this->input->post('multipleprice_qty[]') as $index => $value) {
					$multipleprice_qty_arr[$i] = $this->input->post('multipleprice_qty['.$index.']',TRUE);
					$multipleprice_price_arr[$i] = $this->input->post('multipleprice_price['.$index.']',TRUE);
					$i++;

					//validasi mp tanpa variant
					if (empty($valid_mp[$value])) {
						$valid_mp[$value] = $value;
					}
					else{
						$valid_input = false;
						$msg_err['multipleprice'] = 'Duplicate Multipleprice Qty: '.$value;
					}
				}
			}
		}
		else{
			//validasi variant
			if (!empty($this->input->post('variantlist[]'))) {
				foreach ($this->input->post('variantlist[]') as $key => $value) {
					$this->form_validation->set_rules('variantlist['.$key.'][name]', 'Variant Name', 'trim|required|alpha_numeric_spaces|max_length[50]');
					$this->form_validation->set_rules('variantlist['.$key.'][sku]', 'Variant SKU', 'trim|alpha_numeric|max_length[50]');
					$this->form_validation->set_rules('variantlist['.$key.'][barcode]', 'Variant Barcode', 'trim|alpha_numeric|max_length[50]');

					$this->form_validation->set_rules('variantlist['.$key.'][pb]', 'Variant Price Buy', 'trim|greater_than_equal_to[0]');
					$this->form_validation->set_rules('variantlist['.$key.'][ps]', 'Variant Price Sell', 'trim|greater_than_equal_to[0]');



					//validation multipleprice
					if (!empty($this->input->post('variantlist['.$key.'][multipleprice][]'))) {
						//init
						$variant_mp=array();
						foreach ($this->input->post('variantlist['.$key.'][multipleprice][]') as $key2 => $value2) {
							//formvalidation
							$this->form_validation->set_rules('variantlist['.$key.'][multipleprice]['.$key2.'][qty]', 'multipleprice qty', 'trim|required|greater_than_equal_to[0]');
							$this->form_validation->set_rules('variantlist['.$key.'][multipleprice]['.$key2.'][price]', 'multipleprice price', 'trim|required|greater_than_equal_to[0]');

							//cek duplikat qty
							if (empty($variant_mp[$key][$value2['qty']])) {
								$variant_mp[$key][$value2['qty']] = $value2['qty'];
							}
							else{
								$valid_input = false;
							}
						}
					}
				}
			}




			//validasi untuk variant multipleprice
			if ($this->input->post('variant_multipleprice_qty[]') || $this->input->post('variant_multipleprice_price[]')) {
				//buat validasi
				foreach ($this->input->post('variant_multipleprice_qty[]') as $index => $qtyprice) {
					foreach ($qtyprice as $index2 => $value) {
						$this->form_validation->set_rules('variant_multipleprice_qty['.$index.']['.$index2.']', 'qty variant multipleprice', 'trim|required|greater_than_equal_to[0]');
						$this->form_validation->set_rules('variant_multipleprice_price['.$index.']['.$index2.']', 'price variant multipleprice', 'trim|required|greater_than_equal_to[0]');
					}
				}
			}
		}
		
		//photo
		$this->form_validation->set_rules('photo', 'photo', 'callback_validate_photo');


		if ($this->form_validation->run() == FALSE || $valid_input == FALSE) {
			/* set error multipleprice (no variant) */
			$msg_err['multipleprice2'] = (!empty($msg_err['multipleprice'])) ? $msg_err['multipleprice'] : '';
			$msg_err['multipleprice'] = (!empty(form_error('multipleprice_qty[]'))) ? form_error('multipleprice_qty[]') : form_error('multipleprice_price[]');
			$msg_err['multipleprice'] = (!empty($msg_err['multipleprice'])) ? $msg_err['multipleprice'] : $msg_err['multipleprice2'];

			

			$msg_err['price_sell'] = (!empty($msg_err['multipleprice'])) ? $msg_err['multipleprice'] : form_error('price_sell');
			$msg_err['transfermarkup'] = (!empty(form_error('transfermarkup_type'))) ? form_error('transfermarkup_type') : form_error('transfermarkup');

			$err_commission_staff = (!empty(form_error('commission_staff_type'))) ? form_error('commission_staff_type') : form_error('commission_staff');
			$err_commission_customer = (!empty(form_error('commission_customer_type'))) ? form_error('commission_customer_type') : form_error('commission_customer');
			$msg_err['commission'] = (!empty($err_commission_staff)) ? $err_commission_staff : $err_commission_customer;
			

			//output format
			$draw_json = array(
				'status' => 'error',
				'message' => 'Create Record Failed',
				'error_data' => array(
					'catalogue_type' => form_error('catalogue_type'),
					'name' => form_error('name'),
					'type' => (!empty(form_error('type'))) ? form_error('type') : $msg_err['type'],
					'category' => (!empty(form_error('category'))) ? form_error('category') : $msg_err['category'],
					'subcategory' => (!empty(form_error('subcategory'))) ? form_error('subcategory') : $msg_err['subcategory'],
					'unit' => (!empty(form_error('unit'))) ? form_error('unit') : $msg_err['unit'],
					'purchasereportcategory' => (!empty(form_error('purchasereportcategory'))) ? form_error('purchasereportcategory') : $msg_err['purchasereportcategory'],
					'outlet' => (!empty(form_error('outlet[]'))) ? form_error('outlet[]') : $msg_err['outlet'],
					'stockmanagement' => form_error('stockmanagement'),
					'account_purchase' => (!empty(form_error('account_purchase'))) ? form_error('account_purchase') : $msg_err['account_purchase'],
					'account_sales' => (!empty(form_error('account_sales'))) ? form_error('account_sales') : $msg_err['account_sales'],
					'barcode' => (!empty(form_error('barcode'))) ? form_error('barcode') : $msg_err['barcode'],
					'sku' => (!empty(form_error('sku'))) ? form_error('sku') : $msg_err['sku'],
					'taxgratuity' => form_error('taxgratuity[]'),
					'transfermarkup' => $msg_err['transfermarkup'],
					'commission' => $msg_err['commission'],
					'menu_active' => form_error('menu_active'),
					'price_buy' => form_error('price_buy'),
					'price_sell' => $msg_err['price_sell'],
					'variant' => form_error('variant'),
					'variantlist' => array(),
					'photo' => form_error('photo')
				)
			);

			//variantlist error
			if ($this->input->post('variant')==true && !empty($this->input->post('variantlist[]'))) {
				foreach ($this->input->post('variantlist[]') as $key => $value) {
					$draw_json['error_data']['variantlist'][$key] = array(
						'name' => form_error('variantlist['.$key.'][name]'),
						'sku' => form_error('variantlist['.$key.'][sku]'),
						'barcode' => form_error('variantlist['.$key.'][barcode]'),
						'price_buy' => form_error('variantlist['.$key.'][pb]'),
						'price_sell' => form_error('variantlist['.$key.'][ps]'),
						'multipleprice' => array()
					);


					//multipleprice
					if (!empty($this->input->post('variantlist['.$key.'][multipleprice][]'))) {
						$variant_mp=array();
						$variant_mp_valid = true;
						foreach ($this->input->post('variantlist['.$key.'][multipleprice][]') as $key2 => $value2) {
							$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['qty'] = form_error('variantlist['.$key.'][multipleprice]['.$key2.'][qty]');
							$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['price'] = form_error('variantlist['.$key.'][multipleprice]['.$key2.'][price]');


							//cek duplikat qty
							if (empty($variant_mp[$key][$value2['qty']])) {
								$variant_mp[$key][$value2['qty']] = $value2['qty'];
							}
							else{
								$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['qty'] = 'Duplicate Multipleprice Qty: '.$value2['qty'];
							}
						}
					}
					else{
						$draw_json['error_data']['variantlist'][$key]['multipleprice'] = array();
					}
				}
			}
		} else {
			//DATA INSERT
			$data_insert = array(
				'name' => $this->input->post('name',TRUE),
				'catalogue_type' => $this->input->post('catalogue_type',TRUE),
				'product_type_fkid' => $this->input->post('type',TRUE),
				'product_category_fkid' => $this->input->post('category',TRUE),
				'product_subcategory_fkid' => $this->input->post('subcategory',TRUE),
				'purchase_report_category_fkid' => $this->input->post('purchasereportcategory',TRUE),
				'sku' => (!empty($this->input->post('sku'))) ? strtoupper($this->input->post('sku',TRUE)) : null,
				'account_fkid_purchase' => $this->input->post('account_purchase'),
				'account_fkid_sales' => $this->input->post('account_sales'),
				'unit_fkid' => $this->input->post('unit',TRUE),
				'stock_management' => (!empty($this->input->post('stockmanagement'))) ? TRUE : FALSE,
				'barcode' => (!empty($this->input->post('barcode'))) ? strtoupper($this->input->post('barcode',TRUE)) : null,
				// 'photo' => $photo_name,
			);

			//insert data master
			$this->db->trans_start();
			$response = $this->Products_model->insert($data_insert);
			if ($response) {
				$product_id = $this->db->insert_id();

				//insert detail
				foreach ($selected_outlet as $outlet_id) {
					//init
					$variantlist = ($this->input->post('variant')==true) ? $this->input->post('variantlist[]') : [1];

					foreach ($variantlist as $index => $variant_name) {
						//re-init
						$data_insert_detail = array();

						if ($this->input->post('variant')==true) {
							//data have variant
							$variant_id = '';
							$variant_name = $this->input->post('variantlist['.$index.'][name]');
							$cek_variantname = $this->Variant_model->get_by_variantname_product($variant_name, $product_id);
							if ($cek_variantname) {
								//variant sudah ada
								$variant_id = $cek_variantname->variant_id;
							}
							else{
								//insert variant baru
								$data_variant = array(
									'variant_name' => $this->input->post('variantlist['.$index.'][name]',TRUE),
									'variant_sku' => strtoupper($this->input->post('variantlist['.$index.'][sku]',TRUE)),
									'variant_barcode' => strtoupper($this->input->post('variantlist['.$index.'][barcode]',TRUE)),
									'product_fkid' => $product_id,
								);
								$response_variant = $this->Variant_model->insert($data_variant);
								if ($response_variant) {
									$variant_id = $this->db->insert_id();
								}
							}

							if (!empty($variant_id)) {
								//insert variant
								$data_insert_detail = array(
									'product_fkid' => $product_id,
									'outlet_fkid' => $outlet_id,
									'price_buy_start' => $this->input->post('variantlist['.$index.'][pb]'),
									'price_buy' => $this->input->post('variantlist['.$index.'][pb]'),
									'price_sell' => $this->input->post('variantlist['.$index.'][ps]'),
									'voucher' => $datapost['voucher'],
									'discount' => $datapost['discount'],
									'transfer_markup_type' => $this->input->post('transfermarkup_type',TRUE),
									'transfer_markup' => $this->input->post('transfermarkup',TRUE),
									'commission_staff_type' => $this->input->post('commission_staff_type',true),
									'commission_staff' => $this->input->post('commission_staff',true),
									'commission_customer_type' => $this->input->post('commission_customer_type',true),
									'commission_customer' => $this->input->post('commission_customer',true),
									'active' => $this->input->post('menu_active',TRUE),
									'variant_fkid' => $variant_id
								);
							}
						}
						else{
							//data no-variant
							$data_insert_detail = array(
								'product_fkid' => $product_id,
								'outlet_fkid' => $outlet_id, //foreach by outlet
								'price_buy_start' => $this->input->post('price_buy', TRUE),
								'price_buy' => $this->input->post('price_buy', TRUE),
								'price_sell' => $this->input->post('price_sell',TRUE),
								'voucher' => $datapost['voucher'],
								'discount' => $datapost['discount'],
								'transfer_markup_type' => $this->input->post('transfermarkup_type',TRUE),
								'transfer_markup' => $this->input->post('transfermarkup',TRUE),
								'commission_staff_type' => $this->input->post('commission_staff_type',true),
								'commission_staff' => $this->input->post('commission_staff',true),
								'commission_customer_type' => $this->input->post('commission_customer_type',true),
								'commission_customer' => $this->input->post('commission_customer',true),
								'active' => $this->input->post('menu_active',TRUE),
							);
						}

						//insert products_detail
						if (!empty($data_insert_detail)) {
							$response_detail = $this->Products_detail_model->insert($data_insert_detail);
							if ($response_detail) {
								$product_detail_id = $this->db->insert_id();

								// multipleprice START
								if ($this->input->post('variant')==false) {
									//insert multiple price START
									if (!empty($this->input->post('multipleprice_qty[]')) || $this->input->post('multipleprice_price[]')) {
										foreach ($this->input->post('multipleprice_qty[]') as $index => $value) {
											$your_qty = $this->input->post('multipleprice_qty['.$index.']');
											$your_price = $this->input->get_post('multipleprice_price['.$index.']');

											$dataMultiplePrice = array(
												'product_detail_fkid' => $product_detail_id, //primary key setelah input product_detail
												'qty' => $your_qty,
												'price' => $your_price
											);
											$inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePrice);
										}
									}
								}
								else{
									//multipleprice variant
									if (!empty($this->input->post('variantlist['.$index.'][multipleprice]'))) {
										foreach ($this->input->post('variantlist['.$index.'][multipleprice]') as $index2 => $value) {
											$your_qty = $this->input->post('variantlist['.$index.'][multipleprice]['.$index2.'][qty]');
											$your_price = $this->input->post('variantlist['.$index.'][multipleprice]['.$index2.'][price]');
											$dataMultiplePriceVariant = array(
												'product_detail_fkid' => $product_detail_id,
												'qty' => $your_qty,
												'price' => $your_price
											);
											$inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePriceVariant);
										}
									}
								}
								// multipleprice END
								

								//insert tax gratuity START
								if (!empty($selected_taxgratuity)) {
									foreach ($selected_taxgratuity as $index => $value) {
										$dataTax = array(
											'tax_fkid' => $value,
											'product_detail_fkid' => $product_detail_id, //primary key setelah input product_detail
										);
										$insert_taxgratuity = $this->Taxdetail_model->insert_taxgratuity($dataTax);
									}
								}
								//insert tax gratuity END
							}//endif: response_detail
						}//endif: if !empty($data_insert_detail);
					}//endforeach: variantlist
				}//endforeach: looping insert by outlet

				
				//UPLOAD GAMBAR product (update DB dengan url gambar)
				$path = $this->google_storage_path;
				$version = date('YmdHis');

				$data_upload = [
					'photo' => $this->google_storage->upload('photo', $path, $product_id.'_'.$version)
				];
				$this->Products_model->update($product_id, $data_upload);
			}
			$r = $this->db->trans_complete();
			if ($r) {
				//draw json
				$draw_json = array(
					'status' => 'success',
					'message' => 'Create Record Success'
				);
			}else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Create Record Failed'
				);
			}
		}

		//output
		echo format_json($draw_json);
	}

	public function create_v1()
	{
		//init
		$draw_json = array();
		$valid_input = true;
		$msg_err = array();

		//validasi
		$this->_rules('create');

		//cek type
		$msg_err['type'] = '';
		$row_type = $this->Type_model->get_by_id($this->input->post('type'));
		if (!$row_type) {
			$valid_input = false;
			$msg_err['type'] = 'Invalid Data Type';
		}

		//cek category
		$msg_err['category'] = '';
		$row_category = $this->Products_category_model->get_by_id($this->input->post('category'));
		if (!$row_category) {
			$valid_input = false;
			$msg_err['category'] = 'Invalid Data Category';
		}

		//cek subcategory
		$msg_err['subcategory'] = '';
		$row_subcategory = $this->Products_subcategory_model->get_by_id($this->input->post('subcategory'));
		if (!$row_subcategory) {
			$valid_input = false;
			$msg_err['subcategory'] = 'Invalid Data Sub-Category';
		}

		//cek unit
		$msg_err['unit'] = '';
		$row_unit = $this->Unit_model->get_by_id($this->input->post('unit'));
		if (!$row_unit) {
			$valid_input = false;
			$msg_err['unit'] = 'Invalid Data Unit';
		}

		//cek purchase report category
		$msg_err['purchasereportcategory'] = '';
		$row_purchasereportcategory = $this->Purchase_report_category_model->get_by_id($this->input->post('purchasereportcategory'));
		if (!$row_purchasereportcategory) {
			$valid_input = false;
			$msg_err['purchasereportcategory'] = 'Invalid Data Purchase Report Category';
		}

		//cek outlet
		$msg_err['outlet'] = '';
		$selected_outlet = array();
		if (!empty($this->input->post('outlet[]'))) {
			foreach ($this->input->post('outlet[]') as $outlet_id) {
				$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($row_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}
			if (count($selected_outlet)<=0) {
				$valid_input = false;
				$msg_err['outlet'] = 'Invalid Data Outlet';
			}
		}

		//cek account
		$msg_err['account_purchase'] = '';
		if (!empty($this->input->post('account_purchase'))) {
			$row_account_purchase = $this->Accounts_model->get_by_id($this->input->post('account_purchase'));
			if (!$row_account_purchase) {
				$valid_input = false;
				$msg_err['account_purchase'] = 'Invalid Data Account';
			}
		}
		$msg_err['account_sales'] = '';
		if (!empty($this->input->post('account_sales'))) {
			$row_account_sales = $this->Accounts_model->get_by_id($this->input->post('account_sales'));
			if (!$row_account_sales) {
				$valid_input = false;
				$msg_err['account_sales'] = 'Invalid Data Account';
			}
		}

		//cek barcode
		$msg_err['barcode'] = '';
		if (!empty($this->input->post('barcode'))) {
			$row_barcode = $this->Products_model->get_by_barcode($this->input->post('barcode'));
			if ($row_barcode) {
				$valid_input = false;
				$msg_err['barcode'] = 'Barcode is already in use.';
			}
		}

		//cek taxgratuity
		$selected_taxgratuity = array();
		$datapost['voucher'] = 'off';
		$datapost['discount'] = 'off';
		if (!empty($this->input->post('taxgratuity[]'))) {
			foreach ($this->input->post('taxgratuity[]') as $id) {
				switch ($id) {
					case 'voucher':
						$datapost['voucher'] = 'on';
						break;
					case 'discount':
						$datapost['discount'] = 'on';
						break;
					default:
						$row_taxgratuity = $this->Gratuity_model->get_by_id($id);
						if ($row_taxgratuity) {
							$selected_taxgratuity[] = $id;
						}
						break;
				}
			}
		}

		$msg_err['sku'] = '';
		//cek sku
		if (!empty($this->input->post('sku'))) {
			$row_sku = $this->Products_model->get_by_sku($this->input->post('sku'));
			if ($row_sku) {
				$valid_input = false;
				$msg_err['sku'] = 'SKU is already in use.';
			}
		}

		//cek multipleprice
		if ($this->input->post('variant')==false) {
			//cek multipleprice
			$multipleprice_qty_arr = array();
			$multipleprice_price_arr = array();
			if (($this->input->post('multipleprice_qty[]')) || ($this->input->post('multipleprice_price[]'))) {
				$i=0;
				$valid_mp = array();
				foreach ($this->input->post('multipleprice_qty[]') as $index => $value) {
					$multipleprice_qty_arr[$i] = $this->input->post('multipleprice_qty['.$index.']',TRUE);
					$multipleprice_price_arr[$i] = $this->input->post('multipleprice_price['.$index.']',TRUE);
					$i++;

					//validasi mp tanpa variant
					if (empty($valid_mp[$value])) {
						$valid_mp[$value] = $value;
					}
					else{
						$valid_input = false;
						$msg_err['multipleprice'] = 'Duplicate Multipleprice Qty: '.$value;
					}
				}
			}
		}
		else{
			//validasi variant
			if (!empty($this->input->post('variantlist[]'))) {
				foreach ($this->input->post('variantlist[]') as $key => $value) {
					$this->form_validation->set_rules('variantlist['.$key.'][name]', 'Variant Name', 'trim|required|alpha_numeric_spaces|max_length[50]');
					$this->form_validation->set_rules('variantlist['.$key.'][sku]', 'Variant SKU', 'trim|alpha_numeric|max_length[50]');
					$this->form_validation->set_rules('variantlist['.$key.'][barcode]', 'Variant Barcode', 'trim|alpha_numeric|max_length[50]');

					$this->form_validation->set_rules('variantlist['.$key.'][pb]', 'Variant Price Buy', 'trim|greater_than_equal_to[0]');
					$this->form_validation->set_rules('variantlist['.$key.'][ps]', 'Variant Price Sell', 'trim|greater_than_equal_to[0]');



					//validation multipleprice
					if (!empty($this->input->post('variantlist['.$key.'][multipleprice][]'))) {
						//init
						$variant_mp=array();
						foreach ($this->input->post('variantlist['.$key.'][multipleprice][]') as $key2 => $value2) {
							//formvalidation
							$this->form_validation->set_rules('variantlist['.$key.'][multipleprice]['.$key2.'][qty]', 'multipleprice qty', 'trim|required|greater_than_equal_to[0]');
							$this->form_validation->set_rules('variantlist['.$key.'][multipleprice]['.$key2.'][price]', 'multipleprice price', 'trim|required|greater_than_equal_to[0]');

							//cek duplikat qty
							if (empty($variant_mp[$key][$value2['qty']])) {
								$variant_mp[$key][$value2['qty']] = $value2['qty'];
							}
							else{
								$valid_input = false;
							}
						}
					}
				}
			}




			//validasi untuk variant multipleprice
			if ($this->input->post('variant_multipleprice_qty[]') || $this->input->post('variant_multipleprice_price[]')) {
				//buat validasi
				foreach ($this->input->post('variant_multipleprice_qty[]') as $index => $qtyprice) {
					foreach ($qtyprice as $index2 => $value) {
						$this->form_validation->set_rules('variant_multipleprice_qty['.$index.']['.$index2.']', 'qty variant multipleprice', 'trim|required|greater_than_equal_to[0]');
						$this->form_validation->set_rules('variant_multipleprice_price['.$index.']['.$index2.']', 'price variant multipleprice', 'trim|required|greater_than_equal_to[0]');
					}
				}
			}
		}
		

		
		/* PROSES OLAH GAMBAR START */
		$photo = $this->_upload_photo(); // PROSES OLAH GAMBAR
		$photo_status = $photo['status_upload'];    // upload foto berhasil atau tidak
		$photo_available = $photo['available'];     // submit form memiliki gambar atau tidak
		$error_imageupload = $photo['error_msg'];   // pesan error saat upload foto
		$photo_path = $photo['path'];               // path direktori foto
		$photo_name = $photo['name'];               // nama foto yang diupload
		/* PROSES OLAH GAMBAR END   */


		if ($this->form_validation->run() == FALSE || $valid_input == FALSE || ($photo_available==true && $photo_status==false)) {
			/* set error multipleprice (no variant) */
			$msg_err['multipleprice2'] = (!empty($msg_err['multipleprice'])) ? $msg_err['multipleprice'] : '';
			$msg_err['multipleprice'] = (!empty(form_error('multipleprice_qty[]'))) ? form_error('multipleprice_qty[]') : form_error('multipleprice_price[]');
			$msg_err['multipleprice'] = (!empty($msg_err['multipleprice'])) ? $msg_err['multipleprice'] : $msg_err['multipleprice2'];

			

			$msg_err['price_sell'] = (!empty($msg_err['multipleprice'])) ? $msg_err['multipleprice'] : form_error('price_sell');
			$msg_err['transfermarkup'] = (!empty(form_error('transfermarkup_type'))) ? form_error('transfermarkup_type') : form_error('transfermarkup');

			$err_commission_staff = (!empty(form_error('commission_staff_type'))) ? form_error('commission_staff_type') : form_error('commission_staff');
			$err_commission_customer = (!empty(form_error('commission_customer_type'))) ? form_error('commission_customer_type') : form_error('commission_customer');
			$msg_err['commission'] = (!empty($err_commission_staff)) ? $err_commission_staff : $err_commission_customer;
			

			//hapus gambar yang diupload bila validasi salah
			if ($photo_status===true) {
				$path = $photo_path.$photo_name;
				unlink($path); //hapus gambar yang baru saja diupload saat input gagal
			}

			//output format
			$draw_json = array(
				'status' => 'error',
				'message' => 'Create Record Failed',
				'error_data' => array(
					'catalogue_type' => form_error('catalogue_type'),
					'name' => form_error('name'),
					'type' => (!empty(form_error('type'))) ? form_error('type') : $msg_err['type'],
					'category' => (!empty(form_error('category'))) ? form_error('category') : $msg_err['category'],
					'subcategory' => (!empty(form_error('subcategory'))) ? form_error('subcategory') : $msg_err['subcategory'],
					'unit' => (!empty(form_error('unit'))) ? form_error('unit') : $msg_err['unit'],
					'purchasereportcategory' => (!empty(form_error('purchasereportcategory'))) ? form_error('purchasereportcategory') : $msg_err['purchasereportcategory'],
					'outlet' => (!empty(form_error('outlet[]'))) ? form_error('outlet[]') : $msg_err['outlet'],
					'stockmanagement' => form_error('stockmanagement'),
					'account_purchase' => (!empty(form_error('account_purchase'))) ? form_error('account_purchase') : $msg_err['account_purchase'],
					'account_sales' => (!empty(form_error('account_sales'))) ? form_error('account_sales') : $msg_err['account_sales'],
					'barcode' => (!empty(form_error('barcode'))) ? form_error('barcode') : $msg_err['barcode'],
					'sku' => (!empty(form_error('sku'))) ? form_error('sku') : $msg_err['sku'],
					'taxgratuity' => form_error('taxgratuity[]'),
					'transfermarkup' => $msg_err['transfermarkup'],
					'commission' => $msg_err['commission'],
					'menu_active' => form_error('menu_active'),
					'price_buy' => form_error('price_buy'),
					'price_sell' => $msg_err['price_sell'],
					'variant' => form_error('variant'),
					'variantlist' => array(),
					'photo' => $error_imageupload
				)
			);

			//variantlist error
			if ($this->input->post('variant')==true && !empty($this->input->post('variantlist[]'))) {
				foreach ($this->input->post('variantlist[]') as $key => $value) {
					$draw_json['error_data']['variantlist'][$key] = array(
						'name' => form_error('variantlist['.$key.'][name]'),
						'sku' => form_error('variantlist['.$key.'][sku]'),
						'barcode' => form_error('variantlist['.$key.'][barcode]'),
						'price_buy' => form_error('variantlist['.$key.'][pb]'),
						'price_sell' => form_error('variantlist['.$key.'][ps]'),
						'multipleprice' => array()
					);


					//multipleprice
					if (!empty($this->input->post('variantlist['.$key.'][multipleprice][]'))) {
						$variant_mp=array();
						$variant_mp_valid = true;
						foreach ($this->input->post('variantlist['.$key.'][multipleprice][]') as $key2 => $value2) {
							$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['qty'] = form_error('variantlist['.$key.'][multipleprice]['.$key2.'][qty]');
							$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['price'] = form_error('variantlist['.$key.'][multipleprice]['.$key2.'][price]');


							//cek duplikat qty
							if (empty($variant_mp[$key][$value2['qty']])) {
								$variant_mp[$key][$value2['qty']] = $value2['qty'];
							}
							else{
								$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['qty'] = 'Duplicate Multipleprice Qty: '.$value2['qty'];
							}
						}
					}
					else{
						$draw_json['error_data']['variantlist'][$key]['multipleprice'] = array();
					}
				}
			}
		} else {
			//DATA INSERT
			$data_insert = array(
				'name' => $this->input->post('name',TRUE),
				'catalogue_type' => $this->input->post('catalogue_type',TRUE),
				'product_type_fkid' => $this->input->post('type',TRUE),
				'product_category_fkid' => $this->input->post('category',TRUE),
				'product_subcategory_fkid' => $this->input->post('subcategory',TRUE),
				'purchase_report_category_fkid' => $this->input->post('purchasereportcategory',TRUE),
				'sku' => (!empty($this->input->post('sku'))) ? strtoupper($this->input->post('sku',TRUE)) : null,
				'account_fkid_purchase' => $this->input->post('account_purchase'),
				'account_fkid_sales' => $this->input->post('account_sales'),
				'unit_fkid' => $this->input->post('unit',TRUE),
				'stock_management' => (!empty($this->input->post('stockmanagement'))) ? TRUE : FALSE,
				'barcode' => (!empty($this->input->post('barcode'))) ? strtoupper($this->input->post('barcode',TRUE)) : null,
				'photo' => $photo_name,
			);

			//insert data master
			$response = $this->Products_model->insert($data_insert);
			if ($response) {
				$product_id = $this->db->insert_id();

				//insert detail
				foreach ($selected_outlet as $outlet_id) {
					//init
					$variantlist = ($this->input->post('variant')==true) ? $this->input->post('variantlist[]') : [1];

					foreach ($variantlist as $index => $variant_name) {
						//re-init
						$data_insert_detail = array();

						if ($this->input->post('variant')==true) {
							//data have variant
							$variant_id = '';
							$variant_name = $this->input->post('variantlist['.$index.'][name]');
							$cek_variantname = $this->Variant_model->get_by_variantname_product($variant_name, $product_id);
							if ($cek_variantname) {
								//variant sudah ada
								$variant_id = $cek_variantname->variant_id;
							}
							else{
								//insert variant baru
								$data_variant = array(
									'variant_name' => $this->input->post('variantlist['.$index.'][name]',TRUE),
									'variant_sku' => strtoupper($this->input->post('variantlist['.$index.'][sku]',TRUE)),
									'variant_barcode' => strtoupper($this->input->post('variantlist['.$index.'][barcode]',TRUE)),
									'product_fkid' => $product_id,
								);
								$response_variant = $this->Variant_model->insert($data_variant);
								if ($response_variant) {
									$variant_id = $this->db->insert_id();
								}
							}

							if (!empty($variant_id)) {
								//insert variant
								$data_insert_detail = array(
									'product_fkid' => $product_id,
									'outlet_fkid' => $outlet_id,
									'price_buy_start' => $this->input->post('variantlist['.$index.'][pb]'),
									'price_buy' => $this->input->post('variantlist['.$index.'][pb]'),
									'price_sell' => $this->input->post('variantlist['.$index.'][ps]'),
									'voucher' => $datapost['voucher'],
									'discount' => $datapost['discount'],
									'transfer_markup_type' => $this->input->post('transfermarkup_type',TRUE),
									'transfer_markup' => $this->input->post('transfermarkup',TRUE),
									'commission_staff_type' => $this->input->post('commission_staff_type',true),
									'commission_staff' => $this->input->post('commission_staff',true),
									'commission_customer_type' => $this->input->post('commission_customer_type',true),
									'commission_customer' => $this->input->post('commission_customer',true),
									'active' => $this->input->post('menu_active',TRUE),
									'variant_fkid' => $variant_id
								);
							}
						}
						else{
							//data no-variant
							$data_insert_detail = array(
								'product_fkid' => $product_id,
								'outlet_fkid' => $outlet_id, //foreach by outlet
								'price_buy_start' => $this->input->post('price_buy', TRUE),
								'price_buy' => $this->input->post('price_buy', TRUE),
								'price_sell' => $this->input->post('price_sell',TRUE),
								'voucher' => $datapost['voucher'],
								'discount' => $datapost['discount'],
								'transfer_markup_type' => $this->input->post('transfermarkup_type',TRUE),
								'transfer_markup' => $this->input->post('transfermarkup',TRUE),
								'commission_staff_type' => $this->input->post('commission_staff_type',true),
								'commission_staff' => $this->input->post('commission_staff',true),
								'commission_customer_type' => $this->input->post('commission_customer_type',true),
								'commission_customer' => $this->input->post('commission_customer',true),
								'active' => $this->input->post('menu_active',TRUE),
							);
						}

						//insert products_detail
						if (!empty($data_insert_detail)) {
							$response_detail = $this->Products_detail_model->insert($data_insert_detail);
							if ($response_detail) {
								$product_detail_id = $this->db->insert_id();

								// multipleprice START
								if ($this->input->post('variant')==false) {
									//insert multiple price START
									if (!empty($this->input->post('multipleprice_qty[]')) || $this->input->post('multipleprice_price[]')) {
										foreach ($this->input->post('multipleprice_qty[]') as $index => $value) {
											$your_qty = $this->input->post('multipleprice_qty['.$index.']');
											$your_price = $this->input->get_post('multipleprice_price['.$index.']');

											$dataMultiplePrice = array(
												'product_detail_fkid' => $product_detail_id, //primary key setelah input product_detail
												'qty' => $your_qty,
												'price' => $your_price
											);
											$inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePrice);
										}
									}
								}
								else{
									//multipleprice variant
									if (!empty($this->input->post('variantlist['.$index.'][multipleprice]'))) {
										foreach ($this->input->post('variantlist['.$index.'][multipleprice]') as $index2 => $value) {
											$your_qty = $this->input->post('variantlist['.$index.'][multipleprice]['.$index2.'][qty]');
											$your_price = $this->input->post('variantlist['.$index.'][multipleprice]['.$index2.'][price]');
											$dataMultiplePriceVariant = array(
												'product_detail_fkid' => $product_detail_id,
												'qty' => $your_qty,
												'price' => $your_price
											);
											$inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePriceVariant);
										}
									}
								}
								// multipleprice END
								

								//insert tax gratuity START
								if (!empty($selected_taxgratuity)) {
									foreach ($selected_taxgratuity as $index => $value) {
										$dataTax = array(
											'tax_fkid' => $value,
											'product_detail_fkid' => $product_detail_id, //primary key setelah input product_detail
										);
										$insert_taxgratuity = $this->Taxdetail_model->insert_taxgratuity($dataTax);
									}
								}
								//insert tax gratuity END
							}//endif: response_detail
						}//endif: if !empty($data_insert_detail);
					}//endforeach: variantlist
				}//endforeach: looping insert by outlet

				//draw json
				$draw_json = array(
					'status' => 'success',
					'message' => 'Create Record Success'
				);
			}
		}

		//output
		echo format_json($draw_json);
	}

	public function get($product_id=null)
	{
		//init
		$draw_json = array();
		$valid_input = true;

		//cek data
		$row = $this->Products_model->get_by_id($product_id);
		if ($row) {

			//cek gambar ada atau tidak
			$photo = noimage();
			if (!empty($row->photo)) {
				//-- cek local storage
				$photo_path = $this->photo_path.$row->photo;
				if (file_exists($photo_path) && !empty($row->photo)) {
					$photo = base_url($photo_path);
				}
				//-- cek di google storage
				else{
					if ($this->google_storage->file_exists($row->photo)) {
						$photo = $row->photo;
					}
				}
			}



			//get available outlet
			$outlet = array();
			$outlet_novariant = false;
			//v1
			// $result_outlet = $this->Products_detail_model->get_by_product_id($product_id);
			//v2
			$this->db->select('
				pd.product_detail_id,
				pd.outlet_fkid,
				o.name AS outlet_name,
				pd.variant_fkid,
				v.variant_name,
				p.data_status
			');
			$this->db->from('products_detail pd');
			$this->db->join('outlets o', 'pd.outlet_fkid = o.outlet_id', 'left');
			$this->db->join('products_detail_variant v', 'pd.variant_fkid = v.variant_id', 'left');
			$this->db->join('products p', 'pd.product_fkid = p.product_id', 'left');
			$this->db->where('o.data_status', 'on');
			$this->db->where('p.data_status', 'on');
			$this->db->where('pd.data_status', 'on');
			$this->db->where('pd.product_fkid', $product_id);
			if ($this->session->userdata('user_type')=='employee') {
				$this->db->where_in('outlet_fkid', $this->session->userdata('outlet_access'));
			}
			$result_outlet = $this->db->get()->result();
			foreach ($result_outlet as $r) {
				$outlet[] = array(
					'id' => $r->product_detail_id,
					'outlet_id' => $r->outlet_fkid,
					'outlet_name' => htmlentities($r->outlet_name),
					'variant_id' => $r->variant_fkid,
					'variant_name' => htmlentities($r->variant_name),
				);

				if ($r->variant_fkid==null && $r->data_status=='on') {
					$outlet_novariant = true;
				}
			}

			//get product variant
			$variant = array();
			// $result_variant = $this->Variant_model->get_by_product($product_id);
			$result_variant = $this->Variant_model->get_by_active($product_id);
			foreach ($result_variant as $r) {
				if ($r->variant_id==null) {
					$variant[] = array(
						'variant_id' => null,
						'variant_name' => htmlentities('*No Variant')
					);
				}else{
					$variant[] = array(
						'variant_id' => $r->variant_id,
						'variant_name' => htmlentities($r->variant_name),
					);
				}
			}
			// if ($outlet_novariant==true) {
			// 	$variant[] = array(
			// 		'variant_id' => null,
			// 		'variant_name' => htmlentities('*No Variant')
			// 	);
			// }
			
			$draw_json = array(
				'status' => 'success',
				'message' => '',
				'data' => array(
					'product_id' => $row->product_id,
					'product_name' => htmlentities($row->product_name),
					'catalogue_type' => $row->catalogue_type,
					'product_type_id' => $row->product_type_fkid,
					'product_category_id' => $row->product_category_fkid,
					'product_subcategory_id' => $row->product_subcategory_fkid,
					'unit_id' => $row->unit_fkid,
					'purchase_report_category_id' => $row->purchase_report_category_fkid,
					'stockmanagement' => $row->stock_management,
					'account_purchase' => (!empty($row->account_fkid_purchase)) ? $row->account_fkid_purchase : '',
					'account_sales' => (!empty($row->account_fkid_sales)) ? $row->account_fkid_sales : '',
					'sku' => $row->sku,
					'barcode' => $row->barcode,
					'photo' => $photo,
					'outlet' => $outlet,
					'variant' => (empty($variant)) ? false : true,
					'variant_list' => $variant
				)
			);
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function get_detail($product_id=null, $outlet_id=null)
	{
		//init
		$draw_json = array();
		$valid_input = true;
		$message_error = '';
		$multipleprice = array();

		//cek product
		$row_product = $this->Products_model->get_by_id($product_id);
		if (!$row_product) {
			$valid_input = false;
			$msg_err['product'] = 'Invalid Data Product';
		}

		//cek outlet
		$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
		if (!$row_outlet) {
			$valid_input = false;
			$msg_err['outlet'] = 'Invalid Data Outlet';
		}

		//get data
		if ($valid_input==true) {
			$result_data = $this->Products_detail_model->get_by_product_outlet($product_id, $outlet_id)->result();
			$jumlah_data = 0;
			$variant_data = false;
			$draw_json_data = array();
			foreach ($result_data as $key => $row) {
				if ($row->product_detail_data_status=='on') {
					$jumlah_data++;
					$variant_data = (!empty($row->variant_fkid)) ? true : false;

					//get multipleprice
					$row_multipleprice_data = array();
					$row_multipleprice = $this->Multipleprice_model->getMultiplePrice($row->product_detail_id);
					foreach ($row_multipleprice as $a) {
						$row_multipleprice_data[] = array(
							'multipleprice_id' => $a->multipleprice_id,
							'qty' => $a->qty,
							'price' => $a->price
						);
					}

					//get tax gratuity
					$row_taxgratuity_data = array();
					$row_taxgratuity = $this->Taxdetail_model->get_taxgratuity($row->product_detail_id);
					foreach ($row_taxgratuity as $a) {
						$row_taxgratuity_data[] = array(
							'taxdetail_id' => $a->taxdetail_id,
							'tax_id' => $a->tax_fkid,
							'tax_name' => htmlentities($a->tax_name),
						);
					}

					//set json data
					$draw_json_data[] = array(
						'id' => $row->product_detail_id,
						'variant_name' => htmlentities($row->variant_name),
						'variant_sku' => htmlentities($row->variant_sku),
						'variant_barcode' => htmlentities($row->variant_barcode),
						'price_buy' => $row->price_buy,
						'price_sell' => $row->price_sell,
						'multipleprice' => $row_multipleprice_data,
						'voucher' => $row->voucher,
						'discount' => $row->discount,
						'taxgratuity' => $row_taxgratuity_data,
						'transfermarkup_type' => $row->transfer_markup_type,
						'transfermarkup' => $row->transfer_markup,
						'commission_staff_type' => $row->commission_staff_type,
						'commission_staff' => $row->commission_staff,
						'commission_customer_type' => $row->commission_customer_type,
						'commission_customer' => $row->commission_customer,
						'menu_active' => $row->active
					);
				}
			}


			//output
			if ($jumlah_data>0) {
				$draw_json = array(
					'status' => 'success',
					'variant' => (($jumlah_data>1) || ($variant_data==true)) ? true : false,
					'data' => $draw_json_data,
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Record Not Found',
				);
			}
			
		}
		else{
			$message_error = (!empty($msg_err['product'])) ? $msg_err['product'] : $msg_err['outlet'];
			$draw_json = array(
				'status' => 'error',
				'message' => $message_error
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function update()
	{
		//init
		$draw_json = array();
		$valid_input = true;
		$msg_err = array();

		//cek data
		$id = $this->input->post('id');
		$row = $this->Products_model->get_by_id($id);
		if ($row) {
			//proses validasi update
			$this->_rules('update');

			//cek type
			$msg_err['type'] = '';
			$row_type = $this->Type_model->get_by_id($this->input->post('type'));
			if (!$row_type) {
				$valid_input = false;
				$msg_err['type'] = 'Invalid Data Type';
			}

			//cek category
			$msg_err['category'] = '';
			$row_category = $this->Products_category_model->get_by_id($this->input->post('category'));
			if (!$row_category) {
				$valid_input = false;
				$msg_err['category'] = 'Invalid Data Category';
			}

			//cek subcategory
			$msg_err['subcategory'] = '';
			$row_subcategory = $this->Products_subcategory_model->get_by_id($this->input->post('subcategory'));
			if (!$row_subcategory) {
				$valid_input = false;
				$msg_err['subcategory'] = 'Invalid Data Sub-Category';
			}

			//cek purchase report category
			$msg_err['purchasereportcategory'] = '';
			$row_purchasereportcategory = $this->Purchase_report_category_model->get_by_id($this->input->post('purchasereportcategory'));
			if (!$row_purchasereportcategory) {
				$valid_input = false;
				$msg_err['purchasereportcategory'] = 'Invalid Data Purchase Report Category';
			}

			//cek unit
			$msg_err['unit'] = '';
			$row_unit = $this->Unit_model->get_by_id($this->input->post('unit'));
			if (!$row_unit) {
				$valid_input = false;
				$msg_err['unit'] = 'Invalid Data Unit';
			}

			//cek outlet
			$msg_err['outlet'] = '';
			$selected_outlet = array();
			$available_outlet = array();
			if (!empty($this->input->post('outlet[]'))) {
				foreach ($this->input->post('outlet[]') as $outlet_id) {
					$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
					if ($row_outlet) {
						$selected_outlet[] = $outlet_id;
						$available_outlet[] = array(
							'outlet_id' => $row_outlet->outlet_id,
							'outlet_name' => htmlentities($row_outlet->name),
						);
					}
				}
				if (count($selected_outlet)<=0) {
					$valid_input = false;
					$msg_err['outlet'] = 'Invalid Data Outlet';
				}
			}

			//get current available outlet
			$this->db->select('pd.outlet_fkid AS outlet_id');
			$this->db->from('products_detail pd');
			$this->db->join('outlets o', 'o.outlet_id = pd.outlet_fkid', 'left');
			$this->db->where('product_fkid', $id);
			$this->db->group_by('outlet_fkid');
			$get_current = $this->db->get()->result();
			$old_available_outlet = array();
			foreach ($get_current as $col) {
				$old_available_outlet[$col->outlet_id] = $col->outlet_id;
			}


			//cek account
			$msg_err['account_purchase'] = '';
			if (!empty($this->input->post('account_purchase'))) {
				$row_account_purchase = $this->Accounts_model->get_by_id($this->input->post('account_purchase'));
				if (!$row_account_purchase) {
					$valid_input = false;
					$msg_err['account_purchase'] = 'Invalid Data Account';
				}
			}
			$msg_err['account_sales'] = '';
			if (!empty($this->input->post('account_sales'))) {
				$row_account_sales = $this->Accounts_model->get_by_id($this->input->post('account_sales'));
				if (!$row_account_sales) {
					$valid_input = false;
					$msg_err['account_sales'] = 'Invalid Data Account';
				}
			}

			//cek sku
			$msg_err['sku'] = '';
			if (!empty($this->input->post('sku'))) {
				$row_sku = $this->Products_model->get_by_sku($this->input->post('sku'));
				if ($row_sku && $row_sku->product_id!=$id) {
					$valid_input = false;
					$msg_err['sku'] = 'SKU is already in use.';
				}
			}

			//cek barcode
			$msg_err['barcode'] = '';
			if (!empty($this->input->post('barcode'))) {
				$row_barcode = $this->Products_model->get_by_barcode($this->input->post('barcode'));
				if ($row_barcode && $row_barcode->product_id!=$id) {
					$valid_input = false;
					$msg_err['barcode'] = 'Barcode is already in use.';
				}
			}


			/* PROSES OLAH GAMBAR START */
			// $photo = $this->_upload_photo(); // PROSES OLAH GAMBAR
			// $photo_status = $photo['status_upload'];    // upload foto berhasil atau tidak
			// $photo_available = $photo['available'];     // submit form memiliki gambar atau tidak
			// $error_imageupload = $photo['error_msg'];   // pesan error saat upload foto
			// $photo_path = $photo['path'];               // path direktori foto
			// $photo_name = $photo['name'];               // nama foto yang diupload
			/* PROSES OLAH GAMBAR END   */

			//run validation
			if ($this->form_validation->run() == FALSE || $valid_input == FALSE /*|| ($photo_available==true && $photo_status==false)*/) {
				//hapus gambar yang diupload bila validasi salah
				// if ($photo_status===true) {
				// 	$path = $photo_path.$photo_name;
				// 	unlink($path); //hapus gambar yang baru saja diupload saat input gagal
				// }

				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Failed',
					'error_data' => array(
						'id' => form_error('id'),
						'catalogue_type' => form_error('catalogue_type'),
						'name' => form_error('name'),
						'type' => (!empty(form_error('type'))) ? form_error('type') : $msg_err['type'],
						'category' => (!empty(form_error('category'))) ? form_error('category') : $msg_err['category'],
						'subcategory' => (!empty(form_error('subcategory'))) ? form_error('subcategory') : $msg_err['subcategory'],
						'unit' => (!empty(form_error('unit'))) ? form_error('unit') : $msg_err['unit'],
						'purchasereportcategory' => (!empty(form_error('purchasereportcategory'))) ? form_error('purchasereportcategory') : $msg_err['purchasereportcategory'],
						'outlet' => (!empty(form_error('outlet[]'))) ? form_error('outlet[]') : $msg_err['outlet'],
						'stockmanagement' => form_error('stockmanagement'),
						'account_purchase' => (!empty(form_error('account_purchase'))) ? form_error('account_purchase') : $msg_err['account_purchase'],
						'account_sales' => (!empty(form_error('account_sales'))) ? form_error('account_sales') : $msg_err['account_sales'],
						'sku' => (!empty(form_error('sku'))) ? form_error('sku') : $msg_err['sku'],
						'barcode' => (!empty(form_error('barcode'))) ? form_error('barcode') : $msg_err['barcode'],
						'photo' => form_error('photo') //$error_imageupload
					)
				);
			}
			else{
				// //hapus gambar lama bila update gambar
				// if ($photo_status===TRUE && $photo_available===TRUE) {
				// 	$filename = $photo_path.$row->photo;
				// 	if (file_exists($filename) && !empty($row->photo)) {
				// 		unlink($filename);
				// 	}
				// }

				//update master products
				$data_update = array(
					'name' => $this->input->post('name',TRUE),
					'catalogue_type' => $this->input->post('catalogue_type'),
					'product_type_fkid' => $this->input->post('type'),
					'product_category_fkid' => $this->input->post('category'),
					'product_subcategory_fkid' => $this->input->post('subcategory'),
					'purchase_report_category_fkid' => $this->input->post('purchasereportcategory'),
					'unit_fkid' => $this->input->post('unit'),
					'stock_management' => $this->input->post('stockmanagement'),
					'sku' => (!empty($this->input->post('sku'))) ? $this->input->post('sku') : null,
					'barcode' => (!empty($this->input->post('barcode'))) ? $this->input->post('barcode') : null,
					// 'photo' => ($photo_available==true && $photo_status==true) ? $photo_name : $row->photo
				);

				//update gambar outlet logo
				if ($this->input->post('photo2_url') != $row->photo) {
					if (!empty($_FILES['photo']['name'])) {
						// echo "gambar diganti";
						$path = $this->google_storage_path;
						$version = '-'.current_millis();
						$data_update['photo'] = $this->google_storage->upload('photo', $path, $id . $version);
					}
					else{
						// echo "gambar dihapus";
						$data_update['photo'] = '';
					}


					//hapus gambar lama di local storage
					$filename = $this->photo_path.$row->photo;
					if (file_exists($filename) && !empty($row->photo)) {
						unlink($filename);
					}

					//hapus gambar lama di google storage
					$this->google_storage->delete($row->photo);
				}


				//check photo
				$photo = noimage();
				if (!empty($data_update['photo'])) {
					$photo = $data_update['photo'];
				}else{
					$photo = $row->photo;
				}

				
				$this->db->trans_start();
				$response = $this->Products_model->update($id, $data_update);
				if ($response) {
					//turn off where not selected outlet
					if ($this->session->userdata('user_type')=='employee') {
						$this->privilege->refresh_outlet_access(); //refresh outlet access
						$outlet_access = $this->session->userdata('outlet_access');
						$this->db->where_in('outlet_fkid', $outlet_access);
					}else{
						$this->db->where_not_in('outlet_fkid', $selected_outlet);
					}
					$response_detail = $this->Products_detail_model->update_by_product_id($id, ['data_status' => 'off']);
					if ($response_detail) {
						//cek produk apakah ada variant (ambil variant berdasarkan yang masih aktif)
						$this->db->select('product_fkid, variant_fkid');
						$this->db->from('products_detail');
						$this->db->where('product_fkid', $id);
						$this->db->where('data_status', 'on');
						// $this->db->where('variant_fkid !=', null);
						$this->db->distinct();
						$data_active_variant = $this->db->get()->result();

						//update data detail
						foreach ($selected_outlet as $outlet_id) {
							//aktifkan product di outlet if selected_outlet!=current available outlet
							$response_detail_outlet = $this->Products_detail_model->get_by_product_and_outlet($id, $outlet_id);
							if ($response_detail_outlet) {
								//kalau product ada di outlet, maka nyalakan
								$this->Products_detail_model->update_by_product_and_outlet($id, $outlet_id, [
									'data_status' => 'on'
								]);
							}
							else{
								$tmpArrDataInsertProductDetail = [];
								//cek kalau ada variant
								if (count($data_active_variant)>1) {
									foreach ($data_active_variant as $rd) {
										$tmpArrDataInsertProductDetail[] = array(
											'outlet_fkid' =>$outlet_id,
											'product_fkid' => $id,
											'variant_fkid' => $rd->variant_fkid,
											'price_buy_start' => 0,
											'price_buy' => 0,
											'voucher' => 'off',
											'discount' => 'off',
											'active' => 'off'
										);
									}
								}else{
									$tmp_variant = null;
									if (isset($data_active_variant[0]->variant_fkid)) {
										$tmp_variant = $data_active_variant[0]->variant_fkid;
									}

									//kalau product belum ada di outlet, maka tambahkan
									$tmpArrDataInsertProductDetail[] = array(
										'outlet_fkid' => $outlet_id,
										'product_fkid' => $id,
										'variant_fkid' => $tmp_variant,
										'price_buy_start' => 0,
										'price_buy' => 0,
										'voucher' => 'off',
										'discount' => 'off',
										'active' => 'off'
									);
								}

								foreach ($tmpArrDataInsertProductDetail as $data_productsdetail_insert) {
									$this->Products_detail_model->insert($data_productsdetail_insert);
								}
							}
						}//endforeach: checkout outlet id
					}//endif: update master product
				}
				$trans_complete = $this->db->trans_complete();
				if ($trans_complete) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Update Record Success',
						'data' => array(
							'outlet' => $available_outlet,
							'photo' => $photo
						)
					);
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Update Record Error'
					);
				}
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function update_detail()
	{
		//init
		$draw_json = array();	//simpan format output
		$valid_input = true;	//verifikasi inputan benar atau tidak
		$product_detail_id = $this->input->post('id');
		$product_id = $this->input->post('product_id');

		/* VALIDASI MANUAL INPUTAN START */
		//cek product
		$msg_err['product'] = '';
		$row_product = $this->Products_model->get_by_id($product_id);
		if (!$row_product) {
			$valid_input = false;
			$msg_err['product'] = 'Invalid Product';
		}
		//cek outlet
		$msg_err['outlet'] = '';
		if ($this->input->post('outlet')!='all') {
			$row_outlet = $this->Outlets_model->get_by_id($this->input->post('outlet'));
			if (!$row_outlet) {
				$valid_input = false;
				$msg_err['outlet'] = 'Invalid Outlet';
			}
		}

		//cek taxgratuity
		$selected_taxgratuity = array();
		$datapost['voucher'] = 'off';
		$datapost['discount'] = 'off';
		if (!empty($this->input->post('taxgratuity[]'))) {
			foreach ($this->input->post('taxgratuity[]') as $id) {
				switch ($id) {
					case 'voucher':
						$datapost['voucher'] = 'on';
						break;
					case 'discount':
						$datapost['discount'] = 'on';
						break;
					default:
						$row_taxgratuity = $this->Gratuity_model->get_by_id($id);
						if ($row_taxgratuity) {
							$selected_taxgratuity[] = $id;
						}
						break;
				}
			}
		}

		// VALIDASI CI START
		$this->form_validation->set_rules('outlet', 'outlet', 'trim|required');
		$this->form_validation->set_rules('taxgratuity[]', 'tax & gratuity', 'trim');
		$this->form_validation->set_rules('menu_active', 'menu active', 'trim|required|in_list[on_all,on_sales,on_link,off]', array(
			'in_list' => 'The %s must All, Sales, Link, Off.'
		));
		$this->form_validation->set_rules('variant', 'variant', 'trim|required|in_list[0,1]', array(
			'in_list' => 'The %s must Yes or No.'
		));


		// * kalau ada variant
		if ($this->input->post('variant')==true && empty($this->input->post('variantlist[]'))) {
			$valid_input = false;
			$msg_err['variant'] = 'Variant not Found';
		}
		elseif ($this->input->post('variant')==true && !empty($this->input->post('variantlist[]'))) {

			//get all variant on this product
			$temp_product_variant = array();
			$product_variant = $this->Variant_model->get_by_product($product_id);
			if (!empty($product_variant)) {
				foreach ($product_variant as $value) {
					$temp_product_variant[$value->variant_sku] = $value->variant_name;
				}
			}

			//validasi inputan variant
			foreach ($this->input->post('variantlist[]') as $key => $value) {
				//variant list
				$this->form_validation->set_rules('variantlist['.$key.'][name]', 'variant name', 'trim|required|max_length[50]');
				$this->form_validation->set_rules('variantlist['.$key.'][sku]', 'variant sku', 'trim|max_length[50]|alpha_numeric');
				$this->form_validation->set_rules('variantlist['.$key.'][barcode]', 'variant barcode', 'trim|max_length[100]|alpha_dash');
				$this->form_validation->set_rules('variantlist['.$key.'][pb]', 'variant price buy', 'trim|required|greater_than_equal_to[0]');
				$this->form_validation->set_rules('variantlist['.$key.'][ps]', 'variant price sell', 'trim|required|greater_than_equal_to[0]');

				
				//cek sku antar variant
				$post_name = $this->input->post('variantlist['.$key.'][name]', true);
				$post_sku = $this->input->post('variantlist['.$key.'][sku]', true);

				$msg_err[$key]['sku'] = '';

				if (!empty($temp_product_variant[$post_sku])) {
					if (strtoupper($temp_product_variant[$post_sku]) != strtoupper($post_name) && !empty($post_sku)) {
						$msg_err[$key]['sku'] = 'SKU '.$post_sku.' already used by variant '.$temp_product_variant[$post_sku];
						$valid_input = false;
					}
				}


				//transfer markup
				$temp_type = $this->input->post('variant_tr-type['.$key.']',true);
				$temp_val = $this->input->post('variant_tr-value['.$key.']',true);
				if (!empty($temp_type) || !empty($temp_val)) {
					$this->form_validation->set_rules('variant_tr-type['.$key.']', 'transfer markup', 'trim|required|in_list[percent,nominal]');
					$this->form_validation->set_rules('variant_tr-value['.$key.']', 'transfer markup value', 'trim|required|numeric');
				}
				


				//commission staff
				$temp_type = $this->input->post('variant_commstaff-type['.$key.']',true);
				$temp_val = $this->input->post('variant_commstaff-value['.$key.']',true);
				if (!empty($temp_type || !empty($temp_val))) {
					$this->form_validation->set_rules('variant_commstaff-type['.$key.']', 'staff commission', 'trim|required|in_list[percent,nominal]');
					$this->form_validation->set_rules('variant_commstaff-value['.$key.']', 'staff commission value', 'trim|required|numeric');
				}
			

				//commission customer
				$temp_type = $this->input->post('variant_commcustomer-type['.$key.']',true);
				$temp_val = $this->input->post('variant_commcustomer-value['.$key.']',true);
				if (!empty($temp_type || !empty($temp_val))) {
					$this->form_validation->set_rules('variant_commcustomer-type['.$key.']', 'customer commission', 'trim|required|in_list[percent,nominal]');
					$this->form_validation->set_rules('variant_commcustomer-value['.$key.']', 'customer commission value', 'trim|required|numeric');
				}



				//validation multipleprice
				if (!empty($this->input->post('variantlist['.$key.'][multipleprice][]'))) {
					//init
					$variant_mp=array();
					foreach ($this->input->post('variantlist['.$key.'][multipleprice][]') as $key2 => $value2) {
						//formvalidation
						$this->form_validation->set_rules('variantlist['.$key.'][multipleprice]['.$key2.'][qty]', 'multipleprice qty', 'trim|required|greater_than_equal_to[0]');
						$this->form_validation->set_rules('variantlist['.$key.'][multipleprice]['.$key2.'][price]', 'multipleprice price', 'trim|required|greater_than_equal_to[0]');

						//cek duplikat qty
						if (empty($variant_mp[$key][$value2['qty']])) {
							$variant_mp[$key][$value2['qty']] = $value2['qty'];
						}
						else{
							$valid_input = false;
						}
					}
				}
			}
			//VARIANT VALIDATION END
		}
		else{
			//NO VARIANT VALIDATION START
			$this->form_validation->set_rules('price_buy', 'price buy', 'trim|required|greater_than_equal_to[0]');
			$this->form_validation->set_rules('price_sell', 'price sell', 'trim|required|greater_than_equal_to[0]');

			if (!empty($this->input->post('transfermarkup_type')) || !empty($this->input->post('transfermarkup'))) {
				$this->form_validation->set_rules('transfermarkup_type', 'transfer markup type', 'trim|required|in_list[percent,nominal]');
				$this->form_validation->set_rules('transfermarkup', 'transfer markup value', 'trim|required|numeric');
			}

			//kalau ada multipleprice
			if (!empty($this->input->post('multipleprice_qty[]')) || !empty($this->input->post('multipleprice_price[]'))) {
				$this->form_validation->set_rules('multipleprice_qty[]', 'qty of multipleprice', 'trim|required|greater_than_equal_to[0]');
				$this->form_validation->set_rules('multipleprice_price[]', 'price of multipleprice', 'trim|required|greater_than_equal_to[0]');


				$validasi_mp = array();
				foreach ($this->input->post('multipleprice_qty[]') as $key => $value) {
					if (empty($validasi_mp[$value])) {
						$validasi_mp[$value] = $value;
					}
					else{
						$valid_input = false;
						$msg_err['multipleprice'] = 'Duplicate Multipleprice Qty: '.$value;
					}
				}
			}

			//kalau ada komisi
			$this->_rules('update_detail');
		}
		$this->form_validation->set_error_delimiters('', '');
		// VALIDASI CI END
		


		if ($this->form_validation->run() == FALSE || $valid_input==false) {
			/* reformat error */
			$msg_err['outlet'] = (!empty(form_error('outlet'))) ? form_error('outlet') : $msg_err['outlet'];
			$msg_err['price_sell'] = form_error('price_sell');
			$msg_err['transfermarkup'] = (!empty(form_error('transfermarkup_type'))) ? form_error('transfermarkup_type') : form_error('transfermarkup');
			$msg_err['variant_sku'] = (!empty($msg_err['variant_sku'])) ? $msg_err['variant_sku'] : form_error('variant_sku[]');

			$draw_json = array(
				'status' => 'error',
				'message' => 'Update Record Invalid',
				'error_data' => array(
					'outlet' => $msg_err['outlet'],
					'price_buy' => form_error('price_buy'),
					'price_sell' => $msg_err['price_sell'],
					'multipleprice' => (!empty($msg_err['multipleprice'])) ? $msg_err['multipleprice'] : '',
					'transfermarkup' => $msg_err['transfermarkup'],
					'commission_staff' => form_error('commission_staff'),
					'commission_customer' => form_error('commission_customer'),
					'menu_active' => form_error('menu_active'),
					'variant' => (!empty($msg_err['variant'])) ? $msg_err['variant'] : form_error('variant')
				)
			);

			//variant
			if (!empty($this->input->post('variantlist[]'))) {
				foreach ($this->input->post('variantlist[]') as $key => $value) {
					$draw_json['error_data']['variantlist'][$key]['name'] = form_error('variantlist['.$key.'][name]');
					$draw_json['error_data']['variantlist'][$key]['sku'] = (!empty(form_error('variantlist['.$key.'][sku]'))) ? form_error('variantlist['.$key.'][sku]') : $msg_err[$key]['sku'];
					$draw_json['error_data']['variantlist'][$key]['barcode'] = form_error('variantlist['.$key.'][barcode]');
					$draw_json['error_data']['variantlist'][$key]['price_buy'] = form_error('variantlist['.$key.'][pb]');
					$draw_json['error_data']['variantlist'][$key]['price_sell'] = form_error('variantlist['.$key.'][ps]');

					//multipleprice
					if (!empty($this->input->post('variantlist['.$key.'][multipleprice][]'))) {
						$variant_mp=array();
						$variant_mp_valid = true;
						foreach ($this->input->post('variantlist['.$key.'][multipleprice][]') as $key2 => $value2) {
							$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['qty'] = form_error('variantlist['.$key.'][multipleprice]['.$key2.'][qty]');
							$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['price'] = form_error('variantlist['.$key.'][multipleprice]['.$key2.'][price]');


							//cek duplikat qty
							if (empty($variant_mp[$key][$value2['qty']])) {
								$variant_mp[$key][$value2['qty']] = $value2['qty'];
							}
							else{
								$draw_json['error_data']['variantlist'][$key]['multipleprice'][$key2]['qty'] = 'Duplicate Multipleprice Qty: '.$value2['qty'];
							}
						}
					}
					else{
						$draw_json['error_data']['variantlist'][$key]['multipleprice'] = array();
					}

					//transfer
					$draw_json['error_data']['variantlist'][$key]['transfermarkup'] = array(
						'type' => form_error('variant_tr-type['.$key.']'),
						'value' => form_error('variant_tr-value['.$key.']')
					);

					//commission staff
					$draw_json['error_data']['variantlist'][$key]['commission_staff'] = array(
						'type' => form_error('variant_commstaff-type['.$key.']'),
						'value' => form_error('variant_commstaff-value['.$key.']')
					);

					//commission customer
					$draw_json['error_data']['variantlist'][$key]['commission_customer'] = array(
						'type' => form_error('variant_commcustomer-type['.$key.']'),
						'value' => form_error('variant_commcustomer-value['.$key.']')
					);
				}
			}
		}
		else {
			//ambil selected outlet START
			$masterdetail_id_array = array();
			$masterdetail_oid_array = array();

			$this->db->trans_start();

			if ($this->input->post('outlet')=='all') {
				/* ambil data outlet mana saja yang menyediakan produk ini */
				//get master detail
				$this->db->where('product_fkid', $product_id);
				$this->db->where('product_detail_data_status', 'on');
				$this->db->where('outlet_adminid', $this->session->userdata('admin_id'));
				if ($this->session->userdata('user_type')=='employee') {
					$this->db->where_in('outlet_fkid', $this->session->userdata('outlet_access'));
				}
				$result = $this->db->get('view_catalogue_detail')->result();
				$masterdetail_oid_prev = '';
				foreach ($result as $a) {
					if ($a->outlet_fkid!=$masterdetail_oid_prev) {
						$masterdetail_oid_prev = $a->outlet_fkid;
						$masterdetail_id_array[] = $a->product_detail_id;
						$masterdetail_oid_array[] = $a->outlet_fkid;
					}
				}
			}
			else{
				$masterdetail_id_array[0] = $product_detail_id;
				$masterdetail_oid_array[0] = $this->input->post('outlet');
			}

			//update master detail (by: Outlet yang dipilih)
			$i=0;
			foreach ($masterdetail_id_array as $masterdetail_id) {
				$product_id = $this->input->post('product_id');
				$outlet_id = $masterdetail_oid_array[$i];

				# proses ini bermasalah dengan transaction
				// //hapus data yang sudah ada dan mati di outlet yang tidak terpakai
				// $this->db->where('data_status', 'off');
				// $this->Products_detail_model->delete_by_product_outlet($product_id, $outlet_id);

				//matikan semua data master detail
				$response = $this->Products_detail_model->update_by_product_and_outlet($product_id,$outlet_id,['data_status'=>'off']);
				if ($response) {
					//cek variant
					if ($this->input->post('variant')==true) {
						//kalau ada variant
						foreach ($this->input->post('variantlist[]') as $index => $value) {
							$variant_name = $this->input->post('variantlist['.$index.'][name]',TRUE);
							$variant_sku = $this->input->post('variantlist['.$index.'][sku]',TRUE);
							$variant_sku = strtoupper($variant_sku);
							$variant_barcode = $this->input->post('variantlist['.$index.'][barcode]',TRUE);
							$variant_barcode = strtoupper($variant_barcode);
							$product_detail_id = ''; //product_detail_id variant

							//cek nama variant
							$variant_id = null;
							$cek_variantname = $this->Variant_model->get_by_variantname_product($variant_name, $product_id);
							if ($cek_variantname) {
								//kalau variant sudah ada
								$variant_id = $cek_variantname->variant_id;

								//update data variant
								$data_variant_update = array(
									'variant_name' => $variant_name,
									'variant_sku' => $variant_sku,
									'variant_barcode' => $variant_barcode
								);
								$this->Variant_model->update($variant_id, $data_variant_update);
							}
							else{
								$data_variant_insert = array(
									'variant_name' => $variant_name,
									'variant_sku' => $variant_sku,
									'variant_barcode' => $variant_barcode,
									'product_fkid' => $product_id,
								);
								$response_variant = $this->Variant_model->insert($data_variant_insert);
								$variant_id = ($response_variant) ? $this->db->insert_id() : '';
							}

							/* cek variant sudah ada atau belum di outlet */
							$row_variant = $this->Products_detail_model->get_by_product_outlet_variantname($product_id, $outlet_id, $variant_name);
							if ($row_variant) {
								//kalau variant sudah ada, aktifkan (update)
								$data_variant_update = array(
									'price_buy' => $this->input->post('variantlist['.$index.'][pb]'),
									'price_sell' => $this->input->post('variantlist['.$index.'][ps]'),
									'active' => $this->input->post('menu_active'),
									'voucher' => $datapost['voucher'],
									'discount' => $datapost['discount'],
									'data_status' => 'on',
									'transfer_markup_type' => $this->input->post('variant_tr-type['.$index.']'),
									'transfer_markup' => $this->input->post('variant_tr-value['.$index.']'),
									'commission_staff_type' => $this->input->post('variant_commstaff-type['.$index.']'),
									'commission_staff' => $this->input->post('variant_commstaff-value['.$index.']'),
									'commission_customer_type' => $this->input->post('variant_commcustomer-type['.$index.']'),
									'commission_customer' => $this->input->post('variant_commcustomer-value['.$index.']'),
									'variant_fkid' => $variant_id,
								);
								$response2 = $this->Products_detail_model->update($row_variant->product_detail_id, $data_variant_update);
								if ($response2) {
									$masterdetail_id = $row_variant->product_detail_id;
								}

							}
							else{
								//kalau variant belum ada, tambahkan
								$data_variant_insert = array(
									'product_fkid' => $product_id,
									'outlet_fkid' => $outlet_id,
									'price_buy_start' => $this->input->post('variantlist['.$index.'][pb]'),
									'price_buy' => $this->input->post('variantlist['.$index.'][pb]'),
									'price_sell' => $this->input->post('variantlist['.$index.'][ps]'),
									'voucher' => $datapost['voucher'],
									'discount' => $datapost['discount'],
									'active' => $this->input->post('menu_active'),
									'transfer_markup_type' => $this->input->post('variant_tr-type['.$index.']'),
									'transfer_markup' => $this->input->post('variant_tr-value['.$index.']'),
									'commission_staff_type' => $this->input->post('variant_commstaff-type['.$index.']'),
									'commission_staff' => $this->input->post('variant_commstaff-value['.$index.']'),
									'commission_customer_type' => $this->input->post('variant_commcustomer-type['.$index.']'),
									'commission_customer' => $this->input->post('variant_commcustomer-value['.$index.']'),
									'variant_fkid' => $variant_id,
								);
								$response_insert = $this->Products_detail_model->insert($data_variant_insert);
								if ($response_insert) {
									$masterdetail_id = $this->db->insert_id();
								}//endif: response insert
							}


							//TAX GRATUITY PROCESS
							// delete old tax gratuity
							$this->Taxdetail_model->delete_by_productdetail($masterdetail_id);
							// insert new data product detail
							if (!empty($selected_taxgratuity)) {
								foreach ($selected_taxgratuity as $indexTG => $value) {
									$this->Taxdetail_model->insert_taxgratuity(array(
										'tax_fkid' => $value,
										'product_detail_fkid' => $masterdetail_id, //primary key setelah input product_detail
									));
								}
							}

							//MULTIPLEPRICE
							$this->Multipleprice_model->delete_by_productdetail($masterdetail_id); //delete old multipleprice
							if (!empty($this->input->post('variantlist['.$index.'][multipleprice]'))) {

								// insert new data multipleprice
								$insertbatchMP = array();
								$cm = current_millis();
								foreach ($this->input->post('variantlist['.$index.'][multipleprice][]') as $indexMP2 => $value2) {
									$insertbatchMP[] = array(
										'product_detail_fkid' => $masterdetail_id,
										'qty' => $value2['qty'],
										'price' => $value2['price'],
										'data_created' => $cm,
										'data_modified' => $cm,
										'data_status' => 'on',
									);
								}
								$this->Multipleprice_model->insertbatch_multipleprice($insertbatchMP);
							}


							// //buat output
							// $draw_json = array(
							// 	'status' => 'success',
							// 	'message' => 'Update Record Success'
							// );
						}//endforeach: response
					}
					else{
						//kalau tidak ada variant
						//update data master detail
						$data_update = array(
							'price_buy' => $this->input->post('price_buy'),
							'price_sell' => $this->input->post('price_sell'),
							'active' => $this->input->post('menu_active'),
							'voucher' => $datapost['voucher'],
							'discount' => $datapost['discount'],
							'transfer_markup_type' => $this->input->post('transfermarkup_type'),
							'transfer_markup' => $this->input->post('transfermarkup'),
							'commission_staff_type' => $this->input->post('commission_staff_type'),
							'commission_staff' => $this->input->post('commission_staff'),
							'commission_customer_type' => $this->input->post('commission_customer_type'),
							'commission_customer' => $this->input->post('commission_customer')
						);

						//ambil data yang no variant
						$row_novariant = $this->Products_detail_model->get_by_product_outlet_variant($product_id, $outlet_id, null);
						if ($row_novariant) {
							//update data kalau belum ada
							$masterdetail_id = $row_novariant->product_detail_id;
							$data_update['data_status'] = 'on';
							$response2 = $this->Products_detail_model->update($masterdetail_id, $data_update);
						}
						else{
							//insert data master detail tanpa variant
							$data_update['product_fkid'] = $product_id;
							$data_update['outlet_fkid'] = $outlet_id;
							$data_insert['price_buy_start'] = $this->input->post('price_buy');
							$response2 = $this->Products_detail_model->insert($data_update);
							$masterdetail_id = $this->db->insert_id();
						}

						//update tax and multipleprice
						if ($response2) {
							//update tax gratuity START
							$update_taxgratuityDB = $this->Taxdetail_model->update_taxgratuity_datastatus_by_masterdetailid($masterdetail_id, 'off');
							if ($update_taxgratuityDB===TRUE) {
								$draw_json = array(
									'status' => 'success',
									'message' => 'Turn off All taxgratuity'
								);

								//manipulasi taxgratuity START
								$get_result = $this->Taxdetail_model->get_taxgratuity_by_masterdetailid($masterdetail_id);
								$row = $get_result['data'];
								$num_row = $get_result['num_rows'];

								//insert tax gratuity START
								if (!empty($selected_taxgratuity)) {
									foreach ($selected_taxgratuity as $index => $value) {
										$dataTax = array(
											'tax_fkid' => $value,
											'product_detail_fkid' => $masterdetail_id, //primary key setelah input product_detail
											'data_status' => 'on'
										);

										//cek apakah data sudah ada atau belum
										$this->db->where('tax_fkid', $value);
										$this->db->where('product_detail_fkid', $masterdetail_id);
										$cek = $this->db->get('products_detail_taxdetail');
										$cek_row = $cek->row();
										$cek_numrow = $cek->num_rows();

										if ($cek_row) {
											$updateData = $this->Taxdetail_model->update_taxgratuity($masterdetail_id, $dataTax);
										}
										else{
											$insert_taxgratuity = $this->Taxdetail_model->insert_taxgratuity($dataTax);
										}
									}
								}
							}

							//update multiple price START
							$update_multiplepriceDB = $this->Multipleprice_model->update_multiprice_datastatus_by_masterdetailid($masterdetail_id, 'off');
							if ($update_multiplepriceDB===TRUE) {
								$draw_json = array(
									'status' => 'success',
									'message' => 'Turn off multiple price success'
								);

								//manipulasi multipleprice START
								$get_result = $this->Multipleprice_model->get_multiprice_by_masterdetailid($masterdetail_id);
								$row = $get_result['data'];
								$num_row = $get_result['num_rows'];

								//insert multiple price START
								$i3 = 0;
								if (!empty($this->input->post('multipleprice_qty[]')) || $this->input->post('multipleprice_price[]')) {
									foreach ($this->input->post('multipleprice_qty[]') as $index => $value) {
										$your_qty = $this->input->post('multipleprice_qty['.$index.']');
										$your_price = $this->input->get_post('multipleprice_price['.$index.']');

										$dataMultiplePrice = array(
											'product_detail_fkid' => $masterdetail_id, //primary key setelah input product_detail
											'qty' => $your_qty,
											'price' => $your_price,
											'data_status' => 'on'
										);

										if ($i3 < $num_row) {
											//update data
											$multipleprice_id = $row[$i3]->multipleprice_id; //primary key detail multiprice
											$updateData = $this->Multipleprice_model->update_multiprice($multipleprice_id, $dataMultiplePrice);
										}
										else{
											//tambah data
											$inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePrice);
										}
										$i3++;
									}
								}
							}

							$draw_json = array(
								'status' => 'success',
								'message' => 'Update Record Success'
							);
						}
					}//endif: cek variant
				}//endif: turn off data

				$i++;
			}//endforeach: update master detail


			$trans_status = $this->db->trans_complete();
			if ($trans_status) {
				//buat output
				$draw_json = array(
					'status' => 'success',
					'message' => 'Update Record Success'
				);
			}else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Failed'
				);
			}

		}

		//output
		echo format_json($draw_json);
	}

	public function delete($id=null, $deletetype=null)
	{
		//init
		$draw_json = array();
		$response = false;

		//cek
		$row = $this->Products_model->get_by_id($id);
		if ($row) {
			$response = $this->Products_model->delete($id); //delete data
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Record Success'
				);
			}
			else{
				if ($deletetype=='permanent') {
					//set status off
					$response = $this->Products_model->delete_soft($id);
					if ($response) {
						if ($this->session->userdata('user_type')=='employee') {
							$draw_json = array(
								'status' => 'success',
								'message' => 'Product removed from your outlets!'
							);
						}else{
							$draw_json = array(
								'status' => 'success',
								'message' => 'Delete Record Permanently'
							);
						}
					}
					else{
						$draw_json = array(
							'status' => 'error',
							'message' => 'Delete Record Failed'
						);
					}
				}
				else{
					$draw_json = array(
						'status' => 'pending',
						'message' => 'Data in Use'
					);
				}
			}


			//delete success action
			if ($response) {
				//delete product photo
				$photo_name = $row->photo;
				$photo_path = $this->photo_path.$photo_name;
				if (file_exists($photo_path) && !empty($photo_name)) {
					unlink($photo_path);
				}
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function _rules($action=null)
	{
		//MAIN VALIDATION
		if ($action=='create' || $action=='update') {
			/* RULES VALIDASI INPUT START */
			$this->form_validation->set_rules('catalogue_type', 'catalog type', 'trim|required|in_list[equipment,product,ingridient]',
				array(
					'in_list' => 'The %s must Equipment, Product, Ingridient.'
				)
			);
			$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
			$this->form_validation->set_rules('type', 'type', 'trim|required');
			$this->form_validation->set_rules('category', 'category', 'trim|required');
			$this->form_validation->set_rules('subcategory', 'subcategory', 'trim|required');
			$this->form_validation->set_rules('unit', 'unit', 'trim|required');
			$this->form_validation->set_rules('purchasereportcategory', 'purchase report category', 'trim|required');
			$this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
			$this->form_validation->set_rules('stockmanagement', 'stock management', 'trim|required|in_list[1,0]',array(
				'in_list' => 'The %s must Yes or No.'
			));
			$this->form_validation->set_rules('account_purchase', 'account of purchase', 'trim');
			$this->form_validation->set_rules('account_sales', 'account of sales', 'trim');
			$this->form_validation->set_rules('barcode', 'barcode', 'trim|min_length[5]|max_length[30]|alpha_numeric');

			$this->form_validation->set_rules('photo', 'photo', 'callback_validate_photo');
		}
		

		/* RULES VALIDASI INPUT END */
		switch ($action) {
			case 'create':
				$this->form_validation->set_rules('taxgratuity[]', 'tax & gratuity', 'trim');
				$this->form_validation->set_rules('transfermarkup_type', 'transfer markup type', 'trim|in_list[percent,nominal]');
				if (!empty($this->input->post('transfermarkup_type'))) {
					$this->form_validation->set_rules('transfermarkup', 'transfer markup value', 'trim|required|greater_than_equal_to[0]');
				}

				$this->form_validation->set_rules('commission_staff_type', 'staff commission type', 'trim|in_list[percent,nominal]');
				if (!empty($this->input->post('commission_staff_type'))) {
					$this->form_validation->set_rules('commission_staff', 'commission staff value', 'trim|required|greater_than_equal_to[0]');
				}
				$this->form_validation->set_rules('commission_customer_type', 'customer commission type', 'trim|in_list[percent,nominal]');
				if (!empty($this->input->post('commission_customer_type'))) {
					$this->form_validation->set_rules('commission_customer', 'commission customer value', 'trim|required|greater_than_equal_to[0]');
				}

				$this->form_validation->set_rules('menu_active', 'menu active', 'trim|required|in_list[on_all,on_sales,on_link,off]',array(
						'in_list' => 'The %s must All, Sales, Link or Off.'
					)
				);
				$this->form_validation->set_rules('variant', 'variant', 'trim|required|in_list[0,1]',array(
					'in_list' => 'The %s must Yes or No.'
				));

				//variant validation
				if ($this->input->post('variant')==true) {
					// $this->form_validation->set_rules('variant_name[]', 'variant name', 'trim|required|max_length[50]');
					// $this->form_validation->set_rules('variant_sku[]', 'variant sku', 'trim|max_length[50]');
					// $this->form_validation->set_rules('variant_barcode[]', 'variant barcode', 'trim|max_length[100]');
					// $this->form_validation->set_rules('variant_pb[]', 'variant price buy', 'trim|required|greater_than_equal_to[0]');
					// $this->form_validation->set_rules('variant_ps[]', 'variant price sell', 'trim|required|greater_than_equal_to[0]');
				}
				else{
					$this->form_validation->set_rules('sku', 'sku', 'trim|max_length[30]|alpha_dash');
					$this->form_validation->set_rules('price_buy', 'buying price', 'trim|required|greater_than_equal_to[0]');
					$this->form_validation->set_rules('price_sell', 'selling price', 'trim|required|greater_than_equal_to[0]');

					/* kalau multipleprice no variant tidak kosong */
					if (!empty($this->input->post('multipleprice_qty[]')) || !empty($this->input->post('multipleprice_price[]'))) {
						$this->form_validation->set_rules('multipleprice_qty[]', 'qty of multipleprice', 'trim|required|greater_than_equal_to[0]');
						$this->form_validation->set_rules('multipleprice_price[]', 'price of multipleprice', 'trim|required|greater_than_equal_to[0]');
					}
				}
				break;
			case 'update':
				$this->form_validation->set_rules('id', 'id', 'trim|required');
				$this->form_validation->set_rules('sku', 'sku', 'trim|max_length[30]|alpha_dash');
				break;
			case 'update_detail':
				$this->form_validation->set_rules('commission_staff_type', 'staff commission type', 'trim|in_list[percent,nominal]');
				if (!empty($this->input->post('commission_staff_type'))) {
					$this->form_validation->set_rules('commission_staff', 'commission staff value', 'trim|required|greater_than_equal_to[0]');
				}
				$this->form_validation->set_rules('commission_customer_type', 'customer commission type', 'trim|in_list[percent,nominal]');
				if (!empty($this->input->post('commission_customer_type'))) {
					$this->form_validation->set_rules('commission_customer', 'commission customer value', 'trim|required|greater_than_equal_to[0]');
				}
				break;
			default:
				die('Undefined rules action');
				break;
		}

		$this->form_validation->set_error_delimiters('', '');
	}

	public function validate_photo()
	{
		if (!empty($_FILES['photo']['name'])) {
			$file_ext = pathinfo($_FILES['photo']['name'],PATHINFO_EXTENSION);
			$filename = password_hash(date('YmdHis'), PASSWORD_DEFAULT);
			$config['file_name'] = 'productphoto_'.preg_replace("/\W|_/", "", $filename).'.'.$file_ext;
			$config['upload_path'] = $this->tmp_upload_path;
			$config['allowed_types'] = 'gif|jpg|jpeg|png';
			$config['max_size'] = $this->max_upload_size;
			// $config['max_size']  = '100';
			// $config['max_width']  = '1024';
			// $config['max_height']  = '768';
			
			$this->load->library('upload', $config);
			
			if ( ! $this->upload->do_upload('photo')){
				$error_message = $this->upload->display_errors();
				$error_message = str_replace('<p>', '', $error_message);
				$error_message = str_replace('</p>', '', $error_message);
				$this->form_validation->set_message('validate_photo', $error_message );
				return false;
			}
			else{
				$data = $this->upload->data();
				unlink($data['full_path']);
				return true;
			}
		}
		else{
			return true;
		}
	}

	public function _upload_photo()
	{
		/* PROSES OLAH GAMBAR by aziz */
		//inisialisasi variabel
		$error_imageupload = '';
		$photo_available = false;   //form ada submit foto atau tidak
		$photo_upload = false;      //foto berhasil diupload atau tidak

		/*konfigurasi upload gambar start*/
		// $photo_path = product_url(); //(ambil dari assets_helper)
		// $photo_path = str_replace(base_url(), '', $photo_path); //ambil path dari assets/images/products

		/* photo path by private $photo_path */
		$photo_path = $this->photo_path;
		$photo_name = $this->session->userdata('admin_id').'_'.$this->input->post('name').'_'.date('YmdHis'); //temporary name
		$photo_name = strtolower($photo_name); //mengubah nama file menjadi kecil semua
		$photo_name = str_replace(' ', '', $photo_name); //fungsi untuk menghilangkan spasi pada nama
		$config['upload_path']          = $photo_path; //'./_tes_upload/'.$album;
		$config['allowed_types']        = 'gif|jpg|png|jpeg'; //ekstensi yang diizinkan
		$config['max_size']             = $this->max_upload_size; //maksimal size
		$config['file_name']            = $photo_name; //nama gambar akan diganti saat diupload
		//$config['max_width']  = '1024'; //atur lebar maksimal gambar
		//$config['max_height']  = '768'; //atur tinggi maksimal gambar
		$config['remove_spaces'] = true;
		/*konfigurasi upload gambar end*/

		/*proses upload gambar start*/
		//buat direktori berdasarkan owner
		$dir_exist = true;
		if (!is_dir($photo_path)) {
			/* kalau mkdir() disable, comment dari sini */
			$old = umask(0);
			mkdir($photo_path, 0775, true); //buat direktori image produk untuk per-owner
			umask($old);
			/* kalau mkdir() disable, comment sampai sini */
			$dir_exist = false; // dir not exist
			$draw_json = array(
				'status' => 'error',
				'message' => 'Dir not Exist'
			);
		}

		//upload gambar
		$this->load->library('upload', $config);
		if (!empty($_FILES['photo']['name']) && $_FILES['photo']['name']!="") { //bila upload foto
			$photo_available = true;
			if ( ! $this->upload->do_upload('photo')){
				$error_imageupload = array('error' => $this->upload->display_errors());
				$error_imageupload = $error_imageupload['error'];
				$photo_upload = false; //status upload
				//echo "upload gagal";
			}
			else{
				$data = array('upload_data' => $this->upload->data());
				$photo_name = $data['upload_data']['file_name'];
				$photo_upload = true; //status upload
				//echo "upload success";
			}
		}
		else{
			$photo_available = false;//custom
			$photo_name = '';
			//echo "tidak ada gambar";
		}
		/* PROSES OLAH GAMBAR END*/
		return $photo = array(
			'status_upload' => $photo_upload,
			'available' => $photo_available,
			'error_msg' => $error_imageupload,
			'path' => $photo_path,
			'name' => $photo_name,
		);
	}


	public function modal_export()
	{
		$template_path = 'products/product-catalogue/export_import/';

		$data = array(
			'ajaxActionExport' => $this->url.'export_process',
			'ajaxActionImportCSV_product' => $this->url.'import-product/import_process',
			'formselect_outlet' => $this->Outlets_model->form_select(),
			'formselect_taxgratuity' => $this->Gratuity_model->form_select(),
			'template_importproduct' => $this->url.'import-product/import_template',
			'export_filetype_list' => ['xls' => 'Excel']
		);

		//IMPORT SECTION
		$data['download_template_text'] = 'Download Template (.csv)';
		$data['allowed_file_type']		= array('.csv');



		//==========================================================================
		// if (ENVIRONMENT!=='production') {
		// 	$version = (ENVIRONMENT==='production') ? 'v1' : 'v2';
			$version = 'v2';

			//EXPORT URL
			$data['ajaxActionExport'] = $this->url.$version.'/export';

			//EXPORT FILE LIST
			$data['export_filetype_list'] = array(
				'xlsx'	=> 'Excel',
				'xls'	=> 'Excel'
			);


			//IMPORT URL
			$data['ajaxActionImportCSV_product']	= $this->url.$version.'/import';
			$data['template_importproduct']			= $this->url.$version.'/import_template';

			$data['download_template_text']			= 'Download Template (.xlsx)';
			$data['allowed_file_type']				= array('.xlsx','.xls');
		// }
		//==========================================================================



		$html_form_export = $this->load->view($template_path.'v2/export_html_v', $data, true);
		$html_form_import = $this->load->view($template_path.'v2/import_html_v', $data, true);

		$data['html_form_export'] = $html_form_export;
		$data['html_form_import'] = $html_form_import;
		echo $this->load->view($template_path.'v2/export_import_v', $data, true);
	}

	public function modal_linkmenu()
	{
		$template_path = 'products/product-catalogue/linkmenu/';

		$data = array(
			'ajaxActionExport' => '',
			'ajaxActionImportCSV_linkmenu' => $this->url.'linkmenu-import/import_process',
			'formselect_outlet' => $this->Outlets_model->form_select(),
		);

		$html_form_export = '';
		$html_form_import = $this->load->view($template_path.'v2/modal_import_html', $data, TRUE);

		$data['html_form_export'] = $html_form_export;
		$data['html_form_import'] = $html_form_import;
		echo $this->load->view($template_path.'v2/modal_v', $data, TRUE);
	}

	public function modal_multipleprice()
	{
		$template_path = 'products/product-catalogue/multipleprice/';

		$data = array(
			'url_template_download' => $this->url.'multipleprice-import/template',
			'ajaxActionExport' => '',
			'ajaxActionImport' => $this->url.'multipleprice-import/import_process',
			'formselect_outlet' => $this->Outlets_model->form_select(),
		);

		$html_form_export = '';
		$html_form_import = $this->load->view($template_path.'v2/modal_import_html', $data, TRUE);

		$data['html_form_export'] = $html_form_export;
		$data['html_form_import'] = $html_form_import;
		echo $this->load->view($template_path.'v2/modal_v', $data, TRUE);
	}

	/* ajax */
	public function ajax_createmaster()
	{
		//init
		$draw_json = array();

		//validation
		$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('master_add', 'master add', 'trim|required|in_list[type,category,subcategory,purchasereportcategory,unit,outlet]',array(
			'in_list' => 'Invalid Data Type.'
		));

		//custom validation
		switch ($this->input->post('master_add')) {
			case 'unit':
				$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[10]');
				$this->form_validation->set_rules('description', 'description', 'trim|required|max_length[30]');
				break;
			case 'outlet':
				$this->form_validation->set_rules('address', 'address', 'trim|required|max_length[100]');
				$this->form_validation->set_rules('phone', 'phone', 'trim|required|is_natural|max_length[15]');
				break;
			default:
				# code...
				break;
		}
		$this->form_validation->set_error_delimiters('<div>', '</div>');
		if ($this->form_validation->run() == FALSE) {
			//$msg_err = (!empty(form_error('master_add'))) ? 'Invalid Data Type!' : form_error('name');
			$draw_json = array(
				'status' => 'error',
				'message' => validation_errors()
			);
		} else {
			$data['name'] = $this->input->post('name',true);
			switch ($this->input->post('master_add')) {
				case 'type':
					$response = $this->Type_model->insert($data);
					break;
				case 'category':
					$response = $this->Products_category_model->insert($data);
					break;
				case 'subcategory':
					$response = $this->Products_subcategory_model->insert($data);
					break;
				case 'purchasereportcategory':
					$response = $this->Purchase_report_category_model->insert($data);
					break;
				case 'unit':
					$data['description'] = $this->input->post('description',TRUE);
					$response = $this->Unit_model->insert($data);
					break;
				case 'outlet':
					$data['address'] = $this->input->post('address',TRUE);
					$data['phone'] = $this->input->post('phone');
					$response = $this->Outlets_model->insert($data);
					break;
				default:
					$response = null;
					break;
			}
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Create Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Create Record Failed'
				);
			}
		}

		//output
		echo format_json($draw_json);
	}

	public function ajax_selectoption($master_type='')
	{
		//init
		$draw_json = array();
		$data = array();
		$response = array();
		$id = '';
		$code = '';

		switch ($master_type) {
			case 'type':
				$response = $this->Type_model->form_select();
				$id = 'products_type_id';
				$code = 'code';
				break;
			case 'category':
				$response = $this->Products_category_model->form_select();
				$id = 'product_category_id';
				$code = 'code';
				break;
			case 'subcategory':
				$response = $this->Products_subcategory_model->form_select();
				$id = 'product_subcategory_id';
				$code = 'code';
				break;
			case 'purchasereportcategory':
				$id = 'purchase_report_category_id';
				$response = $this->Purchase_report_category_model->form_prc();
				$code = '';
				break;
			case 'unit':
				$id = 'unit_id';
				$response = $this->Unit_model->form_select();
				$code = 'description';
				break;
			case 'outlet':
				$id = 'outlet_id';
				$response = $this->Outlets_model->form_select();
				$code = '';
				break;
			default:
				$draw_json = array(
					'status' => 'error',
					'message' => 'Data Not Found'
				);
				break;
		}

		//render data
		if (!empty($response)) {
			foreach ($response as $a) {
				$data[] = array(
					'id' => $a->$id,
					'name' => htmlentities($a->name),
					'code' => (!empty($code)) ? htmlentities($a->$code) : $code,
				);
			}
			$draw_json = array(
				'status' => 'success',
				'message' => 'Success',
				'data' => $data
			);
		}

		//output
		echo format_json($draw_json);
	}

}

/* End of file Catalogue.php */
/* Location: ./application/controllers/products/Catalogue.php */