<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Subcategory extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->model('products/products_subcategory/Products_subcategory_model');
	}

	public function index()
	{
		$data['page_title'] = 'Product Sub-Category';
		$this->template->view('products/main_master/master_v2', $data);
	}

	public function datatables()
	{
		$json = $this->Products_subcategory_model->datatables();
		$json_decode = json_decode($json);
		$data_array = array();
		foreach ($json_decode->data as $r) {
			$data_array[] = array(
				'id' => $r->id,
				'name' => htmlentities($r->name),
				'code' => htmlentities($r->code),
			);
		}

		//remake json data
		$draw_json = array(
			'draw' => $json_decode->draw,
			'recordsTotal' => $json_decode->recordsTotal,
			'recordsFiltered' => $json_decode->recordsFiltered,
			'data' => $data_array
		);

		//output
		echo format_json($draw_json);
	}

	public function action($action=null)
	{
		//init
		$draw_json = array();
		$valid_input = true;

		//cek data (id, code, category)
		/* cek id */
		$msg_err_id = '';
		if ($action=='update') {
			$cek_id = $this->Products_subcategory_model->get_by_id($this->input->post('id'));
			if (!$cek_id) {
				$valid_input = false;
				$msg_err_id = 'Record Not Found';
			}
		}

		$msg_err_code = '';
		/* cek code */
		$msg_err_code = '';
		if ($valid_input==true && !empty($this->input->post('code'))) {
			$cek_code = $this->Products_subcategory_model->get_by_code($this->input->post('code'));
			if ($cek_code) {
				switch ($action) {
					case 'create':
						$valid_input = false;
						break;
					case 'update':
						if ($cek_code->product_subcategory_id!=$this->input->post('id')) {
							$valid_input = false;
						}
						break;
					default: break;
				}
				$msg_err_code = ($valid_input==false) ? 'Code already used.' : '' ;
			}
		}
		
		//validasi
		$this->_rules();

		if ($this->form_validation->run() == FALSE || $valid_input==false) {
			$err_msg = (($action=='create') ? 'Create' : 'Update') .' Record Failed';
			$draw_json = array(
				'status' => 'error',
				'message' => (!empty($msg_err_id)) ? $msg_err_id : $err_msg,
				'error_data' => array(
					'id' => (!empty(form_error('id'))) ? form_error('id') : $msg_err_id,
					'code' => (!empty(form_error('code'))) ? form_error('code') : $msg_err_code,
					'name' => form_error('name'),
				)
			);
		} else {
			$data['name'] = $this->input->post('name',true);
			$data['code'] = (!empty($this->input->post('code'))) ? strtoupper($this->input->post('code',true)) : null;

			//create or update data
			if ($action=='create') {
				$response = $this->Products_subcategory_model->insert($data);
			}
			else{
				$response = $this->Products_subcategory_model->update($this->input->post('id'),$data);
			}

			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => (($action=='create') ? 'Create' : 'Update') .' Record Success'
				);
			}
		}

		//output
		echo format_json($draw_json);
	}

	public function delete($id=null)
	{
		//cek
		$row = $this->Products_subcategory_model->get_by_id($id);
		if ($row) {
			//cek relation
			$row_relation = $this->Products_subcategory_model->check_relation($id);
			if ($row_relation) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Record is Being Used'
				);
			}
			else{
				//hapus data
				$response = $this->Products_subcategory_model->delete($id);
				if ($response) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Delete Record Success'
					);
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Delete Record Failed'
					);
				}
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function _rules() 
	{
		$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('code', 'code', 'trim|max_length[10]|alpha_dash');

		$this->form_validation->set_rules('id', 'id', 'trim');
		$this->form_validation->set_error_delimiters('', '');
	}

}

/* End of file Subcategory.php */
/* Location: ./application/controllers/products/Subcategory.php */