<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_import extends CI_Controller {

	private $filename='UNIQ_Template_-_Import-Product-Catalogue';

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login
		$this->user_role->products('read_access'); //employee role access page

		$this->load->library('CSVReader');
		$this->load->model('products/type/Type_model');
		$this->load->model('products/products_category/Products_category_model');
		$this->load->model('products/products_subcategory/Products_subcategory_model');
		$this->load->model('products/ingridients/Purchase_report_category_model');
		$this->load->model('products/ingridients/Unit_model');
		$this->load->model('products/gratuity/Gratuity_model');
		$this->load->model('outlet/Outlets_model');

		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_catalogue_taxgratuity_model');

		$this->load->model('products/products_catalogue/Products_import_model');
	}

	public function import_template()
	{
		//ambil semua data products
		$result = $this->Products_model->get_all_view();

		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=".$this->filename.".csv");
		header("Content-Transfer-Encoding: binary");



		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output,array(
			"Product Name (*max 30char)",
			"Catalogue Type (equipment/product/ingridient)",
			"Product Type (*max 30 char)",
			"Product Category (*max 30 char)",
			"Product Sub-Category (*max 30 char)",
			"Purchase Report Category (*max 30 char)",
			"Unit (*max 10 char)",
			"Unit Description (*max 30 char)",
			"Stock Management (y/n)",
			"Barcode (*max 30 char)",
			"SKU (*max 30 char)",
			"Buy Price (*numeric)",
			"Sell Price (*numeric)",
			"Voucher (y/n)",
			"Discount (y/n)",
			"Active Menu (all/sales/link/off)",
			"Active on Outlet (y/n)",
		));
		// fputcsv($handle, $fields, "\t");
		// foreach ($result as $data) {
			fputcsv($output, array(
				'My Product 1', 'equipment', 'My Type 1', 'My Category 1', 'My Sub-Category 1', 'Purchase Report 1', 'kg', 'Sample Unit Kilos', 'Y', 'BC00001', 'SKU00001', 10000, 15000, 'Y', 'N', 'all', 'y'
			));
			fputcsv($output, array(
				'My Product 2', 'product', 'My Type 1', 'My Category 2', 'My Sub-Category 1', 'Purchase Report 1', 'gr', 'Sample Unit Gram', 'N', 'BC00002', 'SKU00002', 1500, 2000, 'Y', 'N', 'sales', 'y'
			));
			fputcsv($output, array(
				'My Product 3', 'ingridient', 'My Type 2', 'My Category 1', 'My Sub-Category 2', 'Purchase Report 2', 'kg', 'Sample Unit Kilos', 'Y', 'BC00003', 'SKU00003', 0, 0, 'Y', 'N', 'link', 'y'
			));
		// }
	}

	public function import_process()
	{
		//protect direct acccess
		if (!$this->input->post()) {
			echo "Direct Access Disallowed.";die();
		}

		//init
		$draw_json = array();
		$valid = true; //untuk validasi input
		$valid_data = true; //untuk validasi isi CSV
		$valid_header = true;
		$error_list = array();
		$success_import = 0;

		//data yang dikirim
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');
		$datapost_tax = $this->input->post('tax[]');

		//validasi input
		if ($valid==true) { //cek CSV
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV not found'
				);
			}
		}
		if ($valid==true) { //cek outlet
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}
		
		//validasi header
		if ($valid==true) {
			$import_header = $this->csvreader->header($datapost_file);
			foreach ($import_header as $index => $value) {
				$col = $index+1;

				switch ($index) {
					case 0:
						//cek header kolom 1 harus "Product Name"
						$header_name = 'Product Name';
						$value = substr($value, 0, 12);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 1:
						//cek header kolom 2 harus "Catalogue Type"
						$header_name = 'Catalogue Type';
						$value = substr($value, 0, 14);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 2:
						$header_name = 'Product Type';
						$value = substr($value, 0, 12);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 3:
						$header_name = 'Product Category';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 4:
						$header_name = 'Product Sub-Category';
						$value = substr($value, 0, 20);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 5:
						$header_name = 'Purchase Report Category';
						$value = substr($value, 0, 24);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 6:
						$header_name = 'Unit';
						$value = substr($value, 0, 4);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 7:
						$header_name = 'Unit Description';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 8:
						$header_name = 'Stock Management';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 9:
						$header_name = 'Barcode';
						$value = substr($value, 0, 7);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 10:
						$header_name = 'SKU';
						$value = substr($value, 0, 3);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 11:
						$header_name = 'Buy Price';
						$value = substr($value, 0, 9);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 12:
						$header_name = 'Sell Price';
						$value = substr($value, 0, 10);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 13:
						$header_name = 'Voucher';
						$value = substr($value, 0, 7);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 14:
						$header_name = 'Discount';
						$value = substr($value, 0, 8);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 15:
						$header_name = 'Active Menu';
						$value = substr($value, 0, 11);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 16:
						$header_name = 'Active on Outlet';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					default: break;
				}
			}

			if (count($error_list)>0) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}

		//validasi data (cek data apakah ada yang tidak valid) setiap baris
		if ($valid==true) {
			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			$baris = 0;
			foreach ($result_csv_parse as $row) {
				//get data
				$temp = array(
					'product_name' => $row[0],
					'catalogue_type' => $row[1],
					'product_type' => $row[2],
					'product_category' => $row[3],
					'product_subcategory' => $row[4],
					'purchase_report_category' => $row[5],
					'unit' => $row[6],
					'unit_description' => $row[7],
					'stock_management' => $row[8],
					'barcode' => $row[9],
					'sku' => $row[10],
					//-----
					'price_buy' => $row[11],
					'price_sell' => $row[12],
					'voucher' => $row[13],
					'discount' => $row[14],
					'active_menu' => $row[15],
					'active_outlet' => $row[16],
				);

				//cek product name
				if (empty($temp['product_name'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Name Required.';
				}
				if (strlen($temp['product_name'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Name max. 30 chars. ('.htmlentities($temp['product_name']).')';
				}

				//cek catalogue
				if (empty($temp['catalogue_type'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Catalogue Type Required.';
				}
				else{
					if ($temp['catalogue_type'] != 'equipment' && $temp['catalogue_type'] != 'ingridient' && $temp['catalogue_type'] != 'product') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Catalogue Type Invalid. ('.htmlentities($temp['catalogue_type']).')';
					}
				}

				//cek product type
				if (empty($temp['product_type'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Type Required.';
				}
				if (strlen($temp['product_type'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Type max. 30 chars. ('.htmlentities($temp['product_type']).')';
				}

				//cek product category
				if (empty($temp['product_category'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Category Required.';
				}
				if (strlen($temp['product_category'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Category max. 30 chars. ('.htmlentities($temp['product_category']).')';
				}

				//cek product subcategory
				if (empty($temp['product_subcategory'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Sub-Category Required.';
				}
				if (strlen($temp['product_subcategory'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Sub-Category max. 30 chars. ('.htmlentities($temp['product_subcategory']).')';
				}

				//cek purchase report category
				if (empty($temp['purchase_report_category'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Purchase Report Category Required.';
				}
				if (strlen($temp['purchase_report_category'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Purchase Report Category max. 30 chars. ('.htmlentities($temp['purchase_report_category']).')';
				}

				//cek unit
				if (empty($temp['unit'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Unit Required.';
				}
				if (strlen($temp['unit'])>10) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Unit max. 10 chars. ('.htmlentities($temp['unit']).')';
				}

				//cek unit description
				if (empty($temp['unit_description'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Unit Description Required.';
				}
				if (strlen($temp['unit_description'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Unit Description max. 30 chars. ('.htmlentities($temp['unit_description']).')';
				}

				//cek stock management
				if (empty($temp['stock_management'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Stock Management Required.';
				}
				if (strtolower($temp['stock_management'])!='y' && strtolower($temp['stock_management'])!='n') {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Stock Management Invalid.';
				}

				//cek barcode
				if (empty($temp['barcode'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Barcode Required.';
				}
				if (strlen($temp['barcode'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Barcode max. 30 chars. ('.htmlentities($temp['barcode']).')';
				}

				//cek sku
				if (empty($temp['sku'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': SKU Required.';
				}
				if (strlen($temp['sku'])>30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': SKU max. 30 chars. ('.htmlentities($temp['sku']).')';
				}

				


				//cek price buy
				if (is_numeric($temp['price_buy']) && $temp['price_buy']<0) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Buy Price min. 0. ('.htmlentities($temp['price_buy']).')';
				}
				else{
					if (empty($temp['price_buy']) && !is_numeric($temp['price_buy'])) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Buy Price Required.';
					}
					else{
						if (!is_numeric($temp['price_buy'])) {
							$valid = false;
							$error_list[] = '#'.($baris+1).': Buy Price must numeric. ('.htmlentities($temp['price_buy']).')';
						}
					}
				}

				//cek price sell
				if (is_numeric($temp['price_sell']) && $temp['price_sell']<0) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Sell Price min. 0. ('.htmlentities($temp['price_sell']).')';
				}
				else{
					if (empty($temp['price_sell']) && !is_numeric($temp['price_sell'])) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Sell Price Required.';
					}
					else{
						if (!is_numeric($temp['price_sell'])) {
							$valid = false;
							$error_list[] = '#'.($baris+1).': Sell Price must numeric. ('.htmlentities($temp['price_sell']).')';
						}
					}
				}

				//cek voucher
				if (empty($temp['voucher'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Voucher Required.';
				}
				if (strtolower($temp['voucher'])!='y' && strtolower($temp['voucher'])!='n') {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Voucher Invalid.';
				}

				//cek discount
				if (empty($temp['discount'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Discount Required.';
				}
				if (strtolower($temp['discount'])!='y' && strtolower($temp['discount'])!='n') {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Discount Invalid.';
				}

				//cek active menu
				if (empty($temp['active_menu'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Active Menu Required.';
				}
				else{
					$var_am = strtolower($temp['active_menu']);
					if ($var_am!='all' && $var_am!='sales' && $var_am!='link' && $var_am!='off') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Active Menu Invalid. ('.$temp['active_menu'].')';
					}
				}

				
				
				//cek active outlet
				if (empty($temp['active_outlet'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Active Outlet Required.';
				}
				else{
					if (strtolower($temp['active_outlet'])!='y' && strtolower($temp['active_outlet'])!='n') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Active Outlet Invalid. ('.$temp['active_outlet'].')';
					}
				}
				
				$baris++;
			}

			if (!empty($error_list)) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Data!',
					'error_data' => $error_list
				);
			}
		}

		//proses olah import data
		if ($valid===true) {
			//cek outlet yang dicentang apakah valid atau tidak
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//cek tax yang dicentang apakah valid atau tidak
			$selected_tax = array();
			if (!empty($datapost_tax)) {
				foreach ($datapost_tax as $tax_id) {
					$valid_tax = $this->Gratuity_model->get_by_id($tax_id);
					if ($valid_tax) {
						$selected_tax[] = $tax_id;
					}
				}
			}


			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $row) {
				$temp = array(
					'name'						=> $row[0],
					'catalogue_type'			=> $row[1], 
					'type'						=> $row[2], //$products_type_id
					'category'					=> $row[3], //$products_category_id
					'subcategory' 				=> $row[4], //$products_subcategory_id
					'pure_category'				=> $row[5], //$purchase_report_category_id
					'unit'						=> $row[6], //$unit_id
					'unit_description'			=> $row[7], //varchar 30
					'stock_management'			=> $row[8], //boolean (y/n)
					'barcode'					=> $row[9], //varchar 30
					'sku'						=> $row[10], //varchar 30
					'price_buy'					=> $row[11], //numeric
					'price_sell'				=> $row[12], //numeric
					'voucher'					=> $row[13], //on|off
					'discount'					=> $row[14], //on|off
					'menu_active'				=> $row[15], //on_all|on_sales|on_link|off
					'active_outlet'				=> $row[16], //on|off (y/n)
				);

				//validasikan data (master)
				#cek catalogue type
				$cek_products_type = $this->Type_model->get_by_name($temp['type']); //cari berdasarkan nama
				if ($cek_products_type) {
					//kalau ditemukan, ambil id dari product type
					$products_type_id = $cek_products_type->products_type_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_type_insert = array('name' => $temp['type']);
					$insert_products_type = $this->Type_model->insert($products_type_insert);
					if ($insert_products_type) {
						$products_type_id = $this->db->insert_id();
					}
				}

				#cek products category
				$cek_products_category = $this->Products_category_model->get_by_name($temp['category']);
				if ($cek_products_category) {
					//kalau ditemukan, ambil id dari product category
					$products_category_id = $cek_products_category->product_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_category_insert = array('name' => $temp['category']);
					$insert_products_category = $this->Products_category_model->insert($products_category_insert);
					if ($insert_products_category) {
						$products_category_id = $this->db->insert_id();
					}
				}

				#cek products subcategory
				$cek_products_subcategory = $this->Products_subcategory_model->get_by_name($temp['subcategory']);
				if ($cek_products_subcategory) {
					//kalau ditemukan, ambil id dari product subcategory
					$products_subcategory_id = $cek_products_subcategory->product_subcategory_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_subcategory_insert = array('name' => $temp['subcategory']);
					$insert_products_subcategory = $this->Products_subcategory_model->insert($products_subcategory_insert);
					if ($insert_products_subcategory) {
						$products_subcategory_id = $this->db->insert_id();
					}
				}

				#cek purchase report category
				$cek_purchase_report_category = $this->Purchase_report_category_model->get_by_name($temp['pure_category']);
				if ($cek_purchase_report_category) {
					//kalau ditemukan, ambil id dari purchase report category
					$purchase_report_category_id = $cek_purchase_report_category->purchase_report_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$purchase_report_category_insert = array('name' => $temp['pure_category']);
					$insert_purecategory = $this->Purchase_report_category_model->insert($purchase_report_category_insert);
					if ($insert_purecategory) {
						$purchase_report_category_id = $this->db->insert_id();
					}
				}

				#cek unit
				$cek_unit = $this->Unit_model->get_by_name($temp['unit']);
				if ($cek_unit) {
					//kalau ditemukan, ambil id dari unit
					$unit_id = $cek_unit->unit_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$unit_insert = array('name' => $temp['unit'], 'description' => $temp['unit_description']);
					$insert_unit = $this->Unit_model->insert($unit_insert);
					if ($insert_unit) {
						$unit_id = $this->db->insert_id();
					}
				}

				#cek stock management
				$stock_management = (strtolower($temp['stock_management'])=='y') ? true : false;

				$price_buy = (is_numeric($temp['price_buy'])) ? $temp['price_buy'] : 0;
				$price_sell = (is_numeric($temp['price_sell'])) ? $temp['price_sell'] : 0;

				#cek voucher (statis)
				$voucher = (strtolower($temp['voucher'])=='y') ? 'on' : 'off';

				#cek discount (statis)
				$discount = (strtolower($temp['discount'])=='y') ? 'on' : 'off';

				$active_outlet = (strtolower($temp['active_outlet'])=='y') ? 'on' : 'off';

				#cek menu active
				$menu_active = strtolower($temp['menu_active']);
				switch ($menu_active) {
					case 'all':
						$menu_active = 'on_all';
						break;
					case 'sales':
						$menu_active = 'on_sales';
						break;
					case 'link':
						$menu_active = 'on_link';
						break;
					default:
						$menu_active = 'off';
						break;
				}


				//data yang akan diinsert (master)
				$data_import = array(
					'name' => $temp['name'],
					'catalogue_type' => $temp['catalogue_type'],
					'product_type_fkid' => $products_type_id,
					'product_category_fkid' => $products_category_id,
					'product_subcategory_fkid' => $products_subcategory_id,
					'purchase_report_category_fkid' => $purchase_report_category_id,
					'unit_fkid' => $unit_id,
					'stock_management' => $stock_management,
					'barcode' => (!empty($temp['barcode'])) ? $temp['barcode'] : null,
					'sku' => (!empty($temp['sku'])) ? $temp['sku'] : null,
					'data_status' => 'on',
				);


				//ambil data master products berdasarkan SKU
				$data_product = $this->Products_model->get_by_sku($temp['sku']);
				if ($data_product) {
					//bila data sudah ada, update data (master)
					$products_id = $data_product->product_id;
					$response = $this->Products_model->update($products_id, $data_import);
				}
				else{
					//ambil data master products berdasarkan nama product
					$data_product = $this->Products_model->get_by_name($temp['name']);
					if ($data_product) {
						// $products_id = $data_product->product_id;
						// $response = $this->Products_model->update($products_id, $data_import);

						//revisi koding START
						if ($data_product->sku==$temp['sku']) {
							$response = $this->Products_model->update($products_id, $data_import);
						}
						else{
							$response = $this->Products_model->insert($data_import);
							if ($response) {
								$products_id = $this->db->insert_id(); // ambil primary key
							}
						}
						//revisi koding END
					}
					else{
						//bila data belum ada
						$response = $this->Products_model->insert($data_import);
						if ($response) {
							$products_id = $this->db->insert_id(); // ambil primary key
						}
					}
				}


				//insert ke detail
				# matikan data di detail
				// $data_detaildata_to_off = array('data_status' => 'off');
				// $response = $this->Products_detail_model->update_by_parent_id($products_id, $data_detaildata_to_off); //matikan semua data

				# cek, product sudah ada di outlet atau belum
				foreach ($selected_outlet as $data_outlet_id) {
					$product_detail_id = null; //init var
					$cek_detail = $this->Products_detail_model->get_by_parent_and_outlet($products_id, $data_outlet_id);
					if ($cek_detail) {
						$product_detail_id = $cek_detail->product_detail_id;
						//hidupkan menu di outlet bila sudah ada
						$data_detaildata_to_on = array(
							'price_buy' => $price_buy,
							'price_sell' => $price_sell,
							'voucher' => $voucher,
							'discount' => $discount,
							'active' => $menu_active,
							'data_status' => $active_outlet,
						);
						$response = $this->Products_detail_model->update($cek_detail->product_detail_id, $data_detaildata_to_on);
					}
					else{
						//insert menu untuk outlet
						$data_insert_products_on_outlet = array(
							'product_fkid' => $products_id,
							'outlet_fkid' => $data_outlet_id,
							'price_buy_start' => $price_buy,
							'price_buy' => $price_buy,
							'price_sell' => $price_sell,
							'voucher' => $voucher,
							'discount' => $discount,
							'active' => $menu_active,
						);
						$response = $this->Products_detail_model->insert($data_insert_products_on_outlet);
						$product_detail_id = $this->db->insert_id();
					}

					
					#matikan data di detail tax berdasarkan product detail id
					$tax_detail_update_off = $this->Products_catalogue_taxgratuity_model->update_taxgratuity_datastatus_by_masterdetailid($product_detail_id, 'off');
					//insert ke detail tax
					if (!empty($selected_tax)) {
						foreach ($selected_tax as $tax_id) {
							//cek apakah data sudah ada
							$cek_tax_detail = $this->Products_catalogue_taxgratuity_model->get_taxdetail_by_tax_masterdetail($tax_id,$product_detail_id);
							if ($cek_tax_detail) {
								//jika tax sudah ada (update jadi on)
								$taxdetail_update_on = array('data_status' => 'on');
								$this->Products_catalogue_taxgratuity_model->update_taxdetail_by_tax_masterdetail($tax_id, $product_detail_id, $taxdetail_update_on);
							}
							else{
								//jika tax belum ada (maka insert)
								$taxdetail_insert = array(
									'tax_fkid' => $tax_id,
									'product_detail_fkid' => $product_detail_id,
								);
								$this->Products_catalogue_taxgratuity_model->insert_taxgratuity($taxdetail_insert);
							}
						}
					}//endif: selected tax
					$success_import++;
				}//endforeach: selected outlet
			}//endforeach: parsing data csv

			//response success
			$draw_json = array(
				'status' => 'success',
				'message' => $success_import/count($datapost_outlet).' of '.count($result_csv_parse).' data have been added to '.count($datapost_outlet).' outlets!'
			);
		}



		//output json
		echo format_json($draw_json);
	}

}

/* End of file Product_import.php */
/* Location: ./application/controllers/products/product-catalogue_menu/Product_import.php */