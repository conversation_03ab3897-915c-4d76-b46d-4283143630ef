<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Linkmenu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		$this->user_role->products('read_access'); //employee role access page

		//model
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_linkmenu_model','Linkmenu_model');
		$this->load->model('outlet/Outlets_model'); //form
	}

	//LINKMENU
	public function get_linkmenu_by_outlet($outlet_id=null, $product_id=null)
	{
		//inisialisasi
		$draw_json = array();
		$valid = true;
		$error_message = '';

		//cek outlet
		if ($valid) {
			$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
			if (!$row_outlet) {
				$valid = false;
				$error_message = 'Invalid Outlet';
			}
		}

		//cek product
		if ($valid) {
			$row_product = $this->Products_model->get_by_id($product_id);
			if (!$row_product) {
				$valid = false;
				$error_message = 'Invalid Product';
			}
		}

		//set data
		if ($valid) {
			//ambil data linkmenu
			$data_linkmenu = array(
				'outlet_id' => $outlet_id,
				'product_id' => $product_id
			);
			$result_linkmenu = $this->Linkmenu_model->get_linkmenu_by_outlet($data_linkmenu);
			$data = array();
			foreach ($result_linkmenu->result() as $a) {
				$data[] = array(
					'linkmenu_id' => $a->linkmenu_id,
					'name' => htmlentities($a->name),
					'description' => htmlentities($a->description),
					'order' => $a->order_no,
					'action' => '<span onclick="detailLinkmenu($(this),'.$a->linkmenu_id.')" class="btn btn-success btn-xs">Detail</span> <span onclick="editLinkmenu('.$a->linkmenu_id.')" class="btn btn-warning btn-xs">Edit</span> <span onclick="deleteLinkmenu('.$a->linkmenu_id.')" class="fa fa-trash btn btn-danger btn-xs"></span>'
				);
			}

			//set json
			$draw_json = array(
				'status' => 'success',
				'data' => $data
			);
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => $error_message
			);
		}

		//output
		echo format_json($draw_json);
	}
	public function get_linkmenu_by_productdetail($product_id=null, $product_detail_id=null)
	{
		//inisialisasi
		$draw_json = array();
		$valid = true;
		$error_message = '';

		//cek product
		if ($valid) {
			$row_product = $this->Products_model->get_by_id($product_id);
			if (!$row_product) {
				$valid = false;
				$error_message = 'Invalid Product';
			}
		}

		//set data
		if ($valid) {
			//ambil data linkmenu
			$result_linkmenu = $this->Linkmenu_model->get_linkmenu_by_productdetail($product_detail_id);
			$data = array();
			foreach ($result_linkmenu->result() as $a) {
				$data[] = array(
					'linkmenu_id' => $a->linkmenu_id,
					'name' => htmlentities($a->name),
					'description' => htmlentities($a->description),
					'order' => $a->order_no,
					'multiplechoice' => $a->is_multiplechoice,
					'action' => '<span onclick="detailLinkmenu($(this),'.$a->linkmenu_id.')" class="btn btn-success btn-xs">Detail</span> <span onclick="editLinkmenu('.$a->linkmenu_id.')" class="btn btn-warning btn-xs">Edit</span> <span onclick="deleteLinkmenu('.$a->linkmenu_id.')" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></span>'
				);
			}

			//set json
			$draw_json = array(
				'status' => 'success',
				'data' => $data
			);
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => $error_message
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function get_linkmenu_by_id($linkmenu_id=null, $product_id=null)
	{
		//init
		$valid = true;
		$error_message = '';

		//cek product
		$row_product = $this->Products_model->get_by_id($product_id);
		if (!$row_product) {
			$valid = false;
			$error_message = 'Invalid Product';
		}

		if ($valid) {
			$row = $this->Linkmenu_model->get_linkmenu_by_id($linkmenu_id, $product_id)->row();
			if ($row) {
				$draw_json = array(
					'status' => 'success',
					'data' => array(
						'linkmenu_id'		=> $row->linkmenu_id,
						'name'				=> htmlentities($row->name),
						'description'		=> htmlentities($row->description),
						'multiplechoice'	=> $row->is_multiplechoice,
						'order'				=> $row->order_no,
					)
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Record Not Found'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => $error_message
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function create()
	{
		//init
		$draw_json = array();
		$valid = true;
		$product_id = $this->input->post('productid',true);

		//cek product
		$err_msg['product'] = '';
		$row_product = $this->Products_model->get_by_id($product_id);
		if (!$row_product) {
			$valid = false;
			$err_msg['product'] = 'Invalid Product';
		}

		//cek outlet
		$err_msg['outlet'] = '';
		$selected_outlet = array();
		if (!empty($this->input->post('outlet[]'))) {
			foreach ($this->input->post('outlet[]') as $outlet_id) {
				$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($row_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}
		}
		if (empty($selected_outlet)) {
			$valid = false;
			$err_msg['outlet'] = 'Invalid Outlet';
		}


		$this->form_validation->set_rules('variant', 'variant', 'trim|required');
		$this->_rules();
		if ($this->form_validation->run() === FALSE || $valid == false) {
			$draw_json = array(
				'status' => 'error',
				'message'=> 'Create Record Failed',
				'error_data' => array(
					'product' => (!empty(form_error('productid'))) ? form_error('productid') : $err_msg['product'],
					'name' => form_error('name'),
					'description' => form_error('description'),
					'order' => form_error('order'),
					'multiplechoice' => form_error('multiplechoice'),
					'variant' => form_error('variant'),
					'outlet' => (!empty(form_error('outlet[]'))) ? form_error('outlet[]') : $err_msg['outlet'],
				)
			);
		}
		else {
			$this->db->trans_start();
			$response = false;
			foreach ($selected_outlet as $outlet_id) {
				$data_post_create = array(
					'name' => $this->input->post('name',true),
					'description' => $this->input->post('description',true),
					'order_no' => $this->input->post('order',true),
					'is_multiplechoice' => $this->input->post('multiplechoice',true),
					'product_fkid' => $this->input->post('productid',true),
					'outlet_fkid' => $outlet_id
				);

				//ambil product detail id
				$variant_id = (empty($this->input->post('variant')) || strtolower($this->input->post('variant'))=='null') ? null : $this->input->post('variant');
				$row = $this->Products_detail_model->get_by_product_outlet_variant($data_post_create['product_fkid'], $outlet_id, $variant_id);
				if ($row) {
					//cek linkmenu dengan nama tersebut apakah sudah ada, kalau belum tambahkan. kalau sudah replace
					$rowlink = $this->Linkmenu_model->get_by_name_outlet_productdetail($data_post_create['name'], $outlet_id, $row->product_detail_id);
					if ($rowlink) {
						$response = $this->Linkmenu_model->update_linkmenu($rowlink->linkmenu_id, $data_post_create);
					}else{
						$data_post_create['product_detail_fkid'] = $row->product_detail_id;
						$response = $this->Linkmenu_model->insert_linkmenu($data_post_create);
					}
				}
			}

			$response_transaction = $this->db->trans_complete();
			if ($response && $response_transaction) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Create Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Create Record Error'
				);
			}
		}

		//output
		echo format_json($draw_json);
	}

	public function update()
	{
		$draw_json = array();
		$this->_rules();
		/*custom rules start*/
		$this->form_validation->set_rules('outlet[]', 'outlet', 'trim');
		/*custom rules end*/
		

		if ($this->form_validation->run() === FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message'=> 'Update Record Failed',
				'error_data' => array(
					'name' => form_error('name'),
					'description' => form_error('description'),
					'order' => form_error('order'),
					'multiplechoice' => form_error('multiplechoice'),
					'outlet' => form_error('outlet[]'),
				)
			);
		} else {
			//data yang dikirim
			$data_post = array(
				'id' => $this->input->post('id',true),
				'name' => $this->input->post('name', true),
				'description' => $this->input->post('description', true),
				'order' => $this->input->post('order', true),
				'multiplechoice' => $this->input->post('multiplechoice',true),
				//'outlet' => $this->input->post('outlet[]', true),
				'product_id' => $this->input->post('productid', true),
			);

			//jika action update
			$data_post_update = array(
				'id' => $data_post['id'],
				'name' => $data_post['name'],
				'description' => $data_post['description'],
				'order_no' => $data_post['order'],
				'is_multiplechoice' => $data_post['multiplechoice'],
				'product_fkid' => $data_post['product_id'],
			);

			$response = $this->Linkmenu_model->update_linkmenu($data_post_update['id'], $data_post_update);
			if ($response===true) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Update Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Error'
				);
			}
		}

		echo format_json($draw_json);
	}

	public function delete($linkmenu_id=null, $product_id=null)
	{
		$draw_json = array();
		$valid = true;
		$error_message = '';

		//cek product
		$row_product = $this->Products_model->get_by_id($product_id);
		if (!$row_product) {
			$valid = false;
			$error_message = 'Invalid Product';
		}

		if ($valid) {
			$response = $this->Linkmenu_model->delete_linkmenu($linkmenu_id, $product_id);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message'=> 'Delete Record Success'
				);
			}
			else {
				$draw_json = array(
					'status' => 'error',
					'message'=> 'Delete Record Error'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => $error_message
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function _rules() 
	{
		$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
		$this->form_validation->set_rules('description', 'description', 'trim|max_length[30]');
		$this->form_validation->set_rules('order', 'order no', 'trim|required|is_numeric');
		$this->form_validation->set_rules('multiplechoice', 'multiple choice', 'trim|required|in_list[0,1]', array(
			'in_list' => 'The %s must Yes or No.'
		));
		$this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
		$this->form_validation->set_rules('productid', 'product id', 'trim|required');

		$this->form_validation->set_rules('id', 'linkmenu_id', 'trim');
		$this->form_validation->set_error_delimiters('', '');
	}

	//LINKMENU DETAIL
	public function get_linkmenu_detail($linkmenu_id=null)
	{
		$draw_json = array();
		$data = array();
		$result = $this->Linkmenu_model->get_detail_by_linkmenu_id($linkmenu_id);

		foreach ($result as $a) {
			$temp = array(
				'linkmenu_detail_id' => $a->linkmenu_detail_id,
				'product_detail_id' => $a->product_detail_fkid,
				'product_detail_name' => htmlentities($a->product_name_onlinkdetail),
				'variant_name' => htmlentities($a->variant_name),
				'price_sell' => $a->price_sell_onoutlet,
				'price_add' => $a->price_add,
			);

			array_push($data, $temp);
		}

		$draw_json = array(
			'status' => 'success',
			'data' => $data
		);

		echo format_json($draw_json);
	}

	public function update_linkmenu_detail($linkmenu_detail_id=null)
	{
		//init
		$draw_json = array();
		$valid = true;
		$error_message = '';

		//cek linkmenu detail
		$row = $this->Linkmenu_model->get_linkmenudetail_by_detail_id($linkmenu_detail_id);
		if (!$row) {
			$valid = false;
			$error_message = 'Invalid ID';
		}

		//rules validasi
		$this->form_validation->set_rules('price_add', 'additional price', 'trim|required|is_numeric');
		$this->form_validation->set_rules('linkmenu_id', 'linkmenu id', 'trim|required|is_numeric');
		$this->form_validation->set_error_delimiters('', '');

		//data yang dikirim
		$data_post = array(
			'linkmenu_detail_id' => $linkmenu_detail_id,
			'linkmenu_id' => $this->input->post('linkmenu_id', true),
			'price_add' => $this->input->post('price_add', true),
		);


		if ($this->form_validation->run() == FALSE || $valid == false) {
			$error_message = (!empty($error_message)) ? $error_message : form_error('price_add');
			$error_message = (!empty($error_message)) ? $error_message : form_error('linkmenu_id');
			$draw_json = array(
				'status' => 'error',
				'message' => $error_message,
				'error_data' => array(
					'linkmenu_id' => form_error('linkmenu_id'),
					'price_add' => form_error('price_add'),
				)
			);
		} else {
			$response = $this->Linkmenu_model->update_linkmenudetail($linkmenu_detail_id, $data_post);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Update Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Error'
				);
			}
		}

		//output
		echo format_json($draw_json);
	}

	public function delete_linkmenu_detail($linkmenu_detail_id=null)
	{
		$draw_json = array();

		//cek data
		$row = $this->Linkmenu_model->get_linkmenudetail_by_detail_id($linkmenu_detail_id);
		if ($row) {
			$response = $this->Linkmenu_model->delete_linkmenudetail($linkmenu_detail_id);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Delete Record Update'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		echo format_json($draw_json);
	}

	//ADD PRODUCT TO LINKMENU
	public function datatables_product_outlet($outlet_id=null)
	{
		$jsondata = $this->Linkmenu_model->json_product_by_outlet($outlet_id); //ambil data json datatable
		$json_decode = json_decode($jsondata); //pecah data json

		$dataArray = array();
        foreach ($json_decode->data as $a) {
            //encoding string untuk mencegah XSS injection dengan htmlentities()
            $detail_id = $a->product_detail_id;
        	$id = $a->product_fkid;
        	$name = htmlentities($a->product_name); //encoding string
        	$name = (!empty($a->variant_name)) ? $name.' ('.$a->variant_name.')' : $name;
            $barcode = htmlentities($a->barcode);
            $sku = htmlentities($a->sku);
            $unit_name = htmlentities($a->unit_name);
            $price_sell = $a->price_sell;
        	
            //tombol action pada datatable
        	$action = "<a href='javascript:void(0)' class='actionAddToForm' var-id='{$detail_id}'><span class='btn btn-primary btn-xs'>Add</span></a>";

            //data yang akan ditampilkan dalam json
        	$temp_data = array(
        		'product_detail_id' => $detail_id,
        		'product_fkid' => $id,
        		'product_name' => $name,
        		'primary_product' => htmlentities($a->primary_product),
                'barcode' => $barcode,
                'sku' => $sku,
                'unit_name' => $unit_name,
                'price_sell' => $price_sell,
                'action_link' => $action,
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
	}

	public function add_linkmenu_detail($linkmenu_id=null)
	{
		//inisialisasi variabel
		$draw_json = array();

		//validation rules
		$this->form_validation->set_rules('product_priceadd[]', 'additional price', 'trim|required|is_numeric');
		$this->form_validation->set_error_delimiters('', '');

		if ($this->form_validation->run() == FALSE || empty($linkmenu_id)) {
			$error_message = '';
			if ($linkmenu_id==null || !is_numeric($linkmenu_id)) {
				$error_message = 'Invalid Link Menu';
			}
			$error_message = (empty($error_message) ? form_error('product_priceadd[]') : $error_message);

			$draw_json = array(
				'status' => 'error',
				'message' => $error_message
			);
		} else {
			//echo "ini hasil true";
			$data_post = array(
				'price_add' => $this->input->post('product_priceadd[]', true),
				'outlet_id' => $this->input->post('outlet', true)
			);

			$data_no = 0;
			$success_insert = 0;
			$this->db->trans_start();
			foreach ($data_post['price_add'] as $index => $value) {
				$data_post_create = array(
					'linkmenu_id' => $linkmenu_id,
					'product_detail_id' => $index,
					'price_add' => $value
				);

				//hapus data yang sebelumnya sudah ada
				$this->Linkmenu_model->delete_linkmenudetail_by_linkmenu_productdetail($linkmenu_id, $index);

				//cek apakah benar product ada di outlet
				$response = $this->Linkmenu_model->get_product_onoutlet($index)->row();
				if ($response) {
					//jika benar ada di outlet
					$outlet_id = $response->outlet_fkid;
					if ($outlet_id==$data_post['outlet_id']) {
						$response_insert = $this->Linkmenu_model->add_linkmenu_product($data_post_create);
						if ($response_insert) {
							$success_insert++;
						}
					}
				}

				$data_no++;
			}
			$response_transaction = $this->db->trans_complete();

			if ($data_no==$success_insert && $response_transaction) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Create Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'success',
					'message' => 'Some Record not Added'
				);
			}
			
		}
		echo format_json($draw_json);
	}

}

/* End of file Linkmenu.php */
/* Location: ./application/controllers/products/product-catalogue_menu/Linkmenu.php */