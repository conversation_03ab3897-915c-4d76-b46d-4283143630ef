<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_multipleprice_import extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login
		$this->user_role->products('read_access'); //employee role access page

		$this->load->library('CSVReader');
		$this->load->model('outlet/Outlets_model');
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Multipleprice_model');
	}

	public function import_template()
	{
		//ambil semua data products
		$result = $this->Products_model->get_all_view();

		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=template_import_multipleprice.csv");
		header("Content-Transfer-Encoding: binary");



		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output,array(
			"Product Name", "Qty","Price","Qty","Price","Qty","Price","Qty","Price","Qty","Price","Qty","Price","Qty","Price","Qty","Price","Qty","Price","Qty","Price","Qty","Price",
		));
		// fputcsv($handle, $fields, "\t");
		foreach ($result as $data) {
			fputcsv($output, array($data->product_name));
		}
	}

	public function import_process()
	{
		//protect direct acccess
		if (!$this->input->post()) {
			echo "Direct Access Disallowed.";die();
		}

		//init
		$draw_json = array(); //output format json
		$valid = true; //cek validasi inputan
		$valid_header = true;
		$selected_outlet = array();
		$error_list = array(); //simpan error import
		$valid_import = array();

		//datapost
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');

		//validasi input
		if ($valid==true) { //cek CSV
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV File Not Found'
				);
			}
		}
		if ($valid==true) { //cek outlet
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}

		//validasi header
		if ($valid==true) {
			//validasi header
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			while (($index) < count($import_header)) {
				$col=$index+1;
				$value = $import_header[$index];
				switch ($index) {
					case 0:
						//cek header kolom 1 harus "Product Name"
						$header_name = 'Product Name';
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					default:
						//dinamyc check
						if ($index%2==1) {
							$header_name = 'Qty';
							if ($value!=$header_name) {
								$valid_header = false;
								$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
							}

							//cek qty
							if (empty($import_header[$index+=1])) {
								$valid_header = false;
								$error_list[] = '- Header on column #'.($col+1).' Qty not Found';
							}
						}
						elseif ($index%2==0) {
							$header_name = 'Price';
							if ($value!=$header_name) {
								$valid_header = false;
								$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
							}
						}
						break;
				}//endswitch
				$index++;
			}

			//kumpulkan error yang ada di header
			if ($valid_header===false) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}

		//validasi data (cek data apakah ada yang tidak valid) setiap baris
		if ($valid==true) {
			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);

			$baris = 0;
			foreach ($result_csv_parse as $row) {
				//get data
				$temp = array(
					'product_name' => $row[0],
				);

				//validasi product name
				$row_productname = $this->Products_model->get_by_name($temp['product_name']);
				if ($row_productname) {
					$valid_import[$baris]['product_id'] = $row_productname->product_id;
					$valid_import[$baris]['product_name'] = $row_productname->name;
				}
				else{
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Name not Found ('.htmlentities($row[0]).')';
				}

				$index = 0; //index terakhir yang dipakai sebelum dinamyc header
				$valid_import[$baris]['item'] = array();
				while ((($index+=1) < count($row))) {
					//init
					$productitem_qty = '';
					$productitem_price = '';

					//cek data adalah akhir
					if (empty($row[$index]) && empty($row[$index+1])) {
						//kalau qty dan price kosong, maka cek qty dan price selanjutnya
						$index+=1;
					}
					else{
						//validasi Qty (jumlah barang)
						if ($index%2==1) {
							//validasi Qty
							if (!empty($row[$index]) && !is_numeric($row[$index])) {
								$valid = false;
								$error_list[] = '#'.($baris+1).'-'.($index+1).': Qty must numeric ('.htmlentities($row[$index]).')';
							}
							else{
								if ($row[$index] <= 0) {
									$valid = false;
									$error_list[] = '#'.($baris+1).'-'.($index+1).': Qty must more than 0 ('.htmlentities($row[$index]).')';
								}
								else{
									$productitem_qty = $row[$index];
								}
							}


							//qty
							$index+=1;
							//validasi Price
							if (empty($row[$index]) || !is_numeric($row[$index])) {
								$valid = false;
								$error_list[] = '#'.($baris+1).'-'.($index+1).': Price must numeric ('.htmlentities($row[$index]).')';
							}
							else{
								if ($row[$index] < 0) {
									$valid = false;
									$error_list[] = '#'.($baris+1).'-'.($index+1).': Price cannot less than 0 ('.htmlentities($row[$index]).')';
								}
								else{
									$productitem_price = $row[$index];
								}
							}

							$valid_import[$baris]['item'][] = array(
								'qty' => $productitem_qty,
								'price' => $productitem_price
							);
						}
					}
				}//endwhile
				$baris++;
			}

			if ($valid === false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Import Failed!',
					'error_data' => $error_list
				);
			}
		}//endif: validasi data

		//proses import
		if ($valid==true) {
			//init
			$import_success = 0;
			$import_failed = 0;
			$import_delete = 0;
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//import data sesuai outlet
			foreach ($selected_outlet as $selected_outlet_id) {
				//init
				$product_id = '';
				$product_detail_id = '';
				$item_product_id = '';
				$item_product_detail_id = '';
				$item_product_qty = '';
				$item_product_price = '';

				//data tiap baris
				foreach ($valid_import as $r) {
					//cek apakah product_name tersedia di outlet, kalau tidak tambahkan
					$row_product = $this->Products_detail_model->get_by_product_and_outlet($r['product_id'], $selected_outlet_id);
					if ($row_product) {
						$product_id = $row_product->product_fkid;
						$product_detail_id = $row_product->product_detail_id;
					}
					else{
						$data_productdetail_add = array(
							'product_fkid' => $r['product_id'],
							'outlet_fkid' => $selected_outlet_id,
							'active' => 'on_all',
						);
						$response_productadd = $this->Products_detail_model->insert($data_productdetail_add);
						$product_id = $r['product_id'];
						$product_detail_id = $this->db->insert_id();
					}

					//hapus data lama, dan hapus data breakdown kalau tidak ada item 
					$response_delete = $this->Multipleprice_model->delete_by_product_detail_id($product_detail_id);

					//insert detail breakdown
					$data_multiprice_detail_batch = array(); //init
					if (count($r['item'])>0) {
						foreach ($r['item'] as $ritem) {
							//tambah ke data detail (insert batch)
							$data_multiprice_detail_batch[] = array(
								'product_detail_fkid' => $product_detail_id,
								'qty' => $ritem['qty'],
								'price' => $ritem['price'],
								'data_created' => current_millis(),
								'data_modified' => current_millis(),
								'data_status' => 'on'
							);
						}//endforeach item

						//insert batch detail
						$response_detail = $this->Multipleprice_model->insertbatch($data_multiprice_detail_batch);
						($response_detail) ? $import_success+=1 : $import_failed+=1;
					}
					else{
						//simpan jumlah data dari product_name
						($response_delete) ? $import_delete+=1 : '';
					}
				}//endforeach: baris
			}//endforeach: outlet

			//output import success
			$draw_json['status'] = 'success';
			$draw_json['message'] = ($import_success/count($selected_outlet)).'/'.count($valid_import) .' breakdown data have been imported!';
			if ($import_delete>0) {
				$draw_json['message'] = ($import_success/count($selected_outlet)).' imported and '.($import_delete/count($selected_outlet)).' deleted of '.count($valid_import) .' line!';
			}
		}

		//output
		echo format_json($draw_json);
	}

}

/* End of file Product_multipleprice_import.php */
/* Location: ./application/controllers/products/product-catalogue_menu/Product_multipleprice_import.php */