<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Linkmenu_import extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login
		$this->user_role->products('read_access'); //employee role access page

		$this->load->library('CSVReader');
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_linkmenu_model');
		$this->load->model('outlet/Outlets_model');
	}

	public function import_template()
	{
		//ambil semua data products
		$result = $this->Products_model->get_all();

		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=template_linkmenu.csv");
		header("Content-Transfer-Encoding: binary");



		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output, array("Product Name","Link Name","Link Sort Order","Product to Link","Additional Price","Product to Link","Additional Price","Product to Link","Additional Price","Product to Link","Additional Price","Product to Link","Additional Price","Product to Link","Additional Price","Product to Link","Additional Price","Product to Link","Additional Price","Product to Link","Additional Price"));
		// fputcsv($handle, $fields, "\t");
		foreach ($result as $data) {
			fputcsv($output, array($data->name));
		}
	}

	public function import_process()
	{
		// init
		$draw_json = array();
		$valid = true;
		$selected_outlet = array();
		$error_list = array();
		$baris = 1;
		$kolom = 'A';

		// data post yang dikirim
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');

		/* VALIDASI POST DATA START */
		//cek CSV
		if ($valid==true) {
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV not found'
				);
			}
		}
		//cek outlet
		if ($valid==true) {
			if (!empty($datapost_outlet)) {
				foreach ($datapost_outlet as $outlet_id) {
					$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
					if ($row_outlet) {
						$selected_outlet[] = $outlet_id;
					}
				}
			}
			if (empty($selected_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}
		/* VALIDASI POST DATA END */


		/* VALIDASI HEADER START */
		if ($valid==true) {
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			while (($index) < count($import_header)) {
				$valid_header = true;
				$value = $import_header[$index];
				if ($index>0) {
					++$kolom;
				}


				switch ($index) {
					case 0:
						//cek header kolom 1 harus "Product Name"
						$header_name = 'Product Name';
						$value = substr($value, 0, 12);
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '#'.$kolom.$baris.': Header must '.$header_name.'. ('.$value.')';
						}
						break;
					case 1:
						$header_name = 'Link Name';
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '#'.$kolom.$baris.': Header must '.$header_name.'. ('.$value.')';
						}
						break;
					case 2:
						$header_name = 'Link Sort Order';
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '#'.$kolom.$baris.': Header must '.$header_name.'. ('.$value.')';
						}
						break;
					default:
						//dinamyc check
						if ($index%2==1) {
							$header_name = 'Product to Link';
							if ($value!=$header_name) {
								$valid_header = false;
								$error_list[] = '#'.$kolom.$baris.': Header must '.$header_name.'. ('.$value.')';
							}

							//cek additional price
							if (empty($import_header[$index+=1])) {
								$valid_header = false;
								$kolom++;
								$error_list[] = '#'.$kolom.$baris.': Header Additional Price not found.';
							}
						}
						elseif ($index%2==0) {
							$header_name = 'Additional Price';
							if ($value!=$header_name) {
								$valid_header = false;
								$error_list[] = '#'.$kolom.$baris.': Header must '.$header_name.'. ('.$value.')';
							}
						}
						break;
				}//endswitchs
				$index++;
			}

			//kumpulkan error yang ada di header
			if ((count($error_list)>0) || $valid_header==false) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}//endif: validasi header
		/* VALIDASI HEADER END */


		/* VALIDASI ISI DATA START */
		if ($valid==true) {
			//init
			$valid_import = array();

			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $row) {
				//init
				$kolom = 'A';
				$kolom_index = 0;
				$baris++;
				$product_id = '';
				$variant_id = '';

				//get data
				$temp = array(
					'product_name' => $row[0],
					'link_name' => $row[1],
					'sortorder' => $row[2],
				);

				//validasi product name
				$row_productname = $this->Products_model->get_by_name($temp['product_name']);
				if ($row_productname) {
					$valid_import[$baris]['product_id'] = $row_productname->product_id;
					$valid_import[$baris]['product_name'] = $row_productname->name;
				}
				else{
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Product Name not Found. ('.htmlentities($temp['product_name']).')';
				}

				//validasi link name
				$kolom++;
				$valid_import[$baris]['link_name'] = $temp['link_name'];
				if (empty($temp['link_name'])) {
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Link Name Required. ('.htmlentities($temp['link_name']).')';
				}
				if (strlen($temp['link_name']) > 30) {
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Link Name max. 30 char ('.htmlentities($temp['link_name']).')';
				}

				//validasi link sort order
				$kolom++;
				$valid_import[$baris]['sortorder'] = $temp['sortorder'];
				if (!is_numeric($temp['sortorder'])) {
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Link Sort Order must numeric. ('.htmlentities($temp['sortorder']).')';
				}
				else{
					if ($temp['sortorder'] <= 0) {
						$valid = false;
						$error_list[] = '#'.$kolom.$baris.': Link Sort Order must more than 0. ('.htmlentities($temp['sortorder']).')';
					}
				}


				//validasi dynamic Product to Link & Additional Price
				$index = 2; //index mulainya product to link (kolom ke-tiga)
				$valid_import[$baris]['item'] = array();
				while ((($index+=1) < count($row))) {
					//init
					$kolom++;
					$productlink_id = '';
					$productlink_name = '';
					$productlink_price = '';

					//cek data adalah akhir
					if (empty($row[$index]) && empty($row[$index+1])) {
						//kalau product to link dan additional price kosong, maka cek ptl dan ad selanjutnya
						$index+=1;
					}
					else{
						//validasi Product to Link (product yang akan dimasukkan ke dalam link)
						if ($index%2==1) {
							//product to link
							$row_productlink = $this->Products_model->get_by_name($row[$index]);
							if ($row_productlink) {
								$productlink_id = $row_productlink->product_id;
								$productlink_name = htmlentities($row_productlink->name);
							}
							else{
								$valid = false;
								$error_list[] = '#'.$baris.$kolom.': Product on Product to Link not found. ('.htmlentities($row[$index]).')';
							}



							//additional price
							$index+=1;
							$kolom++;
							//validasi Additional Price
							if (!is_numeric($row[$index])) {
								$valid = false;
								$error_list[] = '#'.$baris.$kolom.': Additional Price must numeric. ('.htmlentities($row[$index]).')';
							}
							else{
								if ($row[$index] < 0) {
									$valid = false;
									$error_list[] = '#'.$baris.$kolom.': Additional Price must more than 0. ('.htmlentities($row[$index]).')';
								}
								else{
									$productlink_price = $row[$index];
								}
							}

							$valid_import[$baris]['item'][] = array(
								'productlink_id' => $productlink_id,
								'productlink_name' => $productlink_name,
								'additional_price' => $productlink_price
							);
						}
					}
				}//endwhile
				$baris++;
			}
			if (count($error_list)>0 || $valid==false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Import Failed!',
					'error_data' => $error_list
				);
			}
		}
		/* VALIDASI ISI DATA END */

		/* IMPORT DATA START */
		$import_data = 0;
		$import_data_success = 0;
		$import_data_failed = 0;
		if ($valid==true) {			
			//import data sesuai outlet
			foreach ($selected_outlet as $selected_outlet_id) {
				//init
				$product_id = '';
				$product_detail_id = '';
				$link_id = '';
				$productlink_id = '';
				$productlink_price = '';

				//data tiap baris
				foreach ($valid_import as $r) {
					//cek apakah product_name tersedia di outlet, kalau tidak tambahkan
					// echo print_r($value);
					//$product_id = $r['product_id'];
					$row_product = $this->Products_detail_model->get_by_product_and_outlet($r['product_id'], $selected_outlet_id);
					if ($row_product) {
						$product_id = $row_product->product_fkid;
						$product_detail_id = $row_product->product_detail_id;
					}
					else{
						$data_productdetail_add = array(
							'product_fkid' => $r['product_id'],
							'outlet_fkid' => $selected_outlet_id,
							'active' => 'on_all',
						);
						$response_productadd = $this->Products_detail_model->insert($data_productdetail_add);
						$product_id = $r['product_id'];
						$product_detail_id = $this->db->insert_id();
					}

					//cek link name, kalau ada hapus semua
					$row_link = $this->Products_linkmenu_model->get_by_name_outlet_product($r['link_name'], $selected_outlet_id, $r['product_id']);
					if ($row_link) {
						$link_id = $row_link->linkmenu_id;

						//update link, (untuk sortorder)
						$row_link_update = array(
							'order_no' => $r['sortorder']
						);
						$this->Products_linkmenu_model->update_by_id($link_id, $row_link_update);

						//delete detail by linkmenu_id (hapus data lama)
						$this->Products_linkmenu_model->delete_detail_by_linkmenu($link_id);
					}
					else{
						//tambahkan link baru
						$datalinkadd = array(
							'name' => $r['link_name'],
							'description' => '-',
							'order_no' => $r['sortorder'],
							'outlet_fkid' => $selected_outlet_id,
							'product_fkid' => $r['product_id'],
						);
						$response_linkadd = $this->Products_linkmenu_model->insert($datalinkadd);
						if ($response_linkadd) {
							$link_id = $this->db->insert_id();
						}
					}


					//insert detail linkmenu
					$data_linkmenu_detail_batch = array();
					if (count($r['item'])>0) {
						foreach ($r['item'] as $rv) {
							$product_detail_id_batch = null;

							//cek product sudah ada di outlet belum
							$row_productdetail = $this->Products_detail_model->get_by_product_and_outlet($rv['productlink_id'], $selected_outlet_id);
							if ($row_productdetail) {
								$product_detail_id_batch = $row_productdetail->product_detail_id;

								//update jadi on link / all
								if ($row_productdetail->active=='on_sales') {
									$data_productdetail_update = array(
										'active' => 'on_all',
										'data_status' => 'on'
									);
									$this->Products_detail_model->update($product_detail_id_batch,$data_productdetail_update);
								}
								elseif ($row_productdetail->active=='off') {
									$data_productdetail_update = array(
										'active' => 'on_link',
										'data_status' => 'on'
									);
									$this->Products_detail_model->update($product_detail_id_batch,$data_productdetail_update);
								}
							}
							else{
								//tambah baru
								$data_detailadd = array(
									'product_fkid' => $rv['productlink_id'],
									'outlet_fkid' => $selected_outlet_id,
									'active' => 'on_link',
								);
								$response_productdetailadd = $this->Products_detail_model->insert($data_detailadd);
								$product_detail_id_batch = $this->db->insert_id();
							}

							//tambah ke data detail (insert batch)
							$data_linkmenu_detail_batch[] = array(
								'linkmenu_fkid' => $link_id,
								'product_detail_fkid' => $product_detail_id_batch,
								'price_add' => $rv['additional_price'],
								'data_created' => current_millis(),
								'data_modified' => current_millis(),
							);
						}//endforeach item

						$response_detail = $this->Products_linkmenu_model->insertbatch_detail($data_linkmenu_detail_batch);
						($response_detail) ? $import_data_success++ : $import_data_failed++;
						$import_data++;
					}
					else{
						//hapus data linkmenu kalau tidak ada item
						$this->Products_linkmenu_model->delete_by_id($link_id);
					}
				}//endforeach: baris
			}//endforeach: outlet

			$draw_json = array(
				'status' => 'success',
				'message' => ($import_data_success/count($selected_outlet)).'/'.count($valid_import) .' link menu data have been imported!'
			);
		}
		/* IMPORT DATA END */

		echo format_json($draw_json);
	}

	public function import_process2()
	{
		//protect direct acccess
		if (!$this->input->post()) {
			echo "Direct Access Disallowed.";die();
		}

		//init
		$draw_json = array(); //output format json
		$valid = true; //cek validasi inputan
		$valid_header = true;
		$selected_outlet = array();
		$error_list = array(); //simpan error import
		$valid_import = array();

		//datapost
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');

		//validasi input
		if ($valid==true) { //cek CSV
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV File Not Found'
				);
			}
		}
		if ($valid==true) { //cek outlet
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}

		//validasi header
		if ($valid==true) {
			//validasi header
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			while (($index) < count($import_header)) {
				$col=$index+1;
				$value = $import_header[$index];
				switch ($index) {
					case 0:
						//cek header kolom 1 harus "Product Name"
						$header_name = 'Product Name';
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 1:
						$header_name = 'Link Name';
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 2:
						$header_name = 'Link Sort Order';
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					default:
						//dinamyc check
						if ($index%2==1) {
							$header_name = 'Product to Link';
							if ($value!=$header_name) {
								$valid_header = false;
								$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
							}

							//cek additional price
							if (empty($import_header[$index+=1])) {
								$valid_header = false;
								$error_list[] = '- Header on column #'.($col+1).' Additional Price not Found';
							}
						}
						elseif ($index%2==0) {
							$header_name = 'Additional Price';
							if ($value!=$header_name) {
								$valid_header = false;
								$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
							}
						}
						break;
				}//endswitch
				$index++;
			}

			//kumpulkan error yang ada di header
			if ($valid_header===false) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}

		//validasi data (cek data apakah ada yang tidak valid) setiap baris
		if ($valid==true) {
			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);

			$baris = 0;
			foreach ($result_csv_parse as $row) {
				//get data
				$temp = array(
					'product_name' => $row[0],
					'link_name' => htmlentities($row[1]),
					'sortorder' => $row[2],
				);

				//validasi product name
				$row_productname = $this->Products_model->get_by_name($temp['product_name']);
				if ($row_productname) {
					$valid_import[$baris]['product_id'] = $row_productname->product_id;
					$valid_import[$baris]['product_name'] = $row_productname->name;
				}
				else{
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Name not Found ('.htmlentities($temp['product_name']).')';
				}

				//validasi link name
				$valid_import[$baris]['link_name'] = $temp['link_name'];
				if (empty($temp['link_name'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Link Name Required ('.htmlentities($temp['link_name']).')';
				}
				if (strlen($temp['link_name']) > 30) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Link Name max. 30 char ('.htmlentities($temp['link_name']).')';
				}

				//validasi link sort order
				$valid_import[$baris]['sortorder'] = $temp['sortorder'];
				if (!is_numeric($temp['sortorder'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Link Sort Order must numeric ('.htmlentities($temp['sortorder']).')';
				}
				else{
					if ($temp['sortorder'] <= 0) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Link Sort Order must more than 0 ('.htmlentities($temp['sortorder']).')';
					}
				}

				$index = 2; //index mulainya product to link
				$valid_import[$baris]['item'] = array();
				while ((($index+=1) < count($row))) {
					//init
					$productlink_id = '';
					$productlink_name = '';
					$productlink_price = '';

					//cek data adalah akhir
					if (empty($row[$index]) && empty($row[$index+1])) {
						//kalau product to link dan additional price kosong, maka cek ptl dan ad selanjutnya
						$index+=1;
					}
					else{
						//validasi Product to Link (product yang akan dimasukkan ke dalam link)
						if ($index%2==1) {
							//product to link
							$row_productlink = $this->Products_model->get_by_name($row[$index]);
							if ($row_productlink) {
								$productlink_id = $row_productlink->product_id;
								$productlink_name = htmlentities($row_productlink->name);
							}
							else{
								$valid = false;
								$error_list[] = '#'.($baris+1).'-'.($index+1).': Product on Product to Link not Found ('.htmlentities($row[$index]).')';
							}



							//additional price
							$index+=1;
							//validasi Additional Price
							if (!is_numeric($row[$index])) {
								$valid = false;
								$error_list[] = '#'.($baris+1).'-'.($index+1).': Additional Price must numeric ('.htmlentities($row[$index]).')';
							}
							else{
								if ($row[$index] < 0) {
									$valid = false;
									$error_list[] = '#'.($baris+1).'-'.($index+1).': Additional Price must more than 0 ('.htmlentities($row[$index]).')';
								}
								else{
									$productlink_price = $row[$index];
								}
							}

							$valid_import[$baris]['item'][] = array(
								'productlink_id' => $productlink_id,
								'productlink_name' => $productlink_name,
								'additional_price' => $productlink_price
							);
						}
					}
				}//endwhile
				$baris++;
			}

			if ($valid === false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Import Failed!',
					'error_data' => $error_list
				);
			}
		}

		//proses import
		if ($valid==true) {
			//init
			$import_success = 0;
			$import_failed = 0;
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//import data sesuai outlet
			foreach ($selected_outlet as $selected_outlet_id) {
				//init
				$product_id = '';
				$product_detail_id = '';
				$link_id = '';
				$productlink_id = '';
				$productlink_price = '';

				//data tiap baris
				foreach ($valid_import as $r) {
					//cek apakah product_name tersedia di outlet, kalau tidak tambahkan
					// echo print_r($value);
					//$product_id = $r['product_id'];
					$row_product = $this->Products_detail_model->get_by_product_and_outlet($r['product_id'], $selected_outlet_id);
					if ($row_product) {
						$product_id = $row_product->product_fkid;
						$product_detail_id = $row_product->product_detail_id;
					}
					else{
						$data_productdetail_add = array(
							'product_fkid' => $r['product_id'],
							'outlet_fkid' => $selected_outlet_id,
							'active' => 'on_all',
						);
						$response_productadd = $this->Products_detail_model->insert($data_productdetail_add);
						$product_id = $r['product_id'];
						$product_detail_id = $this->db->insert_id();
					}

					//cek link name, kalau ada hapus semua
					$row_link = $this->Products_linkmenu_model->get_by_name_outlet_product($r['link_name'], $selected_outlet_id, $r['product_id']);
					if ($row_link) {
						$link_id = $row_link->linkmenu_id;

						//update link, (untuk sortorder)
						$row_link_update = array(
							'order_no' => $r['sortorder']
						);
						$this->Products_linkmenu_model->update_by_id($link_id, $row_link_update);

						//delete detail by linkmenu_id (hapus data lama)
						$this->Products_linkmenu_model->delete_detail_by_linkmenu($link_id);
					}
					else{
						//tambahkan link baru
						$datalinkadd = array(
							'name' => $r['link_name'],
							'description' => '-',
							'order_no' => $r['sortorder'],
							'outlet_fkid' => $selected_outlet_id,
							'product_fkid' => $r['product_id'],
						);
						$response_linkadd = $this->Products_linkmenu_model->insert($datalinkadd);
						if ($response_linkadd) {
							$link_id = $this->db->insert_id();
						}
					}


					//insert detail linkmenu
					$data_linkmenu_detail_batch = array();
					if (count($r['item'])>0) {
						foreach ($r['item'] as $rv) {
							$product_detail_id_batch = null;

							//cek product sudah ada di outlet belum
							$row_productdetail = $this->Products_detail_model->get_by_product_and_outlet($rv['productlink_id'], $selected_outlet_id);
							if ($row_productdetail) {
								$product_detail_id_batch = $row_productdetail->product_detail_id;

								//update jadi on link / all
								if ($row_productdetail->active=='on_sales') {
									$data_productdetail_update = array(
										'active' => 'on_all',
										'data_status' => 'on'
									);
									$this->Products_detail_model->update($product_detail_id_batch,$data_productdetail_update);
								}
								elseif ($row_productdetail->active=='off') {
									$data_productdetail_update = array(
										'active' => 'on_link',
										'data_status' => 'on'
									);
									$this->Products_detail_model->update($product_detail_id_batch,$data_productdetail_update);
								}
							}
							else{
								//tambah baru
								$data_detailadd = array(
									'product_fkid' => $rv['productlink_id'],
									'outlet_fkid' => $selected_outlet_id,
									'active' => 'on_link',
								);
								$response_productdetailadd = $this->Products_detail_model->insert($data_detailadd);
								$product_detail_id_batch = $this->db->insert_id();
							}

							//tambah ke data detail (insert batch)
							$data_linkmenu_detail_batch[] = array(
								'linkmenu_fkid' => $link_id,
								'product_detail_fkid' => $product_detail_id_batch,
								'price_add' => $rv['additional_price'],
								'data_created' => current_millis(),
								'data_modified' => current_millis(),
							);
						}//endforeach item

						$response_detail = $this->Products_linkmenu_model->insertbatch_detail($data_linkmenu_detail_batch);
						($response_detail) ? $import_success+=1 : $import_failed+=1;
					}
					else{
						//hapus data linkmenu kalau tidak ada item
						$this->Products_linkmenu_model->delete_by_id($link_id);
					}
				}//endforeach: baris
			}//endforeach: outlet

			$draw_json = array(
				'status' => 'success',
				'message' => ($import_success/count($selected_outlet)).'/'.count($valid_import) .' link menu data have been imported!'
			);
		}




		//output
		echo format_json($draw_json);
	}

}

/* End of file Linkmenu_import.php */
/* Location: ./application/controllers/products/product-catalogue_menu/Linkmenu_import.php */