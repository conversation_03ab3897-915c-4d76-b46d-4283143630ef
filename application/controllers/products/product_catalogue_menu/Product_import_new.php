<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_import_new extends CI_Controller {

	private $filename='UNIQ-Template-Import-Product-Catalogue';

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login
		$this->user_role->products('read_access'); //employee role access page

		$this->load->library('CSVReader');
		$this->load->model('products/type/Type_model');
		$this->load->model('products/products_category/Products_category_model');
		$this->load->model('products/products_subcategory/Products_subcategory_model');
		$this->load->model('products/ingridients/Purchase_report_category_model');
		$this->load->model('products/ingridients/Unit_model');
		$this->load->model('products/gratuity/Gratuity_model');
		$this->load->model('outlet/Outlets_model');

		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_catalogue_taxgratuity_model');

		$this->load->model('products/products_catalogue/Products_import_model');
		$this->load->model('products/products_catalogue/Products_variant_model');
	}

	public function import_template()
	{
		//ambil semua data products
		$result = $this->Products_model->get_all_view();

		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=".$this->filename.".csv");
		header("Content-Transfer-Encoding: binary");



		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		// fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output, array(
			"Product Name* (max 30char)",
			"Catalogue Type* [equipment/product/ingredient]",
			"Product Type* (max 30 char)",
			"Product Category* (max 30 char)",
			"Product Sub-Category* (max 30 char)",
			"Purchase Report Category* (max 30 char)",
			"Unit* (max 10 char)",
			"Unit Description* (max 30 char)",
			"Stock Management* [y/n]",
			"Barcode (Optional, max 30 char)",
			"SKU (Optional, max 30 char | must be filled if product have SKU)",
			"Transfer Markup Type (Optional: [percent/nominal])",
			"Transfer Markup (Optional: numeric)",
			"Staff Commission Type (Optional: [percent/nominal])",
			"Staff Commission (Optional: numeric)",
			"Customer Commission Type (Optional: [percent/nominal])",
			"Customer Commission (Optional: numeric)",
			"Buy Price* (numeric)",
			"Sell Price* (numeric)",
			"Voucher* [y/n]",
			"Discount* [y/n]",
			"Active Menu* [all/sales/link/off]",
			"Active on Outlet* [y/n]",
			"Have Variant* [y/n]",
			"Variant Name* #1 (Optional: max. 50 char)",
			"Variant SKU #1 (Optional: max. 50 char)",
			"Variant Barcode #1 (Optional: max. 100 char)",
			"Variant Buy Price* #1 (numeric)",
			"Variant Sell Price* #1 (numeric)",
			"Variant Name* #2 (Optional: max. 50 char)",
			"Variant SKU #2 (Optional: max. 50 char)",
			"Variant Barcode #2 (Optional: max. 100 char)",
			"Variant Buy Price* #2 (numeric)",
			"Variant Sell Price* #2 (numeric)",
			"Variant Name* #3 (Optional: max. 50 char)",
			"Variant SKU #3 (Optional: max. 50 char)",
			"Variant Barcode #3 (Optional: max. 100 char)",
			"Variant Buy Price* #3 (numeric)",
			"Variant Sell Price* #3 (numeric)",
			"Variant Name* #4 (Optional: max. 50 char)",
			"Variant SKU #4 (Optional: max. 50 char)",
			"Variant Barcode #4 (Optional: max. 100 char)",
			"Variant Buy Price* #4 (numeric)",
			"Variant Sell Price* #4 (numeric)",
			"Variant Name* #5 (Optional: max. 50 char)",
			"Variant SKU #5 (Optional: max. 50 char)",
			"Variant Barcode #5 (Optional: max. 100 char)",
			"Variant Buy Price* #5 (numeric)",
			"Variant Sell Price* #5 (numeric)",
		));
		// fputcsv($handle, $fields, "\t");
		
		for ($i=1; $i <= 10; $i++) { 
			$uid = current_millis().$i;

			//katalog tipe
			$template['catalogue_type'] = 'product';
			switch ($i%3) {
				case 0:
					$template['catalogue_type'] = 'product';
					break;
				case 1:
					$template['catalogue_type'] = 'equipment';
					break;
				case 2:
					$template['catalogue_type'] = 'ingredient';
					break;
				default:
					$template['catalogue_type'] = 'product';
					break;
			}

			//stok management
			$template['stock_management'] = (($i%5)==1) ? 'Y' : 'N';

			//transfer type
			$template['transfer_type'] = (($i%3)==1) ? 'percent' : 'nominal';

			//active menu
			$template['active_menu'] = (($i%4)==1) ? 'link' : 'all';
			
			//data
			fputcsv($output, array(
				'Gen. Product '.$i, $template['catalogue_type'], 'Gen. Type '.$i, 'Gen. Category '.$i, 'Gen. Sub-Category '.$i, 'Purchase Report '.$i, 'kg', 'Kilos', $template['stock_management'], 'BC'.$uid, 'SKU'.$uid, $template['transfer_type'], $i, '','', '','', 10000, 15000, 'Y', 'N', $template['active_menu'], 'y', 'y',
				'Sample Variant #1','VSKU100001','VBC100001', 1000, 1500, //variant #1
				'Sample Variant #2','VSKU100002','VBC100002', 1000, 1500, //variant #2
				'Sample Variant #3','VSKU100003','VBC100003', 2000, 2500, //variant #3
				'Sample Variant #4','VSKU100004','VBC100004', 2500, 4000, //variant #4
				'Sample Variant #5','VSKU100005','VBC100005', 2500, 5000, //variant #5
			));
		}
	}

	public function import_process()
	{
		// protect direct acccess
		if (!$this->input->post()) {
			echo "Direct Access Disallowed.";die();
		}

		// init
		$draw_json = array(); //output format json
		$valid = true; //cek validasi inputan
		$valid_header = true;
		$selected_outlet = array();
		$error_list = array(); //simpan error import
		$statis_header_count = 0;

		// data post yang dikirim
		$datapost_file = $_FILES['import_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');
		$datapost_tax = $this->input->post('tax[]');

		/* VALIDASI POST DATA START */
		//cek CSV
		if ($valid==true) {
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV not found'
				);
			}
		}
		//cek outlet
		if ($valid==true) {
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}
		/* VALIDASI POST DATA END */



		// VALIDASI HEADER START
		if ($valid==true) {
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			$col = 'A';

			//header list
			$header_name = [
				'Product Name',
				'Catalogue Type',
				'Product Type',
				'Product Category',
				'Product Sub-Category',
				'Purchase Report Category',
				'Unit',
				'Unit Description',
				'Stock Management',
				'Barcode',
				'SKU',
				'Transfer Markup Type',
				'Transfer Markup',
				'Staff Commission Type',
				'Staff Commission',
				'Customer Commission Type',
				'Customer Commission',
				'Buy Price',
				'Sell Price',
				'Voucher',
				'Discount',
				'Active Menu',
				'Active on Outlet',
				'Have Variant',
			];
			$statis_header_count = count($header_name);

			//dynamic header
			for ($i=$statis_header_count; $i < count($import_header); $i++) { 
				switch ($i % 5) {
					case '4':
						$header_name[] = 'Variant Name';
						break;
					case '0':
						$header_name[] = 'Variant SKU';
						break;
					case '1':
						$header_name[] = 'Variant Barcode';
						break;
					case '2':
						$header_name[] = 'Variant Buy Price';
						break;
					case '3':
						$header_name[] = 'Variant Sell Price';
						break;
					default: break;
				}//endswitch: dynamic check
			}
			

			//proses validasi header START
			while (($index) < count($header_name)) {
				//setting kolom huruf
				if ($index>0) $col++;
				
				//cek header valid atau tidak
				$header_file_original = (!empty($import_header[$index])) ? $import_header[$index] : ''; //get header di file
				$header_file = substr($header_file_original, 0, strlen($header_name[$index])); //get header di kodingan ($header_name)
				if ($header_file!=$header_name[$index]) {
					$valid = false;
					$valid_header = false;
					$error_list[] = '- Header on column #'.$col.'1 must '.$header_name[$index].' ('.$header_file.')';
				}

				$index++;
			}
			//proses validasi header END

			//kumpulkan error yang ada di header
			if (count($error_list)>0) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}//endif: validasi header
		// VALIDASI HEADER END


		// VALIDASI ISI DATA START
		if ($valid==true) {
			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);

			$baris = 0;
			foreach ($result_csv_parse as $row) {
				//setting baris huruf
				$row_huruf = 'A';
				$baris_angka=$baris+2;
				$variant_index_begin = ($statis_header_count-1);
				
				//get data satu baris
				$temp = array(
					'product_name'				=> $row[0],
					'catalogue_type'			=> $row[1],
					'product_type'				=> $row[2],
					'product_category'			=> $row[3],
					'product_subcategory'		=> $row[4],
					'purchase_report_category'	=> $row[5],
					'unit'						=> $row[6],
					'unit_description'			=> $row[7],
					'stock_management'			=> $row[8],
					'barcode'					=> $row[9],
					'sku'						=> $row[10],
					'transfer_markup_type'		=> $row[11],
					'transfer_markup'			=> $row[12],
					'commission_staff_type'		=> $row[13],
					'commission_staff'			=> $row[14],
					'commission_customer_type'	=> $row[15],
					'commission_customer'		=> $row[16],
					'price_buy'					=> $row[17],
					'price_sell'				=> $row[18],
					'voucher'					=> $row[19],
					'discount'					=> $row[20],
					'active_menu'				=> $row[21],
					'active_outlet'				=> $row[22],
					'variant_status'			=> $row[23],
				);


				//cek product name
				// $row_huruf++;
				if (empty($temp['product_name'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Product Name Required.';
				}
				else{
					if (strlen($temp['product_name'])>30) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Product Name max. 30 chars. ('.htmlentities($temp['product_name']).')';
					}
				}
				
				//cek catalogue
				$row_huruf++;
				if (empty($temp['catalogue_type'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Catalogue Type Required.';
				}
				else{
					$temp_ori['catalogue_type'] = $temp['catalogue_type'];
					$temp['catalogue_type'] = strtolower($temp['catalogue_type']);
					if ($temp['catalogue_type'] != 'equipment' && $temp['catalogue_type'] != 'ingredient' && $temp['catalogue_type'] != 'product') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Catalogue Type must equipment / ingredient / product. ('.htmlentities($temp_ori['catalogue_type']).')';
					}
				}
				
				//cek product type
				$row_huruf++;
				if (empty($temp['product_type'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Product Type Required.';
				}
				else{
					if (strlen($temp['product_type'])>30) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Product Type max. 30 chars. ('.htmlentities($temp['product_type']).')';
					}
				}
				
				//cek product category
				$row_huruf++;
				if (empty($temp['product_category'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Product Category Required.';
				}
				else{
					if (strlen($temp['product_category'])>30) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Product Category max. 30 chars. ('.htmlentities($temp['product_category']).')';
					}
				}
				
				//cek product subcategory
				$row_huruf++;
				if (empty($temp['product_subcategory'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Product Sub-Category Required.';
				}
				else{
					if (strlen($temp['product_subcategory'])>30) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Product Sub-Category max. 30 chars. ('.htmlentities($temp['product_subcategory']).')';
					}
				}
				
				//cek purchase report category
				$row_huruf++;
				if (empty($temp['purchase_report_category'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Purchase Report Category Required.';
				}
				else{
					if (strlen($temp['purchase_report_category'])>30) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Purchase Report Category max. 30 chars. ('.htmlentities($temp['purchase_report_category']).')';
					}
				}
				
				//cek unit
				$row_huruf++;
				if (empty($temp['unit'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Unit Required.';
				}
				else{
					if (strlen($temp['unit'])>10) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Unit max. 10 chars. ('.htmlentities($temp['unit']).')';
					}
				}
				
				//cek unit description
				$row_huruf++;
				if (empty($temp['unit_description'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Unit Description Required.';
				}
				else{
					if (strlen($temp['unit_description'])>30) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Unit Description max. 30 chars. ('.htmlentities($temp['unit_description']).')';
					}
				}
				
				//cek stock management
				$row_huruf++;
				if (empty($temp['stock_management'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Stock Management Required.';
				}
				else{
					if (strtolower($temp['stock_management'])!='y' && strtolower($temp['stock_management'])!='n') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Stock Management must y/n.';
					}
				}
				
				//cek barcode
				$row_huruf++;
				if (strlen($temp['barcode'])>30) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Barcode max. 30 chars. ('.htmlentities($temp['barcode']).')';
				}
				
				//cek sku
				$row_huruf++;
				// if (empty($temp['sku'])) {
				// 	$valid = false;
				// 	$error_list[] = '#'.($row_huruf).$baris_angka.': SKU Required.';
				// }
				// else{
					if (strlen($temp['sku'])>30) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': SKU max. 30 chars. ('.htmlentities($temp['sku']).')';
					}
				// }
				
				//TRANSFER MARKUP
				$row_huruf++;
				if (!empty($temp['transfer_markup_type'])) {
					$temp_ori['transfer_markup_type'] = $temp['transfer_markup_type'];
					$temp['transfer_markup_type'] = strtolower($temp['transfer_markup_type']);

					//cek transfer markup type
					if ($temp['transfer_markup_type']!='percent' && $temp['transfer_markup_type']!='nominal') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Transfer Markup Type must percent/nominal. ('.htmlentities($temp_ori['transfer_markup_type']).')';
					}

					//cek transfer markup
					$row_huruf++;
					if (is_numeric($temp['transfer_markup']) && $temp['transfer_markup']<0) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Transfer Markup min. 0. ('.htmlentities($temp['transfer_markup']).')';
					}
					else{
						if (empty($temp['transfer_markup']) && !is_numeric($temp['transfer_markup'])) {
							$valid = false;
							$error_list[] = '#'.($row_huruf).$baris_angka.': Transfer Markup Required.';
						}
						else{
							if (!is_numeric($temp['transfer_markup'])) {
								$valid = false;
								$error_list[] = '#'.($row_huruf).$baris_angka.': Transfer Markup must numeric. ('.htmlentities($temp['transfer_markup']).')';
							}
						}
					}
				} else { $row_huruf++; }


				//STUFF COMMISSION
				$row_huruf++;
				if (!empty($temp['commission_staff_type'])) {
					$temp_ori['commission_staff_type'] = $temp['commission_staff_type'];
					$temp['commission_staff_type'] = strtolower($temp['commission_staff_type']);

					//cek staff commission type
					if ($temp['commission_staff_type']!='percent' && $temp['commission_staff_type']!='nominal') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Staff Commission Type must percent/nominal. ('.htmlentities($temp_ori['commission_staff_type']).')';
					}

					//cek staff commission
					$row_huruf++;
					if (is_numeric($temp['commission_staff']) && $temp['commission_staff']<0) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Staff Commission min. 0. ('.htmlentities($temp['commission_staff']).')';
					}
					else{
						if (empty($temp['commission_staff']) && !is_numeric($temp['commission_staff'])) {
							$valid = false;
							$error_list[] = '#'.($row_huruf).$baris_angka.': Staff Commission Required.';
						}
						else{
							if (!is_numeric($temp['commission_staff'])) {
								$valid = false;
								$error_list[] = '#'.($row_huruf).$baris_angka.': Staff Commission must numeric. ('.htmlentities($temp['commission_staff']).')';
							}
						}
					}
				} else { $row_huruf++; }


				//CUSTOMER COMMISSION
				$row_huruf++;
				if (!empty($temp['commission_customer_type'])) {
					$temp_ori['commission_customer_type'] = $temp['commission_customer_type'];
					$temp['commission_customer_type'] = strtolower($temp['commission_customer_type']);

					//cek staff commission type
					if ($temp['commission_customer_type']!='percent' && $temp['commission_customer_type']!='nominal') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Customer Commission Type must percent/nominal. ('.htmlentities($temp_ori['commission_customer_type']).')';
					}

					//cek customer commission
					$row_huruf++;
					if (is_numeric($temp['commission_customer']) && $temp['commission_customer']<0) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Customer Commission min. 0. ('.htmlentities($temp['commission_customer']).')';
					}
					else{
						if (empty($temp['commission_customer']) && !is_numeric($temp['commission_customer'])) {
							$valid = false;
							$error_list[] = '#'.($row_huruf).$baris_angka.': Customer Commission Required.';
						}
						else{
							if (!is_numeric($temp['commission_customer'])) {
								$valid = false;
								$error_list[] = '#'.($row_huruf).$baris_angka.': Customer Commission must numeric. ('.htmlentities($temp['commission_customer']).')';
							}
						}
					}
				} else { $row_huruf++; }



				//cek price buy
				$row_huruf++;
				if (is_numeric($temp['price_buy']) && $temp['price_buy']<0) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Buy Price min. 0. ('.htmlentities($temp['price_buy']).')';
				}
				else{
					if (empty($temp['price_buy']) && !is_numeric($temp['price_buy'])) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Buy Price Required.';
					}
					else{
						if (!is_numeric($temp['price_buy'])) {
							$valid = false;
							$error_list[] = '#'.($row_huruf).$baris_angka.': Buy Price must numeric. ('.htmlentities($temp['price_buy']).')';
						}
					}
				}

				//cek price sell
				$row_huruf++;
				if (is_numeric($temp['price_sell']) && $temp['price_sell']<0) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Sell Price min. 0. ('.htmlentities($temp['price_sell']).')';
				}
				else{
					if (empty($temp['price_sell']) && !is_numeric($temp['price_sell'])) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).': Sell Price Required.';
					}
					else{
						if (!is_numeric($temp['price_sell'])) {
							$valid = false;
							$error_list[] = '#'.($row_huruf).': Sell Price must numeric. ('.htmlentities($temp['price_sell']).')';
						}
					}
				}

				//cek voucher
				$row_huruf++;
				if (empty($temp['voucher'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Voucher Required.';
				}
				else{
					if (strtolower($temp['voucher'])!='y' && strtolower($temp['voucher'])!='n') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).': Voucher must y/n.';
					}
				}
				
				//cek discount
				$row_huruf++;
				if (empty($temp['discount'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Discount Required.';
				}
				else{
					if (strtolower($temp['discount'])!='y' && strtolower($temp['discount'])!='n') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Discount must y/n.';
					}
				}
				
				//cek active menu
				$row_huruf++;
				if (empty($temp['active_menu'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Active Menu Required.';
				}
				else{
					$var_am = strtolower($temp['active_menu']);
					if ($var_am!='all' && $var_am!='sales' && $var_am!='link' && $var_am!='off') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Active Menu must all/sales/link/off. ('.$temp['active_menu'].')';
					}
				}

				//cek active outlet
				$row_huruf++;
				if (empty($temp['active_outlet'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Active Outlet Required.';
				}
				else{
					if (strtolower($temp['active_outlet'])!='y' && strtolower($temp['active_outlet'])!='n') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Active Outlet must y/n. ('.$temp['active_outlet'].')';
					}
				}

				//cek have variant / variant_status
				$row_huruf++;
				if (empty($temp['variant_status'])) {
					$valid = false;
					$error_list[] = '#'.($row_huruf).$baris_angka.': Have Variant Required.';
				}
				else{
					if (strtolower($temp['variant_status'])!='y' && strtolower($temp['variant_status'])!='n') {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Have Variant must y/n. ('.$temp['variant_status'].')';
					}
				}
				
				
				
				
				if ($temp['variant_status']=='y') {
					//init variant variable
					$index = $variant_index_begin; //index terakhir yang dipakai sebelum dinamyc header
					$count_variant = 0;
					$jumlah_kolom_variant = 5; //jumlah kolom satu data variant

					//check data antar variant START
					$temp_unique_variant_sku = array(); //menampung SKU dari CSV
					$temp_unique_variant_sku_db = array(); //menampung SKU dari DB
					$temp_product_id = $this->Products_model->get_by_name($temp['product_name']);
					if ($temp_product_id) {
						//cek SKU
						if ($temp_product_id->sku == $temp['sku']) {
							//cek variant SKU
							$temp_unique_variant_sku_db = $this->Products_variant_model->get_by_product($temp_product_id->product_id);
						}
					}
					else{
						//cek by SKU
						$temp_product_id = $this->Products_model->get_by_sku($temp['sku']);
						if ($temp_product_id) {
							$temp_unique_variant_sku_db = $this->Products_variant_model->get_by_product($temp_product_id->product_id);
						}
					}

					while ((($index+=1) < count($row))) {
						//init
						$row_huruf++;
						$variant_have_data = false; //variable untuk cek satu data variant
						$temp_variant_name = '';

						//cek apakah satu data variant itu kosong?
						for ($vi=0; $vi < $jumlah_kolom_variant; $vi++) { 
							$variant_have_data = (empty($row[$index+$vi])) ? $variant_have_data : true;
						}

						//jika data satu variant kosong semua, cek data variant selanjutnya (next while)
						if ($variant_have_data==false) {
							$index+=$jumlah_kolom_variant; //skip index

							//increment kolom HURUF
							$row_huruf++;
							$row_huruf++;
							$row_huruf++;
							$row_huruf++;
							$row_huruf++;

						} else {
							$count_variant++;

							//dinamyc check
							switch ($index % $jumlah_kolom_variant) {
								//variant name
								case '4':
									//cek variant name
									if (empty($row[$index])) {
										$valid = false;
										$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Name Required.';
									}
									if (!empty($row[$index]) && strlen($row[$index])>50) {
										$valid = false;
										$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Name max. 50 char. ('.$row[$index].')';
									}
									$temp_variant_name = $row[$index];
									break;
								case '0':
									//cek variant SKU
									if (strlen($row[$index])>50) {
										$valid = false;
										$error_list[] = '#'.($row_huruf).$baris_angka.': Variant SKU max. 50 char. ('.$row[$index].')';
									}

									//cek unique SKU
									if (!empty($row[$index]) && !empty($temp_unique_variant_sku)) {
										//cek SKU antar variant di file
										foreach ($temp_unique_variant_sku as $key => $value) {
											if ($row[$index]==$key) {
												$valid = false;
												$error_list[] = '#'.($row_huruf).$baris_angka.': Duplicate Variant SKU. ('.$row[$index].')';
											}
										}

										//cek SKU antar variant di DB (kalau pernah insert)
										if ($temp_product_id) {
											foreach ($temp_unique_variant_sku_db as $key => $value) {
												if ($value->variant_sku == $row[$index]) {
													if ($value->variant_name != $row[$index-1]) {
														$valid = false;
														$error_list[] = '#'.($row_huruf).$baris_angka.': SKU already used on '.$row[$index-1].'. ('.$row[$index].')';
													}
												}
											}
										}
									}

									$temp_unique_variant_sku[$row[$index]] = $temp_variant_name;
									break;
								case '1':
									//cek variant Barcode
									if (strlen($row[$index])>100) {
										$valid = false;
										$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Barcode max. 100 char.';
									}
									break;
								case '2':
									//cek variant price buy
									if (is_numeric($row[$index]) && $row[$index]<0) {
										$valid = false;
										$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Buy Price min. 0. ('.htmlentities($row[$index]).')';
									}
									else{
										if (empty($row[$index]) && !is_numeric($row[$index])) {
											$valid = false;
											$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Buy Price Required.';
										}
										else{
											if (!is_numeric($row[$index])) {
												$valid = false;
												$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Buy Price must numeric. ('.htmlentities($row[$index]).')';
											}
										}
									}
									break;
								case '3':
									//cek variant price sell
									if (is_numeric($row[$index]) && $row[$index]<0) {
										$valid = false;
										$error_list[] = '#'.($row_huruf).': Variant Sell Price min. 0. ('.htmlentities($row[$index]).')';
									}
									else{
										if (empty($row[$index]) && !is_numeric($row[$index])) {
											$valid = false;
											$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Sell Price Required.';
										}
										else{
											if (!is_numeric($row[$index])) {
												$valid = false;
												$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Sell Price must numeric. ('.htmlentities($row[$index]).')';
											}
										}
									}
									break;
								default: break;
							}//endswitch: dynamic check
						}
					}//endwhile

					//bila satu tidak ada variant tapi variant_status=='y'
					if ($count_variant==0) {
						$valid = false;
						$error_list[] = '#'.($row_huruf).$baris_angka.': Variant Required.';
					}
				}//endif: have variant

				$baris++;
			}

			if ($valid === false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Import Failed!',
					'error_data' => $error_list
				);
			}
		}//endif: validasi data import
		// VALIDASI ISI DATA END


		// proses import data (otw)
		//proses olah import data
		if ($valid===true) {
			//cek outlet yang dicentang apakah valid atau tidak
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//cek tax yang dicentang apakah valid atau tidak
			$selected_tax = array();
			if (!empty($datapost_tax)) {
				foreach ($datapost_tax as $tax_id) {
					$valid_tax = $this->Gratuity_model->get_by_id($tax_id);
					if ($valid_tax) {
						$selected_tax[] = $tax_id;
					}
				}
			}


			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $baris => $row) {
				$temp = array(
					'name'						=> $this->security->xss_clean($row[0]), //product_name
					'catalogue_type'			=> strtolower($row[1]), //catalogue_type
					'type'						=> $this->security->xss_clean($row[2]), //$products_type_id
					'category'					=> $this->security->xss_clean($row[3]), //$products_category_id
					'subcategory' 				=> $this->security->xss_clean($row[4]), //$products_subcategory_id
					'pure_category'				=> $this->security->xss_clean($row[5]), //$purchase_report_category_id
					'unit'						=> $this->security->xss_clean($row[6]), //$unit_id
					'unit_description'			=> $this->security->xss_clean($row[7]), //varchar 30
					'stock_management'			=> $this->security->xss_clean($row[8]), //boolean (y/n)
					'barcode'					=> $this->security->xss_clean($row[9]), //varchar 30
					'sku'						=> $this->security->xss_clean($row[10]), //varchar 30
					'transfer_markup_type'		=> $row[11],
					'transfer_markup'			=> $row[12],
					'commission_staff_type'		=> $row[13],
					'commission_staff'			=> $row[14],
					'commission_customer_type'	=> $row[15],
					'commission_customer'		=> $row[16],
					'price_buy'					=> $row[17], //numeric
					'price_sell'				=> $row[18], //numeric
					'voucher'					=> $row[19], //on|off
					'discount'					=> $row[20], //on|off
					'active_menu'				=> $row[21], //on_all|on_sales|on_link|off
					'active_outlet'				=> $row[22], //on|off (y/n)
					'variant_status'			=> strtolower($row[23]),
				);


				
				//validasikan data (master)
				#cek catalogue type
				$cek_products_type = $this->Type_model->get_by_name($temp['type']); //cari berdasarkan nama type
				if ($cek_products_type) {
					//kalau ditemukan, ambil id dari product type
					$products_type_id = $cek_products_type->products_type_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_type_insert = array('name' => $temp['type']);
					$insert_products_type = $this->Type_model->insert($products_type_insert);
					if ($insert_products_type) {
						$products_type_id = $this->db->insert_id();
					}
				}

				#cek products category
				$cek_products_category = $this->Products_category_model->get_by_name($temp['category']);
				if ($cek_products_category) {
					//kalau ditemukan, ambil id dari product category
					$products_category_id = $cek_products_category->product_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_category_insert = array('name' => $temp['category']);
					$insert_products_category = $this->Products_category_model->insert($products_category_insert);
					if ($insert_products_category) {
						$products_category_id = $this->db->insert_id();
					}
				}

				#cek products subcategory
				$cek_products_subcategory = $this->Products_subcategory_model->get_by_name($temp['subcategory']);
				if ($cek_products_subcategory) {
					//kalau ditemukan, ambil id dari product subcategory
					$products_subcategory_id = $cek_products_subcategory->product_subcategory_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_subcategory_insert = array('name' => $temp['subcategory']);
					$insert_products_subcategory = $this->Products_subcategory_model->insert($products_subcategory_insert);
					if ($insert_products_subcategory) {
						$products_subcategory_id = $this->db->insert_id();
					}
				}

				#cek purchase report category
				$cek_purchase_report_category = $this->Purchase_report_category_model->get_by_name($temp['pure_category']);
				if ($cek_purchase_report_category) {
					//kalau ditemukan, ambil id dari purchase report category
					$purchase_report_category_id = $cek_purchase_report_category->purchase_report_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$purchase_report_category_insert = array('name' => $temp['pure_category']);
					$insert_purecategory = $this->Purchase_report_category_model->insert($purchase_report_category_insert);
					if ($insert_purecategory) {
						$purchase_report_category_id = $this->db->insert_id();
					}
				}

				#cek unit
				$cek_unit = $this->Unit_model->get_by_name($temp['unit']);
				if ($cek_unit) {
					//kalau ditemukan, ambil id dari unit
					$unit_id = $cek_unit->unit_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$unit_insert = array('name' => $temp['unit'], 'description' => $temp['unit_description']);
					$insert_unit = $this->Unit_model->insert($unit_insert);
					if ($insert_unit) {
						$unit_id = $this->db->insert_id();
					}
				}


				#cek stock management (statis)
				$stock_management = (strtolower($temp['stock_management'])=='y') ? true : false;

				$price_buy = (is_numeric($temp['price_buy'])) ? $temp['price_buy'] : 0;
				$price_sell = (is_numeric($temp['price_sell'])) ? $temp['price_sell'] : 0;

				#cek voucher (statis)
				$voucher = (strtolower($temp['voucher'])=='y') ? 'on' : 'off';

				#cek discount (statis)
				$discount = (strtolower($temp['discount'])=='y') ? 'on' : 'off';

				#cek menu active
				$menu_active = strtolower($temp['active_menu']);
				switch ($menu_active) {
					case 'all':
						$menu_active = 'on_all';
						break;
					case 'sales':
						$menu_active = 'on_sales';
						break;
					case 'link':
						$menu_active = 'on_link';
						break;
					default:
						$menu_active = 'off';
						break;
				}

				#active outlet
				$active_outlet = (strtolower($temp['active_outlet'])=='y') ? 'on' : 'off';




				

				
				//data untuk MASTER
				$data_import = array(
					'name'							=> $temp['name'],
					'catalogue_type'				=> strtolower($temp['catalogue_type']),
					'product_type_fkid'				=> $products_type_id,
					'product_category_fkid'			=> $products_category_id,
					'product_subcategory_fkid'		=> $products_subcategory_id,
					'purchase_report_category_fkid' => $purchase_report_category_id,
					'unit_fkid'						=> $unit_id,
					'stock_management'				=> $stock_management,
					'barcode'						=> (!empty($temp['barcode'])) ? $temp['barcode'] : null,
					'sku'							=> (!empty($temp['sku'])) ? $temp['sku'] : null,
					'data_status'					=> 'on',
				);

				//ambil data master products berdasarkan SKU
				$data_product = $this->Products_model->get_by_sku($temp['sku']);
				if ($data_product) {
					//bila data sudah ada, update data (master)
					$products_id = $data_product->product_id;
					$response = $this->Products_model->update($products_id, $data_import);
				}
				else{
					//ambil data master products berdasarkan nama product
					$data_product = $this->Products_model->get_by_name_sku($temp['name'], $temp['sku']);
					if ($data_product) {
						// $products_id = $data_product->product_id;
						// $response = $this->Products_model->update($products_id, $data_import);

						//revisi koding START
						$sku_db = (empty($data_product->sku) || $data_product->sku=="") ? "" : $data_product->sku;
						$sku_file = (empty($temp['sku']) || $temp['sku']=="") ? "" : $temp['sku'];
						if ($sku_db==$sku_file) {
							$products_id = $data_product->product_id;
							$response = $this->Products_model->update($products_id, $data_import);
						}
						else{
							$response = $this->Products_model->insert($data_import);
							if ($response) {
								$products_id = $this->db->insert_id(); // ambil primary key
							}
						}
						//revisi koding END
					}
					else{
						//bila data belum ada
						$response = $this->Products_model->insert($data_import);
						if ($response) {
							$products_id = $this->db->insert_id(); // ambil primary key
						}
					}
				}



				
				//insert ke detail
				# matikan data di detail
				// $response = $this->Products_detail_model->update_by_parent_id($products_id, array(
				// 	'data_status' => 'off'
				// )); //matikan semua data

				# cek, product sudah ada di outlet atau belum
				foreach ($selected_outlet as $data_outlet_id) {
					//matikan product detail berdasarkan product+outlet yang dicentang
					$this->Products_detail_model->update_by_product_and_outlet($products_id, $data_outlet_id, array(
						'data_status' => 'off'
					));

					//init var
					$inserted_product_detail_id = array();
					$product_detail_id = null;
					$variant_id = null;



					//data untuk MASTER DETAIL
					$data_import_master_detail = array(
						'product_fkid'			=> $products_id,
						'outlet_fkid'			=> $data_outlet_id,
						'transfer_markup_type'	=> $temp['transfer_markup_type'],
						'transfer_markup'		=> $temp['transfer_markup'],
						'commission_staff_type' => $temp['commission_staff_type'],
						'commission_staff'		=> $temp['commission_staff'],
						'commission_customer_type' => $temp['commission_customer_type'],
						'commission_customer'	=> $temp['commission_customer'],
						'price_buy_start'		=> $price_buy,
						'price_buy'				=> $price_buy,
						'price_sell'			=> $price_sell,
						'voucher'				=> $voucher,
						'discount'				=> $discount,
						'active'				=> $menu_active,
					);


					//cek pakai variant atau tidak
					if (strtolower($temp['variant_status'])=='y') {
						$index = ($statis_header_count-1); //index terakhir yang dipakai sebelum dinamyc header
						$variant_pb = 0;
						$variant_ps = 0;



						while ((($index+=1) < count($row))) {
							//inisialisasi
							$variant_id = null;


							//cek data adalah akhir
							if (empty($row[$index]) && empty($row[$index+1]) && empty($row[$index+2])) {
								//kalau variant name, variant price buy, variant price sell kosong
								//maka cek variant name, variant price buy, variant price sell selanjutnya
								$index+=4;
							}
							else{
								//cek variant
								$variant_name 		= $row[$index]; $index++;
								$variant_sku		= (!empty($row[$index])) ? $row[$index] : null; $index++;
								$variant_barcode	= (!empty($row[$index])) ? $row[$index] : null; $index++;
								$variant_pb			= (!empty($row[$index])) ? $row[$index] : 0; $index++;
								$variant_ps			= (!empty($row[$index])) ? $row[$index] : 0; // $index++;
								
								

								

								//cek variant name sudah ada atau belum
								$row_variant_name = $this->Products_variant_model->get_by_variantname_product($variant_name, $products_id);
								if ($row_variant_name) {
									$variant_id = $row_variant_name->variant_id;

									//update variant name
									$this->Products_variant_model->update($variant_id, array(
										'variant_name'		=> $variant_name,
										'variant_sku'		=> $variant_sku,
										'variant_barcode'	=> $variant_barcode,
									));
								}
								else{
									//insert variant name
									$response_variant = $this->Products_variant_model->insert(array(
										'variant_name'		=> $variant_name,
										'variant_sku'		=> $variant_sku,
										'variant_barcode'	=> $variant_barcode,
										'product_fkid'		=> $products_id,
									));
									$variant_id = $this->db->insert_id();
								}


								/* simpan variant */
								//remake data variant
								//data untuk MASTER DETAIL
								// $data_import_master_detail = array(
								// 	'product_fkid'			=> $products_id,
								// 	'outlet_fkid'			=> $data_outlet_id,
								// 	'transfer_markup_type'	=> $temp['transfer_markup_type'],
								// 	'transfer_markup'		=> $temp['transfer_markup'],
								// 	'price_buy_start'		=> $variant_pb,
								// 	'price_buy'				=> $variant_pb,
								// 	'price_sell'			=> $variant_ps,
								// 	'voucher'				=> $voucher,
								// 	'discount'				=> $discount,
								// 	'active'				=> $menu_active,
								// 	'variant_fkid'			=> $variant_id,
								// 	'data_status'			=> 'on'
								// );


								//cek variant di outlet
								$row_product_outlet_variant = $this->Products_detail_model->get_by_product_outlet_variant($products_id, $data_outlet_id, $variant_id);
								if ($row_product_outlet_variant) {
									//update data
									$data_import_master_detail['price_buy'] = $variant_pb;
									$data_import_master_detail['price_sell'] = $variant_ps;
									$data_import_master_detail['variant_fkid'] = $variant_id;
									$data_import_master_detail['data_status'] = 'on';
									$this->Products_detail_model->update($row_product_outlet_variant->product_detail_id, $data_import_master_detail);

									$inserted_product_detail_id[] = $row_product_outlet_variant->product_detail_id;
								}
								else{
									//insert data
									$data_import_master_detail['price_buy_start'] = $variant_pb;
									$data_import_master_detail['price_buy'] = $variant_pb;
									$data_import_master_detail['price_sell'] = $variant_ps;
									$data_import_master_detail['variant_fkid'] = $variant_id;
									$this->Products_detail_model->insert($data_import_master_detail);

									$inserted_product_detail_id[] = $this->db->insert_id();
								}
							}//verify empty
						}//endwhile (status=y)

						//set off yang variant_fkid=null
						$this->Products_detail_model->update_by_product_outlet_variant($products_id, $data_outlet_id, null, array(
							'data_status' => 'off'
						));
					}
					else{
						//cek product on outlet by product
						$cek_detail = $this->Products_detail_model->get_by_product_outlet_variant($products_id, $data_outlet_id, null);
						if ($cek_detail) {
							//update data dan hidupkan menu di outlet bila sudah ada
							$data_detaildata_to_on = array(
								'transfer_markup_type'		=> $temp['transfer_markup_type'],
								'transfer_markup'			=> $temp['transfer_markup'],
								'commission_staff_type'		=> $temp['commission_staff_type'],
								'commission_staff'			=> $temp['commission_staff'],
								'commission_customer_type'	=> $temp['commission_customer_type'],
								'commission_customer'		=> $temp['commission_customer'],
								'price_buy'					=> $price_buy,
								'price_sell'				=> $price_sell,
								'voucher'					=> $voucher,
								'discount'					=> $discount,
								'active'					=> $menu_active,
								'variant_fkid'				=> $variant_id,
								'data_status'				=> $active_outlet,
							);
							$response = $this->Products_detail_model->update($cek_detail->product_detail_id, $data_detaildata_to_on);
							$inserted_product_detail_id[] = $cek_detail->product_detail_id;
						}
						else{
							$response = $this->Products_detail_model->insert($data_import_master_detail);
							$product_detail_id = $this->db->insert_id();
							$inserted_product_detail_id[] = $product_detail_id;
						}
					}






					/*
					#cek variant
					if (!empty($temp['variant_name']) || !empty($temp['variant_sku'])) {
						//cek variant sku sudah ada atau belum
						$row_variant_name = $this->Products_variant_model->get_by_variantname_product($temp['variant_name'], $products_id);
						if ($row_variant_name) {
							$variant_id = $row_variant_name->variant_id;

							//update variant sku di variant name
							$this->Products_variant_model->update($variant_id, array(
								'variant_name' => $temp['variant_name'],
								'variant_sku' => $temp['variant_sku'],
							));
						}
						else{
							$response_variant = $this->Products_variant_model->insert(array(
								'variant_name' => $temp['variant_name'],
								'variant_sku' => $temp['variant_sku'],
								'product_fkid' => $products_id,
							));
							$variant_id = $this->db->insert_id();
						}

						//set off: kalau tidak punya variant
						$response = $this->Products_detail_model->update_by_product_outlet_variant($products_id, $data_outlet_id, null, array('data_status' => 'off'));
					}
					*/





					//INSERT TAX
					foreach ($inserted_product_detail_id as $product_detail_idx) {
						#matikan data di detail tax berdasarkan product detail id
						$tax_detail_update_off = $this->Products_catalogue_taxgratuity_model->update_taxgratuity_datastatus_by_masterdetailid($product_detail_idx, 'off');
						//insert ke detail tax
						if (!empty($selected_tax)) {
							foreach ($selected_tax as $tax_id) {
								//cek apakah data sudah ada
								$cek_tax_detail = $this->Products_catalogue_taxgratuity_model->get_taxdetail_by_tax_masterdetail($tax_id,$product_detail_idx);
								if ($cek_tax_detail) {
									//jika tax sudah ada (update jadi on)
									$taxdetail_update_on = array('data_status' => 'on');
									$this->Products_catalogue_taxgratuity_model->update_taxdetail_by_tax_masterdetail($tax_id, $product_detail_idx, $taxdetail_update_on);
								}
								else{
									//jika tax belum ada (maka insert)
									$taxdetail_insert = array(
										'tax_fkid' => $tax_id,
										'product_detail_fkid' => $product_detail_idx,
									);
									$this->Products_catalogue_taxgratuity_model->insert_taxgratuity($taxdetail_insert);
								}
							}
						}//endif: selected tax
					}
					
				}//endforeach: selected outlet
			}//endforeach: parsing data csv

			//response success
			$draw_json = array(
				'status' => 'success',
				'message' => count($result_csv_parse).' data have been added to '.count($datapost_outlet).' outlets!'
			);
		}

		//output json
		echo format_json($draw_json);
	}

	public function import_template_BAK()
	{
		//ambil semua data products
		$result = $this->Products_model->get_all_view();

		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=".$this->filename.".csv");
		header("Content-Transfer-Encoding: binary");



		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output,array(
			"Product Name (*max 30char)",
			"Catalogue Type (equipment/product/ingredient)",
			"Product Type (*max 30 char)",
			"Product Category (*max 30 char)",
			"Product Sub-Category (*max 30 char)",
			"Purchase Report Category (*max 30 char)",
			"Unit (*max 10 char)",
			"Unit Description (*max 30 char)",
			"Stock Management (y/n)",
			"Barcode (*max 30 char)",
			"SKU (*max 30 char)",
			"Transfer Markup Type (percent/nominal)",
			"Transfer Markup (*numeric)",
			"Buy Price (*numeric)",
			"Sell Price (*numeric)",
			"Voucher (y/n)",
			"Discount (y/n)",
			"Active Menu (all/sales/link/off)",
			"Active on Outlet (y/n)",
			"Have Variant (y/n)",
			"Variant Name #1 (*Optional, max. 50 char)",
			"Variant Buy Price #1 (*numeric)",
			"Variant Sell Price #1 (*numeric)",
			"Variant Name #2 (*Optional, max. 50 char)",
			"Variant Buy Price #2 (*numeric)",
			"Variant Sell Price #2 (*numeric)",
			"Variant Name #3 (*Optional, max. 50 char)",
			"Variant Buy Price #3 (*numeric)",
			"Variant Sell Price #3 (*numeric)",
			"Variant Name #4 (*Optional, max. 50 char)",
			"Variant Buy Price #4 (*numeric)",
			"Variant Sell Price #4 (*numeric)",
			"Variant Name #5 (*Optional, max. 50 char)",
			"Variant Buy Price #5 (*numeric)",
			"Variant Sell Price #5 (*numeric)",
		));
		// fputcsv($handle, $fields, "\t");
		// foreach ($result as $data) {
			fputcsv($output, array(
				'My Product 1', 'equipment', 'My Type 1', 'My Category 1', 'My Sub-Category 1', 'Purchase Report 1', 'kg', 'Sample Unit Kilos', 'Y', 'BC00001', 'SKU00001', 'percent', 7, 10000, 15000, 'Y', 'N', 'all', 'y', 'y',
				'Sample Variant #1', 1000, 1500, //variant #1
				'Sample Variant #2', 1000, 1500, //variant #2
				'Sample Variant #3', 2000, 2500, //variant #3
				'Sample Variant #4', 2500, 4000, //variant #4
				'Sample Variant #5', 2500, 5000, //variant #5
			));
			fputcsv($output, array(
				'My Product 2', 'product', 'My Type 1', 'My Category 2', 'My Sub-Category 1', 'Purchase Report 1', 'gr', 'Sample Unit Gram', 'N', 'BC00002', 'SKU00002', 'nominal', 7000, 1500, 2000, 'Y', 'N', 'sales', 'y', 'n',
			));
			fputcsv($output, array(
				'My Product 3', 'ingredient', 'My Type 2', 'My Category 1', 'My Sub-Category 2', 'Purchase Report 2', 'kg', 'Sample Unit Kilos', 'Y', 'BC00003', 'SKU00003', 'percent', 0, 0, 0, 'Y', 'N', 'link', 'n', 'y',
				'Sample Variant #2', 1000, 1500, //variant #2
				'Sample Variant #3', 2000, 2500, //variant #3
				'Sample Variant #5', 2500, 5000, //variant #5
			));
		// }
	}

	public function import_process_BAK()
	{
		// protect direct acccess
		if (!$this->input->post()) {
			echo "Direct Access Disallowed.";die();
		}

		// init
		$draw_json = array(); //output format json
		$valid = true; //cek validasi inputan
		$valid_header = true;
		$selected_outlet = array();
		$error_list = array(); //simpan error import

		// data post yang dikirim
		$datapost_file = $_FILES['import_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');
		$datapost_tax = $this->input->post('tax[]');

		/* VALIDASI POST DATA START */
		//cek CSV
		if ($valid==true) {
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV not found'
				);
			}
		}
		//cek outlet
		if ($valid==true) {
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}
		/* VALIDASI POST DATA END */

		// validasi header
		if ($valid==true) {
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			while (($index) < count($import_header)) {
				$col=$index+1;
				$value = $import_header[$index];
				switch ($index) {
					case 0:
						//cek header kolom 1 harus "Product Name"
						$header_name = 'Product Name';
						$value = substr($value, 0, 12);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 1:
						//cek header kolom 2 harus "Catalogue Type"
						$header_name = 'Catalogue Type';
						$value = substr($value, 0, 14);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 2:
						$header_name = 'Product Type';
						$value = substr($value, 0, 12);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 3:
						$header_name = 'Product Category';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 4:
						$header_name = 'Product Sub-Category';
						$value = substr($value, 0, 20);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 5:
						$header_name = 'Purchase Report Category';
						$value = substr($value, 0, 24);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 6:
						$header_name = 'Unit';
						$value = substr($value, 0, 4);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 7:
						$header_name = 'Unit Description';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 8:
						$header_name = 'Stock Management';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 9:
						$header_name = 'Barcode';
						$value = substr($value, 0, 7);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 10:
						$header_name = 'SKU';
						$value = substr($value, 0, 3);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 11:
						$header_name = 'Transfer Markup Type';
						$value = substr($value, 0, 20);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 12:
						$header_name = 'Transfer Markup';
						$value = substr($value, 0, 15);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 13:
						$header_name = 'Buy Price';
						$value = substr($value, 0, 9);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 14:
						$header_name = 'Sell Price';
						$value = substr($value, 0, 10);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 15:
						$header_name = 'Voucher';
						$value = substr($value, 0, 7);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 16:
						$header_name = 'Discount';
						$value = substr($value, 0, 8);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 17:
						$header_name = 'Active Menu';
						$value = substr($value, 0, 11);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 18:
						$header_name = 'Active on Outlet';
						$value = substr($value, 0, 16);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 19:
						$header_name = 'Have Variant';
						$value = substr($value, 0, 12);
						if ($value!=$header_name) {
							$valid = false;
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
						}
						break;
					default:
						//dinamyc check
						switch ($index % 3) {
							case '2':
								$header_name = 'Variant Name';
								$value = substr($value, 0, 12);
								if ($value!=$header_name) {
									$valid = false;
									$valid_header = false;
									$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
								}
								break;
							case '0':
								$header_name = 'Variant Buy Price';
								$value = substr($value, 0, 17);
								if ($value!=$header_name) {
									$valid = false;
									$valid_header = false;
									$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
								}
								break;
							case '1':
								$header_name = 'Variant Sell Price';
								$value = substr($value, 0, 18);
								if ($value!=$header_name) {
									$valid = false;
									$valid_header = false;
									$error_list[] = '- Header on column #'.$col.' must '.$header_name.' ('.$value.')';
								}
								break;
							default: break;
						}//endswitch: dynamic check
						break;
				}//endswitch
				$index++;
			}

			//kumpulkan error yang ada di header
			if (count($error_list)>0) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}//endif: validasi header

		// validasi isi data
		if ($valid==true) {
			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);

			$baris = 0;
			foreach ($result_csv_parse as $row) {
				//get data
				$temp = array(
					'product_name' => $row[0],
					'catalogue_type' => $row[1],
					'product_type' => $row[2],
					'product_category' => $row[3],
					'product_subcategory' => $row[4],
					'purchase_report_category' => $row[5],
					'unit' => $row[6],
					'unit_description' => $row[7],
					'stock_management' => $row[8],
					'barcode' => $row[9],
					'sku' => $row[10],
					'transfer_markup_type' => $row[11],
					'transfer_markup' => $row[12],
					'price_buy' => $row[13],
					'price_sell' => $row[14],
					'voucher' => $row[15],
					'discount' => $row[16],
					'active_menu' => $row[17],
					'active_outlet' => $row[18],
					'variant_status' => $row[19],
				);

				//cek product name
				if (empty($temp['product_name'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Name Required.';
				}
				else{
					if (strlen($temp['product_name'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Product Name max. 30 chars. ('.htmlentities($temp['product_name']).')';
					}
				}
				
				//cek catalogue
				if (empty($temp['catalogue_type'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Catalogue Type Required.';
				}
				else{
					if ($temp['catalogue_type'] != 'equipment' && $temp['catalogue_type'] != 'ingredient' && $temp['catalogue_type'] != 'product') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Catalogue Type must equipment / ingredient / product. ('.htmlentities($temp['catalogue_type']).')';
					}
				}
				
				//cek product type
				if (empty($temp['product_type'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Type Required.';
				}
				else{
					if (strlen($temp['product_type'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Product Type max. 30 chars. ('.htmlentities($temp['product_type']).')';
					}
				}
				
				//cek product category
				if (empty($temp['product_category'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Category Required.';
				}
				else{
					if (strlen($temp['product_category'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Product Category max. 30 chars. ('.htmlentities($temp['product_category']).')';
					}
				}
				
				//cek product subcategory
				if (empty($temp['product_subcategory'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Product Sub-Category Required.';
				}
				else{
					if (strlen($temp['product_subcategory'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Product Sub-Category max. 30 chars. ('.htmlentities($temp['product_subcategory']).')';
					}
				}
				
				//cek purchase report category
				if (empty($temp['purchase_report_category'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Purchase Report Category Required.';
				}
				else{
					if (strlen($temp['purchase_report_category'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Purchase Report Category max. 30 chars. ('.htmlentities($temp['purchase_report_category']).')';
					}
				}
				
				//cek unit
				if (empty($temp['unit'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Unit Required.';
				}
				else{
					if (strlen($temp['unit'])>10) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Unit max. 10 chars. ('.htmlentities($temp['unit']).')';
					}
				}
				
				//cek unit description
				if (empty($temp['unit_description'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Unit Description Required.';
				}
				else{
					if (strlen($temp['unit_description'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Unit Description max. 30 chars. ('.htmlentities($temp['unit_description']).')';
					}
				}
				
				//cek stock management
				if (empty($temp['stock_management'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Stock Management Required.';
				}
				else{
					if (strtolower($temp['stock_management'])!='y' && strtolower($temp['stock_management'])!='n') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Stock Management must y/n.';
					}
				}
				
				//cek barcode
				// if (empty($temp['barcode'])) {
				// 	$valid = false;
				// 	$error_list[] = '#'.($baris+1).': Barcode Required.';
				// }
				// else{
					if (strlen($temp['barcode'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Barcode max. 30 chars. ('.htmlentities($temp['barcode']).')';
					}
				// }

				
				//cek sku
				if (empty($temp['sku'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': SKU Required.';
				}
				else{
					if (strlen($temp['sku'])>30) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': SKU max. 30 chars. ('.htmlentities($temp['sku']).')';
					}
				}
				
				//cek transfer markup type
				if (empty($temp['transfer_markup_type'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': SKU Required.';
				}
				else{
					if ($temp['transfer_markup_type']!='percent' && $temp['transfer_markup_type']!='nominal') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Transfer Markup Type must percent/nominal. ('.htmlentities($temp['transfer_markup_type']).')';
					}
				}

				//cek transfer markup
				if (is_numeric($temp['transfer_markup']) && $temp['transfer_markup']<0) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Transfer Markup min. 0. ('.htmlentities($temp['transfer_markup']).')';
				}
				else{
					if (empty($temp['transfer_markup']) && !is_numeric($temp['transfer_markup'])) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Transfer Markup Required.';
					}
					else{
						if (!is_numeric($temp['transfer_markup'])) {
							$valid = false;
							$error_list[] = '#'.($baris+1).': Transfer Markup must numeric. ('.htmlentities($temp['transfer_markup']).')';
						}
					}
				}

				//cek price buy
				if (is_numeric($temp['price_buy']) && $temp['price_buy']<0) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Buy Price min. 0. ('.htmlentities($temp['price_buy']).')';
				}
				else{
					if (empty($temp['price_buy']) && !is_numeric($temp['price_buy'])) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Buy Price Required.';
					}
					else{
						if (!is_numeric($temp['price_buy'])) {
							$valid = false;
							$error_list[] = '#'.($baris+1).': Buy Price must numeric. ('.htmlentities($temp['price_buy']).')';
						}
					}
				}

				//cek price sell
				if (is_numeric($temp['price_sell']) && $temp['price_sell']<0) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Sell Price min. 0. ('.htmlentities($temp['price_sell']).')';
				}
				else{
					if (empty($temp['price_sell']) && !is_numeric($temp['price_sell'])) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Sell Price Required.';
					}
					else{
						if (!is_numeric($temp['price_sell'])) {
							$valid = false;
							$error_list[] = '#'.($baris+1).': Sell Price must numeric. ('.htmlentities($temp['price_sell']).')';
						}
					}
				}

				//cek voucher
				if (empty($temp['voucher'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Voucher Required.';
				}
				else{
					if (strtolower($temp['voucher'])!='y' && strtolower($temp['voucher'])!='n') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Voucher must y/n.';
					}
				}
				
				//cek discount
				if (empty($temp['discount'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Discount Required.';
				}
				else{
					if (strtolower($temp['discount'])!='y' && strtolower($temp['discount'])!='n') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Discount must y/n.';
					}
				}
				
				//cek active menu
				if (empty($temp['active_menu'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Active Menu Required.';
				}
				else{
					$var_am = strtolower($temp['active_menu']);
					if ($var_am!='all' && $var_am!='sales' && $var_am!='link' && $var_am!='off') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Active Menu must all/sales/link/off. ('.$temp['active_menu'].')';
					}
				}

				//cek active outlet
				if (empty($temp['active_outlet'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Active Outlet Required.';
				}
				else{
					if (strtolower($temp['active_outlet'])!='y' && strtolower($temp['active_outlet'])!='n') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Active Outlet must y/n. ('.$temp['active_outlet'].')';
					}
				}

				//cek have variant / variant_status
				if (empty($temp['variant_status'])) {
					$valid = false;
					$error_list[] = '#'.($baris+1).': Have Variant Required.';
				}
				else{
					if (strtolower($temp['variant_status'])!='y' && strtolower($temp['variant_status'])!='n') {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Have Variant must y/n. ('.$temp['variant_status'].')';
					}
				}
				
				
				
				
				if ($temp['variant_status']=='y') {
					$index = 19; //index terakhir yang dipakai sebelum dinamyc header
					$count_variant = 0;
					while ((($index+=1) < count($row))) {
						// echo "name:".$row[$index];

						//cek data adalah akhir
						if (empty($row[$index]) && empty($row[$index+1]) && empty($row[$index+2])) {
							//kalau variant name, variant price buy, variant price sell kosong
							//maka cek variant name, variant price buy, variant price sell selanjutnya
							$index+=3;
						}
						else{
							$count_variant++;
							//dinamyc check
							switch ($index % 3) {
								//variant name
								case '2':
									//cek variant name
									if (empty($row[$index])) {
										$valid = false;
										$error_list[] = '#'.($baris+1).': Variant Name Required.';
									}
									if (!empty($row[$index]) && strlen($row[$index])>50) {
										$valid = false;
										$error_list[] = '#'.($baris+1).': Variant Name max. 50 char. ('.$row[$index].')';
									}
									break;
								case '0':
									//cek variant price buy
									if (is_numeric($row[$index]) && $row[$index]<0) {
										$valid = false;
										$error_list[] = '#'.($baris+1).': Variant Buy Price min. 0. ('.htmlentities($row[$index]).')';
									}
									else{
										if (empty($row[$index]) && !is_numeric($row[$index])) {
											$valid = false;
											$error_list[] = '#'.($baris+1).': Variant Buy Price Required.';
										}
										else{
											if (!is_numeric($row[$index])) {
												$valid = false;
												$error_list[] = '#'.($baris+1).': Variant Buy Price must numeric. ('.htmlentities($row[$index]).')';
											}
										}
									}
									break;
								case '1':
									//cek variant price sell
									if (is_numeric($row[$index]) && $row[$index]<0) {
										$valid = false;
										$error_list[] = '#'.($baris+1).': Variant Sell Price min. 0. ('.htmlentities($row[$index]).')';
									}
									else{
										if (empty($row[$index]) && !is_numeric($row[$index])) {
											$valid = false;
											$error_list[] = '#'.($baris+1).': Variant Sell Price Required.';
										}
										else{
											if (!is_numeric($row[$index])) {
												$valid = false;
												$error_list[] = '#'.($baris+1).': Variant Sell Price must numeric. ('.htmlentities($row[$index]).')';
											}
										}
									}
									break;
								default: break;
							}//endswitch: dynamic check
						}
					}//endwhile
					if ($count_variant==0) {
						$valid = false;
						$error_list[] = '#'.($baris+1).': Variant Required.';
					}
				}//endif: have variant

				$baris++;
			}

			if ($valid === false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Import Failed!',
					'error_data' => $error_list
				);
			}
		}//endif: validasi data


		// proses import data (otw)
		//proses olah import data
		if ($valid===true) {
			//cek outlet yang dicentang apakah valid atau tidak
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//cek tax yang dicentang apakah valid atau tidak
			$selected_tax = array();
			if (!empty($datapost_tax)) {
				foreach ($datapost_tax as $tax_id) {
					$valid_tax = $this->Gratuity_model->get_by_id($tax_id);
					if ($valid_tax) {
						$selected_tax[] = $tax_id;
					}
				}
			}


			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $baris => $row) {
				$temp = array(
					'name'						=> $row[0], //product_name
					'catalogue_type'			=> $row[1], //catalogue_type
					'type'						=> $row[2], //$products_type_id
					'category'					=> $row[3], //$products_category_id
					'subcategory' 				=> $row[4], //$products_subcategory_id
					'pure_category'				=> $row[5], //$purchase_report_category_id
					'unit'						=> $row[6], //$unit_id
					'unit_description'			=> $row[7], //varchar 30
					'stock_management'			=> $row[8], //boolean (y/n)
					'barcode'					=> $row[9], //varchar 30
					'sku'						=> $row[10], //varchar 30
					'transfer_markup_type'		=> $row[11],
					'transfer_markup'			=> $row[12],
					'price_buy'					=> $row[13], //numeric
					'price_sell'				=> $row[14], //numeric
					'voucher'					=> $row[15], //on|off
					'discount'					=> $row[16], //on|off
					'active_menu'				=> $row[17], //on_all|on_sales|on_link|off
					'active_outlet'				=> $row[18], //on|off (y/n)
					'variant_status'			=> strtolower($row[19]),
				);


				
				//validasikan data (master)
				#cek catalogue type
				$cek_products_type = $this->Type_model->get_by_name($temp['type']); //cari berdasarkan nama type
				if ($cek_products_type) {
					//kalau ditemukan, ambil id dari product type
					$products_type_id = $cek_products_type->products_type_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_type_insert = array('name' => $temp['type']);
					$insert_products_type = $this->Type_model->insert($products_type_insert);
					if ($insert_products_type) {
						$products_type_id = $this->db->insert_id();
					}
				}

				#cek products category
				$cek_products_category = $this->Products_category_model->get_by_name($temp['category']);
				if ($cek_products_category) {
					//kalau ditemukan, ambil id dari product category
					$products_category_id = $cek_products_category->product_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_category_insert = array('name' => $temp['category']);
					$insert_products_category = $this->Products_category_model->insert($products_category_insert);
					if ($insert_products_category) {
						$products_category_id = $this->db->insert_id();
					}
				}

				#cek products subcategory
				$cek_products_subcategory = $this->Products_subcategory_model->get_by_name($temp['subcategory']);
				if ($cek_products_subcategory) {
					//kalau ditemukan, ambil id dari product subcategory
					$products_subcategory_id = $cek_products_subcategory->product_subcategory_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$products_subcategory_insert = array('name' => $temp['subcategory']);
					$insert_products_subcategory = $this->Products_subcategory_model->insert($products_subcategory_insert);
					if ($insert_products_subcategory) {
						$products_subcategory_id = $this->db->insert_id();
					}
				}

				#cek purchase report category
				$cek_purchase_report_category = $this->Purchase_report_category_model->get_by_name($temp['pure_category']);
				if ($cek_purchase_report_category) {
					//kalau ditemukan, ambil id dari purchase report category
					$purchase_report_category_id = $cek_purchase_report_category->purchase_report_category_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$purchase_report_category_insert = array('name' => $temp['pure_category']);
					$insert_purecategory = $this->Purchase_report_category_model->insert($purchase_report_category_insert);
					if ($insert_purecategory) {
						$purchase_report_category_id = $this->db->insert_id();
					}
				}

				#cek unit
				$cek_unit = $this->Unit_model->get_by_name($temp['unit']);
				if ($cek_unit) {
					//kalau ditemukan, ambil id dari unit
					$unit_id = $cek_unit->unit_id;
				}
				else{
					//kalau belum ada, tambahkan data lalu ambil id-nya
					$unit_insert = array('name' => $temp['unit'], 'description' => $temp['unit_description']);
					$insert_unit = $this->Unit_model->insert($unit_insert);
					if ($insert_unit) {
						$unit_id = $this->db->insert_id();
					}
				}


				#cek stock management (statis)
				$stock_management = (strtolower($temp['stock_management'])=='y') ? true : false;

				$price_buy = (is_numeric($temp['price_buy'])) ? $temp['price_buy'] : 0;
				$price_sell = (is_numeric($temp['price_sell'])) ? $temp['price_sell'] : 0;

				#cek voucher (statis)
				$voucher = (strtolower($temp['voucher'])=='y') ? 'on' : 'off';

				#cek discount (statis)
				$discount = (strtolower($temp['discount'])=='y') ? 'on' : 'off';

				#cek menu active
				$menu_active = strtolower($temp['active_menu']);
				switch ($menu_active) {
					case 'all':
						$menu_active = 'on_all';
						break;
					case 'sales':
						$menu_active = 'on_sales';
						break;
					case 'link':
						$menu_active = 'on_link';
						break;
					default:
						$menu_active = 'off';
						break;
				}

				#active outlet
				$active_outlet = (strtolower($temp['active_outlet'])=='y') ? 'on' : 'off';




				

				
				//data untuk MASTER
				$data_import = array(
					'name' => $temp['name'],
					'catalogue_type' => $temp['catalogue_type'],
					'product_type_fkid' => $products_type_id,
					'product_category_fkid' => $products_category_id,
					'product_subcategory_fkid' => $products_subcategory_id,
					'purchase_report_category_fkid' => $purchase_report_category_id,
					'unit_fkid' => $unit_id,
					'stock_management' => $stock_management,
					'barcode' => (!empty($temp['barcode'])) ? $temp['barcode'] : null,
					'sku' => (!empty($temp['sku'])) ? $temp['sku'] : null,
					'data_status' => 'on',
				);

				//ambil data master products berdasarkan SKU
				$data_product = $this->Products_model->get_by_sku($temp['sku']);
				if ($data_product) {
					//bila data sudah ada, update data (master)
					$products_id = $data_product->product_id;
					$response = $this->Products_model->update($products_id, $data_import);
				}
				else{
					//ambil data master products berdasarkan nama product
					$data_product = $this->Products_model->get_by_name($temp['name']);
					if ($data_product) {
						// $products_id = $data_product->product_id;
						// $response = $this->Products_model->update($products_id, $data_import);

						//revisi koding START
						if ($data_product->sku==$temp['sku']) {
							$response = $this->Products_model->update($products_id, $data_import);
						}
						else{
							$response = $this->Products_model->insert($data_import);
							if ($response) {
								$products_id = $this->db->insert_id(); // ambil primary key
							}
						}
						//revisi koding END
					}
					else{
						//bila data belum ada
						$response = $this->Products_model->insert($data_import);
						if ($response) {
							$products_id = $this->db->insert_id(); // ambil primary key
						}
					}
				}



				
				//insert ke detail
				# matikan data di detail
				// $response = $this->Products_detail_model->update_by_parent_id($products_id, array(
				// 	'data_status' => 'off'
				// )); //matikan semua data

				# cek, product sudah ada di outlet atau belum
				foreach ($selected_outlet as $data_outlet_id) {
					//matikan product detail berdasarkan product+outlet yang dicentang
					$this->Products_detail_model->update_by_product_and_outlet($products_id, $data_outlet_id, array(
						'data_status' => 'off'
					));

					//init var
					$inserted_product_detail_id = array();
					$product_detail_id = null;
					$variant_id = null;



					//data untuk MASTER DETAIL
					$data_import_master_detail = array(
						'product_fkid'			=> $products_id,
						'outlet_fkid'			=> $data_outlet_id,
						'transfer_markup_type'	=> $temp['transfer_markup_type'],
						'transfer_markup'		=> $temp['transfer_markup'],
						'price_buy_start'		=> $price_buy,
						'price_buy'				=> $price_buy,
						'price_sell'			=> $price_sell,
						'voucher'				=> $voucher,
						'discount'				=> $discount,
						'active'				=> $menu_active,
					);


					//cek pakai variant atau tidak
					if (strtolower($temp['variant_status'])=='y') {
						$index = 19; //index terakhir yang dipakai sebelum dinamyc header
						$variant_pb = 0;
						$variant_ps = 0;

						while ((($index+=1) < count($row))) {
							// echo "name:".$row[$index];
							//inisialisasi
							$variant_id = null;


							//cek data adalah akhir
							if (empty($row[$index]) && empty($row[$index+1]) && empty($row[$index+2])) {
								//kalau variant name, variant price buy, variant price sell kosong
								//maka cek variant name, variant price buy, variant price sell selanjutnya
								$index+=3;
							}
							else{
								//cek variant
								$variant_name = $row[$index];
								$variant_pb = $row[$index+1];
								$variant_ps = $row[$index+2];
								$index+=2; //update $index

								// echo "vn: $variant_name|$variant_pb|$variant_ps|";

								//cek variant name sudah ada atau belum
								$row_variant_name = $this->Products_variant_model->get_by_variantname_product($variant_name, $products_id);
								if ($row_variant_name) {
									$variant_id = $row_variant_name->variant_id;

									//update variant name
									$this->Products_variant_model->update($variant_id, array(
										'variant_name' => $variant_name,
									));
								}
								else{
									//insert variant name
									$response_variant = $this->Products_variant_model->insert(array(
										'variant_name' => $variant_name,
										'product_fkid' => $products_id,
									));
									$variant_id = $this->db->insert_id();
								}


								/* simpan variant */
								//remake data variant
								//data untuk MASTER DETAIL
								// $data_import_master_detail = array(
								// 	'product_fkid'			=> $products_id,
								// 	'outlet_fkid'			=> $data_outlet_id,
								// 	'transfer_markup_type'	=> $temp['transfer_markup_type'],
								// 	'transfer_markup'		=> $temp['transfer_markup'],
								// 	'price_buy_start'		=> $variant_pb,
								// 	'price_buy'				=> $variant_pb,
								// 	'price_sell'			=> $variant_ps,
								// 	'voucher'				=> $voucher,
								// 	'discount'				=> $discount,
								// 	'active'				=> $menu_active,
								// 	'variant_fkid'			=> $variant_id,
								// 	'data_status'			=> 'on'
								// );


								//cek variant di outlet
								$row_product_outlet_variant = $this->Products_detail_model->get_by_product_outlet_variant($products_id, $data_outlet_id, $variant_id);
								if ($row_product_outlet_variant) {
									//update data
									$data_import_master_detail['price_buy'] = $variant_pb;
									$data_import_master_detail['price_sell'] = $variant_ps;
									$data_import_master_detail['variant_fkid'] = $variant_id;
									$data_import_master_detail['data_status'] = 'on';
									$this->Products_detail_model->update($row_product_outlet_variant->product_detail_id, $data_import_master_detail);

									$inserted_product_detail_id[] = $row_product_outlet_variant->product_detail_id;
								}
								else{
									//insert data
									$data_import_master_detail['price_buy_start'] = $variant_pb;
									$data_import_master_detail['price_buy'] = $variant_pb;
									$data_import_master_detail['price_sell'] = $variant_ps;
									$data_import_master_detail['variant_fkid'] = $variant_id;
									$this->Products_detail_model->insert($data_import_master_detail);

									$inserted_product_detail_id[] = $this->db->insert_id();
								}
							}//verify empty
						}//endwhile (status=y)

						//set off yang variant_fkid=null
						$this->Products_detail_model->update_by_product_outlet_variant($products_id, $data_outlet_id, null, array(
							'data_status' => 'off'
						));
					}
					else{
						//cek product on outlet by product
						$cek_detail = $this->Products_detail_model->get_by_product_outlet_variant($products_id, $data_outlet_id, null);
						if ($cek_detail) {
							//update data dan hidupkan menu di outlet bila sudah ada
							$data_detaildata_to_on = array(
								'transfer_markup_type' => $temp['transfer_markup_type'],
								'transfer_markup' => $temp['transfer_markup'],
								'price_buy' => $price_buy,
								'price_sell' => $price_sell,
								'voucher' => $voucher,
								'discount' => $discount,
								'active' => $menu_active,
								'variant_fkid' => $variant_id,
								'data_status' => $active_outlet,
							);
							$response = $this->Products_detail_model->update($cek_detail->product_detail_id, $data_detaildata_to_on);
							$inserted_product_detail_id[] = $cek_detail->product_detail_id;
						}
						else{
							$response = $this->Products_detail_model->insert($data_import_master_detail);
							$product_detail_id = $this->db->insert_id();
							$inserted_product_detail_id[] = $product_detail_id;
						}
					}






					/*
					#cek variant
					if (!empty($temp['variant_name']) || !empty($temp['variant_sku'])) {
						//cek variant sku sudah ada atau belum
						$row_variant_name = $this->Products_variant_model->get_by_variantname_product($temp['variant_name'], $products_id);
						if ($row_variant_name) {
							$variant_id = $row_variant_name->variant_id;

							//update variant sku di variant name
							$this->Products_variant_model->update($variant_id, array(
								'variant_name' => $temp['variant_name'],
								'variant_sku' => $temp['variant_sku'],
							));
						}
						else{
							$response_variant = $this->Products_variant_model->insert(array(
								'variant_name' => $temp['variant_name'],
								'variant_sku' => $temp['variant_sku'],
								'product_fkid' => $products_id,
							));
							$variant_id = $this->db->insert_id();
						}

						//set off: kalau tidak punya variant
						$response = $this->Products_detail_model->update_by_product_outlet_variant($products_id, $data_outlet_id, null, array('data_status' => 'off'));
					}
					*/





					//INSERT TAX
					foreach ($inserted_product_detail_id as $product_detail_idx) {
						#matikan data di detail tax berdasarkan product detail id
						$tax_detail_update_off = $this->Products_catalogue_taxgratuity_model->update_taxgratuity_datastatus_by_masterdetailid($product_detail_idx, 'off');
						//insert ke detail tax
						if (!empty($selected_tax)) {
							foreach ($selected_tax as $tax_id) {
								//cek apakah data sudah ada
								$cek_tax_detail = $this->Products_catalogue_taxgratuity_model->get_taxdetail_by_tax_masterdetail($tax_id,$product_detail_idx);
								if ($cek_tax_detail) {
									//jika tax sudah ada (update jadi on)
									$taxdetail_update_on = array('data_status' => 'on');
									$this->Products_catalogue_taxgratuity_model->update_taxdetail_by_tax_masterdetail($tax_id, $product_detail_idx, $taxdetail_update_on);
								}
								else{
									//jika tax belum ada (maka insert)
									$taxdetail_insert = array(
										'tax_fkid' => $tax_id,
										'product_detail_fkid' => $product_detail_idx,
									);
									$this->Products_catalogue_taxgratuity_model->insert_taxgratuity($taxdetail_insert);
								}
							}
						}//endif: selected tax
					}
					
				}//endforeach: selected outlet
			}//endforeach: parsing data csv

			//response success
			$draw_json = array(
				'status' => 'success',
				'message' => count($result_csv_parse).' data have been added to '.count($datapost_outlet).' outlets!'
			);
		}

		//output json
		echo format_json($draw_json);
	}

}

/* End of file Product_import.php */
/* Location: ./application/controllers/products/product-catalogue_menu/Product_import_new.php */