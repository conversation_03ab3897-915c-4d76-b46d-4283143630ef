<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Multipleprice_import_new extends Auth_Controller {

	private $filename='UNIQ-Template-Import-Product-MultiplePrice';

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->library('CSVReader');
		$this->load->model('outlet/Outlets_model');
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_variant_model','Variant_model');
		$this->load->model('products/products_catalogue/Multipleprice_model');
	}

	public function template()
	{
		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=".$this->filename.".csv");
		header("Content-Transfer-Encoding: binary");

		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output, array(
			"Product Name*",
			"Product SKU (Optional, if you have same Product Name)",
			"Variant Name (Optional, if you have variant)",
			"Qty*",
			"Price by Unit*",
			"Qty*",
			"Price by Unit*",
			"Qty*",
			"Price by Unit*",
			"Qty*",
			"Price by Unit*",
			"Qty*",
			"Price by Unit*",
		));
		// fputcsv($handle, $fields, "\t");


		//ambil semua data products
		// $result = $this->Products_model->get_all_view();
		$this->db->select('
			DISTINCT(p.name) AS product_name,
			p.sku,
			pv.variant_name,
		');
		$this->db->from('products p');
		$this->db->join('products_detail pd', 'pd.product_fkid = p.product_id');
		$this->db->join('products_detail_variant pv', 'pv.variant_id = pd.variant_fkid', 'left');
		$this->db->where('p.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('pd.data_status', 'on');
		$this->db->order_by('product_name', 'asc');
		$this->db->order_by('variant_name', 'asc');
		$result = $this->db->get()->result();

		foreach ($result as $data) {
			fputcsv($output, array(
				$data->product_name,
				$data->sku,
				$data->variant_name,
			));
		}
	}

	public function import_process()
	{
		// protect direct acccess
		if (!$this->input->post()) {
			echo "Direct Access Disallowed.";die();
		}

		// init
		$draw_json = array(); //output format json
		$valid = true; //cek validasi inputan
		$valid_header = true;
		$selected_outlet = array();
		$error_list = array(); //simpan error import
		$statis_header_count = 0;

		// data post yang dikirim
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');

		//log to storage
		$this->load->library('google/Google_storage');
		$path = 'logs/import_product-multipleprice/'.ENVIRONMENT.'/'.date('Y-m').'/'.$this->session->userdata('admin_id').'/';
		$file = date('d-His').'__'.$_FILES['csv_file']['name'];
		$this->google_storage->upload('csv_file', $path, $file, true);


		//validasi input
		if ($valid==true) { //cek CSV
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV File Not Found'
				);
			}
		}
		if ($valid==true) { //cek outlet
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}



		// VALIDASI HEADER START
		if ($valid==true) {
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			$col = 'A';

			//header list
			$header_name = [
				"Product Name",
				"Product SKU",
				"Variant Name",
			];
			$header_name_dynamic = [
				"Qty",
				"Price",
			];


			//proses setting header
			$statis_header_count = count($header_name);
			for ($i=count($header_name); $i < count($import_header); $i++) { 
				$hdi = (!empty($hdi)) ? $hdi : 0;
				$hdi = ($hdi<count($header_name_dynamic)) ? $hdi : 0;
				$header_name[] = $header_name_dynamic[$hdi];
				$hdi++;
			}

			//proses validasi header START
			while (($index) < count($header_name)) {
				//setting kolom huruf
				if ($index>0) $col++;
				
				//cek header valid atau tidak
				$header_file_original = (!empty($import_header[$index])) ? $import_header[$index] : ''; //get header di file
				$header_file = substr($header_file_original, 0, strlen($header_name[$index])); //get header di kodingan ($header_name)
				if ($header_file!=$header_name[$index]) {
					$valid = false;
					$valid_header = false;
					$error_list[] = '#'.$col.'1: Header must '.$header_name[$index].' ('.$header_file_original.').';
				}

				$index++;
			}
			//proses validasi header END

			//kumpulkan error yang ada di header
			if (count($error_list)>0) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}
		// VALIDASI HEADER END


		// VALIDASI ISI DATA START
		if ($valid==true) {
			//init
			$valid_import = array();
			$baris = 1;

			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $row) {
				//init
				$kolom = 'A';
				$kolom_index = 0;
				$baris++;
				
				$product_id = '';
				$product_detail_id = '';
				$product_variant_id = '';


				//get data 1 baris
				$temp = [
					'product_name'		=> $row[0],
					'product_sku'		=> ((!empty($row[1])) ? $row[1] : null),
					'product_variant'	=> $row[2],
				];


				//validasi product name tanpa SKU dan dengan SKU
				$kolom++;
				$row_productname = $this->Products_model->get_by_name_sku($temp['product_name'], $temp['product_sku']);
				if ($row_productname) {
					$product_id = $row_productname->product_id;
					$valid_import[$baris]['product_id'] = $row_productname->product_id;
					$valid_import[$baris]['product_name'] = $row_productname->name;
				}
				else{
					$valid = false;
					$skunya = (!empty($temp['product_sku'])) ? ' SKU '.$temp['product_sku'] : '';
					$error_list[] = '#'.$kolom.$baris.': Product not Found. ('.htmlentities($temp['product_name']).$skunya.')';
				}

				//validasi variant
				$kolom++;
				if (!empty($temp['product_variant'])) {
					$row_variant = $this->Variant_model->get_by_variantname_product($temp['product_variant'], $product_id);
					if ($row_variant) {
						$product_variant_id = $row_variant->variant_id;
						$valid_import[$baris]['variant_id'] = $row_variant->variant_id;
						$valid_import[$baris]['variant_name'] = $row_variant->variant_name;
					}
					else{
						$valid = false;
						$error_list[] = '#'.$kolom.$baris.': Variant not Found. ('.htmlentities($temp['product_variant']).')';
					}
				}



				//validasi dynamic
				$index = ($statis_header_count-1); //index mulainya product to link (kolom ke-tiga)
				$valid_import[$baris]['item'] = array();
				$temp_validasi_mp = array();
				while ((($index+=1) < count($row))) {
					//init
					$item_qty = 0;
					$item_price = 0;

					//cek data adalah akhir
					if (empty($row[$index]) && empty($row[$index+1])) {
						//kalau product to link dan additional price kosong, maka cek ptl dan ad selanjutnya
						$index+= count($header_name_dynamic)-1;
						$kolom++;$kolom++;
					}
					else{
						//validasi Product to Link (product yang akan dimasukkan ke dalam link)
						if ($index%(count($header_name_dynamic))==1) {
							//validasi Qty
							$kolom++;
							if (!is_numeric($row[$index])) {
								$valid = false;
								$error_list[] = '#'.$kolom.$baris.': Qty must numeric. ('.htmlentities($row[$index]).')';
							}
							else{
								if ($row[$index] <=1 ) {
									$valid = false;
									$error_list[] = '#'.$kolom.$baris.': Qty must more than 1. ('.htmlentities($row[$index]).')';
								}
								else{
									$item_qty = $row[$index];

									//validasi isi
									if (empty($temp_validasi_mp[$item_qty])) {
										$temp_validasi_mp[$item_qty] = $item_qty;
									}
									else{
										$valid = false;
										$error_list[] = '#'.$kolom.$baris.': Qty Duplicate. ('.htmlentities($row[$index]).')';
									}
								}
							}


							//validasi Price
							$index++;
							$kolom++;
							if (!is_numeric($row[$index])) {
								$valid = false;
								$error_list[] = '#'.$kolom.$baris.': Price must numeric. ('.htmlentities($row[$index]).')';
							}
							else{
								if ($row[$index] < 0 ) {
									$valid = false;
									$error_list[] = '#'.$kolom.$baris.': must more than 0. ('.htmlentities($row[$index]).')';
								}
								else{
									$item_price = $row[$index];
								}
							}



							//tampung data
							$valid_import[$baris]['item'][] = array(
								'qty' => $item_qty,
								'price' => $item_price,
							);
						}
					}
				}//endwhile
			}//endforeach: cek data tiap baris dan kolom



			if ($valid === false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Import Failed!',
					'error_data' => $error_list
				);
			}
		}
		// VALIDASI ISI DATA END


		// PROSES IMPORT
		if ($valid==true) {
			//init
			$import_success = 0;
			$import_failed = 0;
			$import_delete = 0;

			//cek selected outlet
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//import data sesuai valid outlet yang dicentang
			foreach ($selected_outlet as $outlet_id) {
				//init
				$product_id = '';
				$product_detail_id = '';
				$item_product_id = '';
				$item_product_detail_id = '';
				$item_product_qty = '';
				$item_product_price = '';

				//cek data tiap baris
				foreach ($valid_import as $r) {
					//cek apakah product_name tersedia di outlet, kalau tidak tambahkan
					$r['variant_id'] = (!empty($r['variant_id'])) ? $r['variant_id'] : null;
					$row_product = $this->Products_detail_model->get_by_product_outlet_variant($r['product_id'], $outlet_id, $r['variant_id']);
					if ($row_product) {
						$product_id = $row_product->product_fkid;
						$product_detail_id = $row_product->product_detail_id;
					}
					else{
						$data_productdetail_add = array(
							'product_fkid' => $r['product_id'],
							'outlet_fkid' => $outlet_id,
							'active' => 'on_all',
							'variant_fkid' => $r['variant_id']
						);
						$response_productadd = $this->Products_detail_model->insert($data_productdetail_add);
						$product_id = $r['product_id'];
						$product_detail_id = $this->db->insert_id();
					}

					//hapus data lama, dan hapus data breakdown kalau tidak ada item 
					$response_delete = $this->Multipleprice_model->delete_by_product_detail_id($product_detail_id);
					
					//insert multipleprice
					$data_multiprice_detail_batch = array(); //init
					if (count($r['item'])>0) {
						foreach ($r['item'] as $ritem) {
							//tambah ke data detail (insert batch)
							$data_multiprice_detail_batch[] = array(
								'product_detail_fkid' => $product_detail_id,
								'qty' => $ritem['qty'],
								'price' => $ritem['price'],
								'data_created' => current_millis(),
								'data_modified' => current_millis(),
								'data_status' => 'on'
							);
						}//endforeach item

						//insert batch detail
						$response_detail = $this->Multipleprice_model->insertbatch($data_multiprice_detail_batch);
						($response_detail) ? $import_success+=1 : $import_failed+=1;
					}
					else{
						//simpan jumlah data dari product_name
						($response_delete) ? $import_delete+=1 : '';
					}
				}//endforeach: valid import
			}//endforeach: outlet

			//output import success
			$draw_json['status'] = 'success';
			$draw_json['message'] = ($import_success/count($selected_outlet)).'/'.count($valid_import) .' multiple price data have been imported!';
			if ($import_delete>0) {
				$draw_json['message'] = 'Import Multiple Price Success!'."\n".($import_success/count($selected_outlet)).' imported and '.($import_delete/count($selected_outlet)).' deleted of '.count($valid_import) .' line!';
			}
		}

		//output
		echo format_json($draw_json);
	}

}

/* End of file Multipleprice_import_new.php */
/* Location: ./application/controllers/products/product-catalogue_menu/Multipleprice_import_new.php */