<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Linkmenu_import_new extends Auth_Controller {

	private $filename='UNIQ-Template-Import-Product-Linkmenu';

	public function __construct()
	{
		parent::__construct();
		$this->load->library('CSVReader');
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_linkmenu_model');
		$this->load->model('outlet/Outlets_model');
		$this->load->model('products/products_catalogue/Products_variant_model','Variant_model');
	}

	public function template()
	{
		// disable caching
		$now = gmdate("D, d M Y H:i:s");
		header("Expires: Tue, 03 Jul 2001 06:00:00 GMT");
		header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
		header("Last-Modified: {$now} GMT");

		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=".$this->filename.".csv");
		header("Content-Transfer-Encoding: binary");


		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel
		fputcsv($output, array(
			"Product Name*",
			"Product SKU (Optional)",
			"Product Variant*",
			"Link Name*",
			"Link Sort Order*",
			"Multiple Choice* [y/n]",
			"Product to Link*",
			"Product SKU to Link (Optional)",
			"Product Variant to Link (Optional)",
			"Additional Price*",
			"Product to Link*",
			"Product SKU to Link (Optional)",
			"Product Variant to Link (Optional)",
			"Additional Price*",
			"Product to Link*",
			"Product SKU to Link (Optional)",
			"Product Variant to Link (Optional)",
			"Additional Price*",
			"Product to Link*",
			"Product SKU to Link (Optional)",
			"Product Variant to Link (Optional)",
			"Additional Price*",
			"Product to Link*",
			"Product SKU to Link (Optional)",
			"Product Variant to Link (Optional)",
			"Additional Price*",
		));


		//ambil data catalogue detail
		$this->db->select('product_name, sku, variant_name');
		$this->db->from('view_catalogue_detail');
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('data_status', 'on');
		$this->db->where('product_detail_data_status', 'on');
		$this->db->order_by('product_name, variant_name', 'asc');
		$this->db->group_by('product_name, sku, variant_name');
		$result = $this->db->get()->result();


		// fputcsv($handle, $fields, "\t");
		foreach ($result as $data) {
			fputcsv($output, array(
				$data->product_name,
				$data->sku,
				$data->variant_name
			));
		}
	}


	public function import_process()
	{
		// protect direct acccess
		if (!$this->input->post()) {
			echo "Direct Access Disallowed.";die();
		}

		// init
		$draw_json = array(); //output format json
		$valid = true; //cek validasi inputan
		$valid_header = true;
		$selected_outlet = array();
		$error_list = array(); //simpan error import
		$statis_header_count = 0;

		// data post yang dikirim
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');

		$this->load->library('google/Google_storage');
		$path = 'logs/import_product-linkmenu/'.ENVIRONMENT.'/'.date('Y-m').'/'.$this->session->userdata('admin_id').'/';
		$file = date('d-His').'__'.$_FILES['csv_file']['name'];
		$this->google_storage->upload('csv_file', $path, $file, true);

		//validasi input
		if ($valid==true) { //cek CSV
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV File Not Found'
				);
			}
		}
		if ($valid==true) { //cek outlet
			if (empty($datapost_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}

		// VALIDASI HEADER START
		if ($valid==true) {
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			$col = 'A';

			//header list
			$header_name = [
				"Product Name",
				"Product SKU",
				"Product Variant",
				"Link Name",
				"Link Sort Order",
				"Multiple Choice",
			];
			$header_name_dynamic = [
				"Product to Link",
				"Product SKU to Link",
				"Product Variant to Link",
				"Additional Price",
			];


			//proses setting header statis + dinamis menjadi satu
			$statis_header_count = count($header_name);
			for ($i=count($header_name)-1; $i < count($import_header)-1; $i++) { 
				$hdi = (!empty($hdi)) ? $hdi : 0;
				$hdi = ($hdi<count($header_name_dynamic)) ? $hdi : 0;
				$header_name[] = $header_name_dynamic[$hdi];
				$hdi++;
			}

			//proses validasi header START
			while (($index) < count($header_name)) {
				//setting kolom huruf
				if ($index>0) $col++;
				
				//cek header valid atau tidak
				$header_file_original = (!empty($import_header[$index])) ? $import_header[$index] : ''; //get header di file
				$header_file = substr($header_file_original, 0, strlen($header_name[$index])); //get header di kodingan ($header_name)
				if ($header_file!=$header_name[$index]) {
					$valid = false;
					$valid_header = false;
					$error_list[] = '#'.$col.'1: Header must '.$header_name[$index].' ('.$header_file_original.').';
				}

				$index++;
			}
			//proses validasi header END

			//kumpulkan error yang ada di header
			if (count($error_list)>0) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}
		// VALIDASI HEADER END


		// VALIDASI ISI DATA START
		if ($valid) {
			//init
			$valid_import = array();
			$baris = 1;

			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $row) {
				//init
				$kolom = 'A';
				$kolom_index = 0;
				$baris++;
				$product_id = '';
				$product_variant_id = '';
				$product_to_link_productdetail_id = '';

				//get data 1 baris
				$temp = [
					'product_name'		=> $row[0],
					'product_sku'		=> ((!empty($row[1])) ? $row[1] : null),
					'product_variant'	=> $row[2],
					'link_name'			=> $row[3],
					'sortorder'			=> $row[4],
					'multiplechoice'	=> $row[5],
				];


				//validasi product name tanpa SKU dan dengan SKU
				$kolom++;

				//cari dengan SKU=null / dengan isi
				$row_productname = $this->Products_model->get_by_name_sku($temp['product_name'], $temp['product_sku']);
				if (!$row_productname) {
					//cari hanya dengan nama product
					$row_productname = $this->Products_model->get_by_name($temp['product_name']);
				}

				//response dari pencarian product
				if ($row_productname) {
					$product_id = $row_productname->product_id;
					$valid_import[$baris]['product_id'] = $row_productname->product_id;
					$valid_import[$baris]['product_name'] = $row_productname->name;
				}
				else{
					$valid = false;
					$skunya = (!empty($temp['product_sku'])) ? ' SKU '.$temp['product_sku'] : '';
					$error_list[] = '#'.$kolom.$baris.': Product not Found. ('.htmlentities($temp['product_name']).$skunya.')';
				}

				//validasi variant
				$kolom++;
				if (!empty($temp['product_variant'])) {
					$row_variant = $this->Variant_model->get_by_variantname_product($temp['product_variant'], $product_id);
					if ($row_variant) {
						$product_variant_id = $row_variant->variant_id;
						$valid_import[$baris]['variant_id'] = $row_variant->variant_id;
						$valid_import[$baris]['variant_name'] = $row_variant->variant_name;
					}
					else{
						$valid = false;
						$error_list[] = '#'.$kolom.$baris.': Variant not Found. ('.htmlentities($temp['product_variant']).')';
					}
				}

				//validasi link name
				$kolom++;
				$valid_import[$baris]['link_name'] = $temp['link_name'];
				if (empty($temp['link_name'])) {
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Link Name Required. ('.htmlentities($temp['link_name']).')';
				}
				if (strlen($temp['link_name']) > 30) {
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Link Name max. 30 char ('.htmlentities($temp['link_name']).')';
				}

				//validasi link sort order
				$kolom++;
				$valid_import[$baris]['sortorder'] = $temp['sortorder'];
				if (!is_numeric($temp['sortorder'])) {
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Link Sort Order must numeric. ('.htmlentities($temp['sortorder']).')';
				}
				else{
					if ($temp['sortorder'] <= 0) {
						$valid = false;
						$error_list[] = '#'.$kolom.$baris.': Link Sort Order must more than 0. ('.htmlentities($temp['sortorder']).')';
					}
				}

				//validasi multiple choice
				$kolom++;
				$valid_import[$baris]['multiplechoice'] = $temp['multiplechoice'];
				if (empty($temp['multiplechoice'])) {
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Multiple Choice Required.';
				}
				else{
					if (strtolower($temp['multiplechoice'])!='y' && strtolower($temp['multiplechoice'])!='n') {
						$valid = false;
						$error_list[] = '#'.$kolom.$baris.': Multiple Choice must Y or N. ('.htmlentities($temp['multiplechoice']).')';
					}
				}


				//validasi dynamic
				$index = ($statis_header_count-1); //index mulainya product to link (kolom ke-tiga)
				$valid_import[$baris]['item'] = array();
				while ((($index+=1) < count($row))) {
					//init
					$productlink_id = '';
					$productlink_name = '';
					$productlink_variantid = '';
					$productlink_variantname = '';
					$productlink_addprice = '';

					//cek data adalah akhir
					if (empty($row[$index]) && empty($row[$index+1]) && empty($row[$index+2]) && empty($row[$index+3])) {
						//kalau product to link dan additional price kosong, maka cek ptl dan ad selanjutnya
						$index+= count($header_name_dynamic)-1;
						$kolom++;$kolom++;$kolom++;$kolom++;
					}
					else{
						//validasi Product to Link (product yang akan dimasukkan ke dalam link)
						if ($index%(count($header_name_dynamic))==2) {
							//product to link
							$kolom++;
							$row_productsku = (!empty($row[$index+1])) ? $row[$index+1] : null;

							//cari dengan SKU=null / dengan isi
							$row_productlink = $this->Products_model->get_by_name_sku($row[$index], $row_productsku);
							if (!$row_productlink) {
								//cari hanya dengan nama product
								$row_productlink = $this->Products_model->get_by_name($row[$index]);
							}

							//response dari pencarian product
							if ($row_productlink) {
								$productlink_id = $row_productlink->product_id;
								$productlink_name = htmlentities($row_productlink->name);
							}
							else{
								$valid = false;
								$error_list[] = '#'.$kolom.$baris.': Product on Product to Link not found. ('.htmlentities($row[$index]).')';
							}

							//product sku to link
							$index++;
							$kolom++;

							//product variant
							$index++;
							$kolom++;

							if (!empty($row[$index])) {
								$row_productvariant = $this->Variant_model->get_by_variantname_product($row[$index], $productlink_id);
								if ($row_productvariant) {
									$productlink_variantid = $row_productvariant->variant_id;
									$productlink_variantname = $row_productvariant->variant_name;
								}
								else{
									$valid = false;
									$error_list[] = '#'.$kolom.$baris.': Product Variant to Link not Found. ('.htmlentities($row[$index]).')';
								}
							}


							//additional price
							$index++;
							$kolom++;
							//validasi Additional Price
							if (!is_numeric($row[$index])) {
								$valid = false;
								$error_list[] = '#'.$kolom.$baris.': Additional Price must numeric. ('.htmlentities($row[$index]).')';
							}
							else{
								if ($row[$index] < 0) {
									$valid = false;
									$error_list[] = '#'.$kolom.$baris.': Additional Price must more than 0. ('.htmlentities($row[$index]).')';
								}
								else{
									$productlink_addprice = $row[$index];
								}
							}


							//tampung data
							$valid_import[$baris]['item'][] = array(
								'product_id' => $productlink_id,
								'product_name' => $productlink_name,
								'product_sku'	=> $row_productsku,
								'product_variantid' => $productlink_variantid,
								'product_variantname' => $productlink_variantname,
								'additional_price' => $productlink_addprice
							);
						}
					}
				}//endwhile
			}//endforeach

			if ($valid === false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Import Failed!',
					'error_data' => $error_list
				);
			}
		}
		// VALIDASI ISI DATA END


		// PROSES IMPORT
		if ($valid) {
			//init
			$import_success = 0;
			$import_success_delete = 0;
			$import_failed = 0;
			$selected_outlet = array();
			foreach ($datapost_outlet as $outlet_id) {
				$valid_outlet = $this->Outlets_model->get_by_id($outlet_id);
				if ($valid_outlet) {
					$selected_outlet[] = $outlet_id;
				}
			}

			//import data sesuai outlet yang dicentang
			foreach ($selected_outlet as $outlet_id) {
				//init
				$product_id = '';
				$product_detail_id = '';
				$link_id = '';
				$productlink_id = '';
				$productlink_price = '';

				//data tiap baris
				foreach ($valid_import as $r) {
					//cek apakah product_name tersedia di outlet, kalau tidak tambahkan
					$row_variant_id = (!empty($r['variant_id'])) ? $r['variant_id'] : null;
					$row_product = $this->Products_detail_model->get_by_product_outlet_variant($r['product_id'], $outlet_id, $row_variant_id);
					if ($row_product) {
						$product_id = $row_product->product_fkid;
						$product_detail_id = $row_product->product_detail_id;
					}
					else{
						$data_productdetail_add = array(
							'product_fkid' => $r['product_id'],
							'outlet_fkid' => $outlet_id,
							'variant_fkid' => $row_variant_id,
							'active' => 'on_all',
						);
						$response_productadd = $this->Products_detail_model->insert($data_productdetail_add);
						$product_id = $r['product_id'];
						$product_detail_id = $this->db->insert_id();
					}

					//cek link name, kalau ada hapus semua
					$row_link = $this->Products_linkmenu_model->get_by_name_outlet_productdetail($r['link_name'], $outlet_id, $product_detail_id);
					if ($row_link) {
						$link_id = $row_link->linkmenu_id;

						//update link, (untuk sortorder)
						$this->Products_linkmenu_model->update_by_id($link_id, array(
							'order_no' => $r['sortorder'],
							'is_multiplechoice' => ($r['multiplechoice']=='y') ? true : false,
						));

						//delete detail by linkmenu_id (hapus data lama)
						$this->Products_linkmenu_model->delete_detail_by_linkmenu($link_id);
					}
					else{
						//tambahkan link baru
						$datalinkadd = array(
							'name' => $r['link_name'],
							'description' => '-',
							'order_no' => $r['sortorder'],
							'is_multiplechoice' => ($r['multiplechoice']=='y') ? true : false,
							'outlet_fkid' => $outlet_id,
							'product_fkid' => $r['product_id'],
							'product_detail_fkid' => $product_detail_id
						);
						$response_linkadd = $this->Products_linkmenu_model->insert($datalinkadd);
						if ($response_linkadd) {
							$link_id = $this->db->insert_id();
						}
					}



					//insert detail linkmenu
					$data_linkmenu_detail_batch = array();
					if (count($r['item'])>0) {
						foreach ($r['item'] as $rv) {
							$product_detail_id_batch = null;

							//cek product sudah ada di outlet belum
							$row_variant = (!empty($rv['product_variantid'])) ? $rv['product_variantid'] : null;
							$row_productdetail = $this->Products_detail_model->get_by_product_outlet_variant($rv['product_id'], $outlet_id, $row_variant);
							if ($row_productdetail) {
								$product_detail_id_batch = $row_productdetail->product_detail_id;

								//update jadi on link / all
								if ($row_productdetail->active=='on_sales') {
									$data_productdetail_update = array(
										'active' => 'on_all',
										'data_status' => 'on'
									);
									$this->Products_detail_model->update($product_detail_id_batch,$data_productdetail_update);
								}
								elseif ($row_productdetail->active=='off') {
									$data_productdetail_update = array(
										'active' => 'on_link',
										'data_status' => 'on'
									);
									$this->Products_detail_model->update($product_detail_id_batch,$data_productdetail_update);
								}
							}
							else{
								//tambah baru product_detail
								$data_detailadd = array(
									'product_fkid' => $rv['product_id'],
									'outlet_fkid' => $outlet_id,
									'variant_fkid' => $row_variant,
									'active' => 'on_link',
								);
								$response_productdetailadd = $this->Products_detail_model->insert($data_detailadd);
								$product_detail_id_batch = $this->db->insert_id();
							}

							//tambah ke data detail (insert batch)
							$data_linkmenu_detail_batch[] = array(
								'linkmenu_fkid' => $link_id,
								'product_detail_fkid' => $product_detail_id_batch,
								'price_add' => $rv['additional_price'],
								'data_created' => current_millis(),
								'data_modified' => current_millis(),
							);
						}//endforeach item

						$response_detail = $this->Products_linkmenu_model->insertbatch_detail($data_linkmenu_detail_batch);
						($response_detail) ? $import_success+=1 : $import_failed+=1;
					}
					else{
						//hapus data linkmenu kalau tidak ada item
						$this->Products_linkmenu_model->delete_by_id($link_id);
						$import_success_delete++;
					}
				}
			}//endforeach: proses import tiap selected outlet


			//output format
			$draw_json['status'] = 'success';
			if ($import_success_delete>0) {
				$draw_json['message'] = ($import_success/count($selected_outlet)).' link have been imported and '.$import_success_delete.' link have been deleted from '.count($valid_import) .' data!';
			} else {
				$draw_json['message'] = ($import_success/count($selected_outlet)).'/'.count($valid_import) .' link have been imported!';
			}
		}//endif: proses import
		


		//output
		echo format_json($draw_json);
	}

}

/* End of file Linkmenu_import_new.php */
/* Location: ./application/controllers/products/product_catalogue_menu/Linkmenu_import_new.php */