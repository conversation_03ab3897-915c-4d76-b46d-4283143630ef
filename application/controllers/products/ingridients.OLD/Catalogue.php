<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Catalogue extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //proteksi halaman by role
        $this->user_role->products('read_access'); //employee role access page
    
        $this->load->model('products/ingridients/Ingridients_catalogue_model');
        $this->load->model('products/type/Type_model'); //form
        $this->load->model('products/ingridients/Ingridients_category_model'); //form
        $this->load->model('products/ingridients/Ingridients_subcategory_model'); //form
        $this->load->model('products/ingridients/Purchase_report_category_model'); //form
        $this->load->model('products/ingridients/Unit_model'); //form
        $this->load->model('outlet/Outlets_model'); //form
        $this->load->model('products/gratuity/Gratuity_model'); //form
    }

    public function index()
    {
        $link = site_url('products/ingridients/catalogue/'); //URL dengan slash
        $data = array(
            'kolomID' => 'ingridients_catalogue_id', //nama kolom primary key pada tabel
            'currentURL' => $link, //link yang sedang diakses
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
            );

        $data['form_select_products_type'] = $this->Type_model->form_select();
        $data['form_select_ingridients_category'] = $this->Ingridients_category_model->form_select();
        $data['form_select_ingridients_subcategory'] = $this->Ingridients_subcategory_model->form_select();
        $data['form_select_purchase_report_category'] = $this->Purchase_report_category_model->form_prc();
        $data['form_select_unit'] = $this->Unit_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_taxgratuity'] = $this->Gratuity_model->form_select();
        

        $this->load->view('products/ingridients/catalogue/catalogue_v', $data);
    }

    public function json()
    {
        header('Content-Type: application/json');
        //echo $this->Ingridients_catalogue_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Ingridients_catalogue_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->ingridients_catalogue_id;
            $name = htmlentities($a->name); //encoding string
            $outlet_name = htmlentities($a->outlet_name);
            $products_type_name = htmlentities($a->product_type_name);
            $ingridients_category_name = htmlentities($a->ingridient_category_name);
            $ingridients_subcategory_name = htmlentities($a->ingridient_subcategory_name);
            $prc_name = htmlentities($a->prc_name);
            $price = ($a->price);
            $price_update = ($a->price_update);
            $price_sell = ($a->price_sell);
            $unit_name = htmlentities($a->unit_name);
            $photo = htmlentities($a->photo);
            $photo = (empty($a->photo)) ? 'default.png' : $a->photo;

            //deteksi gambar ada atau tidak
            $path = 'assets/ingridients/'.$this->session->userdata('admin_id').'/'.$photo;
            $photo = (file_exists($path)) ? site_url('assets/ingridients/').$this->session->userdata('admin_id').'/'.$photo : site_url('assets/ingridients/default.png');


            $active = htmlentities($a->data_status);
            ($active=='on') ? $active='Yes' : $active='No';

            $action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
            $jumlah = "<input type='text' class='form-control' value='0' />"; //untuk spoil
            
            
            $temp_data = array(
                'ingridients_catalogue_id' => $id,
                'name' => $name,
                'outlet_name' => $outlet_name,
                'product_type_name' => $products_type_name,
                'ingridient_category_name' => $ingridients_category_name,
                'ingridient_subcategory_name' => $ingridients_subcategory_name,
                'prc_name' => $prc_name,
                'price' => $price,
                'price_update' => $price_update,
                'price_sell' => $price_sell,
                'unit_name' => $unit_name,
                'photo' => '<img style="height: 40px; max-width: 40px;" src="'.$photo.'"/>',
                'data_status' => $active,
                'jumlah' => $jumlah, //untuk spoil
                'action' => $action
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json); 
        /* custom json output  end */
    }

    //datatable by outlet untuk di ADD breakdown
    public function json_by_outlet($outlet_id=null)
    {
        header('Content-Type: application/json');
        //echo $this->Ingridients_catalogue_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Ingridients_catalogue_model->json_by_outlet($outlet_id); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->ingridients_catalogue_id;
            $name = htmlentities($a->name); //encoding string
            $outlet_name = htmlentities($a->outlet_name);    
            $unit_fkid = htmlentities($a->unit_fkid);
            $price = ($a->price);
            $price_update = ($a->price_update);
            $unit_name = htmlentities($a->unit_name);
            $photo = htmlentities($a->photo);
            $photo = (empty($a->photo)) ? 'default.png' : $a->photo;

            //deteksi gambar ada atau tidak
            $path = 'assets/ingridients/'.$this->session->userdata('admin_id').'/'.$photo;
            $photo = (file_exists($path)) ? site_url('assets/products/').$this->session->userdata('admin_id').'/'.$photo : site_url('assets/ingridients/default.png');

            $active = htmlentities($a->data_status);
            ($active=='on') ? $active='Yes' : $active='No';

            $action  = "<button class='btn btn-primary btn-xs' onclick='actionAddToForm({$id})'>Add</button>";
            
            
            $temp_data = array(
                'ingridients_catalogue_id' => $id,
                'name' => $name,
                'outlet_name' => $outlet_name,
                'price' => $price,
                'price_update' => $price_update,
                'unit_name' => $unit_name,
                'unit_fkid' => $unit_fkid,
                'photo' => '<img style="height: 40px; max-width: 40px;" src="'.$photo.'"/>',
                'data_status' => $active,
                'action' => $action
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }
    // public function jsonSpoil($outlet_id=null){

    //     header('Content-Type: application/json');
    //     //echo $this->Ingridients_catalogue_model->json(); //default json output

    //     /* custom json output  start */
    //     $jsondata = $this->Ingridients_catalogue_model->jsonSpoil($outlet_id); //ambil data json
    //     $json_decode = json_decode($jsondata); //pecah data json

    //     $dataArray = array();
    //     foreach ($json_decode->data as $a) {
    //         $id = $a->ingridients_catalogue_id;
    //         $name = "<span id='name'>".htmlentities($a->name)."</span>"; //encoding string
    //         //$outlet_id = $_GET['outlet'];
    //         $price_update = "<span id='price_update'>".($a->price_update)."</span";
    //         $price_sell = ($a->price_sell);
    //         $unit_name = htmlentities($a->unit_name);
    //         $date = $a->data_created;

    //         $active = htmlentities($a->data_status);
    //         ($active=='on') ? $active='Yes' : $active='No';

            
            
    //         $data_status = "<select name='data_status[]' class='form-control'>";
    //         $data_status.= "<option value='on'>Yes</option>";
    //         $data_status.= "<option value='off'>No</option></select>";

    //         $value=0;
    //         $value2=0;
    //         $ket="";
    //         if($this->session->has_userdata("jum_".$id) || $this->session->has_userdata("ket_".$id)){
    //             $value=$this->session->userdata("jum_".$id);
    //             $value2=($this->session->userdata("jum_".$id))*$a->price_update;
    //             $ket=$this->session->userdata("ket_".$id);
    //         }
    //         $keterangan = "<textarea name=keterangan[] id='ket' onfocusout='keterangan(this)' class='form-control'>".$ket."</textarea>";

    //         $total = "<span id='total'>".$value2."</span>";
    //         $jumlah = "<input type='number' min='0' id='inputJumlah' onchange='jumlah(this)' style='width:75px;' name='jumlah[]' class='form-control' />";
    //         $jumlah .= "<input type='hidden' value=".$id." id='ing_id' name='ingridients_id[]'/>";
    //          //untuk spoil
            
            
    //         $temp_data = array(
    //             'ingridients_catalogue_id' => $id,
    //             'name' => $name,
    //             'price_update' => $price_update,
    //             'price_sell' => $price_sell,
    //             'unit_name' => $unit_name,
    //             'data_status' => $active,
    //             'keterangan' => $keterangan,
    //             'data_status' =>$data_status,
    //             'jumlah' => $jumlah,
    //             'total' => $total,
    //             'data_created' => $date
    //             );
    //         array_push($dataArray, $temp_data);
    //     }

    //     //remake json data
    //     $draw_json = array(
    //         'draw' => $json_decode->draw,
    //         'recordsTotal' => $json_decode->recordsTotal,
    //         'recordsFiltered' => $json_decode->recordsFiltered,
    //         'data' => $dataArray
    //     );

    //     //tampilkan data json
    //     echo json_encode($draw_json); 
    //     /* custom json output  end */
    // }
    public function action($actionType=null) //actionType = 'create / update'
    {
        //inisialisasi variabel
        $error_imageupload = '';
        $photo_available = false;
        $photo_upload = false;
        $photo_name = $this->session->userdata('admin_id').'_'.$this->input->post('name').'_'.date('YmdHis'); //temporary name
        $photo_path = 'assets/ingridients/'.$this->session->userdata('admin_id').'/'; //path gambar produk


        //buat direktori berdasarkan owner
        $dir_exist = true;
        $album = $this->session->userdata('admin_id');
        if (!is_dir($photo_path)) {
            mkdir($photo_path, 0777, true); //buat direktori image produk untuk per-owner
            $dir_exist = false; // dir not exist
        }


        /*konfigurasi upload gambar start*/
        $photo_name = strtolower($photo_name); //mengubah nama file menjadi kecil semua
        $photo_name = str_replace(' ', '', $photo_name); //fungsi untuk menghilangkan spasi pada nama
        $config['upload_path']          = $photo_path; //'./_tes_upload/'.$album;
        $config['allowed_types']        = 'gif|jpg|png|jpeg'; //ekstensi yang diizinkan
        $config['max_size']             = 100; //maksimal size
        $config['file_name']            = $photo_name; //nama gambar akan diganti saat diupload
        //$config['max_width']  = '1024';
        //$config['max_height']  = '768';
        $config['remove_spaces'] = true;
        
        $this->load->library('upload', $config);
        
        if (!empty($_FILES['photo']['name']) && $_FILES['photo']['name']!="") {
            //echo "ada gambar";
            $photo_upload = true;
        
            if ( ! $this->upload->do_upload('photo')){
                $error_imageupload = array('error' => $this->upload->display_errors());
                $error_imageupload = $error_imageupload['error'];
                $error_imageupload = str_replace('<p>', '', $error_imageupload);
                $error_imageupload = str_replace('</p>', '', $error_imageupload);
                $photo_available = false;
                //echo "gagal upload foto";
            }
            else{
                $data = array('upload_data' => $this->upload->data());
                $photo_name = $data['upload_data']['file_name'];
                $photo_available = true;
                //echo "success upload foto";
                //echo $photo_name;
            }
        }
        else{
            //echo "tidak ada gambar";
            $photo_upload = false;
        }
        /*konfigurasi upload gambar end*/


        $this->_rules(); //validasi rules
        $status_submit = true;

        /* custom filter start */
        /* validasi input multiple price START */
        $multipleprice = $this->input->post('multipleprice[]');
        $multipleqty = $this->input->post('qty[]');
        if (count($multipleprice)>0 || count($multipleqty)>0) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|is_numeric');
        }

        // ceking value multiple price START
        $multiplepriceData = '';
        $multiplepriceArrayData = array();
        $i=0;
        foreach ($multipleprice as $index => $value) {
            $multiplepriceData .= $value;
            $multiplepriceArrayData[$i] = $value;
            $i++;
        }

        $qtyData = '';
        foreach ($multipleqty as $index => $value) {
            $qtyData .= $value;
        }
        // ceking value multiple price end
        

        if (!empty($multiplepriceData) || !empty($qtyData)) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|required|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|required|is_numeric');
        }
        /* validasi input multiple price END */



        if ($this->input->post('outlet[]')==null && $this->input->post('outlet')==null) {
            $status_submit = false;
        }


        $taxgratuity = $this->input->post('taxgratuity[]');

        //valuating taxgratuity (permanen) voucher dan discount
        $postform_voucher = (empty($this->input->post('form_voucher'))) ? 'off' : $this->input->post('form_voucher', TRUE);
        $postform_discount = (empty($this->input->post('form_discount'))) ? 'off' : $this->input->post('form_discount', TRUE);


        if ($this->form_validation->run() == FALSE || $status_submit==FALSE || ($photo_upload===TRUE && $photo_available===FALSE)) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');

                //hapus gambar yang diupload bila validasi salah
                if ($photo_available===true) {
                    $path = $photo_path.$photo_name;
                    unlink($path); //hapus gambar yang baru saja diupload saat input gagal
                }
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }


            $error_pricesell = (empty(form_error('price_sell'))) ? form_error('multipleprice[]') : form_error('price_sell');
            $error_pricesell = (empty($error_pricesell)) ? form_error('qty[]') : $error_pricesell;
            $error_tax = (empty($this->input->post('taxgratuity[]'))) ? form_error('taxgratuity[]') : '';

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'outlet' => form_error('outlet[]'),
                        'product_type' => form_error('product_type'),
                        'ingridient_category' => form_error('ingridient_category'),
                        'ingridient_subcategory' => form_error('ingridient_subcategory'),
                        'purchase_report_category' => form_error('purchase_report_category'),
                        'price' => form_error('price'),
                        'price_sell' => $error_pricesell, //form_error('price_sell'),
                        'unit' => form_error('unit'),
                        'photo' => $error_imageupload,
                        'tax' => $error_tax,
                        'data_status' => form_error('active')
                    )
                );
        } else {
            // //data yang dikirim
            // $data = array(
            //     'name' => $this->input->post('name',TRUE),
            //     //'outlet_fkid' => $this->input->post('outlet[]'),
            //     //'outlet_fkid' => 2,
            //     'products_type_fkid' => $this->input->post('product_type', TRUE),
            //     'ingridients_category_fkid' => $this->input->post('ingridient_category', TRUE),
            //     'ingridients_subcategory_fkid' => $this->input->post('ingridient_subcategory', TRUE),
            //     'purchase_report_category_fkid' => $this->input->post('purchase_report_category',TRUE),
            //     //'price' => $this->input->post('price', TRUE),
            //     'price_sell' => $this->input->post('price_sell', TRUE),
            //     'unit_fkid' => $this->input->post('unit', TRUE)
            // );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                /* custom input */
                $outletSubmitted = $this->input->post('outlet[]');
                $data['price'] = $this->input->post('price', TRUE);

                foreach ($outletSubmitted as $a) {
                    if ($a=='all_outlet') {
                        //echo 'ada semua outlet';
                        $dataSemuaOutlet = $this->Outlets_model->form_select();
                        foreach ($dataSemuaOutlet as $b) {
                            //data yang dikirim  //echo "outlet_id: $b | ";
                            $data = array(
                                'name' => $this->input->post('name',TRUE),
                                //'outlet_fkid' => $this->input->post('outlet[]'),
                                //'outlet_fkid' => 2,
                                'products_type_fkid' => $this->input->post('product_type', TRUE),
                                'ingridients_category_fkid' => $this->input->post('ingridient_category', TRUE),
                                'ingridients_subcategory_fkid' => $this->input->post('ingridient_subcategory', TRUE),
                                'purchase_report_category_fkid' => $this->input->post('purchase_report_category',TRUE),
                                'price' => $this->input->post('price', TRUE),
                                'price_sell' => $this->input->post('price_sell', TRUE),
                                'unit_fkid' => $this->input->post('unit', TRUE),
                                'photo' => ($photo_available===true) ? $photo_name : '',
                                'data_status' => $this->input->post('active', TRUE)
                            );
                            $data['outlet_fkid'] = $b->outlet_id;
                            $inputDB = $this->Ingridients_catalogue_model->insert($data);

                            //input multipleprice
                            if ($inputDB['status_insert']===TRUE) {
                                //insert multiple price
                                $i=0;
                                foreach ($multipleqty as $index => $value) {
                                    $data = array(
                                        'ingridient_fkid' => $inputDB['primary_key'], //primary key setelah input product
                                        'qty' => $value,
                                        'price' => $multiplepriceArrayData[$i]
                                        );
                                    $inpuDB_multiprice = $this->Ingridients_catalogue_model->insert_multipleprice($data);
                                    $i++;
                                }

                                //tax gratuity (input start)
                                if (!empty($taxgratuity)) {
                                    foreach ($taxgratuity as $index => $value) {
                                        //echo $index.'_'.$value.'';//cek value apakah benar ter-post
                                        //cek dulu apakah index
                                        $dataTax = array(
                                            'tax_fkid' => $index,
                                            'ingridient_fkid' => $inputDB['primary_key'], //primary key setelah input product
                                            //'data_created' =>
                                            );
                                        $insert_taxgratuity = $this->Ingridients_catalogue_model->insert_taxgratuity($dataTax);
                                        //print_r('==='.$dataTax);
                                    }
                                }
                                //tax gratuity (input end)

                                //buat response
                                $response = array(
                                    'status' => 'success',
                                    'message' => 'Create Record Success'
                                    );
                            }

                        }
                    }
                    else{
                        //data yang dikirim  //echo "outlet_id: $b | ";
                        $data = array(
                            'name' => $this->input->post('name',TRUE),
                            //'outlet_fkid' => $this->input->post('outlet[]'),
                            //'outlet_fkid' => 2,
                            'products_type_fkid' => $this->input->post('product_type', TRUE),
                            'ingridients_category_fkid' => $this->input->post('ingridient_category', TRUE),
                            'ingridients_subcategory_fkid' => $this->input->post('ingridient_subcategory', TRUE),
                            'purchase_report_category_fkid' => $this->input->post('purchase_report_category',TRUE),
                            'price' => $this->input->post('price', TRUE),
                            'price_sell' => $this->input->post('price_sell', TRUE),
                            'unit_fkid' => $this->input->post('unit', TRUE),
                            'photo' => ($photo_available===true) ? $photo_name : '',
                            'data_status' => $this->input->post('active', TRUE)
                        );
                        $data['outlet_fkid'] = $a; //$data['outlet_fkid'] = $b->outlet_id;
                        $inputDB = $this->Ingridients_catalogue_model->insert($data);

                        //kalau input ingridients_catalog berhasil
                        if ($inputDB['status_insert']===TRUE) {
                            //tambahkan input multiprice dengan looping berdasarkan ID product yang didapat
                            //insert multiple price
                            $i=0;
                            foreach ($multipleqty as $index => $value) {
                                $data = array(
                                    'ingridient_fkid' => $inputDB['primary_key'], //primary key setelah input product
                                    'qty' => $value,
                                    'price' => $multiplepriceArrayData[$i]
                                    );
                                $inpuDB_multiprice = $this->Ingridients_catalogue_model->insert_multipleprice($data);
                                $i++;
                            }

                            //tax gratuity (input start)
                            if (!empty($taxgratuity)) {
                                foreach ($taxgratuity as $index => $value) {
                                    //echo $index.'_'.$value.'';//cek value apakah benar ter-post
                                    //cek dulu apakah index
                                    $dataTax = array(
                                        'tax_fkid' => $index,
                                        'ingridient_fkid' => $inputDB['primary_key'], //primary key setelah input product
                                        //'data_created' =>
                                        );
                                    $insert_taxgratuity = $this->Ingridients_catalogue_model->insert_taxgratuity($dataTax);
                                    //print_r('==='.$dataTax);
                                }
                            }
                            //tax gratuity (input end)

                            //buat response
                            $response = array(
                                    'status' => 'success',
                                    'message' => 'Create Record Success'
                                    );

                        }
                    }
                }
                //die();
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //data yang dikirim
                $data = array(
                    'name' => $this->input->post('name',TRUE),
                    //'outlet_fkid' => $this->input->post('outlet[]'),
                    //'outlet_fkid' => 2,
                    'products_type_fkid' => $this->input->post('product_type', TRUE),
                    'ingridients_category_fkid' => $this->input->post('ingridient_category', TRUE),
                    'ingridients_subcategory_fkid' => $this->input->post('ingridient_subcategory', TRUE),
                    'purchase_report_category_fkid' => $this->input->post('purchase_report_category',TRUE),
                    //'price' => $this->input->post('price', TRUE),
                    'price_sell' => $this->input->post('price_sell', TRUE),
                    'unit_fkid' => $this->input->post('unit', TRUE),
                    'data_status' => $this->input->post('active', TRUE)
                );
                /* custom input */
                $data['outlet_fkid'] = $this->input->post('outlet', TRUE);
                $data['price_update'] = $this->input->post('price', TRUE);
                if ($photo_available===true) {
                    $data['photo'] = $photo_name;
                }

                //action untuk update data
                $response = $this->Ingridients_catalogue_model->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');

                //kalau update ingridients_catalog berhasil
                if ($response===TRUE) {
                    $error_imageupload = str_replace('<p>', '', $error_imageupload);
                    $error_imageupload = str_replace('</p>', '', $error_imageupload);

                    //update multiprice dengan looping berdasarkan ID product yang didapat
                    $deleteMultiplePrice_response = $this->Ingridients_catalogue_model->delete_multipleprice($this->input->post('id')); //hapus multipleprice
                    $i=0;
                    foreach ($multipleqty as $index => $value) {
                        $data = array(
                            'ingridient_fkid' => $this->input->post('id', TRUE),
                            'qty' => $value,
                            'price' => $multiplepriceArrayData[$i]
                            );
                        $inpuDB_multiprice = $this->Ingridients_catalogue_model->insert_multipleprice($data);
                        $i++;
                    }

                    //tax gratuity (input start)
                    $deleteTax_response = $this->Ingridients_catalogue_model->delete_taxgratuity($this->input->post('id')); //hapus tax_detail
                    if (!empty($taxgratuity)) {
                        foreach ($taxgratuity as $index => $value) {
                            //echo $index.'_'.$value.'';//cek value apakah benar ter-post
                            //cek dulu apakah index
                            $dataTax = array(
                                'tax_fkid' => $index,
                                'ingridient_fkid' => $this->input->post('id'), //product id
                                );
                            $insert_taxgratuity = $this->Ingridients_catalogue_model->insert_taxgratuity($dataTax);
                            //echo "input tax: ".$insert_taxgratuity.'|';
                            //print_r('==='.$dataTax);
                        }
                    }
                    //tax gratuity (input end)


                    $response = array(
                        'status' => 'success',
                        //'message' => ($photo_available===true) ? $this->session->userdata('message') : $this->session->userdata('message').'. Ingridient photo not uploaded because '.$error_imageupload.'!'
                        'message' => $this->session->userdata('message')
                        );
                }
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
        $row = $this->Ingridients_catalogue_model->get_by_id($id);

        //deteksi gambar ada atau tidak
        $row->photo = (empty($row->photo) ? 'default.png' : $row->photo);
        $path = 'assets/ingridients/'.$this->session->userdata('admin_id').'/'.$row->photo;
        $row->photo = (file_exists($path)) ? site_url('assets/ingridients/').$this->session->userdata('admin_id').'/'.$row->photo : site_url('assets/ingridients/default.png');

        if ($row) {
            /*ambil unit name (untuk add breakdown) */
            $unit_name = $this->Unit_model->get_by_id($row->unit_fkid);
            if ($unit_name) {
                $unit_name = $unit_name->name;
                $unit_name = htmlentities($unit_name); //anti xss
            }
            /*ambil unit name (untuk add breakdown) end*/

            $data = array(
                    'id' => set_value('ingridients_catalogue_id', $row->ingridients_catalogue_id),
                    'name' => set_value('name', html_entity_decode($row->name)),
                    'outlet' => set_value('outlet_fkid', $row->outlet_fkid),
                    'product_type' => set_value('product_type', $row->products_type_fkid),
                    'ingridient_category' => set_value('ingridient_category', $row->ingridients_category_fkid),
                    'ingridient_subcategory' => set_value('ingridient_subcategory', $row->ingridients_subcategory_fkid),
                    'purchase_report_category' => set_value('purchase_report_category', $row->purchase_report_category_fkid),
                    'price' => set_value('price', $row->price),
                    'price_update' => set_value('price_update', $row->price_update),
                    'price_sell' => set_value('price_sell', $row->price_sell),
                    'unit' => set_value('unit', $row->unit_fkid),
                    'unit_name' => set_value('unit_name', $unit_name),
                    'voucher' => set_value('voucher', html_entity_decode($row->voucher)),
                    'discount' => set_value('discount', html_entity_decode($row->discount)),
                    'photo' => set_value('photo', $row->photo),
                    'data_status' => set_value('data_status',$row->data_status)
                );
            $dataArray = array(
                    'status' => 'success',
                    'data' => $data
                );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo json_encode($dataArray);
    }

    public function delete($id=null)
    {
        $row = $this->Ingridients_catalogue_model->get_by_id($id);

        if ($row) {
            $response = $this->Ingridients_catalogue_model->delete($id);
            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function ingridientMultiplePrice($id=null)
    {
        //inisialisasi
        $draw_json = array();

        //ambil data multipleprice
        $row = $this->Ingridients_catalogue_model->getMultiplePrice($id);
        if ($row) {
            foreach ($row as $a) {
                $rowData = array(
                    'id' => htmlentities($a->ic_multipleprice_id),
                    'ingridient_id' => htmlentities($a->ingridient_fkid),
                    'qty' => htmlentities($a->qty),
                    'price' => htmlentities($a->price)
                    );

                //gabungkan data
                array_push($draw_json, $rowData);
            }
        }

        //tampilkan data json
        header('Content-Type: application/json');
        echo json_encode($draw_json);
    }

    public function taxgratuity($ingridient_id=null)
    {
        //inisialisasi
        $draw_json = array();

        //ambil data multipleprice
        $row = $this->Ingridients_catalogue_model->get_taxgratuity($ingridient_id);
        if ($row) {
            foreach ($row as $a) {
                $rowData = array(
                    'id' => htmlentities($a->taxdetail_id),
                    'tax_id' => htmlentities($a->tax_fkid),
                    'ingridient_id' => htmlentities($a->ingridient_fkid)
                    );

                //gabungkan data
                array_push($draw_json, $rowData);
            }
        }

        //tampilkan data json
        header('Content-Type: application/json');
        echo json_encode($draw_json);
    }

    public function _rules() 
    {
    $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
    // $this->form_validation->set_rules('outlet[]', 'outlet', 'trim'); //multiple outlet
    $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required'); //all outlet
    $this->form_validation->set_rules('product_type', 'product type', 'trim|required');
    $this->form_validation->set_rules('ingridient_category', 'ingridient category', 'trim|required');
    $this->form_validation->set_rules('ingridient_subcategory', 'ingridient subcategory', 'trim|required');
    $this->form_validation->set_rules('purchase_report_category', 'purchase report category', 'trim|required');
    $this->form_validation->set_rules('price', 'price', 'trim|required|is_numeric');
    $this->form_validation->set_rules('price_sell', 'selling price', 'trim|required|is_numeric');
    $this->form_validation->set_rules('multipleprice[]', 'price', 'trim|is_numeric'); //multiprice price sell
    $this->form_validation->set_rules('qty[]', 'quantity', 'trim|is_numeric'); //multiprice price sell
    $this->form_validation->set_rules('unit', 'unit', 'trim|required');
    $this->form_validation->set_rules('photo', 'photo', 'trim');
    $this->form_validation->set_rules('active', 'active', 'trim|required');

    $this->form_validation->set_rules('id', 'ingridients_catalogue_id', 'trim');
    $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Catalogue.php */
/* Location: ./application/controllers/products/ingridients/Catalogue.php */