<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ingridient_catalogue extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->products_ingridients_....('read_access'); //employee role access page | sub-submenu inridient (this pge)
        //$this->user_role->products_ingridients('read_access'); //employee role access page | submenu ingridient
        $this->user_role->products('read_access'); //employee role access page
        
        
		$this->load->model('products/ingridients/ingridient_catalogue/Ingridients_catalogue_model','Catalogue_model');
        $this->load->model('products/ingridients/ingridient_catalogue/Ingridients_catalogue_taxgratuity_model','Taxdetail_model');
        $this->load->model('products/ingridients/ingridient_catalogue/Ingridients_catalogue_multipleprice_model', 'Multipleprice_model');
        $this->load->model('products/ingridients/ingridient_catalogue/Ingridients_catalogue_link_model', 'Linkmenu_model');
        $this->load->model('products/type/Type_model'); //form
        $this->load->model('products/ingridients/Ingridients_category_model'); //form
        $this->load->model('products/ingridients/Ingridients_subcategory_model'); //form
        $this->load->model('products/ingridients/Purchase_report_category_model'); //form
        $this->load->model('products/ingridients/Unit_model'); //form
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('products/gratuity/Gratuity_model'); //form
	}

	public function index()
	{
		$link = site_url('products/ingridients/ingridient-catalogue/'); //URL dengan slash
        $data = array(
            'kolomID' => 'ingridient_id', //nama kolom primary key pada tabel
            'currentURL' => $link, //link yang sedang diakses
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionAddMaster' => $link.'add_master',

            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
            );

        
        //form
        $data['form_select_products_type'] = $this->Type_model->form_select();
        $data['form_select_category'] = $this->Ingridients_category_model->form_select();
        $data['form_select_subcategory'] = $this->Ingridients_subcategory_model->form_select();
        $data['form_select_purchase_report_category'] = $this->Purchase_report_category_model->form_prc();
        $data['form_select_unit'] = $this->Unit_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_taxgratuity'] = $this->Gratuity_model->form_select();


		$this->load->view('products/ingridients/catalogue.new/ig_catalogue_v', $data);
	}

    public function add_master()
    {
        //inisialisasi variabel
        $error_imageupload = '';
        $photo_available = false;
        $photo_upload = false;
        
        /* RULES VALIDASI INPUT START */
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('formselect_type', 'type', 'trim|required');
        $this->form_validation->set_rules('formselect_category', 'category', 'trim|required');
        $this->form_validation->set_rules('formselect_subcategory', 'subcategory', 'trim|required');
        $this->form_validation->set_rules('formselect_prc', 'purchase report category', 'trim|required');
        $this->form_validation->set_rules('formselect_unit', 'unit', 'trim|required');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required'); //all outlet
        $this->form_validation->set_rules('barcode', 'barcode', 'trim|min_length[5]|max_length[30]');
        $this->form_validation->set_rules('sku', 'sku', 'trim|min_length[5]|max_length[30]');
        $this->form_validation->set_rules('price_buy', 'buying price', 'trim|required|is_numeric');
        $this->form_validation->set_rules('price_sell', 'selling price', 'trim|required|is_numeric');
        $this->form_validation->set_rules('qty[]', 'quantity', 'trim|is_numeric');
        $this->form_validation->set_rules('multipleprice[]', 'price', 'trim|is_numeric');
        $this->form_validation->set_rules('taxgratuity[]', 'tax & gratuity', 'trim');
        $this->form_validation->set_rules('menu_active', 'menu active', 'trim|required');
        
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
        /* RULES VALIDASI INPUT END */

        /* PROSES OLAH GAMBAR START*/
        /*konfigurasi upload gambar start*/
        $photo_path = 'assets/ingridients/'.$this->session->userdata('admin_id').'/'; //path gambar produk
        $photo_name = $this->input->post('name').'_'.date('YmdHis'); //temporary name
        $photo_name = strtolower($photo_name); //mengubah nama file menjadi kecil semua
        $photo_name = str_replace(' ', '', $photo_name); //fungsi untuk menghilangkan spasi pada nama
        $config['upload_path']          = $photo_path; //'./_tes_upload/'.$album;
        $config['allowed_types']        = 'gif|jpg|png|jpeg'; //ekstensi yang diizinkan
        $config['max_size']             = 100; //maksimal size
        $config['file_name']            = $photo_name; //nama gambar akan diganti saat diupload
        //$config['max_width']  = '1024'; //atur lebar maksimal gambar
        //$config['max_height']  = '768'; //atur tinggi maksimal gambar
        $config['remove_spaces'] = true;
        /*konfigurasi upload gambar end*/
        
        /*proses upload gambar start*/
        //buat direktori berdasarkan owner
        $dir_exist = true;
        if (!is_dir($photo_path)) {
            mkdir($photo_path, 0777, true); //buat direktori image produk untuk per-owner
            $dir_exist = false; // dir not exist
        }
        //upload gambar
        $this->load->library('upload', $config);
        /*custom rule upload foto*/
        if (!empty($_FILES['photo']['name']) && $_FILES['photo']['name']!="") { //custom
        	$photo_upload = true; //custom

	        if ( ! $this->upload->do_upload('photo')){
	            $error_imageupload = array('error' => $this->upload->display_errors());
	            $error_imageupload = $error_imageupload['error'];
	            $photo_available = false;
	            //echo "upload gagal";
	        }
	        else{
	            $data = array('upload_data' => $this->upload->data());
	            $photo_name = $data['upload_data']['file_name'];
	            $photo_available = true;
	            //echo "upload success";
	        }
        }//custom
        else{//custom
            //echo "tidak ada gambar";
            $photo_upload = false;//custom
            $photo_name = '';
        }//custom
        /* PROSES OLAH GAMBAR END*/


        /* CUSTOM FILTER START */
        /*validasi input multiple price START */
        //$multipleprice = $this->input->post('multipleprice[]');
        $multipleprice = (!empty($this->input->post('multipleprice[]'))) ? $this->input->post('multipleprice[]',TRUE) : null;

        //$multipleqty = $this->input->post('qty[]', TRUE);
        $multipleqty = (!empty($this->input->post('qty[]'))) ? $this->input->post('qty[]', TRUE) : null;
        if (count($multipleprice)>0 || count($multipleqty)>0) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|is_numeric');
        }

        /*cek value multiple price START*/
        $multiplepriceData = '';
        $multiplepriceArrayData = array();
        $i=0;
        foreach ($multipleprice as $index => $value) {
            $multiplepriceData .= $value;
            $multiplepriceArrayData[$i] = $value;
            $i++;
        }

        $qtyData = '';
        foreach ($multipleqty as $index => $value) {
            $qtyData .= $value;
        }
        /*cek value multiple price END */
        

        if (!empty($multiplepriceData) || !empty($qtyData)) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|required|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|required|is_numeric');
        }
        /*validasi input multiple price END */


        //verifikasi outlet sudah diisi atau belum multi outlet
        $status_checked_outlet = true;
        if ($this->input->post('outlet[]')==null && $this->input->post('outlet')==null) {
            $status_checked_outlet = false;
        }


        $taxgratuity = $this->input->post('taxgratuity[]');

        //valuating taxgratuity voucher dan discount
        $post_voucher = (empty($this->input->post('form_voucher'))) ? 'off' : 'on';
        $post_discount = (empty($this->input->post('form_discount'))) ? 'off' : 'on'
        ;
        /* custom filter end */
        /* CUSTOM FILTER END */


        //POST VALUE
        $post_value = array(
            //master product value
            'name' => $this->input->post('name', TRUE),
            'formselect_type' => $this->input->post('formselect_type', TRUE),
            'formselect_category' => $this->input->post('formselect_category', TRUE),
            'formselect_subcategory' => $this->input->post('formselect_subcategory', TRUE),
            'formselect_prc' => $this->input->post('formselect_prc', TRUE),
            'formselect_unit' => $this->input->post('formselect_unit', TRUE),
            'barcode' => $this->input->post('barcode', TRUE),
            'sku' => $this->input->post('sku', TRUE),
            'photo' => $photo_name,

            //outlet product set value
            'outlet' => $this->input->post('outlet[]', TRUE),
            'price_buy' => $this->input->post('price_buy', TRUE),
            'price_sell' => $this->input->post('price_sell', TRUE),
            'multipleqty' => $multipleqty,
            'multipleprice' => $multipleprice,
            'taxgratuity' => $taxgratuity,
            'voucher' => $post_voucher,
            'discount' => $post_discount,
            'active' => $this->input->post('menu_active', TRUE)
        );

        
        //VALIDASI INPUT
        if ($this->form_validation->run() == FALSE || $status_checked_outlet==FALSE || 
            ($photo_upload===TRUE && $photo_available===FALSE) //verifikasi kalau action create tidak ada foto
            ) { //end of open if else {

            $error_message = $this->session->set_flashdata('message', 'Error, Record Failed');
            //hapus gambar yang diupload bila validasi salah
            if ($photo_available===true) {
                $path = $photo_path.$photo_name;
                unlink($path); //hapus gambar yang baru saja diupload saat input gagal
            }

            //SETTING OUTPUT ERROR START
            $error_price = (empty(form_error('price_sell'))) ? form_error('multipleprice[]') : form_error('price_sell');
            $error_price = (empty($error_price)) ? form_error('qty[]') : $error_price;
            $error_tax = (empty($this->input->post('taxgratuity[]'))) ? form_error('taxgratuity[]') : '';
            
            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'formselect_type' => form_error('formselect_type'),
                        'formselect_category' => form_error('formselect_category'),
                        'formselect_subcategory' => form_error('formselect_subcategory'),
                        'formselect_prc' => form_error('formselect_prc'),
                        'formselect_unit' => form_error('formselect_unit'),
                        'barcode' => form_error('barcode'),
                        'sku' => form_error('sku'),
                        'outlet' => form_error('outlet[]'),
                        'price_buy' => form_error('price_buy'),
                        'price_sell' => $error_price,
                        'tax' => $error_tax,
                        'menu_active' => form_error('menu_active'),
                        'photo' => $error_imageupload
                    )
                );
            //SETTING OUTPUT ERROR END
            echo json_encode($response); //nanti digabungkan menjadi satu, jadi dihapus saja
        }
        else{
            //action untuk create master product
            /* custom input */
            //inisialisasi variabel
            $primarykey_master = null;
            $primarykey_masterdetail = null;
            $data_untuk_masterdetail = array();

            /*re-format value outlet yang dicentang untuk all outlet*/
            $outletSubmitted = $post_value['outlet'];
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }

            //definisikan kembali data yang akan dikirim/insert ke master product
            $data_untuk_master = array(
                'name' => $post_value['name'],
                'product_type_fkid' => $post_value['formselect_type'],
                'ingridient_category_fkid' => $post_value['formselect_category'],
                'ingridient_subcategory_fkid' => $post_value['formselect_subcategory'],
                'purchase_report_category_fkid' => $post_value['formselect_prc'],
                'unit_fkid' => $post_value['formselect_unit'],
                'barcode' => (empty($post_value['barcode'])) ? null : $post_value['barcode'],
                'sku' => (empty($post_value['sku'])) ? null : $post_value['sku'],
                'photo' => $photo_name,
            );
            //$data_untuk_master['outlet_fkid'] = $b->outlet_id;

            $inputDB = $this->Catalogue_model->insert_master($data_untuk_master); /*edited */
            $primarykey_master = $inputDB['primary_key'];


            if ($inputDB['status_insert']===TRUE) {
                //kalau input master product berhasil

                //inisialisasi data yang dikirim untuk master detail
                $data_untuk_masterdetail = array(
                    'ingridient_fkid' => $primarykey_master,
                    //'outlet_fkid' => null, //definisikan lagi
                    'price_start' => $post_value['price_buy'],
                    'price_update' => $post_value['price_buy'],
                    'price_sell' => $post_value['price_sell'],
                    'voucher' => $post_value['voucher'],
                    'discount' => $post_value['discount'],
                    'active' => $post_value['active']
                    );
                
                foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                    $data_untuk_masterdetail['outlet_fkid'] = $checkedoutlet;
                    $inputDB_detail = $this->Catalogue_model->insert_masterdetail($data_untuk_masterdetail);

                    //tambahkan input multiprice dengan looping berdasarkan ID master product yang didapat
                    if ( $inputDB_detail['status_insert'] === TRUE ) { //kalau input master product_detail berhasil
                        $primarykey_masterdetail = $inputDB_detail['primary_key'];

                        //insert multiple price START
                        $i=0;
                        if (!empty($taxgratuity)) {
                            foreach ($multipleqty as $index => $value) {
                                $dataMultiplePrice = array(
                                    'ingridient_detail_fkid' => $primarykey_masterdetail, //primary key setelah input product_detail
                                    'qty' => $value,
                                    'price' => $multiplepriceArrayData[$i]
                                    );
                                $inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePrice);
                                $i++;
                            }
                        }
                        //insert multiple price END

                        //insert tax gratuity START
                        if (!empty($taxgratuity)) {
                            foreach ($taxgratuity as $index => $value) {
                                //echo $index.'_'.$value.'';//cek value apakah benar ter-post
                                //cek dulu apakah index
                                $dataTax = array(
                                    'tax_fkid' => $index,
                                    'ingridient_detail_fkid' => $primarykey_masterdetail, //primary key setelah input product_detail
                                    );
                                $insert_taxgratuity = $this->Taxdetail_model->insert_taxgratuity($dataTax);
                                //print_r('==='.$dataTax);
                            }
                        }
                        //insert tax gratuity END

                        //buat response
                        $response = array(
                            'status' => 'success',
                            'message' => 'Create Record Success'
                            );
                    }
                    else{ //kalau input master product gagal
                        //kalau gagal input products_detail di DB
                        $response = array(
                            'status' => 'error',
                            'message' => 'Error insert Product to Outlet'
                            );
                    }
                } //end foreach
            }
            else{
                //kalau input master product gagal
                $message_notif = $this->session->set_flashdata('message', 'Error Record Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
            }


            echo json_encode($response);
        }
    }

    public function get_master($master_id=null)
    {
        # code...
    }

	public function json()
    {
        //header('Content-Type: application/json');
        //echo $this->Catalogue_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Catalogue_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->ingridient_id;
        	$name = htmlentities($a->ingridient_name); //encoding string
        	$type_name = htmlentities($a->product_type_name);
        	$category_name = htmlentities($a->ingridient_category_name);
        	$subcategory_name = htmlentities($a->ingridient_subcategory_name);
        	$purchase_report_category_name = htmlentities($a->prc_name);
        	$unit = htmlentities($a->unit_name);
        	
            //deteksi gambar ada atau tidak
            $photo = htmlentities($a->photo);
            $photo = (empty($a->photo)) ? 'default.png' : $a->photo;
            $path = 'assets/ingridients/'.$this->session->userdata('admin_id').'/'.$photo;
            $photo = (file_exists($path)) ? site_url('assets/ingridients/').$this->session->userdata('admin_id').'/'.$photo : site_url('assets/ingridients/default.png');

            $outlet = "<button class='btn btn-primary btn-xs'>Outlet</button>";
        	
            //action
            //$action .= "&nbsp;&nbsp;&nbsp;";
        	$action = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";

            //$action_breakdown = "<a href='javascript:void(0)' onclick='actionShowBreakdown({$id},\"{$name}\",{$outlet_id})'>Show</a>";
            $outlet_id = ''; //biarerrorhilang
            $action_breakdown  = "<button class='btn btn-primary btn-xs' onclick='actionShowBreakdown({$id},\"".($name)."\",{$outlet_id})'>Show</button>";
            $action_link  = "<button class='btn btn-primary btn-xs actionAddToForm' var-productid='{$id}'>Add</button>";
        	
        	
        	$temp_data = array(
        		'ingridient_id' => $id,
        		'ingridient_name' => $name,
        		'product_type_name' => $type_name,
        		'ingridient_category_name' => $category_name,
        		'ingridient_subcategory_name' => $subcategory_name,
        		'prc_name' => $purchase_report_category_name,
        		'unit_name' => $unit,
        		'photo' => '<img style="height: 40px; max-width: 40px;" src="'.$photo.'"/>',
                'outlet' => $outlet,
        		'action' => $action,
                'action_breakdown' => $action_breakdown,
                'action_link' => $action_link
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        //echo json_encode($draw_json);
        echo format_json($draw_json); //function ambil dari helper
        /* custom json output  end */
    }

}

/* End of file Ingridient_catalogue.php */
/* Location: ./application/controllers/products/ingridients/Ingridient_catalogue.php */