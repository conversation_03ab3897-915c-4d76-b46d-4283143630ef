<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Subcategories extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->products_ingridients_....('read_access'); //employee role access page | sub-submenu inridient (this pge)
        //$this->user_role->products_ingridients('read_access'); //employee role access page | submenu ingridient
        $this->user_role->products('read_access'); //employee role access page
        
		$this->load->model('products/ingridients/Ingridients_subcategory_model');
		$this->load->model('products/ingridients/Purchase_report_category_model'); //untuk form select purchase report category
	}

	public function index()
	{
		$link = site_url('products/ingridients/subcategories/'); //URL dengan slash
        $data = array(
            'kolomID' => 'ingridients_subcategory_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
            );

        $data['form_select_purchasereportcategory'] = $this->Purchase_report_category_model->form_prc();

		$this->load->view('products/ingridients/subcategory/subcategories_v', $data);
	}

	public function json()
    {
        header('Content-Type: application/json');
        //echo $this->Ingridients_subcategory_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Ingridients_subcategory_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->ingridients_subcategory_id;
        	$name = htmlentities($a->name); //encoding string
        	$purchasereportcategory_name = htmlentities($a->prc_name);
        	$action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
        	
        	$temp_data = array(
        		'ingridients_subcategory_id' => $id,
        		'name' => $name,
        		'prc_name' => $purchasereportcategory_name,
        		'action' => $action
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'purchase_report_category' => form_error('prc_name')
                    )
                );
        } else {
            //data yang dikirim
            $data = array(
                'name' => $this->input->post('name',TRUE),
                'purchase_report_category_fkid' => $this->input->post('prc_name',TRUE)
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Ingridients_subcategory_model->insert($data);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->Ingridients_subcategory_model->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
    	$row = $this->Ingridients_subcategory_model->get_by_id($id);

        if ($row) {
            $data = array(
					'id' => set_value('ingridients_subcategory_id', $row->ingridients_subcategory_id),
					'name' => set_value('name', html_entity_decode($row->name)),
					'purchase_report_category' => set_value('purchase_report_category_fkid', $row->purchase_report_category_fkid),
	    		);
            $dataArray = array(
	            	'status' => 'success',
	            	'data' => $data
            	);
        } else {
        	$this->session->set_flashdata('message', 'Record Not Found');
        	$dataArray = array(
	        		'status' => 'error',
	        		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
        		);
        }
        echo json_encode($dataArray);
    }

    public function delete($id=null)
    {
    	$row = $this->Ingridients_subcategory_model->get_by_id($id);

        if ($row) {
            $response = $this->Ingridients_subcategory_model->delete($id);
            if ($response==true) {
            	$this->session->set_flashdata('message', 'Record Deleted');
            	$response = array(
            		'status' => 'success',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            } else {
            	$this->session->set_flashdata('message', 'Deleted Failed');
            	$response = array(
            		'status' => 'error',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
            	'status' => 'error',
            	'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            	);
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
	$this->form_validation->set_rules('prc_name', 'purchase report category', 'trim|required');

	$this->form_validation->set_rules('id', 'ingridients_subcategory_id', 'trim|is_numeric');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Subcategories.php */
/* Location: ./application/controllers/products/ingridients/Subcategories.php */