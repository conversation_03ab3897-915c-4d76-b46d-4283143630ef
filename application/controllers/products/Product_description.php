<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_description extends Auth_Controller {

    public function __construct()
    {
        parent::__construct();
        //Do your magic here
        $this->load->model('products/products_description/Products_description_model');
        $this->load->model('outlet/Outlets_model'); //form
    }

	public function index()
	{
		$link = site_url('products/product-description/'); //URL dengan slash
        $data = array(
            'kolomID' => 'product_description_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
            );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();

		// $this->load->view('products/description/product_description_v', $data);
        $data['page_title'] = 'Product Description';
        $data['view_path'] = 'products/description/v2/';
        $this->template->view($data['view_path'].'description_list_v2', $data);
	}

	public function json()
    {
        header('Content-Type: application/json');
        //echo $this->Products_category_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Products_description_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->product_description_id;
        	$name = htmlentities($a->name); //encoding string
        	$outlet_name = htmlentities($a->outlet_name); //encoding string
        	$price_add = $a->price_add;

        	$action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
        	
        	$temp_data = array(
        		'product_description_id' => $id,
        		'name' => $name,
        		'outlet_name' => $outlet_name,
        		'price_add' => $price_add,
        		'action' => $action
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules
        $status_submit = true;

        if ($this->input->post('outlet[]')==null && $this->input->post('outlet')==null) {
            $status_submit = false;
        }


        if ($this->form_validation->run() == FALSE || $status_submit==FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'outlet' => form_error('outlet[]'),
                        'price_add' => form_error('price_add')
                    )
                );
        } else {
            //data yang dikirim
            $data = array(
                'name' => $this->input->post('name',TRUE),
                //'outlet_fkid' => $this->input->post('outlet[]'),
                //'outlet_fkid' => 2,
                'price_add' => $this->input->post('price_add', TRUE)
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                /* custom input */
                $outletSubmitted = $this->input->post('outlet[]');

                foreach ($outletSubmitted as $a) {
                    if ($a=='all_outlet') {
                        //echo 'ada semua outlet';
                        $dataSemuaOutlet = $this->Outlets_model->form_select();
                        foreach ($dataSemuaOutlet as $b) {
                            $data['outlet_fkid'] = $b->outlet_id;
                            $response = $this->Products_description_model->insert($data);
                        }
                    }
                    else{
                        $data['outlet_fkid'] = $a;
                        $response = $this->Products_description_model->insert($data);
                    }
                }
                //die();
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                /* custom input */
                $data['outlet_fkid'] = $this->input->post('outlet', TRUE);
                $data['price_add'] = $this->input->post('price_add', TRUE);

                //action untuk update data
                $response = $this->Products_description_model->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
    	$row = $this->Products_description_model->get_by_id($id);

        if ($row) {
            $data = array(
					'id' => set_value('product_description_id', $row->product_description_id),
					'name' => set_value('name', html_entity_decode($row->name)),
					'outlet' => set_value('outlet_id', html_entity_decode($row->outlet_fkid)), //ambil outlet_fkid
					'outlet_name' => set_value('outlet_name', html_entity_decode($row->outlet_name)),
					'price_add' => set_value('price_add', html_entity_decode($row->price_add))
	    		);
            $dataArray = array(
	            	'status' => 'success',
	            	'data' => $data
            	);
        } else {
        	$this->session->set_flashdata('message', 'Record Not Found');
        	$dataArray = array(
	        		'status' => 'error',
	        		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
        		);
        }
        echo json_encode($dataArray);
    }

    public function delete($id=null)
    {
    	$row = $this->Products_description_model->get_by_id($id);

        if ($row) {
            $response = $this->Products_description_model->delete($id);
            if ($response==true) {
            	$this->session->set_flashdata('message', 'Record Deleted');
            	$response = array(
            		'status' => 'success',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            } else {
            	$this->session->set_flashdata('message', 'Deleted Failed');
            	$response = array(
            		'status' => 'error',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
            	'status' => 'error',
            	'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            	);
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
	$this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required'); //all outlet
	$this->form_validation->set_rules('price_add', 'additional price', 'trim|required|is_numeric');

	$this->form_validation->set_rules('id', 'product_description_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Product_description.php */
/* Location: ./application/controllers/products/products/Product_description.php */