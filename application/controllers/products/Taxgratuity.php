<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Taxgratuity extends Auth_Controller {

    public function __construct()
    {
        parent::__construct();
        //Do your magic here
        $this->load->model('products/gratuity/Gratuity_model');
    }

	public function index()
    {
        $link = site_url('products/taxgratuity/'); //URL dengan slash
        $data = array(
            'kolomID' => 'gratuity_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionDeletePermanent' => $link.'delete_permanent/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );

        // $this->load->view('products/taxgratuity/taxgratuity_v', $data);
        $data['page_title'] = 'Tax & Gratuity';
        $data['view_path'] = 'products/taxgratuity/v2/';
        $this->template->view($data['view_path'].'taxlist_v2', $data);
    }

    public function json()
    {
        /* custom json output  start */
        $jsondata = $this->Gratuity_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->gratuity_id;
            $name = htmlentities($a->name); //encoding string
            $tax_category = htmlentities($a->tax_category);
            $tax_category = ucfirst($tax_category);
            $tax_status = htmlentities($a->tax_status);
            switch ($tax_status) {
                case 'permanent':
                    $tax_status = ucfirst($tax_status);
                    break;
                case 'temp_active':
                    $tax_status = 'Temporary Active';
                    break;
                case 'temp_deactive':
                    $tax_status = 'Temporary De-Active';
                    break;
            }
            $tax_type = htmlentities($a->tax_type); //encoding string
            $tax_type = ucfirst($tax_type);
            $jumlah = htmlentities($a->jumlah); //encoding string
            $action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
            
            
            $temp_data = array(
                'gratuity_id' => $id,
                'name' => $name,
                'tax_category' => $tax_category,
                'tax_status' => $tax_status,
                'tax_type' => $tax_type,
                'jumlah' => $jumlah,
                'action' => $action
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'tax_category' => form_error('tax_category'),
                        'tax_status' => form_error('tax_status'),
                        'tax_type' => form_error('tax_type'),
                        'jumlah' => form_error('jumlah'),
                    )
                );
        } else {
            //data yang dikirim
            $data = array(
                'name' => $this->input->post('name',TRUE),
                'tax_category' => $this->input->post('tax_category',TRUE),
                'tax_status' => $this->input->post('tax_status',TRUE),
                'tax_type' => $this->input->post('tax_type',TRUE),
                'jumlah' => $this->input->post('jumlah',TRUE),
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Gratuity_model->insert($data);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->Gratuity_model->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        echo format_json($response);
    }

    public function get_data($id=null)
    {
        $row = $this->Gratuity_model->get_by_id($id);

        if ($row) {
            $data = array(
                    'id' => $row->gratuity_id,
                    'name' => html_entity_decode($row->name),
                    'tax_category' => html_entity_decode($row->tax_category),
                    'tax_status' => html_entity_decode($row->tax_status),
                    'tax_type' => html_entity_decode($row->tax_type),
                    'jumlah' => html_entity_decode($row->jumlah),
                );
            $dataArray = array(
                    'status' => 'success',
                    'data' => $data
                );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo format_json($dataArray);
    }

    public function delete($id=null)
    {
        $row = $this->Gratuity_model->get_by_id($id);

        if ($row) {
            $response = $this->Gratuity_model->delete($id);
            if ($response==true) {
                $response = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            } else {
                $response = array(
                    'status' => 'warning',
                    'message' => 'Data in Use'
                );
            }
        } else {
            $response = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //response output
        echo format_json($response);
    }

    public function delete_permanent($id=null)
    {
        //init
        $draw_json = array();

        $row = $this->Gratuity_model->get_by_id($id);
        if ($row) {
            //update
            $response = $this->Gratuity_model->update($id,[
                'data_status' => 'off'
            ]);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Record Deleted Permanently'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //response output
        echo format_json($draw_json);
    }



    public function _rules() 
    {
    $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[50]');
    $this->form_validation->set_rules('tax_category', 'category', 'trim|required');
    $this->form_validation->set_rules('tax_status', 'status', 'trim|required');
    $this->form_validation->set_rules('tax_type', 'tax type', 'required');
    $this->form_validation->set_rules('jumlah', 'jumlah', 'trim|required|max_length[10]|is_numeric'); 

    $this->form_validation->set_rules('id', 'gratuity_id', 'trim');
    $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Taxgratuity.php */
/* Location: ./application/controllers/products/Taxgratuity.php */