<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Unit extends Auth_Controller {

    public function __construct()
    {
        parent::__construct();
        //Do your magic here
        $this->load->model('products/ingridients/Unit_model');
    }

	public function index()
	{
		$link = site_url('products/unit/'); //URL dengan slash
        $data = array(
            'kolomID' => 'unit_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
        );


        //template v2
        $data['page_title'] = 'Product Unit';
        $data['view_path'] = 'products/unit/';
        $this->template->view($data['view_path'].'unit_v2', $data);
	}

	public function json()
    {
        header('Content-Type: application/json');
        //echo $this->Unit_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Unit_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->unit_id;
        	$name = htmlentities($a->name); //encoding string
        	$description = htmlentities($a->description);
        	$action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
        	
        	$temp_data = array(
        		'unit_id' => $id,
        		'name' => $name,
        		'description' => $description,
        		'action' => $action
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'description' => form_error('description')
                    )
                );
        } else {
            //data yang dikirim
            $data = array(
                'name' => $this->input->post('name',TRUE),
                'description' => $this->input->post('description',TRUE)
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Unit_model->insert($data);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->Unit_model->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
    	$row = $this->Unit_model->get_by_id($id);

        if ($row) {
            $data = array(
					'id' => set_value('unit_id', $row->unit_id),
					'name' => set_value('name', html_entity_decode($row->name)),
					'description' => set_value('description', $row->description),
	    		);
            $dataArray = array(
	            	'status' => 'success',
	            	'data' => $data
            	);
        } else {
        	$this->session->set_flashdata('message', 'Record Not Found');
        	$dataArray = array(
	        		'status' => 'error',
	        		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
        		);
        }
        echo json_encode($dataArray);
    }

    public function delete($id=null)
    {
    	$row = $this->Unit_model->get_by_id($id);

        if ($row) {
            $response = $this->Unit_model->delete($id);
            if ($response==true) {
            	$this->session->set_flashdata('message', 'Record Deleted');
            	$response = array(
            		'status' => 'success',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            } else {
            	$this->session->set_flashdata('message', 'Deleted Failed');
            	$response = array(
            		'status' => 'error',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
            	'status' => 'error',
            	'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            	);
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[10]');
	$this->form_validation->set_rules('description', 'description', 'trim|required|max_length[30]');

	$this->form_validation->set_rules('id', 'unit_id', 'trim|is_numeric');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Unit.php */
/* Location: ./application/controllers/products/Unit.php */