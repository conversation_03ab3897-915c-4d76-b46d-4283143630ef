<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Import_breakdown_new extends Auth_Controller {

	private $filename = 'UNIQ-Breakdown-Template';

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->library('CSVReader');
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('outlet/Outlets_model');
		$this->load->model('products/breakdown/Breakdown_model');
		$this->load->model('products/products_catalogue/Products_variant_model');
	}

	public function import_template($outlet_id=null)
	{
		//cek outlet
		$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
		if ($row_outlet) {
			$name = str_replace(' ', '.', $row_outlet->name);
			$this->filename .= '_-_'.$name;
		}


		// force download  
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");

		// disposition / encoding on response body
		header("Content-Disposition: attachment;filename=".$this->filename.".csv");
		header("Content-Transfer-Encoding: binary");

		// create a file pointer connected to the output stream
		$output = fopen('php://output', 'w');

		// output the column headings
		fwrite($output, "sep=,\n"); //biar data per-kolom kalau di-excel



		//init
		$breakdown_data = array();
		$max_data = 0;

		//kalau outlet ditemukan
		if ($row_outlet) {
			//ambil semua data products di outlet
			$this->db->select('product_name, variant_name, product_detail_id');
			$this->db->from('view_catalogue_detail');
			$this->db->where('outlet_fkid', $outlet_id);
			$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
			$this->db->where('catalogue_type !=', 'member');
			$this->db->where('data_status', 'on');
			$this->db->order_by('product_name, variant_name', 'asc');
			$result = $this->db->get()->result();

			foreach ($result as $r) {
				$temp1 = array($r->product_name, $r->variant_name);

				//cek breakdown untuk product_detail_id ini
				$result_breakdown = $this->Breakdown_model->get_by_productdetail($r->product_detail_id);
				if (!empty($result_breakdown)) {
					foreach ($result_breakdown as $a) {
						$temp2 = array($a->item_product_name, $a->item_variant_name, $a->qty);
						$temp1 = array_merge($temp1, $temp2);
					}
				}
				

				//set data body
				$breakdown_data[] = $temp1;
				$count_data = count($result_breakdown);
				$max_data = ($count_data>$max_data) ? $count_data : $max_data;
			}


			$filedata_header = array("Product Name", "Variant");
			if ($max_data>0) {

				//set data header
				for ($i=0; $i < ($max_data*3); $i++) { 
					switch ($i % 3) {
						case '0':
							$filedata_header = array_merge($filedata_header, array("Item Product"));
							break;
						case '1':
							$filedata_header = array_merge($filedata_header, array("Item Variant"));
							break;
						case '2':
							$filedata_header = array_merge($filedata_header, array("Item Qty"));
							break;
						default: break;
					}
				}


				//header kalau ada breakdown
				fputcsv($output, $filedata_header);
			}
			else{
				//header kalau tidak ada breakdown
				fputcsv($output, array(
					"Product Name","Variant",
					"Item Product","Item Variant","Item Qty",
					"Item Product","Item Variant","Item Qty",
					"Item Product","Item Variant","Item Qty",
					"Item Product","Item Variant","Item Qty",
					"Item Product","Item Variant","Item Qty",
				));
			}

			//body
			foreach ($breakdown_data as $x) {
				$temp = array();
				fputcsv($output, $x);
			}
		}
		else{
			fputcsv($output, array("Product Name","Variant","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty","Item Product","Item Variant","Item Qty"));
			fputcsv($output, array("Your Product 1","Your Variant", "Your Item", "Your Item Variant", "Your Qty"));
		}
	}

	public function import_process()
	{
		// init
		$draw_json = array(); //output format json
		$valid = true; //cek validasi inputan
		$valid_header = true;
		$selected_outlet = array();
		$error_list = array(); //simpan error import

		// data post yang dikirim
		$datapost_file = $_FILES['csv_file']['tmp_name'];
		$datapost_outlet = $this->input->post('outlet[]');

		//log to storage
		$this->load->library('google/Google_storage');
		$path = 'logs/import_product-breakdown/'.ENVIRONMENT.'/'.date('Y-m').'/'.$this->session->userdata('admin_id').'/';
		$file = date('d-His').'__'.$_FILES['csv_file']['name'];
		$this->google_storage->upload('csv_file', $path, $file, true);

		/* VALIDASI POST DATA START */
		//cek CSV
		if ($valid==true) {
			if (empty($datapost_file)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'CSV not found'
				);
			}
		}
		//cek outlet
		if ($valid==true) {
			if (!empty($datapost_outlet)) {
				foreach ($datapost_outlet as $outlet_id) {
					$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
					if ($row_outlet) {
						$selected_outlet[] = $outlet_id;
					}
				}
			}
			if (empty($selected_outlet)) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Outlet not found'
				);
			}
		}
		/* VALIDASI POST DATA END */


		/* VALIDASI HEADER START */
		if ($valid==true) {
			$import_header = $this->csvreader->header($datapost_file);
			$index=0;
			$col_alpha='A';
			while (($index) < count($import_header)) {
				$value = $import_header[$index];
				if ($index>0) {
					++$col_alpha;
				}


				switch ($index) {
					case 0:
						//cek header kolom 1 harus "Product Name"
						$header_name = 'Product Name';
						$value = substr($value, 0, 12);
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col_alpha.' must '.$header_name.' ('.$value.')';
						}
						break;
					case 1:
						//cek header kolom 2 harus "Variant"
						$header_name = 'Variant';
						$value = substr($value, 0, 7);
						if ($value!=$header_name) {
							$valid_header = false;
							$error_list[] = '- Header on column #'.$col_alpha.' must '.$header_name.' ('.$value.')';
						}
						break;
					default:
						//dinamyc check start
						switch ($index % 3) {
							case '2':
								$header_name = 'Item Product';
								$value = substr($value, 0, 12);
								if ($value!=$header_name) {
									$valid_header = false;
									$error_list[] = '- Header on column #'.$col_alpha.' must '.$header_name.' ('.$value.')';
								}
								break;
							case '0':
								$header_name = 'Item Variant';
								$value = substr($value, 0, 12);
								if ($value!=$header_name) {
									$valid_header = false;
									$error_list[] = '- Header on column #'.$col_alpha.' must '.$header_name.' ('.$value.')';
								}
								break;
							case '1':
								$header_name = 'Item Qty';
								$value = substr($value, 0, 8);
								if ($value!=$header_name) {
									$valid_header = false;
									$error_list[] = '- Header on column #'.$col_alpha.' must '.$header_name.' ('.$value.')';
								}
								break;
							default: break;
						}//endswitch: dinamyc check end
						break;
				}//endswitch
				$index++;
			}

			//kumpulkan error yang ada di header
			if ((count($error_list)>0) || $valid_header==false) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Header!',
					'error_data' => $error_list
				);
			}
		}//endif: validasi header


		/* VALIDASI ISI DATA START */
		if ($valid==true) {
			//init
			$baris = 1;
			$valid_import = array();

			//parse data CSV
			$result_csv_parse = $this->csvreader->parse_file($datapost_file);
			foreach ($result_csv_parse as $column) {
				//init
				$kolom = 'A';
				$kolom_index = 0;
				$baris++;
				$product_id = '';
				$variant_id = '';

				//get data
				$temp = array(
					'product' => $column[0],
					'variant' => $column[1],
				);

				//cek product
				$row_product = $this->Products_model->get_by_name($temp['product'], true);
				if ($row_product) {
					$product_id = $row_product->product_id;
				}
				else{
					$valid = false;
					$error_list[] = '#'.$kolom.$baris.': Product not found. ('.htmlentities($temp['product']).')';
				}

				//cek product dengan variant
				$kolom++;
				$kolom_index++;
				if (!empty($temp['variant'])) {
					$row_variant = $this->Products_variant_model->get_by_variantname_product($temp['variant'], $product_id);
					if ($row_variant->product_fkid==$product_id) {
						$variant_id = $row_variant->variant_id;
					}
					else{
						$valid = false;
						$error_list[] = '#'.$kolom.$baris.': Variant not found on product '.htmlentities($temp['product']).'. ('.htmlentities($temp['variant']).')';
					}
				}

				//save valid data on FIX column
				$valid_import[$baris] = array(
					'product_id' => $product_id,
					'variant_id' => $variant_id,
					'item' => array()
				);

				//init
				$get_duplicate = array();

				//cek value product
				$item_product_id = '';
				while (($kolom_index++) < count($column)) {
					$kolom++;
					// echo $kolom_index.$baris.':'.$column[$kolom_index]."\n";

					//cek data adalah akhir
					if (empty($column[$kolom_index]) && empty($column[$kolom_index+1]) && empty($column[$kolom_index+2])) {
						$kolom_index+=2;
					}
					else{
						if ($kolom_index%3==2) {
							//item product
							$item_product_id = '';
							$col_product = $column[$kolom_index];
							$row_item_product = $this->Products_model->get_by_name($col_product, true);
							if ($row_item_product) {
								$item_product_id = $row_item_product->product_id;
							}
							else{
								$valid = false;
								$error_list[] = '#'.$kolom.$baris.': Item Product not found. ('.htmlentities($col_product).')';
							}
							$valid_import[$baris]['item'][$kolom_index]['product_id'] = $item_product_id;
							$valid_import[$baris]['item'][$kolom_index]['product_name'] = $col_product;

							//item variant
							$kolom++;
							$kolom_index++;
							$col_variant = $column[$kolom_index];
							$item_variant_id = '';
							if (!empty($col_variant)) {
								$row_item_variant = $this->Products_variant_model->get_by_variantname_product($col_variant, $item_product_id, true);
								if ($row_item_variant) {
									$item_variant_id = $row_item_variant->variant_id;
								}
								else{
									$valid = false;
									$error_list[] = '#'.$kolom.$baris.': Item Variant not found on '.htmlentities($col_product).'. ('.htmlentities($col_variant).')';
								}
							}
							$valid_import[$baris]['item'][$kolom_index-1]['variant_id'] = $item_variant_id;
							$valid_import[$baris]['item'][$kolom_index-1]['variant_name'] = $col_variant;

							//item qty
							$kolom++;
							$kolom_index++;
							$col_qty = $column[$kolom_index];
							if (!is_numeric($col_qty) || empty($col_qty)) {
								$valid = false;
								$error_list[] = '#'.$kolom.$baris.': Item Qty must numeric. ('.htmlentities($col_qty).')';
							}
							$valid_import[$baris]['item'][$kolom_index-2]['qty'] = $col_qty;



							//is main item add again?
							$letter = $kolom;
							$letterAscii = ord($letter);
							$letterAscii--;
							$letterAscii--;
							$letter = chr($letterAscii); // 'B'
							if ($product_id==$item_product_id && $variant_id==$item_variant_id) {
								$valid = false;
								$var = (!empty($col_variant)) ? ': '.$col_variant : '';
								$error_list[] = '#'.$letter.$baris.': Item Product is Main Product. ('.$col_product.$var.')';
							}
							else{
								//is duplicate?
								if (empty($get_duplicate[$item_product_id][$item_variant_id])) {
									$get_duplicate[$item_product_id][$item_variant_id] = $col_qty;
								}
								else{
									$valid = false;
									$error_list[] = '#'.$letter.$baris.': Duplicate Item. ('.$col_product.')';
								}
							}
						}
					}
				}//endwhile
			}//endforeach

			//set output
			if (count($error_list)>0 || $valid==false) {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Data!',
					'error_data' => $error_list
				);
			}
		}
		/* VALIDASI ISI DATA END */


		/* INSERT DATA IMPORT START */
		$import_data = 0;
		$import_data_success = 0;
		if ($valid===true) {
			foreach ($selected_outlet as $outlet_id) {
				//ambil data per-line
				foreach ($valid_import as $dataimport) {
					// print_r($dataimport);
					// echo "p:".$dataimport['product_id'];
					// echo "v:".$dataimport['variant_id'];
					// echo "=====================\n <br>";
					$main_product_id = $dataimport['product_id'];
					$main_variant_id = (!empty($dataimport['variant_id'])) ? $dataimport['variant_id'] : null;

					//get main product detail
					$row_pd = $this->Products_detail_model->get_by_product_outlet_variant($main_product_id, $outlet_id, $main_variant_id);
					if ($row_pd) {
						$main_product_detail_id = $row_pd->product_detail_id;
					}
					else{
						$main_product_detail_insertdata = array(
							'product_fkid' => $main_product_id,
							'outlet_fkid' => $outlet_id,
							'variant_fkid' => $main_variant_id,
						);
						$row_pd_insert = $this->Products_detail_model->insert($main_product_detail_insertdata);
						$main_product_detail_id = $this->db->insert_id();
					}


					//delete data yang sudah ada
					$response_delete = $this->Breakdown_model->delete_by_product_outlet_productdetail($main_product_id, $outlet_id, $main_product_detail_id);


					//ambil data item
					foreach ($dataimport['item'] as $dataimportitem) {
						// echo "item pid:".$dataimportitem['product_id'];
						$item_product_id = $dataimportitem['product_id'];
						$item_variant_id = (!empty($dataimportitem['variant_id'])) ? $dataimportitem['variant_id'] : null;
						$item_qty = $dataimportitem['qty'];

						//cek product detail dari item
						$row_item_pd = $this->Products_detail_model->get_by_product_outlet_variant($item_product_id, $outlet_id, $item_variant_id);
						if ($row_item_pd) {
							$item_product_detail_id = $row_item_pd->product_detail_id;
						}
						else{
							$item_product_detail_insertdata = array(
								'product_fkid' => $item_product_id,
								'outlet_fkid' => $outlet_id,
								'variant_fkid' => $item_variant_id
							);
							$row_item_pd_insert = $this->Products_detail_model->insert($item_product_detail_insertdata);
							$item_product_detail_id = $this->db->insert_id();
						}


						//insert ke breakdown
						$data_insert_breakdown = array(
							'outlet_fkid' => $outlet_id,
							'product_fkid' => $main_product_id,
							'product_detail_fkid' => $main_product_detail_id,
							'item_product_fkid' => $item_product_id,
							'item_product_detail_fkid' => $item_product_detail_id,
							'qty' => $item_qty
						);
						$response_insertbreakdown = $this->Breakdown_model->insert($data_insert_breakdown);
						if ($response_insertbreakdown) {
							$import_data_success++;
						}
						$import_data++;

					}
				}
			}
			
			//output
			$draw_json = array(
				'status' => 'success',
				'message' => 'Import '.$import_data_success.'/'.$import_data.' Breakdown Success!'
			);
		}
		/* INSERT DATA IMPORT END */

		echo format_json($draw_json);
	}

}

/* End of file Import_breakdown_v2.php */
/* Location: ./application/controllers/products/breakdown_menu/Import_breakdown_v2.php */