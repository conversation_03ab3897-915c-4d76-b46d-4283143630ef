<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Purchase_report_category extends Auth_Controller {

    public function __construct()
    {
        parent::__construct();
        //Do your magic here
        $this->load->model('products/ingridients/Purchase_report_category_model');
    }

	public function index()
	{
		$link = site_url('products/purchase_report_category/'); //URL dengan slash
        $data = array(
            'kolomID' => 'purchase_report_category_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/' //ambil data yang akan diedit
            );

		// $this->load->view('products/ingridients/purchase_report_category/purchase_report_category_v', $data);
        $data['page_title'] = 'Purchase Report Category';
        $data['view_path'] = 'products/purchase_report_category/';
        $this->template->view($data['view_path'].'prc_list_v2', $data);
	}

	public function json()
    {
        //remake
        $json = $this->Purchase_report_category_model->json();
        $json_decode = json_decode($json);
        $data_array = array();
        foreach ($json_decode->data as $r) {
            $action  = "<a href='javascript:void(0)' onclick='actionEdit({$r->id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$r->id})'><span class='glyphicon glyphicon-trash'></a>";

            $data_array[] = array(
                'id' => $r->id,
                'purchase_report_category_id' => $r->purchase_report_category_id,
                'name' => htmlentities($r->name),
                'operationalcost' => htmlentities($r->operationalcost),
                // 'action' => $action
            );
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $data_array
        );

        //output
        echo format_json($draw_json);
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            $message_error = (($actionType=='create') ? 'Create' : 'Update') .' Record Failed';

            $draw_json = array(
                'status' => 'error',
                'message'=> $message_error,
                'error_data' => array(
                    'name' => form_error('name'),
                    'operationalcost' => form_error('operationalcost')
                )
            );
        } else {
            //data yang dikirim
            $id = $this->input->post('id', true);
            $datapost = array(
                'name' => $this->input->post('name', true),
                'is_operationalcost' => $this->input->post('operationalcost', true)
            );

            //eksekusi query
            $response = ($actionType=='create') ?
                        $this->Purchase_report_category_model->insert($datapost) :
                        $this->Purchase_report_category_model->update($id, $datapost);

            //output json
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => (($actionType=='create') ? 'Create' : 'Update') . ' Record Success'
                );
            }else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => (($actionType=='create') ? 'Create' : 'Update') . ' Record Error'
                );
            }
        }

        //output
        echo format_json($draw_json);
    }

    public function edit($id=null)
    {
    	$row = $this->Purchase_report_category_model->get_by_id($id);
        if ($row) {
            $draw_json = array(
                'status' => 'success',
                'data' => array(
                    'id' => $row->purchase_report_category_id,
                    'name' => htmlentities($row->name),
                    'operationalcost' => $row->is_operationalcost
                )
            );
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }
        echo format_json($draw_json);
    }

    public function delete($id=null)
    {
    	$row = $this->Purchase_report_category_model->get_by_id($id);
        if ($row) {
            $response = $this->Purchase_report_category_model->delete($id);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //output
        echo format_json($draw_json);
    }

    public function _rules() 
    {
    	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('operationalcost', 'Operational Cost', 'trim|required|in_list[1,0]', array(
            'in_list' => 'The % must Yes or No'
        ));

    	$this->form_validation->set_rules('id', 'purchase_report_category_id', 'trim');
    	$this->form_validation->set_error_delimiters('', '');
    }

}

/* End of file Purchase_report_category.php */
/* Location: ./application/controllers/products/ingridients/Purchase_report_category.php */