<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Breakdown extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		
		$this->load->model('products/products/products_catalogue/Products_catalogue_model','Catalogue_model');
		$this->load->model('products/breakdown/Breakdown_model','Breakdown_new_model');
		$this->load->model('products/ingridients/Breakdown_model');
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
	}

	public function index()
	{
		$link = site_url('products/breakdown/'); //URL dengan slash
		$link_product = site_url('products/product-catalogue/'); //URL dengan slash
		$data = array(
			'kolomID' => 'product_id', //nama kolom primary key pada tabel
			'currentURL' => current_url(), //link yang sedang diakses
			'ajaxActionDatatableList' => $link_product.'json/', //tampilkan semua data product
			'ajaxActionDelete' => $link.'delete/',
			'ajaxActionCreate' => $link.'action/create',
			'ajaxActionUpdate' => $link.'action/update',
			'ajaxActiongetDataEdit' => $link.'edit/', //ambil data yang akan diedit
		);

		$data['form_select_outlet'] = $this->Outlets_model->form_select();
		$data['ajaxActionImportCSV'] = $link.'import/import_process';
        
		// $this->load->view('products/ingridients/breakdown/breakdown_v', $data);
		$this->template->view('products/ingridients/breakdown/breakdown_v', $data);
	}

	public function get_product_outletactive($product_id=null)
	{
		$this->db->select('product_id,outlet_fkid,outlet_name');
		$this->db->where('product_fkid', $product_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('product_detail_data_status', 'on'); //ambil product yang aktif
		$this->db->order_by('outlet_name', 'asc');
		$result = $this->db->get('view_catalogue_detail');

		$draw_json = array();
		foreach ($result->result() as $a) {
			$temp = array(
				'outlet_id' => $a->outlet_fkid,
				'outlet_name' => htmlentities($a->outlet_name)
			);

			array_push($draw_json, $temp);
		}

		$draw_json = array(
			'status' => 'success',
			'data' => $draw_json
		);
		echo format_json($draw_json);
	}

	//(DONE)
	public function get_product_breakdown($product_id=null, $outlet_id=null)
	{
		//init
		$draw_json = array();

		//cek outlet
		$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
		if (!$row_outlet) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Invalid Outlet'
			);
		}
		else{
			//get breakdown by product and outlet
			$result = $this->Breakdown_new_model->get_by_product_and_outlet($product_id, $outlet_id);
			foreach ($result as $a) {
				$temp = array(
					'breakdown_id' => $a->breakdown_id,
					'product_id' => $a->item_product_id,
					'product_name' => htmlentities($a->item_product_name),
					'qty' => $a->qty,
					'unit_name' => htmlentities($a->unit_name),
					'price_buy' => $a->price_buy,
					'hpp' => formatDesimal($a->hpp,2),
					'action' => ''
				);

				array_push($draw_json, $temp);
			}

			//json format
			$draw_json = array(
				'status' => 'success',
				'data' => $draw_json
			);
		}

		//output
		echo format_json($draw_json);
	}

	//(DONE)
	public function get_breakdown_id($breakdown_id=null)
	{
		//init
		$draw_json = array();

		//cek data
		$row = $this->Breakdown_new_model->get_by_id($breakdown_id);
		if ($row) {
			$draw_json = array(
				'status' => 'success',
				'data' => array(
					'breakdown_id' => $row->breakdown_id,
					'product_id' => $row->item_product_id,
					'product_name' => htmlentities($row->item_product_name),
					'qty' => $row->qty,
					'price_buy' => $row->price_buy,
					'hpp' => $row->hpp,
					'unit_name' => htmlentities($row->unit_name)
				)
			);
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Data Not Found!'
			);
		}


		//output
		echo format_json($draw_json);
	}

	//(DONE)
	public function update_breakdown()
	{
		$breakdown_id = $this->input->post('breakdown_id');

		//rules validation
		$this->form_validation->set_rules('breakdown_id', 'id', 'trim|required');
		$this->form_validation->set_rules('qty', 'qty', 'trim|required|callback_numeric_wcomma|greater_than_equal_to[0]');
		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

		//validasi CI
		if ($this->form_validation->run() == FALSE) {
			//jika error
			$draw_json = array(
				'status' => 'error',
				'message' => 'Update Record Failed',
				'data' => array(
					'qty' => form_error('qty')
				)
			);
		} else {
			//cek data
			$row = $this->Breakdown_new_model->get_by_id($breakdown_id);
			if ($row) {
				//data yang di-post
				$datapost=array(
					'breakdown_id' => $this->input->post('breakdown_id', true),
					'qty' => $this->input->post('qty', true)
				);

				//update data
				$response = $this->Breakdown_new_model->update($breakdown_id, $datapost);
				if ($response) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Update Record Success'
					);
				}
				else{
					$draw_json = array(
						'status' => 'error',
						'message' => 'Update Record Failed'
					);
				}
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Data Not Found!'
				);
			}//endif: row cek
		}

		//output
		echo format_json($draw_json);
	}

	//(DONE)
	public function delete_breakdown_id($breakdown_id=null){
		//init
		$draw_json = array();

		//cek data
		$row = $this->Breakdown_new_model->get_by_id($breakdown_id);
		if ($row) {
			//delete
			$response = $this->Breakdown_new_model->delete_by_id($breakdown_id);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Delete Record Failed'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Data Not Found!'
			);
		}
		
		//output
		echo format_json($draw_json);
	}

    public function json_breakdown()
    {
        /* custom json output  start */
        $jsondata = $this->Catalogue_model->json_breakdown(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            //encoding string untuk mencegah XSS injection dengan htmlentities()
        	$id = $a->product_id;
        	$name = htmlentities($a->product_name); //encoding string
        	$product_type_name = htmlentities($a->product_type_name);
        	$product_category_name = htmlentities($a->product_category_name);
        	$product_subcategory_name = htmlentities($a->product_subcategory_name);
            $prc_name = htmlentities($a->prc_name);
            $unit_name = htmlentities($a->unit_name);
            
            
            $action_breakdown  = "<span class='btn btn-primary btn-xs' onclick='actionAddToForm({$id})'>Add</span>";
        	
        	
            //data yang akan ditampilkan dalam json
        	$temp_data = array(
        		'product_id' => $id,
        		'product_name' => $name,
        		'product_type_name' => $product_type_name,
        		'product_category_name' => $product_category_name,
        		'product_subcategory_name' => $product_subcategory_name,
                'prc_name' => $prc_name,
                'unit_name' => $unit_name,
                'tombol_addbreakdown' => $action_breakdown,
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json); //function ambil dari helper
        /* custom json output  end */
    }

    //(DONE)
	public function create($product_id=null)
	{
		//inisialisasi variabel
		$draw_json = array();
		$valid = true;
		$datapost = array(
			'ingridient[]' => $this->input->post('ingridient[]', true),
			'outlet[]' => $this->input->post('outlet[]'),
		);

		//proteksi direct access
		if (!$this->input->post() || empty($product_id) || !is_numeric($product_id)) {
			$valid = false;
			$draw_json = array(
				'status' => 'error',
				'message' => 'Direct access!'
			);
		}

		//validasi input
		$this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
		$this->form_validation->set_rules('ingridient[]', 'qty', 'trim|required|callback_numeric_wcomma|greater_than_equal_to[0]');
		$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
		if ($this->form_validation->run() == FALSE) {
			$valid = false;
			$draw_json = array(
				'status' => 'error',
				'message' => 'Create Record Failed!',
				'data' => array(
					'qty' => form_error('ingridient[]'),
					'outlet' => form_error('outlet[]')
				)
			);
		} else {
			$valid = true;
		}

		//validasi selected outlet
		$selected_outlet = array();
		if ($valid==true) {
			if ($datapost['outlet[]']) {
				$outletSubmitted = $datapost['outlet[]'];
				if ($outletSubmitted[0]=='all_outlet') {
					$outletlist = $this->Outlets_model->form_select();
					foreach ($outletlist as $value) {
						$selected_outlet[] = $value->outlet_id;
					}
				}
				else{
					foreach ($datapost['outlet[]'] as $outlet_id) {
						//validasi
						$row_outlet = $this->Outlets_model->get_by_id($outlet_id);
						if ($row_outlet) {
							$selected_outlet[] = $row_outlet->outlet_id;
						}
					}
				}
			}

			if (count($selected_outlet)<=0) {
				$valid = false;
				$draw_json = array(
					'status' => 'error',
					'message' => 'No Outlet Selected!'
				);
			}
		}

		//cek main product (master product)
		if ($valid==true) {
			$row = $this->Products_model->get_by_id($product_id);
			if ($row) {
				$valid = true;

				//update data master
				$data_mainproduct_update = array(
					'stock_management' => true,
					'data_status' => 'on'
				);
				$this->Products_model->update($product_id, $data_mainproduct_update);
			}
			else{
				$valid = false;

				//kalau product tidak ada
				$draw_json = array(
					'status' => 'error',
					'message' => 'Invalid Data'
				);
			}
		}
		
		//cek item product
		if ($valid==true) {
			foreach ($selected_outlet as $selected_outlet_id) {
				//init
				$data_breakdown_insertbatch = array();

				//cek main product di outlet
				$row_mainproduct_outlet = $this->Products_detail_model->get_by_product_and_outlet($product_id, $selected_outlet_id);
				if ($row_mainproduct_outlet) {
					//update jadi on
					$dataupdate_mainproduct_outlet = array(
						'data_status' => 'on'
					);
					$this->Products_detail_model->update($row_mainproduct_outlet->product_detail_id, $dataupdate_mainproduct_outlet);
				}
				else{
					//tambahkan data
					$datainsert_mainproduct_outlet = array(
						'product_fkid' => $product_id,
						'outlet_fkid' => $selected_outlet_id,
						'active' => 'off',
					);
					$this->Products_detail_model->insert($datainsert_mainproduct_outlet);
				}


				//cek item product
				foreach ($datapost['ingridient[]'] as $item_product_id => $item_product_value) {
					//validasi item product
					$row_itemproduct = $this->Products_model->get_by_id($item_product_id);
					if ($row_itemproduct) {
						//update stock management jadi yes
						$dataupdate_itemproduct = array(
							'stock_management' => true,
							'data_status' => 'on'
						);
						$this->Products_model->update($item_product_id, $dataupdate_itemproduct);

						//kalau ada, cek di outlet
						$row_itemproduct_detail = $this->Products_detail_model->get_by_product_and_outlet($item_product_id, $selected_outlet_id);
						if ($row_itemproduct_detail) {
							$item_product_detail_id = $row_itemproduct_detail->product_detail_id;

							//update detailnya
							$dataupdate_itemproduct_detail = array(
								'data_status' => 'on'
							);
							$this->Products_detail_model->update($item_product_detail_id, $dataupdate_itemproduct_detail);
						}
						else{
							//tambahkan baru
							$datainsert_itemproduct_detail = array(
								'product_fkid' => $item_product_id,
								'outlet_fkid' => $selected_outlet_id,
								'active' => 'off'
							);
							$this->Products_detail_model->insert($datainsert_itemproduct_detail);
							$item_product_detail_id = $this->db->insert_id();
						}
					}
					else{
						$valid = false;
					}


					//masukkan ke array batch
					if ($valid==true) {
						$data_breakdown_insertbatch[] = array(
							'product_fkid' => $product_id,
							'outlet_fkid' => $selected_outlet_id,
							'item_product_fkid' => $item_product_id,
							'item_product_detail_fkid' => $item_product_detail_id,
							'qty' => $item_product_value,
							'data_created' => current_millis(),
							'data_modified' => current_millis()
						);
					}
				}

				//insert to db
				if (!empty($data_breakdown_insertbatch)) {
					$this->Breakdown_new_model->insertbatch($data_breakdown_insertbatch);
				}
			}

			$draw_json = array(
				'status' => 'success',
				'message' => 'Create Record Success'
			);
		}

		//output
		echo format_json($draw_json);
	}



	//custom rules float
	public function numeric_wcomma($val)
	{
		$valid = false;
		if (is_numeric($val)) {
			$valid = true;
		}
		if (is_float($val)) {
			$valid = true;
		}

		if ($valid == false) {
        	$this->form_validation->set_message('numeric_wcomma', 'The {field} field must be number or decimal.');
        }
        return $valid;
	}

}

/* End of file Breakdown.php */
/* Location: ./application/controllers/products/ingridients/Breakdown.php */