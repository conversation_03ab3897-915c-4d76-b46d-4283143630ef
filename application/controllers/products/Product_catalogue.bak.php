<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Product_catalogue extends CI_Controller {

    private $photo_path;

	public function __construct()
	{
		parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->products_products('read_access'); //employee role access page | submenu ingridient
        $this->user_role->products('read_access'); //employee role access page
        
        
		$this->load->model('products/products_catalogue/Products_catalogue_model','Catalogue_model');
        $this->load->model('products/products_catalogue/Products_catalogue_taxgratuity_model','Taxdetail_model');
        $this->load->model('products/products_catalogue/Products_catalogue_multipleprice_model', 'Multipleprice_model');
        //$this->load->model('products/products/products_catalogue/Products_catalogue_link_model', 'Linkmenu_model');

		$this->load->model('products/type/Type_model'); //form
		$this->load->model('products/products_category/Products_category_model'); //form
		$this->load->model('products/products_subcategory/Products_subcategory_model'); //form
        $this->load->model('products/ingridients/Purchase_report_category_model'); //form
        $this->load->model('products/ingridients/Unit_model'); //form
        $this->load->model('outlet/Outlets_model'); //form
        $this->load->model('products/gratuity/Gratuity_model'); //form

        //inisialisasi variabel
        $photo_path = product_url();//'assets/outlets/'.$this->session->userdata('admin_id').'/'; //path 
        $photo_path = str_replace(base_url(), '', $photo_path);
        $photo_path = $photo_path.$this->session->userdata('admin_id').'/'; //path kalau ada dir
        $this->photo_path = $photo_path;//.'/'.$this->session->userdata('admin_id');
	}

   

	public function index()
	{
		$link = site_url('products/product-catalogue/'); //URL dengan slash
        $data = array(
            'kolomID' => 'product_id', //nama kolom primary key pada tabel
            'currentURL' => $link, //link yang sedang diakses
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionAddMaster' => $link.'add_master',
            'ajaxActionGetMaster' => $link.'get_master/',
            'ajaxActionUpdateMaster' => $link.'update_master/',
            'ajaxActionGetMasterDetail' => $link.'get_masterdetail/',
            'ajaxActionUpdateMasterDetail' => $link.'update_masterdetail/',
            
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/', //ambil data yang akan diedit
            
            'ajaxActionImportCSV' => $link.'import_process',
            );


        //form
        $data['form_select_products_type'] = $this->Type_model->form_select();
        $data['form_select_products_category'] = $this->Products_category_model->form_select();
        $data['form_select_products_subcategory'] = $this->Products_subcategory_model->form_select();
        $data['form_select_purchase_report_category'] = $this->Purchase_report_category_model->form_prc();
        $data['form_select_unit'] = $this->Unit_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_taxgratuity'] = $this->Gratuity_model->form_select();


		$this->load->view('products/products/catalogue/product_catalogue_v', $data);
	}

    public function add_master()
    {        
        $this->_rules('create'); // VALIDASI INPUT

        /* PROSES OLAH GAMBAR START */
        $photo = $this->_upload_photo(); // PROSES OLAH GAMBAR
        $photo_status = $photo['status_upload'];    // upload foto berhasil atau tidak
        $photo_available = $photo['available'];     // submit form memiliki gambar atau tidak
        $error_imageupload = $photo['error_msg'];   // pesan error saat upload foto
        $photo_path = $photo['path'];               // path direktori foto
        $photo_name = $photo['name'];               // nama foto yang diupload
        /* PROSES OLAH GAMBAR END   */


        /* CUSTOM FILTER START */
        /*validasi input multiple price START */
        //$multipleprice = $this->input->post('multipleprice[]');
        $multipleprice = (!empty($this->input->post('multipleprice[]'))) ? $this->input->post('multipleprice[]',TRUE) : null;

        //$multipleqty = $this->input->post('qty[]', TRUE);
        $multipleqty = (!empty($this->input->post('qty[]'))) ? $this->input->post('qty[]', TRUE) : null;
        if (count($multipleprice)>0 || count($multipleqty)>0) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|is_numeric');
        }

        /*cek value multiple price START*/
        $multiplepriceData = '';
        $multiplepriceArrayData = array();
        $i=0;
        foreach ($multipleprice as $index => $value) {
            $multiplepriceData .= $value;
            $multiplepriceArrayData[$i] = $value;
            $i++;
        }

        $qtyData = '';
        foreach ($multipleqty as $index => $value) {
            $qtyData .= $value;
        }
        /*cek value multiple price END */
        

        if (!empty($multiplepriceData) || !empty($qtyData)) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|required|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|required|is_numeric');
        }
        /*validasi input multiple price END */


        //verifikasi outlet sudah diisi atau belum multi outlet
        $status_checked_outlet = true;
        if ($this->input->post('outlet[]')==null && $this->input->post('outlet')==null) {
            $status_checked_outlet = false;
        }


        $taxgratuity = $this->input->post('taxgratuity[]');

        //valuating taxgratuity voucher dan discount
        $post_voucher = (empty($this->input->post('form_voucher'))) ? 'off' : 'on';
        $post_discount = (empty($this->input->post('form_discount'))) ? 'off' : 'on' 
        ;
        /* custom filter end */
        /* CUSTOM FILTER END */


        //POST VALUE
        $post_value = array(
            //master product value
            'name' => $this->input->post('name', TRUE),
            'formselect_cataloguetype' => $this->input->post('formselect_cataloguetype', TRUE),
            'formselect_type' => $this->input->post('formselect_type', TRUE),
            'formselect_category' => $this->input->post('formselect_category', TRUE),
            'formselect_subcategory' => $this->input->post('formselect_subcategory', TRUE),
            'formselect_prc' => $this->input->post('formselect_prc', TRUE),
            'formselect_unit' => $this->input->post('formselect_unit', TRUE),
            'formselect_stock' => $this->input->post('formselect_stock', TRUE),
            'barcode' => $this->input->post('barcode', TRUE),
            'sku' => $this->input->post('sku', TRUE),
            'photo' => $photo_name,

            //outlet product set value
            'outlet' => $this->input->post('outlet[]', TRUE),
            'price_buy' => $this->input->post('price_buy', TRUE),
            'price_sell' => $this->input->post('price_sell', TRUE),
            'multipleqty' => $multipleqty,
            'multipleprice' => $multipleprice,
            'taxgratuity' => $taxgratuity,
            'voucher' => $post_voucher,
            'discount' => $post_discount,
            'active' => $this->input->post('menu_active', TRUE)
        );

        // CEK BARCODE DAN SKU. barcode dan sku sudah dipakai atau belum START
        $barcode_available = true;
        if (!empty($post_value['barcode'])) {
            $cek_code['code'] = $post_value['barcode'];
            $response = $this->Catalogue_model->cek_barcodesku($cek_code, 'barcode', 'create');
            $barcode_available = $response['available'];
        }

        $sku_available = true;
        if (!empty($post_value['sku'])) {
            $cek_code['code'] = $post_value['sku'];
            $response = $this->Catalogue_model->cek_barcodesku($cek_code, 'sku', 'create');
            $sku_available = $response['available'];
        }
        // CEK BARCODE DAN SKU. barcode dan sku sudah dipakai atau belum END
        

        
        //VALIDASI INPUT
        if ($this->form_validation->run() == FALSE || $status_checked_outlet==FALSE || 
            ($photo_status===FALSE && $photo_available===TRUE) || //verifikasi kalau action create tidak ada foto
            $barcode_available === FALSE || $sku_available === FALSE //verifikasi kalau barcode atau sku sudah dipakai
            ) {

            $error_message = $this->session->set_flashdata('message', 'Error, Record Failed');
            //hapus gambar yang diupload bila validasi salah
            if ($photo_status===true) {
                $path = $photo_path.$photo_name;
                unlink($path); //hapus gambar yang baru saja diupload saat input gagal
            }

            //SETTING OUTPUT ERROR START
            $error_price = (empty(form_error('price_sell'))) ? form_error('multipleprice[]') : form_error('price_sell');
            $error_price = (empty($error_price)) ? form_error('qty[]') : $error_price;
            $error_tax = (empty($this->input->post('taxgratuity[]'))) ? form_error('taxgratuity[]') : '';
            $error_barcode = ($barcode_available===false) ? '<span class="text-danger">Barcode is already in use.</span>' : form_error('barcode');
            $error_sku = ($sku_available===false) ? '<span class="text-danger">SKU is already in use.</span>' : form_error('sku');

            
            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'formselect_cataloguetype' => form_error('formselect_cataloguetype'),
                        'formselect_type' => form_error('formselect_type'),
                        'formselect_category' => form_error('formselect_category'),
                        'formselect_subcategory' => form_error('formselect_subcategory'),
                        'formselect_prc' => form_error('formselect_prc'),
                        'formselect_unit' => form_error('formselect_unit'),
                        'formselect_stock' => form_error('formselect_stock'),
                        'barcode' => $error_barcode,
                        'sku' => $error_sku,
                        'outlet' => form_error('outlet[]'),
                        'price_buy' => form_error('price_buy'),
                        'price_sell' => $error_price,
                        'tax' => $error_tax,
                        'menu_active' => form_error('menu_active'),
                        'photo' => $error_imageupload
                    )
                );
            //SETTING OUTPUT ERROR END
            echo json_encode($response); //nanti digabungkan menjadi satu, jadi dihapus saja
        }
        else{
            //action untuk create master product
            /* custom input */
            //inisialisasi variabel
            $primarykey_master = null;
            $primarykey_masterdetail = null;
            $data_untuk_masterdetail = array();

            /*re-format value outlet yang dicentang untuk all outlet*/
            $outletSubmitted = $post_value['outlet'];
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }

            //definisikan kembali data yang akan dikirim/insert ke master product
            $data_untuk_master = array(
                'name' => $post_value['name'],
                'catalogue_type' => $post_value['formselect_cataloguetype'],
                'product_type_fkid' => $post_value['formselect_type'],
                'product_category_fkid' => $post_value['formselect_category'],
                'product_subcategory_fkid' => $post_value['formselect_subcategory'],
                'purchase_report_category_fkid' => $post_value['formselect_prc'],
                'unit_fkid' => $post_value['formselect_unit'],
                'stock_management' => $post_value['formselect_stock'],
                'barcode' => $post_value['barcode'],
                'sku' => $post_value['sku'],
                'photo' => $photo_name,
            );
            if (empty($data_untuk_master['barcode'])) {
                $data_untuk_master['barcode']=null;
            }
            if (empty($data_untuk_master['sku'])) {
                $data_untuk_master['sku']=null;
            }
            //$data_untuk_master['outlet_fkid'] = $b->outlet_id;

            $inputDB = $this->Catalogue_model->insert_master($data_untuk_master); /*edited */
            $primarykey_master = $inputDB['primary_key'];


            if ($inputDB['status_insert']===TRUE) {
                //kalau input master product berhasil

                //inisialisasi data yang dikirim untuk products_detail
                $data_untuk_masterdetail = array(
                    'product_fkid' => $primarykey_master,
                    'price_buy_start' => $post_value['price_buy'],
                    'price_buy' => $post_value['price_buy'],
                    'price_sell' => $post_value['price_sell'],
                    'voucher' => $post_value['voucher'],
                    'discount' => $post_value['discount'],
                    'active' => $post_value['active']
                    );
                
                foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                    $data_untuk_masterdetail['outlet_fkid'] = $checkedoutlet;
                    $inputDB_detail = $this->Catalogue_model->insert_masterdetail($data_untuk_masterdetail);

                    //tambahkan input multiprice dengan looping berdasarkan ID master product yang didapat
                    if ( $inputDB_detail['status_insert'] === TRUE ) { //kalau input master product_detail berhasil
                        $primarykey_masterdetail = $inputDB_detail['primary_key'];

                        //insert multiple price START
                        $i=0;
                        if (!empty($multipleqty)) {
                            foreach ($multipleqty as $index => $value) {
                                $dataMultiplePrice = array(
                                    'product_detail_fkid' => $primarykey_masterdetail, //primary key setelah input product_detail
                                    'qty' => $value,
                                    'price' => $multiplepriceArrayData[$i]
                                    );

                                //kalau multiprice tidak kosong tambahkan ke database
                                if ($dataMultiplePrice['qty']>0 AND $dataMultiplePrice['price']>0) {
                                    $inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePrice);
                                }
                                $i++;
                            }
                        }
                        //insert multiple price END

                        //insert tax gratuity START
                        if (!empty($taxgratuity)) {
                            foreach ($taxgratuity as $index => $value) {
                                //echo $index.'_'.$value.'';//cek value apakah benar ter-post
                                //cek dulu apakah index
                                $dataTax = array(
                                    'tax_fkid' => $index,
                                    'product_detail_fkid' => $primarykey_masterdetail, //primary key setelah input product_detail
                                    );
                                $insert_taxgratuity = $this->Taxdetail_model->insert_taxgratuity($dataTax);
                                //print_r('==='.$dataTax);
                            }
                        }
                        //insert tax gratuity END

                        //buat response
                        $response = array(
                            'status' => 'success',
                            'message' => 'Create Record Success'
                            );
                    }
                    else{ //kalau input master product gagal
                        //kalau gagal input products_detail di DB
                        $response = array(
                            'status' => 'error',
                            'message' => 'Error insert Product to Outlet'
                            );
                    }
                } //end foreach
            }
            else{
                //kalau input master product gagal
                $message_notif = $this->session->set_flashdata('message', 'Error Record Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
            }


            echo json_encode($response);
        }
    }

    public function get_master($master_id=null)
    {
        //inisialisasi
        $draw_json = array();

        //validasi ID
        if (empty($master_id) || !is_numeric($master_id)) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'ID Invalid'
            );
            echo format_json($draw_json);
            die();
        }

        //ambil data dari database
        $row = $this->Catalogue_model->get_by_id($master_id);
        $row_outlet = $this->Catalogue_model->get_masterdetail($master_id);

        //kalau data ada
        if ($row) {
            //deteksi gambar ada atau tidak
            $photo_name = $row->photo;
            $photo_path = $this->photo_path.$photo_name;
            $row->photo = (file_exists($photo_path)) ? base_url($photo_path) : noimage();


            $outletlist = array();
            foreach ($row_outlet as $ro) {
                $ro_data = array(
                    'outlet_id' => $ro->outlet_fkid,
                    'outlet_name' => $ro->outlet_name
                );
                array_push($outletlist, $ro_data);
            }
            $row_data = array(
                    'id' => $row->product_id,
                    'name' => html_entity_decode($row->product_name),
                    'catalogue_type' => html_entity_decode($row->catalogue_type),
                    'type' => html_entity_decode($row->product_type_fkid),
                    'category' => html_entity_decode($row->product_category_fkid),
                    'subcategory' => html_entity_decode($row->product_subcategory_fkid),
                    'purchase_report_category' => html_entity_decode($row->purchase_report_category_fkid),
                    'unit' => html_entity_decode($row->unit_fkid),
                    'unit_name' => html_entity_decode($row->unit_name),
                    'stock_management' => html_entity_decode($row->stock_management),
                    'barcode' => html_entity_decode($row->barcode),
                    'sku' => html_entity_decode($row->sku),
                    'photo' => html_entity_decode($row->photo),
                    'outlet' => $outletlist
                );
            $draw_json = array(
                    'status' => 'success',
                    'data' => $row_data
                );
        } else {
            $draw_json = array(
                    'status' => 'error',
                    'message' => 'Record Not Found'
                );
        }
        echo format_json($draw_json);
    }

    public function update_master()
    {
        //validasi ID
        $master_id = $this->input->post('id');

        $draw_json = array();
        $old_photo = '';
        if (empty($master_id) || !is_numeric($master_id)) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'ID Invalid'
            );
            echo format_json($draw_json);
            die();
        }
        else{
            //validasi database
            $row = $this->Catalogue_model->get_by_id($master_id);
            if ($row) {
                $old_photo = $row->photo;
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Invalid Data'
                );
                echo format_json($draw_json);
                die();
            }
        }



        $this->_rules('update'); // VALIDASI INPUT

        /* PROSES OLAH GAMBAR START */
        $photo = $this->_upload_photo(); // PROSES OLAH GAMBAR
        $photo_status = $photo['status_upload'];    // upload foto berhasil atau tidak
        $photo_available = $photo['available'];     // submit form memiliki gambar atau tidak
        $error_imageupload = $photo['error_msg'];   // pesan error saat upload foto
        $photo_path = $photo['path'];               // path direktori foto
        $photo_name = $photo['name'];               // nama foto yang diupload
        /* PROSES OLAH GAMBAR END   */


        /* CUSTOM FILTER START */

        //verifikasi outlet sudah diisi atau belum multi outlet
        $status_checked_outlet = true;
        if ($this->input->post('outlet[]')==null && $this->input->post('outlet')==null) {
            $status_checked_outlet = false;
        }
        /* CUSTOM FILTER END */


        //POST VALUE
        $post_value = array(
            //master product value
            'name' => $this->input->post('name', TRUE),
            'formselect_cataloguetype' => $this->input->post('formselect_cataloguetype', TRUE),
            'formselect_type' => $this->input->post('formselect_type', TRUE),
            'formselect_category' => $this->input->post('formselect_category', TRUE),
            'formselect_subcategory' => $this->input->post('formselect_subcategory', TRUE),
            'formselect_prc' => $this->input->post('formselect_prc', TRUE),
            'formselect_unit' => $this->input->post('formselect_unit', TRUE),
            'formselect_stock' => $this->input->post('formselect_stock', TRUE),
            'barcode' => $this->input->post('barcode', TRUE),
            'sku' => $this->input->post('sku', TRUE),
            'photo' => $photo_name,

            //outlet product set value
            'outlet' => $this->input->post('outlet[]', TRUE)
        );


        // CEK BARCODE DAN SKU. barcode dan sku sudah dipakai atau belum START
        $barcode_available = true;
        if (!empty($post_value['barcode'])) {
            $cek_code['id'] = $this->input->post('id');
            $cek_code['code'] = $post_value['barcode'];
            $response = $this->Catalogue_model->cek_barcodesku($cek_code, 'barcode', 'update');
            $barcode_available = $response['available'];
        }
        else{
            //kalau barcode kosong
            $post_value['barcode'] = null;
        }

        $sku_available = true;
        if (!empty($post_value['sku'])) {
            $cek_code['id'] = $this->input->post('id');
            $cek_code['code'] = $post_value['sku'];
            $response = $this->Catalogue_model->cek_barcodesku($cek_code, 'sku', 'update');
            $sku_available = $response['available'];
        }
        else{
            //kalau sku kosong
            $post_value['sku'] = null;
        }
        // CEK BARCODE DAN SKU. barcode dan sku sudah dipakai atau belum END

        
        //VALIDASI INPUT
        if ($this->form_validation->run() == FALSE || $status_checked_outlet==FALSE || 
            ($photo_status===FALSE && $photo_available===TRUE) || //verifikasi kalau action create tidak ada foto
            $barcode_available === FALSE || $sku_available === FALSE //verifikasi kalau barcode atau sku sudah dipakai
            ) { //end of open if else {

            $error_message = $this->session->set_flashdata('message', 'Error, Record Failed');
            //hapus gambar yang diupload bila validasi salah
            if ($photo_available===true) {
                $path = $photo_path.$photo_name;
                unlink($path); //hapus gambar yang baru saja diupload saat input gagal
            }

            //SETTING OUTPUT ERROR START
            $error_price = (empty(form_error('price'))) ? form_error('multipleprice[]') : form_error('price');
            $error_price = (empty($error_price)) ? form_error('qty[]') : $error_price;
            $error_tax = (empty($this->input->post('taxgratuity[]'))) ? form_error('taxgratuity[]') : '';
            $error_barcode = ($barcode_available===false) ? '<span class="text-danger">Barcode is already in use.</span>' : form_error('barcode');
            $error_sku = ($sku_available===false) ? '<span class="text-danger">SKU is already in use.</span>' : form_error('sku');
            
            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'formselect_cataloguetype' => form_error('formselect_cataloguetype'),
                        'formselect_type' => form_error('formselect_type'),
                        'formselect_category' => form_error('formselect_category'),
                        'formselect_subcategory' => form_error('formselect_subcategory'),
                        'formselect_prc' => form_error('formselect_prc'),
                        'formselect_unit' => form_error('formselect_unit'),
                        'formselect_stock' => form_error('formselect_stock'),
                        'outlet' => form_error('outlet[]'),
                        'barcode' => $error_barcode,
                        'sku' => $error_sku,
                        'photo' => $error_imageupload
                    )
                );
            //SETTING OUTPUT ERROR END
            echo json_encode($response); //nanti digabungkan menjadi satu, jadi dihapus saja
        }
        else{
            //hapus gambar lama bila update gambar
            if ($photo_status===TRUE && $photo_available===TRUE) {
                $filename = $photo_path.$row->photo;
                if (file_exists($filename) && !empty($row->photo)) {
                    //echo "The file $filename exists";
                    unlink($filename);
                }
            }

            //action untuk update master product

            /*re-format value outlet yang dicentang untuk all outlet*/
            $outletSubmitted = $post_value['outlet'];
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }

            //definisikan kembali data yang akan dikirim/insert ke master product
            $data_untuk_master = array(
                'name' => $post_value['name'],
                'catalogue_type' => $post_value['formselect_cataloguetype'],
                'product_type_fkid' => $post_value['formselect_type'],
                'product_category_fkid' => $post_value['formselect_category'],
                'product_subcategory_fkid' => $post_value['formselect_subcategory'],
                'purchase_report_category_fkid' => $post_value['formselect_prc'],
                'unit_fkid' => $post_value['formselect_unit'],
                'stock_management' => $post_value['formselect_stock'],
                'barcode' => $post_value['barcode'],
                'sku' => $post_value['sku'],
                'photo' => $photo_name,
            );
            //$data_untuk_master['outlet_fkid'] = $b->outlet_id;

            $updateDB = $this->Catalogue_model->update_master($master_id, $data_untuk_master); /*edited */

            if ($updateDB===TRUE) {
                //UPDATE outlet yang dicentang
                //buat semua products_detail data_status menjadi off untuk data yang mempunyai product_fkid = $master_id
                $data_to_update['data_status'] = 'off';
                $updateDB_off = $this->Catalogue_model->update_masterdetail_by_masterid($master_id, $data_to_update);


                if ($updateDB_off===TRUE) {
                    //UPDATE TO ON DATA DETAIL
                    /* perulangan outlet yang dicentang START */
                    foreach ($outletSubmitted as $checkedoutlet) {
                        //setelah meng-off-kan semua data yang ada di semua outlet, cek apakah menu pernah ada di outlet tersebut
                        $product_on_outlet = $this->Catalogue_model->get_masterdetail_on_outlet($master_id, $checkedoutlet);
                        if ($product_on_outlet['status']===TRUE) {
                            //kalau sudah ada, data_status yang ada di products_detail berdasarkan product_fkid + outlet_fkid diubah menjadi "on"
                            $updateDB_on = $this->Catalogue_model->update_masterdetail_datastatus_by_outlet($checkedoutlet, 'on');
                        }
                        else{
                            if ($product_on_outlet['count_data']==0) {
                                //kalau belum ada, tambahkan data baru untuk outlet tsb di products_detail
                                $data_to_detail = array(
                                    'product_fkid' => $master_id,
                                    'outlet_fkid' => $checkedoutlet,
                                    'price_buy_start' => 0,
                                    'price_buy' => 0,
                                    'voucher' => 'off',
                                    'discount' => 'off',
                                    'active' => 'off'
                                );
                                $insertDB_detail = $this->Catalogue_model->insert_masterdetail($data_to_detail);
                            }
                        }
                    }
                    /* perulangan outlet yang dicentang END */

                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Product Master Updated'
                    );
                }
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Record Failed'
                );
            }

            //output json
            echo format_json($draw_json);
        }
    }

    public function get_masterdetail($master_id, $outlet_id)
    {
        //inisialisasi
        $draw_json = array();
        $temp_data = array();

        //validasi ID
        if (empty($master_id) || !is_numeric($master_id)   || empty($master_id) || !is_numeric($master_id)) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'ID Invalid'
            );
            echo format_json($draw_json);
            die();
        }


        //ambil data dari database
        $row = $this->Catalogue_model->get_masterdetail_on_outlet($master_id, $outlet_id); //get data masterdetail
        if ($row['status']===true) {
            $row = $row['data'];
            $row_data = array(
                    'masterdetail_id' => $row->product_detail_id,
                    'price_buy' => ($row->price_buy),
                    'price_sell' => ($row->price_sell),
                    'voucher' => html_entity_decode($row->voucher),
                    'discount' => html_entity_decode($row->discount),
                    'active' => html_entity_decode($row->active),
                    //multiprice
                    //tax
                );

            //.....get data masterdetail multipleprice
            $row_multipleprice_data = array();
            $row_multipleprice = $this->Multipleprice_model->getMultiplePrice($row_data['masterdetail_id']);
            foreach ($row_multipleprice as $a) {
                $temp_data = array(
                    'multipleprice_id' => $a->multipleprice_id,
                    'qty' => $a->qty,
                    'price' => $a->price
                );

                array_push($row_multipleprice_data, $temp_data);
            }
            $row_data['multipleprice'] = $row_multipleprice_data;

            //.....get data masterdetail taxgratuity
            $row_taxgratuity_data = array();
            $row_taxgratuity = $this->Taxdetail_model->get_taxgratuity($row_data['masterdetail_id']);
            foreach ($row_taxgratuity as $a) {
                $temp_data = array(
                    'taxdetail_id' => $a->taxdetail_id,
                    'tax_id' => $a->tax_fkid
                );

                array_push($row_taxgratuity_data, $temp_data);
            }
            $row_data['taxgratuity'] = $row_taxgratuity_data;

            //buat output format json
            $draw_json = array(
                    'status' => 'success',
                    'data' => $row_data
                );
        } else {
            $this->session->set_flashdata('message', $row['message']);
            $draw_json = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //tampilkan data json
        echo format_json($draw_json);
    }

    public function update_masterdetail()
    {
        //inisialisasi
        $draw_json = array();
        $valid_input = true;

        //validasi ID
        $masterdetail_id = $this->input->post('masterdetail_id', true);
        if ((empty($masterdetail_id) || !is_numeric($masterdetail_id)) && $masterdetail_id!='all') {
            $draw_json = array(
                'status' => 'error',
                'message' => 'ID Invalid'
            );
            echo format_json($draw_json);
            die();
        }
        $outlet_id = $this->input->post('outlet_id');
        if ($outlet_id != 'all_outlet' && !is_numeric($outlet_id)) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Invalid Outlet'
            );
            echo format_json($draw_json);
            die();
        }


        //rules input
        $this->form_validation->set_rules('master_id', 'master', 'trim|required');
        $this->form_validation->set_rules('outlet_id', 'Outlet', 'trim|required');
        $this->form_validation->set_rules('price_buy', 'Buying Price', 'trim|required|is_numeric');
        $this->form_validation->set_rules('price_sell', 'Selling Price', 'trim|required|is_numeric');
        $this->form_validation->set_rules('menu_active', 'Menu Active', 'trim|required');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');


        /* CUSTOM FILTER START */
        $multipleprice = (!empty($this->input->post('multipleprice[]'))) ? $this->input->post('multipleprice[]',TRUE) : null;
        $multipleqty = (!empty($this->input->post('qty[]'))) ? $this->input->post('qty[]', TRUE) : null;

        if (count($multipleprice)>0 || count($multipleqty)>0) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|is_numeric');
        }

        /*cek value multiple price START*/
        $multiplepriceData = '';
        $multiplepriceArrayData = array();
        $i=0;
        foreach ($multipleprice as $index => $value) {
            $multiplepriceData .= $value;
            $multiplepriceArrayData[$i] = $value;
            $i++;
        }

        $qtyData = '';
        foreach ($multipleqty as $index => $value) {
            $qtyData .= $value;
        }
        /*cek value multiple price END */
        
        /*validasi input multiple price START */
        if (!empty($multiplepriceData) || !empty($qtyData)) {
            $this->form_validation->set_rules('multipleprice[]', 'multiple price', 'trim|required|is_numeric');
            $this->form_validation->set_rules('qty[]', 'quantity of multiple price', 'trim|required|is_numeric');
        }
        /*validasi input multiple price END */
        $taxgratuity = $this->input->post('taxgratuity[]');


        //form post data
        $data_post = array(
            'master_id' => $this->input->post('master_id'),
            'outlet_id' => $this->input->post('outlet_id'),
            'price_buy' => $this->input->post('price_buy'),
            'price_sell' => $this->input->post('price_sell'),
            'menu_active' => $this->input->post('menu_active'),

            'voucher' => ($this->input->post('form_voucher')==null) ? 'off' : $this->input->post('form_voucher'),
            'discount' => ($this->input->post('form_discount')==null) ? 'off' : $this->input->post('form_discount')
        );

        //validasi manual
        $data_post_menuactive = $data_post['menu_active'];
        if ($data_post_menuactive != '' && (($data_post_menuactive!='on_all') && ($data_post_menuactive!='on_sales') && ($data_post_menuactive!='on_link') && ($data_post_menuactive!='off'))) {
            $valid_input = false;
        }


        if ($this->form_validation->run() === FALSE || $valid_input===FALSE) {
            //SETTING OUTPUT ERROR START
            $error_message = ($valid_input===FALSE) ? 'Invalid Data' : 'Update Record Failed';
            $error_price = (empty(form_error('price_sell'))) ? form_error('multipleprice[]') : form_error('price');
            $error_price = (empty($error_price)) ? form_error('qty[]') : $error_price;
            
            $draw_json = array(
                'status' => 'error',
                'message' => $error_message,
                'data' => array(
                    'outlet_id' => form_error('outlet_id'),
                    'price_buy' => form_error('price_buy'),
                    'price_sell' => $error_price,
                    'menu_active' => form_error('menu_active')
                )
            );
        } else {
            //cek apakah outlet miliknya si login (belum ada)
            $master_id = $data_post['master_id'];
            $masterdetail_id_array = array();

            /* ERROR TEST */
            if ($data_post['outlet_id']=='all_outlet') {
                //get master detail
                $this->db->where('product_fkid', $master_id);
                $this->db->where('product_detail_data_status', 'on');
                $this->db->where('outlet_adminid', $this->session->userdata('admin_id'));
                $result = $this->db->get('view_products_outlet')->result();
                $i=0;
                foreach ($result as $a) {
                    $masterdetail_id_array[$i] = $a->product_detail_id;
                    $i++;
                }
                //print_r($masterdetail_id_array);die();
            }
            else{
                $masterdetail_id_array[0] = $masterdetail_id;
            }


            foreach ($masterdetail_id_array as $a) {
                $masterdetail_id = $a;
            
                $update_masterdetailDB = $this->Catalogue_model->update_masterdetail($masterdetail_id, $data_post);
                if ($update_masterdetailDB===TRUE) {
                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Update Record Success'
                    );

                    //update multiple price START
                    $update_multiplepriceDB = $this->Multipleprice_model->update_multiprice_datastatus_by_masterdetailid($masterdetail_id, 'off');
                    if ($update_multiplepriceDB===TRUE) {
                        $draw_json = array(
                            'status' => 'success',
                            //'message' => 'Turn off multiple price oke'
                        );


                        //manipulasi multipleprice START
                        $get_result = $this->Multipleprice_model->get_multiprice_by_masterdetailid($masterdetail_id);
                        $row = $get_result['data'];
                        $num_row = $get_result['num_rows'];

                        //insert multiple price START
                        $i=0;
                        if (!empty($multipleqty)) {
                            foreach ($multipleqty as $index => $value) {
                                $dataMultiplePrice = array(
                                        'product_detail_fkid' => $masterdetail_id, //primary key setelah input product_detail
                                        'qty' => $value,
                                        'price' => $multiplepriceArrayData[$i],
                                        'data_status' => 'on'
                                    );

                                if ($dataMultiplePrice['qty'] >0 && $dataMultiplePrice['price'] >0) {
                                    if ($i < $num_row) {
                                        //update data
                                        $multipleprice_id = $row[$i]->multipleprice_id; //primary key detail multiprice
                                        $updateData = $this->Multipleprice_model->update_multiprice($multipleprice_id, $dataMultiplePrice);
                                    }
                                    else{
                                        //tambah data
                                        $inpuDB_multiprice = $this->Multipleprice_model->insert_multiprice($dataMultiplePrice);
                                    }
                                    $i++;
                                }
                            }
                        }
                        //insert multiple price END
                    }
                    //update multiple price END


                    //update tax gratuity START
                    $update_taxgratuityDB = $this->Taxdetail_model->update_taxgratuity_datastatus_by_masterdetailid($masterdetail_id, 'off');
                    if ($update_taxgratuityDB===TRUE) {
                        $draw_json = array(
                            'status' => 'success',
                            'message' => 'Turn off All taxgratuity'
                        );


                        //manipulasi taxgratuity START
                        $get_result = $this->Taxdetail_model->get_taxgratuity_by_masterdetailid($masterdetail_id);
                        // echo $this->db->last_query();
                        // die();
                        $row = $get_result['data'];
                        //print_r($row);
                        $num_row = $get_result['num_rows'];

                        //insert tax gratuity START
                        $i=0;
                        if (!empty($taxgratuity)) {
                            foreach ($taxgratuity as $index => $value) {
                                $dataTax = array(
                                    'tax_fkid' => $index,
                                    'product_detail_fkid' => $masterdetail_id, //primary key setelah input product_detail
                                    'data_status' => 'on'
                                );
                                //print_r($dataTax);

                                //cek apakah data sudah ada atau belum
                                $this->db->where('tax_fkid', $index);
                                $this->db->where('product_detail_fkid', $masterdetail_id);
                                $cek = $this->db->get('products_detail_taxdetail');
                                $cek_row = $cek->row();
                                $cek_numrow = $cek->num_rows();

                                if ($cek_row) {
                                    $updateData = $this->Taxdetail_model->update_taxgratuity($masterdetail_id, $dataTax);
                                }
                                else{
                                    $insert_taxgratuity = $this->Taxdetail_model->insert_taxgratuity($dataTax);
                                }
                                
                                
                                
                                // if ($num_row==1) {
                                //     //$row[$i]->taxdetail_id;
                                //     //update data
                                //     $updateData = $this->Taxdetail_model->update_taxgratuity($masterdetail_id, $dataTax);
                                // }
                                // else{
                                //     //tambah data
                                //     echo $num_row;
                                //     echo "inikah?";
                                //     $insert_taxgratuity = $this->Taxdetail_model->insert_taxgratuity($dataTax);
                                // }
                                $i++;
                            }
                        }
                        //insert tax gratuity END
                    }
                    //update tax gratuity END
                    /* remake */
                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Update Record Success'
                    );
                }
            }
        }

        echo format_json($draw_json);
    }

	public function json()
    {
        //header('Content-Type: application/json');
        //echo $this->Catalogue_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Catalogue_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            //encoding string untuk mencegah XSS injection dengan htmlentities()
        	$id = $a->product_id;
        	$name = htmlentities($a->product_name); //encoding string
            $catalogue_type = htmlentities($a->catalogue_type); //encoding string
            $catalogue_type = ucfirst($catalogue_type); //membuat huruf pertama menjadi Kapital
        	$product_type_name = htmlentities($a->product_type_name);
        	$product_category_name = htmlentities($a->product_category_name);
        	$product_subcategory_name = htmlentities($a->product_subcategory_name);
            $prc_name = htmlentities($a->prc_name);
            $unit_name = htmlentities($a->unit_name);
            $barcode = htmlentities($a->barcode);
            $sku = htmlentities($a->sku);
        	
            //deteksi gambar ada atau tidak
            $photo_name = $a->photo;
            $photo_path = $this->photo_path.$photo_name;
            $photo = (file_exists($photo_path) && !empty($photo_name)) ? base_url($photo_path) : noimage();

            //tombol outlet
            $this->db->select('outlet_name,price_sell');
            $this->db->where('product_fkid', $id);
            $this->db->where('product_detail_data_status', 'on');
            $this->db->order_by('outlet_name', 'asc');
            $result = $this->db->get('view_catalogue_detail')->result();
            $tombol_outlet_available = '';
            $no=0;
            foreach ($result as $a) {
                $tombol_outlet_available .= '<br>';
                $tombol_outlet_available .= htmlentities($a->outlet_name);
                $tombol_outlet_available .= ': '.formatAngka($a->price_sell); //menampilkan harga di tiap outlet
                $no++;
            }
            $tombol_outlet = '<div class="btn btn-primary btn-xs tiptext">Outlet
                                <div class="description">'.$name.' available on: '.$tombol_outlet_available.'</div>
                                </div>';
            $tombol_linkmenu = '<div class="btn btn-primary btn-xs" onclick="tombolLink('.$id.')">Link</div>';
        	
            //tombol action pada datatable
            $action = $tombol_linkmenu."&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='tombolEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";

            //tombol action pada breakdown
            $outlet_id = ''; //biarerrorhilang
            $tombol_show  = "<button class='btn btn-primary btn-xs' onclick='tombolShow({$id})'>Show</button>";
            $action_breakdown  = "<button class='btn btn-primary btn-xs' onclick='actionShowBreakdown({$id},\"".($name)."\",{$outlet_id})'>Show</button>";
        	
        	
            //data yang akan ditampilkan dalam json
        	$temp_data = array(
        		'product_id' => $id,
        		'product_name' => $name,
                'catalogue_type' => $catalogue_type,
        		'product_type_name' => $product_type_name,
        		'product_category_name' => $product_category_name,
        		'product_subcategory_name' => $product_subcategory_name,
                'prc_name' => $prc_name,
                'unit_name' => $unit_name,
                'barcode' => $barcode,
                'sku' => $sku,
        		'photo' => '<img style="height: 40px; max-width: 40px;" src="'.$photo.'"/>',
                'action' => $action,
                'tombol_outlet' => $tombol_outlet,
                //brekdown menu
                'tombol_show' => $tombol_show,
                'action_breakdown' => $action_breakdown,
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json); //function ambil dari helper
        /* custom json output  end */
    }

    public function delete($product_id=null)
    {
        //inisialisasi variabel
        $draw_json = array();

        //validasi product
        if (empty($product_id) || !is_numeric($product_id)) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Invalid Product'
            );
        }
        else{
            $response = $this->Catalogue_model->delete($product_id);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        }

        //tampilkan data json
        echo format_json($draw_json);
    }






    


    // LINK MENU START
    public function get_product_link($product_id=null)
    {
        //inisialisasi
        $response = array();

        if (empty($product_id) || !is_numeric($product_id)) {
            $response = array(
                'status' => 'error',
                'message' => 'Invalid Product'
                );
        }
        else{
            $dataDB = $this->Linkmenu_model->get_product_link($product_id);
            if ($dataDB) {
                //looping ampbil data
                foreach ($dataDB as $a) {
                    $temp_data = array(
                            'id' => $a->pcl_id,
                            'name' => htmlentities($a->name),
                            'description' => htmlentities($a->description),
                            'product_id' => $a->product_detail_fkid,
                            'order_no' => $a->order_no
                        );

                    //gabungkan data
                    array_push($response, $temp_data);
                }
            }
        }

        //tampilkan data json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function action_link($product_id, $actionType=null) //actionType = 'create / update'
    {
        //inisialisasi
        $response = array();

        //validasi ci
        $this->form_validation->set_rules('addlink_id', 'addlink_id', 'trim'); //addlink_id di form addlink
        $this->form_validation->set_rules('name', 'Link Name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('description', 'Link Description', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('order', 'Link Order No', 'trim|required|is_numeric');

        //running validasi
        if ($this->form_validation->run() === FALSE) {
            //inputan tidak valid buat output response error
            if ($actionType=='create') { //error response untuk action create
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') { //error response untuk action update
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'description' => form_error('description'),
                        'order_no' => form_error('order')
                    )
                );
        } else {
            //inputan valid
            //data yang dikirim dari form untuk dikirim ke db
            $data = array(
                'pcl_id' => $this->input->post('id', TRUE),
                'product_detail_fkid' => $product_id,
                'name' => $this->input->post('name',TRUE),
                'description' => $this->input->post('description', TRUE),
                'order_no' => $this->input->post('order', TRUE)
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Linkmenu_model->insert_link($data);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->Linkmenu_model->update_link($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function get_link_data($productlink_id=null)
    {
        //inisialisasi variabel
        $response = array();
        if (empty($productlink_id) || !is_numeric($productlink_id)) {
            $response = array(
                'status' => 'error',
                'message' => 'Invalid Link ID or Undefined'
                );
        }
        else{
            $row = $this->Linkmenu_model->get_link_by_id($productlink_id);
            if ($row) {
                //kalau data ditemukan
                $response = array(
                    'status' => 'success',
                    'data' => array(
                        'id' => set_value('addlink_id', $row->pcl_id),
                        'product_id' => set_value('product_id', $row->product_detail_fkid),
                        'name' => set_value('name', html_entity_decode($row->name)),
                        'description' => set_value('description', html_entity_decode($row->description)),
                        'order_no' => set_value('order', html_entity_decode($row->order_no))
                        )
                    );
            }
            else{
                /* kalau data link tidak ditemukan */
                $this->session->set_flashdata('message', 'Record Not Found');
                $response = array(
                        'status' => 'error',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat ouput json
        echo json_encode($response);
    }

    public function delete_link($productlink_id=null)
    {
        //validasi data apakah data merupakan milik user?
        /* codingan menyusul */

        //inisialisasi
        $response = array();

        $row = $this->Linkmenu_model->get_link_by_id($productlink_id); //change
        if ($row) {
            $response = $this->Linkmenu_model->delete_link($productlink_id); //change

            //hapus semua link detail
            /* next jobdesk */

            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    //product link detail
    public function get_product_link_detail($link_id)
    {
        $response = array();
        if (empty($link_id) || !is_numeric($link_id)) {
            $response = array(
                'status' => 'error',
                'message' => 'Invalid ID or Link Detail'
                );
        }
        else{
            $dataDB = $this->Linkmenu_model->get_product_linkdetail_by_id($link_id);
            if ($dataDB) {
                //looping ampbil data
                foreach ($dataDB as $a) {
                    $temp_data = array(
                            'pcl_detail_id' => $a->pcl_detail_id,
                            'product_link_id' => $a->pcl_fkid,
                            'product_link_name' => htmlentities($a->product_link_name),
                            'name' => htmlentities($a->product_name),
                            'price_add' => htmlentities($a->price_add),
                            'price_normal' => htmlentities($a->price)
                        );

                    //gabungkan data
                    array_push($response, $temp_data);
                }
            }
        }

        //tampilkan data json
        header('Content-Type: application/json');
        echo json_encode($response);
    }
    public function delete_product_link_detail($link_id)
    {
        //validasi data apakah data merupakan milik user?
        /* codingan menyusul */

        //inisialisasi
        $response = array();

        //$row = $this->Catalogue_model->get_link_by_id($productlink_id); //cek data apakah ada / benar
        $row = true;
        if ($row) {
            $response = $this->Linkmenu_model->delete_product_to_link($link_id); //change

            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }
    public function update_product_link_detail($link_id)
    {
        //cek dulu apakah link_id valid dengan pemanipulasi data
        /* desk menyusul */

        //inisialisasi
        $response = array();

        //ceking numerik
        $this->form_validation->set_rules('editpriceadd[]', 'Additional Price', 'trim|required|is_numeric');
        if ($this->form_validation->run() === FALSE) {
            $response = array(
                'status' => 'error',
                'message' => form_error('editpriceadd[]')
                );
        } else {
            foreach ($this->input->post('editpriceadd[]') as $index => $value) {
                $data = array(
                    'pcl_detail_id' => $link_id,
                    'price_add' => $value
                    );
                $response = $this->Linkmenu_model->update_product_to_link($data);
                if ($response===true) {
                    $response = array(
                        'status' => 'success',
                        'message' => 'Update Record Success'
                        );
                }
            }
            
        }

        //tampilkan data json
        header('Content-Type: application/json');
        echo json_encode($response);
    }
    public function add_product_to_link($link_id)
    {
        //inisialisasi
        $response = array();

        if (empty($link_id) || !is_numeric($link_id)) {
            $response = array(
                'status' => 'error',
                'message' => 'Link ID undefined or invalid'
                );
        }
        else{
            //rules input
            $this->form_validation->set_rules('product_priceadd[]', 'Additional Price', 'trim|required|is_numeric');

            //cek apakah link ID merupakan pemilik yang input
            /*(verifikasi data menyusul)*/

            if ($this->form_validation->run() === FALSE) {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed'); //setting pesan error
                $response = array(
                    'status' => 'error',
                    'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                    'data' => array(
                        'price_add' => form_error('product_priceadd[]')
                        )
                    );
            } else {
                //inisialisasi
                $jumlah_data = 0;
                $jumlah_success_insert = 0;

                //insert ke DB table product catalogue link detail
                $postPriceAdd = $this->input->post('product_priceadd[]');

                foreach ($postPriceAdd as $index => $value) {
                    $postData = array(
                        'pcl_fkid' => $link_id,
                        'product_detail_fkid' => $index,
                        'price_add' => $value
                        );

                    $response = $this->Linkmenu_model->insert_product_to_link($postData);
                    ($response===true) ? $jumlah_success_insert++ : '';
                    $jumlah_data++;
                }

                if ($jumlah_data==$jumlah_success_insert && $jumlah_data>0 && $jumlah_success_insert>0) {
                    $response = array(
                        'status' => 'success',
                        'message' => 'Create Record Success'
                        );
                }
                if ($jumlah_data > $jumlah_success_insert && $jumlah_success_insert>0) {
                    $response = array(
                        'status' => 'success',
                        'message' => 'Some Record Error'
                        );
                }
            }
        }

        //tampilkan data json
        header('Content-Type: application/json');
        echo json_encode($response);
        //echo "hasil proses dengan link: $link_id";
    }
    // LINK MENU END



    public function _rules($action=null)
    {
        /* RULES VALIDASI INPUT START */
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('formselect_cataloguetype', 'catalog type', 'trim|required');
        $this->form_validation->set_rules('formselect_type', 'type', 'trim|required');
        $this->form_validation->set_rules('formselect_category', 'category', 'trim|required');
        $this->form_validation->set_rules('formselect_subcategory', 'subcategory', 'trim|required');
        $this->form_validation->set_rules('formselect_prc', 'purchase report category', 'trim|required');
        $this->form_validation->set_rules('formselect_unit', 'unit', 'trim|required');
        $this->form_validation->set_rules('formselect_stock', 'stock management', 'trim|required');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required'); //all outlet
        $this->form_validation->set_rules('barcode', 'barcode', 'trim|min_length[5]|max_length[30]');
        $this->form_validation->set_rules('sku', 'sku', 'trim|min_length[5]|max_length[30]');
        /* RULES VALIDASI INPUT END */

        switch ($action) {
            case 'create':
                $this->form_validation->set_rules('price_buy', 'buying price', 'trim|required|is_numeric');
                $this->form_validation->set_rules('price_sell', 'selling price', 'trim|required|is_numeric');
                $this->form_validation->set_rules('qty[]', 'quantity', 'trim|is_numeric');
                $this->form_validation->set_rules('multipleprice[]', 'price', 'trim|is_numeric');
                $this->form_validation->set_rules('taxgratuity[]', 'tax & gratuity', 'trim');
                $this->form_validation->set_rules('menu_active', 'menu active', 'trim|required');
                break;
            case 'update':
                $this->form_validation->set_rules('id', 'id', 'trim|required');
                break;
            default:
                die('Undefined rules action');
                break;
        }

        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

    public function _upload_photo()
    {
        /* PROSES OLAH GAMBAR by aziz */

        //inisialisasi variabel
        $error_imageupload = '';
        $photo_available = false;   //form ada submit foto atau tidak
        $photo_upload = false;      //foto berhasil diupload atau tidak

        /*konfigurasi upload gambar start*/
        // $photo_path = product_url(); //(ambil dari assets_helper)
        // $photo_path = str_replace(base_url(), '', $photo_path); //ambil path dari assets/images/products

        /* photo path by private $photo_path */
        $photo_path = $this->photo_path;

        $photo_name = $this->session->userdata('admin_id').'_'.$this->input->post('name').'_'.date('YmdHis'); //temporary name
        $photo_name = strtolower($photo_name); //mengubah nama file menjadi kecil semua
        $photo_name = str_replace(' ', '', $photo_name); //fungsi untuk menghilangkan spasi pada nama
        $config['upload_path']          = $photo_path; //'./_tes_upload/'.$album;
        $config['allowed_types']        = 'gif|jpg|png|jpeg'; //ekstensi yang diizinkan
        $config['max_size']             = 100; //maksimal size
        $config['file_name']            = $photo_name; //nama gambar akan diganti saat diupload
        //$config['max_width']  = '1024'; //atur lebar maksimal gambar
        //$config['max_height']  = '768'; //atur tinggi maksimal gambar
        $config['remove_spaces'] = true;
        /*konfigurasi upload gambar end*/
        
        /*proses upload gambar start*/
        //buat direktori berdasarkan owner
        $dir_exist = true;
        if (!is_dir($photo_path)) {
            /* kalau mkdir() disable, comment dari sini */
            $old = umask(0);
            mkdir($photo_path, 0775, true); //buat direktori image produk untuk per-owner
            umask($old);
            /* kalau mkdir() disable, comment sampai sini */
            $dir_exist = false; // dir not exist
            $draw_json = array(
                'status' => 'error',
                'message' => 'Dir not Exist'
            );
        }

        //upload gambar
        $this->load->library('upload', $config);
        if (!empty($_FILES['photo']['name']) && $_FILES['photo']['name']!="") { //bila upload foto
            $photo_available = true;

            if ( ! $this->upload->do_upload('photo')){
                $error_imageupload = array('error' => $this->upload->display_errors());
                $error_imageupload = $error_imageupload['error'];
                $photo_upload = false; //status upload
                //echo "upload gagal";
            }
            else{
                $data = array('upload_data' => $this->upload->data());
                $photo_name = $data['upload_data']['file_name'];
                $photo_upload = true; //status upload
                //echo "upload success";
            }
        }
        else{
            $photo_available = false;//custom
            $photo_name = '';
            //echo "tidak ada gambar";
        }
        /* PROSES OLAH GAMBAR END*/

        return $photo = array(
            'status_upload' => $photo_upload,
            'available' => $photo_available,
            'error_msg' => $error_imageupload,
            'path' => $photo_path,
            'name' => $photo_name,
        );
    }

    public function jsonSpoil($outlet_id=null){

        header('Content-Type: application/json');
        //echo $this->Catalogue_model->jsonSpoil($outlet_id); //default json output

        /* custom json output  start */
        $jsondata = $this->Catalogue_model->jsonSpoil($outlet_id); //ambil data json
        // print_r($jsondata);die();
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->product_detail_id;

            $name = "<span id='name'>".htmlentities($a->name)."</span>"; //encoding string
            //$outlet_id = $_GET['outlet'];
            $price_update = "<span id='price_buy'>".($a->price_update)."</span";
            $price_sell = ($a->price_sell);
            $unit_name = htmlentities($a->unit_name);
            $catalogue_type = htmlentities($a->catalogue_type);
            $category_name=$a->sub_category;
            $date = $a->data_created;

            $active = htmlentities($a->data_status);
            ($active=='on') ? $active='Yes' : $active='No';

            
            
            $data_status = "<select name='data_status[]' class='form-control'>";
            $data_status.= "<option value='on'>Yes</option>";
            $data_status.= "<option value='off'>No</option></select>";

            $value='';
            $value2=0;
            $ket="";
            if($this->session->has_userdata("jum_".$id) || $this->session->has_userdata("ket_".$id)){
                $value=$this->session->userdata("jum_".$id);
                $value2=($this->session->userdata("jum_".$id))*$a->price_update;
                $ket=$this->session->userdata("ket_".$id);
            }
            $keterangan = "<textarea name=keterangan[] id='ket' onfocusout='keterangan(this)' class='form-control'>".$ket."</textarea>";

            $total = "<span id='total'>".$value2."</span>";
            $jumlah = "<input type='number' min='0' id='inputJumlah' onchange='jumlah(this)' style='width:75px;' name='jumlah[]' class='form-control' value='".$value."' />";
            $jumlah .= "<input type='hidden' value=".$id." id='ing_id' name='ingridients_id[]'/>";
             //untuk spoil
            
            
            $temp_data = array(
                'product_detail_id' => $id,
                'name' => $name,
                'price_update' => $price_update,
                'price_sell' => $price_sell,
                'unit_name' => $unit_name,
                'data_status' => $active,
                'keterangan' => $keterangan,
                'data_status' =>$data_status,
                'jumlah' => $jumlah,
                'total' => $total,
                'data_created' => $date,
                'category_name' => $category_name,
                'catalogue_type' => $catalogue_type,
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json); 
        /* custom json output  end */
    }
    
}

/* End of file Product_catalogue.php */
/* Location: ./application/controllers/products/Product_catalogue.php */