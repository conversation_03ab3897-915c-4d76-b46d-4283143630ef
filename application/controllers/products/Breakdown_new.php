<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Breakdown_new extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/breakdown/Breakdown_model','Breakdown_model');
		// $this->load->model('products/ingridients/Breakdown_model');
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('products/products_catalogue/Products_model');
		$this->load->model('products/products_catalogue/Products_detail_model');
		$this->load->model('products/products_catalogue/Products_variant_model');
	}

	public function index()
	{
		$data = array(
			'form_select_outlet' => $this->Outlets_model->form_select()
		);
		// $this->load->view('products/breakdown/breakdown_v', $data);

		//other
		$data['ajax_exportimport'] = current_url().'/modal_exportimport';

		$data['page_title'] = 'Breakdown';
		$this->template->view('products/breakdown/breakdown_v2', $data);

	}

	public function datatables()
	{
		$json = $this->Products_model->json_breakdown();
		$json_decode = json_decode($json);

		$data_array = array();
		foreach ($json_decode->data as $r) {
			$data_array[] = array(
				'product_id' => $r->product_id,
				'product_name' => htmlentities($r->product_name),
				'catalogue_type' => htmlentities(ucfirst($r->catalogue_type))
			);
		}

		//remake json data
		$draw_json = array(
			'draw' => $json_decode->draw,
			'recordsTotal' => $json_decode->recordsTotal,
			'recordsFiltered' => $json_decode->recordsFiltered,
			'data' => $data_array
		);

		//output
		echo format_json($draw_json);
	}

	public function datatables_detail()
	{
		$json = $this->Products_detail_model->json_breakdown_detail();
		$json_decode = json_decode($json);

		$data_array = array();
		foreach ($json_decode->data as $r) {
			$product_name = (!empty($r->variant_name)) ? $r->product_name.' ('.$r->variant_name.')' : $r->product_name;
			// $product_name = $r->product_variant_name;
			$data_array[] = array(
				'product_id' => $r->product_id,
				'product_name' => htmlentities($product_name),
				'unit_name' => htmlentities(ucfirst($r->unit_name)),
				'variant_id' => $r->variant_id,
				'variant_name' => htmlentities($r->variant_name)
			);
		}

		//remake json data
		$draw_json = array(
			'draw' => $json_decode->draw,
			'recordsTotal' => $json_decode->recordsTotal,
			'recordsFiltered' => $json_decode->recordsFiltered,
			'data' => $data_array
		);

		//output
		echo format_json($draw_json);
	}

	public function get_product_outlet($product_id=null)
	{
		//get data on outlet
		if ($this->session->userdata('user_type')=='employee') {
			$this->db->where_in('outlet_fkid', $this->session->userdata('outlet_access'));
		}
		$this->db->select('product_detail_id AS id, outlet_fkid AS outlet_id, outlet_name, variant_fkid AS variant_id, variant_name');
		$result = $this->Products_detail_model->get_by_product_id($product_id);

		//init
		$outlet_list = array();
		$variant_list = array();
		$data = array();

		$outlet_prev = '';
		$variant_prev = null;
		foreach ($result as $r) {
			if ($outlet_prev!=$r->outlet_id) {
				$outlet_list[] = array(
					'outlet_id' => $r->outlet_id,
					'outlet_name' => htmlentities($r->outlet_name)
				);
			}
			$outlet_prev = $r->outlet_id;

			/*
			if ($variant_prev!=$r->variant_id) {
				$variant_list[] = array(
					'variant_id' => $r->variant_id,
					'variant_name' => htmlentities($r->variant_name)
				);
			}
			$variant_prev = $r->variant_id;
			*/
			if (!empty($r->variant_id)) {
				$variant_list[$r->variant_id] = array(
					'variant_id' => $r->variant_id,
					'variant_name' => htmlentities($r->variant_name),
				);
			}
		}

		$draw_json = array(
			'outlet_list' => $outlet_list,
			'variant_list' => $variant_list,
			'result' => $result
		);

		echo format_json($draw_json);
	}

	public function get_breakdown($product_id=null, $outlet_id=null, $variant_id=null)
	{
		//init
		$draw_json = array();

		//ambil product_detail_id
		$variant_id = ($variant_id==0) ? null : $variant_id;
		$row = $this->Products_detail_model->get_by_product_outlet_variant($product_id, $outlet_id, $variant_id);
		if ($row) {
			//get breakdown
			$data = array();
			// $this->db->where('data_status', 'on');
			// $this->db->where('product_detail_data_status', 'on');
			$result = $this->Breakdown_model->get_by_productdetail($row->product_detail_id);
			foreach ($result as $r) {
				$data[] = array(
					'breakdown_id' => $r->breakdown_id,
					'product_name' => htmlentities($r->item_product_name) . ((!empty($r->item_variant_name)) ? ' ('.$r->item_variant_name.')' : ''),
					'price' => $r->price_buy,
					'qty' => $r->qty,
					'unit_name' => htmlentities($r->unit_name),
					'hpp' => $r->hpp,
				);
			}

			$draw_json = array(
				'status' => 'success',
				'data' => $data
			);
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'data' => array()
			);
		}

		echo format_json($draw_json);
	}

	public function create()
	{
		//init
		$draw_json = array();
		$valid = true;
		$error_msg = array(
			'product' => '',
			'outlet' => '',
			'variant' => '',
			'qty' => '',
		);
		$selected_outlet = array();
		$selected_variant = array();
		$ingridient = array();


		//VALIDATION START
		$this->form_validation->set_error_delimiters('', '');

		//cek product
		$this->form_validation->set_rules('product', 'product', 'trim|required');
		$product_id = $this->input->post('product',true);
		$row_product = $this->Products_model->get_by_id($product_id);
		if (!$row_product) {
			$valid = false;
			$error_msg['product'] = 'Invalid Product';
		}

		//cek outlet
		$this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
		if (!empty($this->input->post('outlet[]'))) {
			foreach ($this->input->post('outlet[]') as $outlet_id) {
				$row = $this->Outlets_model->get_by_id($outlet_id);
				if ($row) {
					$selected_outlet[$outlet_id] = htmlentities($row->name);
				}
			}

			if (empty($selected_outlet)) {
				$valid = false;
				$error_msg['outlet'] = 'Invalid Outlet';
			}
		}

		//cek variant
		$this->form_validation->set_rules('variant[]', 'variant', 'trim|required');
		if (!empty($this->input->post('variant[]'))) {
			foreach ($this->input->post('variant[]') as $variant_id) {
				if ($variant_id==0) {
					$selected_variant[] = null;
				}
				else{
					$row = $this->Products_variant_model->get_by_variant_product($variant_id, $product_id);
					if ($row) {
						$selected_variant[] = $variant_id;
					}
				}
			}

			if (empty($selected_variant)) {
				$valid = false;
				$error_msg['variant'] = 'Invalid Variant';
			}
		}


		//cek qty
		$error_msg_qty = array();
		if (!empty($this->input->post('qty[][]')) && $valid==true) {
			foreach ($this->input->post('qty') as $index0 => $value0) {
				//cek product
				$item_product_id = $index0;
				$row_product = $this->Products_model->get_by_id($item_product_id);
				if ($row_product) {
					foreach ($this->input->post('qty['.$index0.']') as $index1 => $value1) {
						$key = key($value1);
						
						//cek variant
						$variant_id = $index1;
						$variant_id = ($variant_id==null || $variant_id=='null') ? null : $variant_id;
						$row_variant = ($variant_id==null) ? true : $this->Products_variant_model->get_by_variant_product($variant_id, $item_product_id);
						if ($row_variant) {
							foreach ($this->input->post('qty['.$index0.']['.$index1.']') as $value) {
								if (!is_numeric($value)) {
									$valid = false;
									$error_msg_qty[] = array(
										'id' => $key,
										'qty' => 'The qty must numeric.'
									);
								}
								else{
									//save item
									$ingridient[] = array(
										'item_product_id' => $item_product_id,
										'variant_id' => $variant_id,
										'qty' => $value,
										'row_id' => $key
									);
								}
							}
						}
					}
				}
			}
		}
		else{
			$valid = false;
			$error_msg['ingredient'] = 'The ingredient is required.';
		}


		
		//validasi
		if ($this->form_validation->run() == FALSE || $valid==false) {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Create Record Failed',
				'error_data' => array(
					'product' => (!empty(form_error('product'))) ? form_error('product') : $error_msg['product'],
					'outlet' => (!empty(form_error('outlet[]'))) ? form_error('outlet[]') : $error_msg['outlet'],
					'variant' => (!empty(form_error('variant[]'))) ? form_error('variant[]') : $error_msg['variant'],
					'ingredient' => (!empty($error_msg['ingredient'])) ? $error_msg['ingredient'] : '',
					'item_list' => $error_msg_qty
				)
			);
		}
		else {
			//init
			$ready_to_insert_batch = array();
			$valid_add = true;
			$cm = current_millis();
			$error_catch = array();

			foreach ($selected_outlet as $outlet_id => $outlet_name) {
				foreach ($selected_variant as $variant_id) {
					//init
					$valid_to_add = array();

					//cek variant di product detail
					$product_detail_id = '';
					$row_pdv = $this->Products_detail_model->get_by_product_outlet_variant($product_id, $outlet_id, $variant_id);
					if ($row_pdv) {
						$product_detail_id = $row_pdv->product_detail_id;
					}
					else{
						//insert manual
						$data_insertPD_mainbreakdown = array(
							'product_fkid' => $product_id,
							'outlet_fkid' => $outlet_id,
							'variant_fkid' => $variant_id,
						);
						$response_pd_mainbreakdown = $this->Products_detail_model->insert($data_insertPD_mainbreakdown);
						$product_detail_id = $this->db->insert_id();
					}

					//get current breakdown list
					$cek = $this->Breakdown_model->get_by_productdetail($product_detail_id);
					foreach ($cek as $r) {
						$valid_to_add[$r->item_product_detail_id] = $r->item_product_name;
					}


					//insert ke breakdown
					foreach ($ingridient as $r) {
						//variable
						$outlet_id = $outlet_id;
						$item_product_id = $r['item_product_id'];
						$item_variant_id = $r['variant_id'];
						$item_qty = $r['qty'];
						$item_product_detail_id = '';

						//cek product di outlet
						$row_pd = $this->Products_detail_model->get_by_product_outlet_variant($item_product_id, $outlet_id, $item_variant_id);
						if ($row_pd) {
							$item_product_detail_id = $row_pd->product_detail_id;
						}
						else{
							//insert PD
							$data_insertPD = array(
								'product_fkid' => $item_product_id,
								'outlet_fkid' => $outlet_id,
								'variant_fkid' => $item_variant_id,
							);
							$response_pd = $this->Products_detail_model->insert($data_insertPD);
							$item_product_detail_id = $this->db->insert_id();
						}


						//insert ke breakdown
						// $data_insert_breakdown = array(
						// 	'outlet_fkid' => $outlet_id,
						// 	'product_fkid' => $product_id,
						// 	'product_detail_fkid' => $product_detail_id, //product detail dari master yang akan di-breakdown
						// 	'item_product_fkid' => $item_product_id,
						// 	'item_product_detail_fkid' => $item_product_detail_id,
						// 	'qty' => $item_qty,
						// );
						// $this->Breakdown_model->insert($data_insert_breakdown);

						if (empty($valid_to_add[$item_product_detail_id])) {
							$ready_to_insert_batch[] = array(
								'outlet_fkid' => $outlet_id,
								'product_fkid' => $product_id,
								'product_detail_fkid' => $product_detail_id,
								'item_product_fkid' => $item_product_id,
								'item_product_detail_fkid' => $item_product_detail_id,
								'qty' => $item_qty,
								'data_created' => $cm,
								'data_modified' => $cm
							);
						}
						else{
							$valid_add = false;
							// echo "data already added: ".$valid_to_add[$item_product_detail_id];
							// echo $r['row_id']."----";
							$error_catch[] = array(
								'id' => $r['row_id'],
								'product' => $valid_to_add[$item_product_detail_id].' already added on Outlet '.$outlet_name.'.'
							);
						}
					} //endforeach: insert item ingridient
				}

			}//endforeach: insert by outlet

			if ($valid_add) {
				//insert batch
				$response = $this->Breakdown_model->insertbatch($ready_to_insert_batch);
				if ($response) {
					$draw_json = array(
						'status' => 'success',
						'message' => 'Create Record Success'
					);
				}
				else {
					$draw_json = array(
						'status' => 'error',
						'message' => 'Create Record Error'
					);
				}
			}
			else {
				$draw_json = array(
					'status' => 'error',
					'message' => 'Data already added!',
					'error_data' => array('item_list' => $error_catch)
				);
			}




		}

		//output
		echo format_json($draw_json);
	}

	public function update()
	{
		//init
		$draw_json = array();
		$valid = true;
		$error_message = '';
		$breakdown_id = $this->input->post('breakdown', TRUE);
		$product_id = $this->input->post('product', TRUE);
		$qty = $this->input->post('qty', TRUE);

		//validation
		$this->form_validation->set_rules('breakdown', 'breakdown', 'trim|required');
		$this->form_validation->set_rules('product', 'product', 'trim|required');
		$this->form_validation->set_rules('qty', 'qty', 'trim|required|is_numeric');
		$this->form_validation->set_error_delimiters('','');

		//cek product
		$row = $this->Products_model->get_by_id($product_id);
		if (!$row) {
			$valid = false;
			$error_message = 'Invalid Product';
		}

		//cek breakdown product
		if ($valid) {
			$row_bp = $this->Breakdown_model->get_by_id($breakdown_id);
			if (!$row_bp) {
				$valid = false;
				$error_message = 'Invalid Breakdown';
			}
		}

		if ($this->form_validation->run() == FALSE || $valid == false) {
			$error_message = (!empty($error_message)) ? $error_message : form_error('product');
			$error_message = (!empty($error_message)) ? $error_message : form_error('breakdown');
			$error_message = (!empty($error_message)) ? $error_message : form_error('qty');

			$draw_json = array(
				'status' => 'error',
				'message' => $error_message
			);
		} else {
			//simpan data
			$data_update = array(
				'qty' => $qty
			);
			$response_update = $this->Breakdown_model->update($breakdown_id, $data_update);
			if ($response_update) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Update Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Update Record Error'
				);
			}
		}

		//output
		echo format_json($draw_json);
	}

	public function delete($breakdown_id=null)
	{
		//cek breakdown id
		$row = $this->Breakdown_model->get_by_id($breakdown_id);
		if ($row) {
			$response = $this->Breakdown_model->delete_by_id($breakdown_id);
			if ($response) {
				$draw_json = array(
					'status' => 'success',
					'message' => 'Delete Record Success'
				);
			}
			else{
				$draw_json = array(
					'status' => 'error',
					'message' => 'Delete Record Error'
				);
			}
		}
		else{
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//output
		echo format_json($draw_json);
	}

	public function modal_exportimport()
	{
		$data = array(
			'form_select_outlet' => $this->Outlets_model->form_select(),
			'url_export' => current_url().'/../export'
		);
		echo $this->load->view('products/breakdown/export_import_breakdown_v', $data, true);
	}

}

/* End of file Breakdown_new.php */
/* Location: ./application/controllers/products/Breakdown_new.php */