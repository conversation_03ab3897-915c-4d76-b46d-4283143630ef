<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Production extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->production_production('read_access'); //employee role access page | submenu
		$this->user_role->production('read_access'); //employee role access page | menu

		$this->load->model('production/production/Production_model');
        $this->load->model('products/ingridients/Unit_model'); //form
        $this->load->model('products/products_catalogue/Products_catalogue_model');
        $this->load->model('outlet/Outlets_model');

        $this->load->model('production/itembreakdown/Production_itembreakdown_model');
	}

	public function index()
	{
		$link = site_url('production/production/'); //URL dengan slash
        $data = array(
            'kolomID' => 'production_id', //nama kolom primary key pada tabel
            'currentURL' => $link,
            'ajaxActionDatatableList' => $link.'json/', //tampilkan semua data product
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'create',
            'ajaxActionUpdate' => $link.'update',
            'ajaxActionGetData' => $link.'get_data/', //ambil data yang akan diedit
        );

        $data['form_select_unit'] = $this->Unit_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_product'] = $this->Products_catalogue_model->form_select();

        $data['page_title'] = 'Production';
		$this->template->view('production/production/production_v', $data);
	}

	public function json()
	{
		/* custom json output  start */
        $jsondata = $this->Production_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->production_id;
        	$recipe_name = htmlentities($a->recipe_name); //encoding string
            $outlet_name = htmlentities($a->outlet_name);
        	$qty = $a->qty_recipe;
            $qty_primary = $a->qty_primary;

        	//tombol show
        	$action = '';
        	$action = '<button class="btn btn-primary btn-xs" onclick="tombolShow('.$id.')">Show</button>';
        	// $action .= "&nbsp;&nbsp;&nbsp;";
        	// $action .= "<a href='javascript:void(0)' onclick='tombolEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
        	
        	$temp_data = array(
        		'production_id' => $id,
        		'recipe_name' => $recipe_name,
                'outlet_name' => $outlet_name,
        		'qty' => $qty,
        		'hpp' => '-',
        		'price_sell' => '-',
        		'porsi' => '-',
        		'hpp_porsi' => '-',
        		'action' => $action
        	);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
	}

    public function get_data($production_id)
    {
        $row = $this->Production_model->get_by_id($production_id);
        if ($row) {
            $harga_beli = 0;
            $harga_jual = 0;
            $outlet_id = $row->outlet_fkid;

            $get_datadetail = $this->Production_model->get_detail_by_id($production_id);
            $datadetail = array();
            foreach ($get_datadetail as $r) {
                //harga produk di outlet
                $harga_di_outlet = $this->Products_catalogue_model->get_masterdetail_on_outlet($r->product_fkid, $outlet_id);
                if (!empty($harga_di_outlet['data'])) {
                    $harga_beli = $harga_di_outlet['data']->price_buy;
                    $harga_jual = $harga_di_outlet['data']->price_sell;
                }

                $temp_data = array(
                    'productiondetail_id' => $r->productiondetail_id,
                    'product_id' => $r->product_fkid,
                    'product_name' => htmlentities($r->product_name),
                    'unit_name' => htmlentities($r->unit_name),
                    'qty' => $r->qty,
                    'harga_beli' => $harga_beli,
                    'harga_jual' => $harga_jual,
                    'detail_type' => htmlentities($r->detail_type)
                );
                array_push($datadetail, $temp_data);
            }

            $draw_json = array(
                'status' => 'success',
                'data' => $datadetail
            );
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Data not Found'
            );
        }

        echo format_json($draw_json);
    }

    public function create()
    {
        //inisialisasi
        $draw_json = array();
        $cek_valid = TRUE;
        $production_id = null;

        //data yang dikirim
        $datapost = array(
            'recipe_id' => $this->input->post('recipe_id', TRUE),
            'qty_recipe' => $this->input->post('qty', TRUE),
            'outlet_id' => $this->input->post('outlet', TRUE),
            'qty_primary' => $this->input->post('qty2', TRUE),

            //form
            'ingridient[]' => $this->input->post('ingridient[]', TRUE),
            'endproduct[]' => $this->input->post('endproduct[]', TRUE),
            'residual[]' => $this->input->post('residual[]', TRUE)
        );

        //simpana ke variabel sendiri
        $outlet_id = $datapost['outlet_id'];

        //setting validasi
        $this->form_validation->set_rules('recipe_id', 'recipe', 'trim|required');
        $this->form_validation->set_rules('qty', 'qty', 'trim|required|is_numeric');
        $this->form_validation->set_rules('outlet', 'outlet', 'trim|required');
        $this->form_validation->set_rules('qty2', 'qty', 'trim|required|is_numeric');
        $this->form_validation->set_rules('ingridient[]', 'ingridient qty', 'trim|required|is_numeric');
        $this->form_validation->set_rules('endproduct[]', 'end product qty', 'trim|required|is_numeric');
        $this->form_validation->set_rules('residual[]', 'residual qty', 'trim|required|is_numeric');

        
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

        //validasi
        if ($this->form_validation->run() == FALSE) {
            $cek_valid = FALSE;
            $draw_json = array(
                'status' => 'error',
                'message' => 'Create Record Failed',
                'data' => array(
                    'recipe' => form_error('recipe_id'),
                    'qty_recipe' => form_error('qty'),
                    'outlet' => form_error('outlet'),
                    'qty_primary' => form_error('qty2'),
                    'ingridient' => form_error('ingridient[]'),
                    'endproduct' => form_error('endproduct[]'),
                    'residual' => form_error('residual[]'),
                )
            );
        } else {
            //cek itembreakdown recipe
            $cek_recipe = $this->Production_itembreakdown_model->get_by_id($datapost['recipe_id']);
            if (!$cek_recipe) {
                $cek_valid = FALSE;
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Invalid Recipe!'
                );
            }
            else{
                //cek outlet
                $cek_outlet = $this->Outlets_model->get_by_id($datapost['outlet_id']);
                if (!$cek_outlet) {
                    $cek_valid = FALSE;
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Invalid Outlet!'
                    );
                }
            }


            if ($cek_valid===TRUE) {
                $data_create = array(
                    'itembreakdown_fkid' => $datapost['recipe_id'],
                    'outlet_fkid' => $datapost['outlet_id'],
                    'qty_recipe' => $datapost['qty_recipe'],
                    'qty_primary' => $datapost['qty_primary']
                );

                $response_datacreate = $this->Production_model->insert_master($data_create);
                $production_id = $this->db->insert_id();

                if ($response_datacreate) {
                    //insert detail ingridient
                    foreach ($datapost['ingridient[]'] as $product_id => $value) {
                        //cek apakah product sudah ada di outlet
                        $row_outlet = $this->Products_catalogue_model->get_masterdetail_on_outlet($product_id, $outlet_id);
                        //kalau product sudah belum ada add dulu
                        if ($row_outlet['status']==false) {
                            //insert product ke product_detail
                            //data master detail
                            $data_insertdetail = array(
                                'product_fkid' => $product_id,
                                'outlet_fkid' => $outlet_id,
                                'price_buy_start' => 0,
                                'price_buy' => 0,
                                'price_sell' => 0,
                                'voucher' => 'off',
                                'discount' => 'off',
                                'active' => 'off',
                            );
                            $response_insertdetail = $this->Products_catalogue_model->insert_masterdetail($data_insertdetail);
                        }
                        
                        $data_createdetail = array(
                            'production_fkid' => $production_id,
                            'product_fkid' => $product_id,
                            'qty' => $value,
                            'detail_type' => 'ingridient'
                        );
                        $insert_detail = $this->Production_model->insert_masterdetail($data_createdetail);
                    }

                    //insert detail endproduct
                    foreach ($datapost['endproduct[]'] as $product_id => $value) {
                        //cek apakah product sudah ada di outlet
                        $row_outlet = $this->Products_catalogue_model->get_masterdetail_on_outlet($product_id, $outlet_id);

                        //kalau product sudah belum ada add dulu
                        if ($row_outlet['status']==false) {
                            //insert product ke product_detail
                            //data master detail
                            $data_insertdetail = array(
                                'product_fkid' => $product_id,
                                'outlet_fkid' => $outlet_id,
                                'price_buy_start' => 0,
                                'price_buy' => 0,
                                'price_sell' => 0,
                                'voucher' => 'off',
                                'discount' => 'off',
                                'active' => 'off',
                            );
                            $response_insertdetail = $this->Products_catalogue_model->insert_masterdetail($data_insertdetail);
                        }

                        $data_createdetail = array(
                            'production_fkid' => $production_id,
                            'product_fkid' => $product_id,
                            'qty' => $value,
                            'detail_type' => 'endproduct'
                        );
                        $insert_detail = $this->Production_model->insert_masterdetail($data_createdetail);
                    }

                    //insert detail residual
                    foreach ($datapost['residual[]'] as $product_id => $value) {
                        //cek apakah product sudah ada di outlet
                        $row_outlet = $this->Products_catalogue_model->get_masterdetail_on_outlet($product_id, $outlet_id);
                        //kalau product sudah belum ada add dulu
                        if ($row_outlet['status']==false) {
                            //insert product ke product_detail
                            //data master detail
                            $data_insertdetail = array(
                                'product_fkid' => $product_id,
                                'outlet_fkid' => $outlet_id,
                                'price_buy_start' => 0,
                                'price_buy' => 0,
                                'price_sell' => 0,
                                'voucher' => 'off',
                                'discount' => 'off',
                                'active' => 'off',
                            );
                            $response_insertdetail = $this->Products_catalogue_model->insert_masterdetail($data_insertdetail);
                        }

                        $data_createdetail = array(
                            'production_fkid' => $production_id,
                            'product_fkid' => $product_id,
                            'qty' => $value,
                            'detail_type' => 'residual'
                        );
                        $insert_detail = $this->Production_model->insert_masterdetail($data_createdetail);
                    }

                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Create Record Success'
                    );
                }//end if datacreate
            }//cek validasi
        }

        echo format_json($draw_json);
    }

    public function delete($production_id=null)
    {
        //inisialisasi
        $draw_json = array();

        //cek data
        $row = $this->Production_model->get_by_id($production_id);
        if ($row) {
            //hapus
            $response = $this->Production_model->delete($production_id);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Data not Found!'
            );
        }

        echo format_json($draw_json);
    }

}

/* End of file Production.php */
/* Location: ./application/controllers/production/Production.php */