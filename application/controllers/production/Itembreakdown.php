<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Itembreakdown extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->production_itembreakdown('read_access'); //employee role access page | submenu
		$this->user_role->production('read_access'); //employee role access page

		$this->load->model('production/itembreakdown/Production_itembreakdown_model');
        $this->load->model('production/itembreakdown/Production_itembreakdown_detail_model');
        $this->load->model('products/ingridients/Unit_model'); //form
        $this->load->model('products/products_catalogue/Products_catalogue_model');
        $this->load->model('outlet/Outlets_model');
	}

	public function index()
	{
		$link = site_url('production/itembreakdown/'); //URL dengan slash
        $data = array(
            'kolomID' => 'itembreakdown_id', //nama kolom primary key pada tabel
            'currentURL' => $link,
            'ajaxActionDatatableList' => $link.'json/', //tampilkan semua data product
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'create',
            'ajaxActionUpdate' => $link.'update',
            'ajaxActionGetData' => $link.'get_data/', //ambil data yang akan diedit
            'ajaxActionGetData_endproductall' => $link.'get_endproduct_all/' //ambil data yang akan diedit
        );

        $data['form_select_unit'] = $this->Unit_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_product'] = $this->Products_catalogue_model->form_select();

        $data['page_title'] = 'Production Item Breakdown';
		$this->template->view('production/itembreakdown/itembreakdown_v', $data);
	}

	public function json()
	{
		/* custom json output  start */
        $jsondata = $this->Production_itembreakdown_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->itembreakdown_id;
        	$name = htmlentities($a->name); //encoding string
        	$qty = $a->qty;
        	$product_name = htmlentities($a->product_name);
        	$unit_name = htmlentities($a->unit_name);

        	//tombol show
        	$action = '<button class="btn btn-primary btn-xs" onclick="tombolShow('.$id.')">Show</button>';
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='tombolEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
            $action_pilih = '<button class="btn btn-primary btn-xs" onclick="tombolPilih('.$id.')">Select</button>';
        	
        	$temp_data = array(
        		'itembreakdown_id' => $id,
        		'name' => $name,
        		'qty' => $qty,
        		'product_name' => $product_name,
        		'unit_name' => $unit_name,
        		'action' => $action,
                'action_pilih' => $action_pilih
        	);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
	}

    public function json_recipeadddetail()
    {
        /* custom json output  start */
        $jsondata = $this->Production_itembreakdown_model->json_recipeadddetail(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->product_id;
            $product_name = htmlentities($a->product_name);
            $unit_name = htmlentities($a->unit_name);

            //tombol show
            $action = '<button class="btn btn-primary btn-xs addProduct" data-id="'.$id.'">Recipe</button>';
            $action .= '&nbsp;&nbsp;&nbsp;';
            $action .= '<button class="btn btn-primary btn-xs addEndProduct" data-id="'.$id.'">Product</button>';
            $action_add = '<button class="btn btn-primary btn-xs addToForm" data-id="'.$id.'">Add</button>';
            
            
            $temp_data = array(
                'product_id' => $id,
                'product_name' => $product_name,
                'unit_name' => $unit_name,
                'action' => $action,
                'action_add' => $action_add
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

    public function get_data($itembreakdown_id)
    {
        $row = $this->Production_itembreakdown_model->get_by_id($itembreakdown_id);
        if ($row) {
            $draw_json = array(
                'status' => 'success',
                'data' => array(
                    'name' => htmlentities($row->name),
                    'product_id' => $row->product_fkid,
                    'qty' => $row->qty,
                    'unit_id' => $row->unit_id,
                    'outlet' => array()
                )
            );

            $getdata = $this->Production_itembreakdown_model->available_outlet($itembreakdown_id);
            foreach ($getdata as $a) {
                $temp_data = array(
                    'outlet_id' => $a->outlet_fkid,
                    'outlet_name' => $a->outlet_name
                );
                array_push($draw_json['data']['outlet'], $temp_data);
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Data not Found'
            );
        }

        echo format_json($draw_json);
    }
    public function get_detail_all($itembreakdown_id, $outlet_id)
    {
        $getdata = $this->Production_itembreakdown_detail_model->get_detail_all($itembreakdown_id, $outlet_id);
        $draw_json = array();
        foreach ($getdata as $a) {
            $getdata2 = $this->Production_itembreakdown_detail_model->get_hpp($a->product_fkid, $outlet_id);
            $hpp = $getdata2->hpp;
            $price_sell = $getdata2->price_sell;

            $temp_data = array(
                'ibdetail_id' => $a->ibdetail_id,
                'product_id' => $a->product_fkid,
                'product_name' => $a->product_name,
                'detail_type' => $a->detail_type,
                'qty' => $a->qty,
                'unit_name' => $a->unit_name,
                'hpp' => $hpp,
                'price_sell' => $price_sell,
                'total' => $a->qty * $hpp
            );
            array_push($draw_json, $temp_data);
        }

        echo format_json($draw_json);
    }

    public function create()
    {
        $draw_json = array();

        //data yang dikirim
        $data_post = array(
            'name' => $this->input->post('name'),
            'product_id' => $this->input->post('product'),
            'qty' => $this->input->post('qty'),
            'outlet' => $this->input->post('outlet[]'),
            'recipedetail[]' => $this->input->post('recipedetail[]'),
            'endproduct[]' => $this->input->post('endproduct[]'),
        );

        //rules validasi
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('product', 'primary product', 'trim|required');
        $this->form_validation->set_rules('qty', 'qty', 'trim|required|is_numeric');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
        $this->form_validation->set_rules('recipedetail[]', 'recipe detail', 'trim|required|is_numeric');
        $this->form_validation->set_rules('endproduct[]', 'end products', 'trim|required|is_numeric');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

        if ($this->form_validation->run() == FALSE) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Create Record Failed',
                'data' => array(
                    'name' => form_error('name'),
                    'product' => form_error('product'),
                    'qty' => form_error('qty'),
                    'outlet' => form_error('outlet[]'),
                    'recipedetail' => form_error('recipedetail[]'),
                    'endproduct' => form_error('endproduct[]')
                )
            );
        } else {
            //masukkan data ke DB master
            $data_insert_master = array(
                'name' => $data_post['name'],
                'product_fkid' => $data_post['product_id'],
                'qty' => $data_post['qty'],
            );
            $response = $this->Production_itembreakdown_model->insert($data_insert_master);
            $primary_key = $this->db->insert_id();
            if ($response) {
                //insert available outlet
                foreach ($data_post['outlet'] as $id => $value) {
                    $getdataoutlet = $this->Outlets_model->get_by_id($value);
                    if ($value == $getdataoutlet->outlet_id) { //kalau outlet yang dicentang valid
                        $checkedOutlet = $value;
                        $data_insert_availableoutlet = array(
                            'itembreakdown_fkid' => $primary_key,
                            'outlet_fkid' => $value
                        );
                        //masukkan ke db
                        $response_insert = $this->Production_itembreakdown_model->insert_available_outlet($data_insert_availableoutlet);

                        //insert recipe detail
                        if (!empty($data_post['recipedetail[]'])) {
                            foreach ($data_post['recipedetail[]'] as $product_idx => $value) {
                                /*cek apakah produk ada di outlet, kalau tidak ada insert dulu */
                                $cekproduk = $this->Production_itembreakdown_detail_model->cek_produk($product_idx, $checkedOutlet);
                                $getproduct_detail = $cekproduk->row();
                                //print_r($getproduct_detail);
                                if ($getproduct_detail) {
                                    $product_detail_id = $getproduct_detail->product_detail_id;
                                }

                                //data untuk dimasukkan ke itembreakdown detail
                                $data_ibdetail = array(
                                    'itembreakdown_fkid' => $primary_key,
                                    'detail_type' => 'recipe',
                                    'outlet_fkid' => $checkedOutlet,
                                    'product_fkid' => $product_idx,
                                    'qty' => $value,
                                );


                                //kalau produk belum ada tambahkan produk
                                if ($cekproduk->num_rows()<1) {
                                    //tambahkan produk ke product_detail
                                    $datainsertproduct = array(
                                        'product_fkid' => $product_idx,
                                        'outlet_fkid' => $checkedOutlet,
                                        'price_buy_start' => 0,
                                        'price_buy' => 0,
                                        'price_sell' => 0,
                                        'voucher' => 'off',
                                        'discount' => 'off',
                                        'active' => 'off',
                                    );
                                    $response_insertproductdetail = $this->Products_catalogue_model->insert_masterdetail($datainsertproduct);
                                    
                                    //tambahkan data ke itembreakdowndetail
                                    $response_ibdetail = $this->Production_itembreakdown_detail_model->insert_detail($data_ibdetail);
                                }
                                elseif ($cekproduk->num_rows()==1) {
                                    //update product jadi on
                                    $response_updateproductdetail = $this->Production_itembreakdown_detail_model->active_produk($product_detail_id);
                                    //echo $this->db->last_query();

                                    //tambahkan data ke itembreakdowndetail
                                    $response_ibdetail = $this->Production_itembreakdown_detail_model->insert_detail($data_ibdetail);
                                }
                                else{
                                    //duplikat data
                                    //echo "|duplikat";
                                    $draw_json = array(
                                        'status' => 'error',
                                        'message' => 'Error: Duplicate Data!'
                                    );
                                }
                            }
                        }//end of insert recipe detail

                        //insert enproduct
                        if (!empty($data_post['endproduct[]'])) {
                            foreach ($data_post['endproduct[]'] as $product_idx => $value) {
                                /*cek apakah produk ada di outlet, kalau tidak ada insert dulu */
                                $cekproduk = $this->Production_itembreakdown_detail_model->cek_produk($product_idx, $checkedOutlet);
                                //kalau product ada di outlet
                                if ($cekproduk->row()) {
                                    $product_detail_id = $cekproduk->row()->product_detail_id;
                                }


                                //data untuk dimasukkan ke itembreakdown detail
                                $data_ibdetail = array(
                                    'itembreakdown_fkid' => $primary_key,
                                    'detail_type' => 'endproduct',
                                    'outlet_fkid' => $checkedOutlet,
                                    'product_fkid' => $product_idx,
                                    'qty' => $value,
                                );


                                //kalau produk belum ada tambahkan produk
                                if ($cekproduk->num_rows()<1) {
                                    //tambahkan produk ke product_detail
                                    $datainsertproduct = array(
                                        'product_fkid' => $product_idx,
                                        'outlet_fkid' => $checkedOutlet,
                                        'price_buy_start' => 0,
                                        'price_buy' => 0,
                                        'price_sell' => 0,
                                        'voucher' => 'off',
                                        'discount' => 'off',
                                        'active' => 'off',
                                    );
                                    $response_insertproductdetail = $this->Products_catalogue_model->insert_masterdetail($datainsertproduct);
                                    
                                    //tambahkan data ke itembreakdowndetail
                                    $response_ibdetail = $this->Production_itembreakdown_detail_model->insert_detail($data_ibdetail);
                                }
                                elseif ($cekproduk->num_rows()==1) {
                                    //update product jadi on
                                    $response_updateproductdetail = $this->Production_itembreakdown_detail_model->active_produk($product_detail_id);
                                    //echo $this->db->last_query();

                                    //tambahkan data ke itembreakdowndetail
                                    $response_ibdetail = $this->Production_itembreakdown_detail_model->insert_detail($data_ibdetail);
                                }
                                else{
                                    //duplikat data
                                    //echo "|duplikat";
                                    $draw_json = array(
                                        'status' => 'error',
                                        'message' => 'Error: Duplicate Data!'
                                    );
                                }
                            }
                        }//end of insert endproduct
                    }//end of kalau outlet yang dicentang valid
                }//end of foreach
                
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Create Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Create Record Failed'
                );
            }

        }

        echo format_json($draw_json);
    }

    public function update()
    {
        $datapost = array(
            'id' => $this->input->post('id',TRUE),
            'name' => $this->input->post('name',TRUE),
            'product' => $this->input->post('product',TRUE), //primary product
            'qty' => $this->input->post('qty',TRUE),
            'outlet' => $this->input->post('outlet_available[]'),
            //'outlet_detail' => $this->input->post('outlet_detail')
        );

        //rules validasi
        $this->form_validation->set_rules('id', 'id', 'trim|required');
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('product', 'primary product', 'trim|required');
        $this->form_validation->set_rules('qty', 'qty', 'trim|required|is_numeric');
        $this->form_validation->set_rules('outlet_available[]', 'outlet', 'trim|required');
        // $this->form_validation->set_rules('outlet_detail', 'outlet detail', 'trim');
        // $this->form_validation->set_rules('recipedetail[]', 'recipe detail', 'trim|required|is_numeric');
        // $this->form_validation->set_rules('endproduct[]', 'end products', 'trim|required|is_numeric');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

        if ($this->form_validation->run() == FALSE) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Update Record Failed',
                'data' => array(
                    'name' => form_error('name'),
                    'product' => form_error('product'),
                    'qty' => form_error('qty'),
                    'outlet' => form_error('outlet_available[]'),
                    //'outlet_detail' => form_error('outlet_detail'),
                    // 'recipedetail_edit' => form_error("recipedetail[]"),
                    // 'endproduct_edit' => form_error('endproduct[]')
                )
            );
        } else {
            //cek data
            $row = $this->Production_itembreakdown_model->get_by_id($datapost['id']);
            if ($row) {
                //data ditemukan
                //update data master
                $datapost_master = array(
                    //'id' => $datapost['id'],
                    'name' => $datapost['name'],
                    'product_fkid' => $datapost['product'],
                    'qty' => $datapost['qty']
                );

                $response = $this->Production_itembreakdown_model->update($datapost['id'], $datapost_master);
                if ($response===true) {
                    //update available outlet
                    /* buat available outlet jadi off semua */
                    $datapost_availableoutlet = array('data_status'=>false);
                    $response_ao_off = $this->Production_itembreakdown_model->update_available_outlet_by_itembreakdown($datapost['id'], $datapost_availableoutlet);
                    //kalau buat jadi off sukses
                    if ($response_ao_off) {
                        foreach ($datapost['outlet'] as $index => $outlet_id) {
                            //cek apakah outlet yang dicentang benar
                            //inisialisasi variabel
                            /* outlet_id = // ID Outlet yang dicentang */

                            //echo "id: ".$outlet_id.'|';
                            $getdataoutlet = $this->Outlets_model->get_by_id($outlet_id);
                            if ($outlet_id == $getdataoutlet->outlet_id) { //kalau outlet yang dicentang valid
                                /* cek apakah di tabel itembreakdown_outlet sudah ada?
                                * kalau sudah ada  maka ganti data_status menjadi true */
                                $cekdata = $this->Production_itembreakdown_model->get_available_outlet($datapost['id'], $outlet_id);
                                if ($cekdata) {
                                    //kalau data ada, update status
                                    $datapost_ao = array(
                                        //'itemoutlet_id' => $cekdata->itemoutlet_id,
                                        'data_status' => true
                                    );
                                    $response_ao_on = $this->Production_itembreakdown_model->update_available_outlet($cekdata->itemoutlet_id, $datapost_ao);
                                }
                                else{
                                    //kalau data belum ada, masukkan ke db
                                    $data_insert_availableoutlet = array(
                                        'itembreakdown_fkid' => $datapost['id'],
                                        'outlet_fkid' => $outlet_id
                                    );
                                    //masukkan ke db
                                    $response_insert = $this->Production_itembreakdown_model->insert_available_outlet($data_insert_availableoutlet);
                                }

                            }
                        }
                    }


                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Update Record Success'
                    );
                }
                else{
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Update Record Failed'
                    );
                }
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Invalid Data on Update'
                );
            }
        }

        echo format_json($draw_json);
    }

    public function update_detail($itembreakdown_id=null, $outlet_id=null)
    {
        $draw_json = array();

        //cek data apakah valid
        $row = $this->Production_itembreakdown_model->get_by_id($itembreakdown_id);
        if ($row) {
            //kalau $itembreakdown_id valid
            $datapost = array(
                'outlet_selected' => $this->input->post('outlet_selected'), //multiple value
                'qty' => $this->input->post('qty[]'),
                'selectdetail' => $this->input->post('selectdetail'), //recipe detail or endproduct
            );

            //buat rules validation
            if ($outlet_id=='alloutlet' || $outlet_id=='multipleoutlet') {
                $this->form_validation->set_rules('outlet_selected[]', 'selected outlet', 'trim|required');
            }
            $this->form_validation->set_rules('qty[]', 'qty', 'trim|required|is_numeric');
            $this->form_validation->set_rules('selectdetail', 'select detail', 'trim|required');
            $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

            //eksekusi validasi
            if ($this->form_validation->run() == FALSE) {
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Record Failed',
                    'data' => array(
                        'outlet_id' => form_error('outlet_id'),
                        'outlet_selected' => form_error('outlet_selected[]'),
                        'qty' => form_error('qty[]'),
                        'selectdetail' => form_error('selectdetail')
                    )
                );
            } else {
                //kalau outlet_id cuma satu outlet
                if (!empty($outlet_id) && $outlet_id!='alloutlet' && $outlet_id!='multipleoutlet') {
                    //cek product yang ditambahkan di form apakah ada di outlet
                    foreach ($datapost['qty'] as $product_id => $qty) {
                        //cek apakah master product valid
                        $row_Product = $this->Products_catalogue_model->get_by_id($product_id);
                        if ($row_Product) {
                            //data yang akan dimasukkan ke tabel itembreakdown_detail
                            $datainsert = array(
                                'itembreakdown_fkid' => $itembreakdown_id,
                                'detail_type' => $datapost['selectdetail'],
                                'outlet_fkid' => $outlet_id,
                                'product_fkid' => $product_id,
                                'qty' => $qty
                            );


                            //kalau master product ada
                            $row_ProductOnOutlet = $this->Products_catalogue_model->get_masterdetail_on_outlet($product_id, $outlet_id);
                            if ($row_ProductOnOutlet['status']==true) {
                                $row_ProductOnOutlet = $row_ProductOnOutlet['data'];
                            }
                            else{
                                //kalau product belum ada di outlet, tambahkan dulu
                                $dataaddproductoutlet = array(
                                    'product_fkid' => $product_id,
                                    'outlet_fkid' => $outlet_id,
                                    'price_buy_start' => 0,
                                    'price_buy' => 0,
                                    'price_sell' => 0,
                                    'voucher' => 'off',
                                    'discount' => 'off',
                                    'active' => 'off'
                                );
                                $response_addproducttooutlet = $this->Products_catalogue_model->insert_masterdetail($dataaddproductoutlet);
                                //seletelah ada, masukkan ke itembreakdown_detail
                                if ($response_addproducttooutlet) {
                                    $response = $this->Production_itembreakdown_detail_model->insert_detail($datainsert);
                                }
                                else{
                                    $draw_json = array(
                                        'status' => 'error',
                                        'message' => 'Failed add product to outlet'
                                    );
                                }
                            }


                            //kalau product ada di outlet
                            if ($row_ProductOnOutlet) {
                                //tambahkan ke itembreakdown_detail
                                $response = $this->Production_itembreakdown_detail_model->insert_detail($datainsert);
                            }
                            else{
                                //kalau product belum ada di outlet, tambahkan dulu
                                //echo "data tidak ada 2";
                            }

                            //output insert
                            if ($response) {
                                $draw_json = array(
                                    'status' => 'success',
                                    'message' => 'Update Record Success'
                                );
                            }
                            else{
                                $draw_json = array(
                                    'status' => 'error',
                                    'message' => 'Update Record Failed'
                                );
                            }
                            //end output insert
                        }
                        else{
                            $draw_json = array(
                                'status' => 'error',
                                'message' => 'Invalid Product ID not Found'
                            );
                        }//cek valid product master
                    }
                }//end Apakah select outlet bukan alloutlet, multipleoutlet
                elseif ($outlet_id=='alloutlet' || $outlet_id=='multipleoutlet') {
                    foreach ($datapost['outlet_selected'] as $outlet_id) {
                        //echo "outlet:".$outlet_id."_";
                        //delete data itembreakdown yang ada di outlet $outlet_id
                        $response_delete = $this->Production_itembreakdown_detail_model->delete_detail_by_outlet($outlet_id, $datapost['selectdetail']);
                        if ($response_delete) {
                            //cek apakah outlet sudah ada di production_itembreakdown_outlet
                            $cekoutlet = $this->Production_itembreakdown_model->get_available_outlet($itembreakdown_id, $outlet_id);
                            if (!$cekoutlet) {
                                //kalau outlet belum ada, tambahkan outlet
                                $data_addoutlet = array(
                                    'itembreakdown_fkid' => $itembreakdown_id,
                                    'outlet_fkid' => $outlet_id
                                );
                                $response_addoutlet = $this->Production_itembreakdown_model->insert_available_outlet($data_addoutlet);
                            }
                            //insert data yang dipost diambil dari line 514
                            foreach ($datapost['qty'] as $product_id => $qty) {
                                //cek apakah master product valid
                                $row_Product = $this->Products_catalogue_model->get_by_id($product_id);
                                if ($row_Product) {
                                    //kalau master product ada
                                    $row_ProductOnOutlet = $this->Products_catalogue_model->get_masterdetail_on_outlet($product_id, $outlet_id);
                                    if ($row_ProductOnOutlet['status']==true) {
                                        $row_ProductOnOutlet = $row_ProductOnOutlet['data'];
                                    }
                                    else{
                                        //kalau product belum ada di outlet, tambahkan dulu
                                        $dataaddproductoutlet = array(
                                            'product_fkid' => $product_id,
                                            'outlet_fkid' => $outlet_id,
                                            'price_buy_start' => 0,
                                            'price_buy' => 0,
                                            'price_sell' => 0,
                                            'voucher' => 'off',
                                            'discount' => 'off',
                                            'active' => 'off'
                                        );
                                        $response_addproducttooutlet = $this->Products_catalogue_model->insert_masterdetail($dataaddproductoutlet);
                                        //seletelah ada, masukkan ke itembreakdown_detail
                                        if ($response_addproducttooutlet) {
                                            $response = $this->Production_itembreakdown_detail_model->insert_detail($datainsert);
                                        }
                                        else{
                                            $draw_json = array(
                                                'status' => 'error',
                                                'message' => 'Failed add product to outlet'
                                            );
                                        }
                                    }


                                    //data yang akan dimasukkan ke tabel itembreakdown_detail
                                    $datainsert = array(
                                        'itembreakdown_fkid' => $itembreakdown_id,
                                        'detail_type' => $datapost['selectdetail'],
                                        'outlet_fkid' => $outlet_id,
                                        'product_fkid' => $product_id,
                                        'qty' => $qty
                                    );



                                    //kalau product ada di outlet
                                    if ($row_ProductOnOutlet) {
                                        //tambahkan ke itembreakdown_detail
                                        $response = $this->Production_itembreakdown_detail_model->insert_detail($datainsert);
                                    }
                                    else{
                                        //kalau product belum ada di outlet, tambahkan dulu
                                        //echo "data tidak ada 2";
                                    }

                                    //output insert
                                    if ($response) {
                                        $draw_json = array(
                                            'status' => 'success',
                                            'message' => 'Update Record Success'
                                        );
                                    }
                                    else{
                                        $draw_json = array(
                                            'status' => 'error',
                                            'message' => 'Update Record Failed'
                                        );
                                    }
                                    //end output insert
                                }
                                else{
                                    $draw_json = array(
                                        'status' => 'error',
                                        'message' => 'Invalid Product ID not Found'
                                    );
                                }//cek valid product master
                            }
                        }
                    }
                }
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Item Breakdown ID not Found'
            );
        }

        echo format_json($draw_json);
    }

    public function update_detail_item($ibdetail_id=null)
    {
        //cek data
        $row = $this->Production_itembreakdown_detail_model->get_by_id($ibdetail_id);
        $draw_json = array();
        if ($row) {
            $datapost = array(
                'qty' => $this->input->post('qty', true)
            );

            //validasi rules
            $this->form_validation->set_rules('qty', 'qty', 'trim|required|is_numeric');

            if ($this->form_validation->run() == FALSE) {
                $err_qty = form_error('qty');
                $err_qty = str_replace('<p>', '', $err_qty);
                $err_qty = str_replace('</p>', '', $err_qty);

                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Record Failed',
                    'data' => array(
                        'qty' => $err_qty
                    )
                );
            } else {
                $response = $this->Production_itembreakdown_detail_model->update_detail($ibdetail_id, $datapost);
                if ($response) {
                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Update Record Success'
                    );
                }
                else{
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Update Record Error'
                    );
                }
            }
            //end of validation run
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Data not Found'
            );
        }

        echo format_json($draw_json);
    }

    public function delete_detail($ibdetail_id)
    {
        //cek detail
        $row = $this->Production_itembreakdown_detail_model->get_by_id($ibdetail_id);
        if ($row) {
            //kalau data ketemu data hapus
            $delete = $this->Production_itembreakdown_detail_model->delete_detail($ibdetail_id);
            if ($delete) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Data not Found'
            );
        }

        echo format_json($draw_json);
    }

    public function delete($id)
    {
        $row = $this->Production_itembreakdown_model->get_by_id($id);
        if ($row) {
            //hapus data
            $response = $this->Production_itembreakdown_model->delete($id);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Data Not Found'
            );
        }

        echo format_json($draw_json);
    }

}

/* End of file Itembreakdown.php */
/* Location: ./application/controllers/production/Itembreakdown.php */