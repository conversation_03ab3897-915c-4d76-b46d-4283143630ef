<?php
defined('BASEPATH') OR exit('No direct script access allowed');
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use \PhpOffice\PhpSpreadsheet\Style;
class Spoil_damage extends Auth_Controller {

    public function __construct() 
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('stock/Spoil_model', 'Spoil_model');
        $this->load->model('products/products/products_catalogue/Products_catalogue_model', 'Product_model');
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('outlet/Outlets_model', 'Unit_model');
        $this->load->model('account/Login_model', 'Login_model');
        $this->load->helper('password');
        $this->load->library('google/Pubsub');

        $this->env = getenv('CI_ENV');
        if (!empty($_ENV['CI_ENV'])) {
          $this->env = $_ENV['CI_ENV'];
        }
        if($this->env === '') {
          $this->env = 'development';
        }
    }

    public function index()
    {
        $link = site_url('stock/spoil/spoil_damage/'); //URL dengan slash
        $data = array(
            'currentUser' => $this->session->userdata['user_name'],
            'kolomID' => 'spoil_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/', //ambil data yang akan diedit
            'ajaxByOutlet' => site_url('stock/opname/opname_inventory/jsonSpoil'),
            'ajaxAuth' => $link.'checkPasswordAuth',
            'saveSession' => $link.'saveSession'
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        //template
        $data['page_title'] = 'Spoil Damage';
        $this->template->view('stock/spoil/v2/spoil_damage_v', $data);
    }

    public function json($type=null,$outlet,$startDate,$endDate)
    {
        header('Content-Type: application/json');

        /* custom json output  start */
        $jsondata = $this->Spoil_model->json($type,$outlet,$startDate,$endDate); //ambil data json
        $total = $this->Spoil_model->get_total($type,$outlet,$startDate,$endDate);
        $json_decode = json_decode($jsondata); //pecah data json
        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->spoil_id;
            $date = millis_to_localtime('Y-m-d H:i',$a->time_created);
            $catalogue_type = $a->catalogue_type;
            $product_fkid= $a->product_fkid;
            $product_name = htmlentities($a->product_name); //encode string
            $outlet_name = htmlentities($a->outlet_name); //encode string
            $category_name = htmlentities($a->category_name); //encode string
            $subcategory_name = htmlentities($a->subcategory_name); //encode string
            $qty = htmlentities($a->qty); //encode string
            $unit = htmlentities($a->unit_name); //encode string
            $price_buy = htmlentities($a->price_buy);//encode string
            $lost = $qty*$price_buy;            
            $admin_name = htmlentities($a->admin_name);//encode string
            $keterangan = htmlentities($a->keterangan);//encode string
            $data_status = htmlentities($a->data_status);
            ($data_status=='on') ? $data_status='Yes' : $data_status='No';

            $action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
            
            
            $temp_data = array(
                'spoil_id' => $id,
                'data_created' => $date,
                'product_name' => $product_name." ".$a->variant,
                'catalogue_type' => $catalogue_type,
                'outlet_name' => $outlet_name,
                'category_name' => $category_name,
                'subcategory_name' => $subcategory_name,
                'qty' => $qty,
                'unit_name' => $unit,
                'price_buy' => $price_buy,
                'lost' => $lost,
                'admin_name' => $admin_name,
                'keterangan' => $keterangan,
                // 'user_input' => $a->user_input,
                // 'data_status' => $data_status,
                // 'action' => $action
                );
                if (!empty($a->user_input)) {
                    $temp_data['user_input']= $a->user_input;
                }else{
                    $temp_data['user_input']=$a->admin_name;
                }
            array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray,
            'total' => $total,
            );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function checkPasswordAuth()
    {
        $response = $this->Login_model->get_by_email($this->session->userdata('user_type'), $this->session->userdata('email'));
        $account_data = $response;
        
        $valid_password = password_validation($this->input->post('password'), $account_data->password); //cek validasi password

        if ($valid_password===TRUE) {
            $response = array(
                'status' => 'success'
                );
        }else{
            $response = array(
                'status' => 'error',
                'message' => 'Password didn\'t match'
                );
        }

        header('Content-Type: application/json');
        echo json_encode($response);
    }  

    public function saveSpoil()
    {
        $dataSpoil = $this->input->post('dataSpoil');
        $data = array();
        foreach ($dataSpoil as $key => $value) {
            $tmpData=array(
            'product_detail_fkid' => $value['product_detail_id'],
            'qty' => $value['qty'],
            'keterangan' => $value['keterangan'],
            'admin_fkid' => $this->session->userdata('admin_id'),
            'data_created' => date('Y-m-d H:i:s'),
            'time_created' => current_millis(),
            );
            if ($this->session->userdata('user_type') == 'employee') {
                $tmpData['user_input'] = $this->session->userdata('user_id');
            }
            array_push($data, $tmpData);
        }
        $response = $this->db->insert_batch('spoils', $data);
        if($response){
        //publish to pubsub 
        $affected_rows = $this->db->affected_rows(); 
        $inserted_ids = array();
        for($i = 0; $i < $affected_rows; $i++){
            $inserted_ids[] = $this->db->insert_id() + $i; 
        }
        file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> spoil ids: " . json_encode($inserted_ids). "\n");            
        $topic = 'spoil-'.$this->env;
        $this->pubsub->publish($topic, json_encode(array('spoil_ids' => $inserted_ids)));
        }
        return $this->output
        ->set_content_type('application/json')
        ->set_status_header(200)
        ->set_output($response);
    }  

    public function action($actionType=null) //actionType = 'create / update'
    {
        $quantity = $this->input->post('jumlah');
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                    'jumlah' => form_error('jumlah'),
                    'keterangan' => form_error('keterangan')
                    )
                );
        } else {
            //data yang dikirim


            //deteksi actionType (create/update)
            if ($actionType=='create') {
                $data = array();
                $count = count($_POST['jumlah']);
                print_r($_POST);die();
                for($i=0; $i < $count; $i++){
                    if($_POST['jumlah'][$i] > 0){
                        $data[] = array(
                            'spoil_id' => '',
                            'product_detail_fkid' => $_POST['ingridients_id'][$i],
                            'qty' => $_POST['jumlah'][$i],
                            'keterangan' => $_POST['keterangan'][$i],
                            'admin_fkid' => $this->session->userdata('admin_id'),
                            'data_created' => date('Y-m-d H:i:s'),
                            'time_created' => current_millis(),
                            );
                    }
                }
                // print_r($data);die();
                //action untuk create data
                $response = $this->Spoil_model->insert($data);
                // echo $this->db->last_query();die();
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                $data = array(
                    'spoil_id' => $this->input->post('id'),
                    'product_detail_fkid' => $_POST['product_id'],
                    'qty' => $quantity,
                    'keterangan' => $_POST['keterangan'],
                    'data_status' => $_POST['data_status'][0],
                    'admin_fkid' => $this->session->userdata('admin_id'),
                    'data_created' => date('Y-m-d H:i:s'),
                    'time_created' => current_millis(),

                    );
                //action untuk update data
                $response = $this->Spoil_model->update($this->input->post('id', TRUE), $data);
                
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function edit($id=null)
    {
        header('Content-Type: application/json');
        $jsondata = $this->Spoil_model->get_by_id($id);
        $json_decode = json_decode($jsondata);

        if ($jsondata) {
            //pecah data json
            $dataArray = array();
            
            foreach ($json_decode->data as $a) {

                $spoil_id = "<span id='spoil_id'>".$a->spoil_id."</span>";
                $product_fkid = "<span id='product_fkid'>".$a->product_fkid."</span>";
                $product_name = "<span id='product_name'>".htmlentities($a->product_name)."</span>"; //encoding string
                //$outlet_id = $_GET['outlet'];
                $price_buy = "<span id='price_buy'>".($a->price_buy)."</span>";
                $unit_name = "<span id='unit_name'>".htmlentities($a->unit_name)."</span>";
                $data_created =  "<span id='data_created'>".htmlentities($a->data_created)."</span>";

                $total = "<span id='total'>".$a->qty*$a->price_buy."</span>";

                $active = ($a->data_status=='off') ? "selected='selected'" : "";
                $data_status = "<select name='data_status[]' class='form-control'>";
                $data_status.= "<option value='on'>Yes</option>";
                $data_status.= "<option value='off' ".$active.">No</option></select>";
                $keterangan = "<textarea name=keterangan id='keterangan' class='form-control'>".htmlentities($a->keterangan)."</textarea>";
                $jumlah = "<input type='number' min='0' id='inputJumlah' style='width:75px;' onchange='jumlah(this)' name='jumlah' class='form-control' value='".$a->qty."' />";
                $jumlah .= "<input type='hidden' value='".$id."' name='product_id'/>";
                 //untuk spoil
                
                
                $temp_data = array(
                    'spoil_id' => $spoil_id,
                    'product_fkid' => $product_fkid,
                    'product_name' => $product_name,
                    'price_buy' => $price_buy,
                    'unit_name' => $unit_name,
                    'keterangan' => $keterangan,
                    'data_status' => $data_status,
                    'jumlah' => $jumlah,
                    'total' => $total,
                    'data_created' => $data_created
                );
                array_push($dataArray, $temp_data);
            }

            $status ='success';
            $message = 'Success Edit Record';
            $draw_json = array(
                'status' => $status,
                'message' => $message,
                'draw' => $json_decode->draw,
                'recordsTotal' => $json_decode->recordsTotal,
                'recordsFiltered' => $json_decode->recordsFiltered,
                'data' => $dataArray
                );
            //tampilkan data json
        
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            
            $status = 'error';
            $message = ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '');
            $draw_json = array(
                'status' => $status,
                'message' => $message,
                'draw' => $json_decode->draw,
                'recordsTotal' => $json_decode->recordsTotal,
                'recordsFiltered' => $json_decode->recordsFiltered,
                'data' => $dataArray
                );
        }

        echo json_encode($draw_json); 
    }

    public function delete($id=null)
    {
        header('Content-Type: application/json');
        $jsondata = $this->Spoil_model->get_by_id($id);

        $json_decode = json_decode($jsondata);

        if ($jsondata) {
            $response = $this->Spoil_model->delete($id);
            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

            //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
        $this->form_validation->set_rules('jumlah', 'jumlah[]', 'min[0]|is_numeric');
        $this->form_validation->set_rules('keterangan', 'keterangan[]', 'max_length[100]');

            //$this->form_validation->set_rules('ingridients_fkid', 'spoil_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

    public function saveSession($key, $value){
        $this->session->set_userdata($key,$value);
        
    }

     public function export_xlsx($type,$start_date,$end_date,$outlet)
	{
        $data = $this->Spoil_model->data_export($type,$outlet,$start_date,$end_date);
		// Create new Spreadsheet object
		$spreadsheet = new Spreadsheet();
		$start_row = 4;
		$start_col = 'A';
		$sheet = $spreadsheet->getActiveSheet();
		// Set document properties
		$spreadsheet->getProperties()
			->setCreator('UNIQ')
			->setLastModifiedBy('www.uniq.id')
			->setTitle('UNIQ STOCK SPOIL Export Document')
			->setSubject('UNIQ STOCK SPOIL Export Document')
			->setDescription('UNIQ STOCK SPOIL Export Document')
			->setKeywords('office 2007, office 2013, office 2016')
			->setCategory('UNIQ');


		$styleArray = [
			'borders' => [
				'allBorders' => [
            		'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN //fine border
        		]
    		]
		];
		// set judul
		$sheet->mergeCells('A1:M1');
		$sheet->setCellValue("A1","SPOIL PRODUCT & INGREDIENTS");
		$sheet->mergeCells('A2:M2');
		$sheet->setCellValue("A2","Outlet :".$data[0]['outlet_name']);
		$sheet->getStyle('A1:B3')->getAlignment()->setHorizontal('center');
		$sheet->getStyle('A1:B3')->getFont()->setBold(true);

		// Set Header
		$excel_header =	array(
            "No",
			"Date",
			"Product Name",
			"Type",
			"Outlet",
			"Categories",
			"Sub category",
			"Qty",
			"Unit",
			"Price",
			"Lost",
			"Autorize",
			"Note",
		);
		foreach ($excel_header as $value) {
			$spreadsheet->setActiveSheetIndex(0)->setCellValue($start_col++ . $start_row, $value);
		}
  
		$sheet->getStyle('A4:M'.(count($data)+4))->applyFromArray($styleArray);

		// set width colom
		$sheet->getColumnDimension('C')->setWidth(30);
		$sheet->getStyle('A4:M4')->getFont()->setBold(true);
		$sheet->getPageSetup()->setPaperSize(9);

		// Add Data to Excel
		$no=0;
		foreach ($data as $a) {
			//init
			$start_col = 'A'; //reset excel column
			$start_row++; //set to next row

            $date = millis_to_localtime('Y-m-d H:i',$a["time_created"]);
            $lost = $a['qty']*$a['price_buy'];

            $spreadsheet->setActiveSheetIndex(0)
			->setCellValue($start_col++ . $start_row, $no+=1 )
			->setCellValue($start_col++ . $start_row, $date)
			->setCellValue($start_col++ . $start_row, $a['product_name']." ".$a['variant'])
			->setCellValue($start_col++ . $start_row, $a['catalogue_type'])
			->setCellValue($start_col++ . $start_row, $a['outlet_name'])
			->setCellValue($start_col++ . $start_row, $a['category_name'])
			->setCellValue($start_col++ . $start_row, $a['subcategory_name'])
			->setCellValue($start_col++ . $start_row, $a['qty'])
			->setCellValue($start_col++ . $start_row, $a['unit_name'])
			->setCellValue($start_col++ . $start_row, $a['price_buy'])
			->setCellValue($start_col++ . $start_row, $lost)
			->setCellValue($start_col++ . $start_row, $a['admin_name'])
			->setCellValue($start_col++ . $start_row, $a['keterangan'])
			;
		}

		// Rename worksheet
		$sheet->setTitle('Stock Spoil');
		// Set active sheet index to the first sheet, so Excel opens this as the first sheet
		$spreadsheet->setActiveSheetIndex(0);

		/* DOWNLOAD FILE */
		// Redirect output to a client’s web browser (Xlsx)
		$filename = 'UNIQ-STOCK SPOIL-'.date('Ymd').'.xlsx';
		header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		header('Content-Disposition: attachment;filename="' . $filename . '"');
		header('Cache-Control: max-age=0');
		// If you're serving to IE 9, then the following may be needed
		header('Cache-Control: max-age=1');

		// If you're serving to IE over SSL, then the following may be needed
		header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
		header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
		header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
		header('Pragma: public'); // HTTP/1.0

		$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
		$writer->save('php://output');
	}

}

/* End of file Spoil.php */
/* Location: ./application/controllers/products/stock/Spoil.php */