<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Spoil_equipment extends Auth_Controller {

	 public function __construct() 
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('stock/Spoil_model', 'Spoil_model');
        $this->load->model('products/products/products_catalogue/Products_catalogue_model', 'Product_model');
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('outlet/Outlets_model', 'Unit_model');
        $this->load->model('account/Login_model', 'Login_model');
        $this->load->helper('password');
    }

    public function index()
    {
        $link = site_url('stock/spoil/spoil_damage/'); //URL dengan slash
        $data = array(
            'currentUser' => $this->session->userdata['user_name'],
            'kolomID' => 'spoil_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json/equipment',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/', //ambil data yang akan diedit
            'ajaxByOutlet' => site_url('products/product-catalogue/jsonSpoil'),
            'ajaxAuth' => $link.'checkPasswordAuth',
            'saveSession' => $link.'saveSession'
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        // $this->load->view('stock/spoil/spoil_equipment_v',$data);
        //template
        $data['page_title'] = 'Spoil Equipment';
        $this->template->view('stock/spoil/v2/spoil_equipment_v', $data);
    }

    public function checkPasswordAuth($password)
    {
        $response = $this->Login_model->get_by_email('admin', $this->session->userdata('email'));
        $account_data = $response;
        
        $valid_password = password_validation($password, $account_data->password); //cek validasi password

        if ($valid_password===TRUE) {
            $response = array(
                'status' => 'success'
                );
        }else{
            $response = array(
                'status' => 'error',
                'message' => 'Password didn\'t match'
                );
        }

        header('Content-Type: application/json');
        echo json_encode($response);
    }    

    public function action($actionType=null) //actionType = 'create / update'
    {
        $quantity = $this->input->post('jumlah');
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                    'jumlah' => form_error('jumlah'),
                    'keterangan' => form_error('keterangan')
                    )
                );
        } else {
            //data yang dikirim


            //deteksi actionType (create/update)
            if ($actionType=='create') {
                $data = array();
                $count = count($_POST['jumlah']);
                for($i=0; $i < $count; $i++){
                    if($_POST['jumlah'][$i] > 0){
                        $data[] = array(
                            'spoil_id' => '',
                            'product_detail_fkid' => $_POST['ingridients_id'][$i],
                            'qty' => $_POST['jumlah'][$i],
                            'keterangan' => $_POST['keterangan'][$i],

                            'admin_fkid' => $this->session->userdata('admin_id'),
                            'data_created' => date('Y-m-d H:i:s')

                            );
                    }
                }
                //action untuk create data
                $response = $this->Spoil_model->insert($data);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                $data = array(
                    'spoil_id' => $this->input->post('id'),
                    'product_detail_fkid' => $_POST['product_id'],
                    'qty' => $quantity,
                    'keterangan' => $_POST['keterangan'],

                    'data_status' => $_POST['data_status'][0],

                    'admin_fkid' => $this->session->userdata('admin_id'),
                    'data_created' => date('Y-m-d H:i:s')

                    );
                //action untuk update data
                $response = $this->Spoil_model->update($this->input->post('id', TRUE), $data);
                
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    }
    public function edit($id=null)
    {

        header('Content-Type: application/json');
        $jsondata = $this->Spoil_model->get_by_id($id);
        
        $json_decode = json_decode($jsondata);

        
        if ($jsondata) {
            //pecah data json
            $dataArray = array();
            
            foreach ($json_decode->data as $a) {

                $spoil_id = "<span id='spoil_id'>".$a->spoil_id."</span>";
                $product_fkid = "<span id='product_fkid'>".$a->product_fkid."</span>";
            $product_name = "<span id='product_name'>".htmlentities($a->product_name)."</span>"; //encoding string
            //$outlet_id = $_GET['outlet'];
            $price_buy = "<span id='price_buy'>".($a->price_buy)."</span>";
            $unit_name = "<span id='unit_name'>".htmlentities($a->unit_name)."</span>";
            $data_created =  "<span id='data_created'>".htmlentities($a->data_created)."</span>";

            $total = "<span id='total'>".$a->qty*$a->price_buy."</span>";

            $active = ($a->data_status=='off') ? "selected='selected'" : "";
            $data_status = "<select name='data_status[]' class='form-control'>";
            $data_status.= "<option value='on'>Yes</option>";
            $data_status.= "<option value='off' ".$active.">No</option></select>";
            $keterangan = "<textarea name=keterangan id='keterangan' class='form-control'>".htmlentities($a->keterangan)."</textarea>";
            $jumlah = "<input type='number' min='0' id='inputJumlah' style='width:75px;' onchange='jumlah(this)' name='jumlah' class='form-control' value='".$a->qty."' />";
            $jumlah .= "<input type='hidden' value='".$id."' name='product_id'/>";
             //untuk spoil
            
            
            $temp_data = array(
                'spoil_id' => $spoil_id,
                'product_fkid' => $product_fkid,
                'product_name' => $product_name,
                'price_buy' => $price_buy,
                'unit_name' => $unit_name,
                'keterangan' => $keterangan,
                'data_status' => $data_status,
                'jumlah' => $jumlah,
                'total' => $total,
                'data_created' => $data_created
                );
            array_push($dataArray, $temp_data);
        }

        
        $status ='success';
        $message = 'Success Edit Record';
        $draw_json = array(
            'status' => $status,
            'message' => $message,
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
            );
        //tampilkan data json
        
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            
            $status = 'error';
            $message = ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '');
            $draw_json = array(
                'status' => $status,
                'message' => $message,
                'draw' => $json_decode->draw,
                'recordsTotal' => $json_decode->recordsTotal,
                'recordsFiltered' => $json_decode->recordsFiltered,
                'data' => $dataArray
                );
        }

        echo json_encode($draw_json); 
    }



    public function delete($id=null)
    {
        header('Content-Type: application/json');
        $jsondata = $this->Spoil_model->get_by_id($id);

        $json_decode = json_decode($jsondata);

        if ($jsondata) {
            $response = $this->Spoil_model->delete($id);
            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

            //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }
    public function _rules() 
    {
        $this->form_validation->set_rules('jumlah', 'jumlah[]', 'min[0]|is_numeric');
        $this->form_validation->set_rules('keterangan', 'keterangan[]', 'max_length[100]');

            //$this->form_validation->set_rules('ingridients_fkid', 'spoil_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

    public function saveSession($key, $value){
        $this->session->set_userdata('Insertspoil['.$key.']',$value);
    }
    public function clearSession(){
        $this->session->unset_userdata('Insertspoil[]');
        
    }
    

}

/* End of file Spoil_productingridient.php */
/* Location: ./application/controllers/products/stock/Spoil_productingridient.php */