<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Stock_menu extends Auth_Controller {

	public function index()
	{
		$nav = $this->template->navigation();
		foreach ($nav[$this->uri->segment(1)]['sub'] as $key => $value) {
			$this->check($value['url']);
		}

		$this->privilege->disable_page();
	}

	private function check($url='')
	{
		//init
		$role = $this->session->userdata('role_web');
		$is_admin = ($this->session->userdata('user_type')=='admin') ? true : false;
		if (!empty($role[$url]['view']) || $is_admin) {
			redirect($url);die();
		}
	}

}

/* End of file Products_menu.php */
/* Location: ./application/controllers/products/Products_menu.php */