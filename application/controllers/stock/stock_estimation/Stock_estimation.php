<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Stock_estimation extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('stock/Stock_estimation_model', 'estimation_model');
        $this->load->model('stock/Day_category_model', 'day_category');
	}

	public function index()
	{
        $link = current_url().'/';
        $data = array(
            'currentUser' => $this->session->userdata['user_name'],
            'kolomID' => 'day_category_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'jsonDayCategory',            
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'get_data/', //ambil data yang akan diedit
            'ajaxActiongetDayBayOutlet' => $link.'get_day_outlet/', //ambil data yang akan diedit
            'ajaxActionDatatableEstimatioan' => $link.'jsonEstimation/',
            'getDayCategory' => $link.'getDayCategory/',
            'ajaxGetProduct' => $link.'getProduct/',
            'insertProduct' => $link.'insertProduct/',
            'ajaxActionDeleteEstimation' => $link.'deleteEstimation/'
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        // $data['form_select_dayCategory'] = $this->day_category->form_select();

        //templating
        $data['page_title'] = 'Stock Estimation';
        $this->template->view('stock/stock_estimation/stock_estimation_v2', $data);
		// $this->load->view('stock/stock_estimation/stock_estimation_v',$data);
	}

	public function jsonDayCategory()
	{
		 header('Content-Type: application/json');

		 $jsondata = $this->day_category->jsonDayCategory(); //ambil data json
        //  print_r($jsondata);die;
		 $json_decode = json_decode($jsondata); //pecah data json
		 $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->id;
            $name = $a->name;
            $monday=$a->monday;
            if ($monday) {
                $monday=$a->monday."<br>";  
            }
            $tuesday=$a->tuesday;
            if ($tuesday) {
                $tuesday=$a->tuesday."<br>";  
            }
            $wednesday=$a->wednesday;
            if ($wednesday) {
                $wednesday=$a->wednesday."<br>";    
            }
            $thursday=$a->thursday;
            if ($thursday) {
                $thursday=$a->thursday."<br>";  
            }
            $friday=$a->friday;
            if ($friday) {
                $friday=$a->friday."<br>";  
            }
            $saturday=$a->saturday;
            if ($saturday) {
                $saturday=$a->saturday."<br>";
            }
            $sunday=$a->sunday;
            if ($sunday) {
                $sunday=$a->sunday."<br>";  
            }
            $outlet_name= $a->outlet_name;
            $day=$monday.$tuesday.$wednesday.$thursday.$friday.$saturday.$sunday;

            $tombol_day = '<div class="btn btn-primary btn-xs tiptext">Day
                                <div class="description">'.$name.' available on:<br> '.$day.'</div>
                                </div>';


            $action  = '<button class="btn btn-xs btn-warning btn-primary" onclick="actionEdit('.$id.')" data-toggle="tooltip" data-placement="top" title="Edit"><i class="fa fa-edit"></i></button>';
            $action .= "&nbsp;";
            $action .= '<button class="btn btn-xs btn-warning btn-danger" onclick="actionDelete('.$id.')" data-toggle="tooltip" data-placement="top" title="Hapus"><i class="fa fa-trash"></i></button>';
            
            
            $temp_data = array(
                'id' => $id,
                'name' => $name,
                'outlet' => $outlet_name,
                'day' => $tombol_day,
                'action' => $action
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
            );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
	}


    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            $response = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                        'name' => form_error('name'),
                        'outlet' => form_error('outlet')
                    )
                );
        } else {

            //data yang dikirim
            $data = array(
                'day_category_name' => $this->input->post('name',TRUE),
                'outlet_fkid' => $this->input->post('outlet',TRUE),                
                'monday' => $this->input->post('monday', true),
                'tuesday' => $this->input->post('tuesday', true),
                'wednesday' => $this->input->post('wednesday', true),
                'thursday' => $this->input->post('thursday', true),
                'friday' => $this->input->post('friday', true),
                'saturday' => $this->input->post('saturday', true),
                'sunday' => $this->input->post('sunday', true),
            );

            if ($actionType=='create') {
                //action untuk create data
                $response = $this->day_category->insert($data);

                $primary_key = $this->db->insert_id();
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->day_category->update($this->input->post('id', TRUE), $data);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $response = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        }

        //buat output dalam format json
        echo format_json($response);
    }

     public function get_data($id)
    {
        
        $row = $this->day_category->get_by_id($id);
        // print_r($row);die();

        if ($row) {
            $day = array(
                $row->monday,
                $row->tuesday, 
                $row->wednesday,
                $row->thursday, 
                $row->friday, 
                $row->saturday, 
                $row->sunday, 
            );
            //ambil day outlet yang sudah ada
            $outlet_id= $row->outlet_fkid;
            $this_day=$this->day_category->dayOutlet($outlet_id);
                $get_day = array(
                    $this_day->monday,
                    $this_day->tuesday,
                    $this_day->wednesday,
                    $this_day->thursday, 
                    $this_day->friday, 
                    $this_day->saturday, 
                    $this_day->sunday,
                );
            //end
            //custom tampil data
            $data = array(
                'id' => set_value('id', $row->day_category_id),
                'name' => set_value('name', $row->day_category_name),
                'outlet' => set_value('outlet', $row->outlet_fkid),
                'day' => set_value('day',$day),
                'get_day' => set_value('get_day',$get_day),
            );
            $dataArray = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo format_json($dataArray);
    }

    public function delete($id=null)
    {
        $row = $this->day_category->get_by_id($id);

        if ($row) {
            $response = $this->day_category->delete($id);
            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //response output
        echo format_json($response);
    }

    public function get_day_outlet($outlet_id)
    {
        $data=$this->day_category->dayOutlet($outlet_id);
        // print_r($data);die();
        if ($data) {
                $day = array(
                    $data->monday,
                    $data->tuesday,
                    $data->wednesday,
                    $data->thursday, 
                    $data->friday, 
                    $data->saturday, 
                    $data->sunday,
                );
            
            // print_r($day);die();
            $dataArray = array(
                'status' => 'success',
                'data' => $data
            );
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $dataArray = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
        echo format_json($dataArray);
    }

     public function _rules() 
    {
        $this->form_validation->set_rules('name', 'name', 'required|max_length[30]');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

    public function jsonEstimation()
    {
        header('Content-Type: application/json');
        $jsondata=$this->estimation_model->jsonEstimation();
        $json_decode = json_decode($jsondata); //pecah data json
        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->id;
            // $action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            // $action .= "&nbsp;&nbsp;&nbsp;";
            $action = '<button class="btn btn-xs btn-warning btn-danger" onclick="actionDeleteEstimation('.$id.')" data-toggle="tooltip" data-placement="top" title="Hapus"><i class="fa fa-trash"></i></button>';

            $temp_data = array(
                'id' => $id,
                'min_stock' => $a->min_stock,
                'max_stock' => $a->max_stock,
                'outlet' => $a->outlet_name,
                'day_category' => $a->day_category_name,
                'product_name' => $a->product_name,
                'action' => $action,
            );
            array_push($dataArray, $temp_data);

        };
        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
            );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
        
    }

    public function getDayCategory($outlet_id)
    {
        $data = $this->day_category->form_select($outlet_id);
        echo json_encode($data);
    }

    //fungsi get produk stock estimatioan
    public function getProduct($outlet_id,$category_id)
    {
        header('Content-Type: application/json');
        $data = $this->estimation_model->jsonGetProduct($outlet_id,$category_id);
         $json_decode = json_decode($data); //pecah data json
         $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->id;
            $stock_min = "<input type='number' min='0' value='".$a->min_stock."' id='stock_min'  style='width:75px;' onchange='stock(this)' name='stock_min[]' class='stock_min form-control'/>";
            // $$stock_min .= "<input type='hidden' value='"$a->product_detail_id;."' name='product_id'/>";
            $stock_max = "<input type='number' min='0' value='".$a->max_stock."' id='stock_max' style='width:75px;' onchange='stock(this)' name='stock_max[]' class='stock_max form-control'/>";
            $stock_max .= "<input type='hidden' value='".$a->id."' name='product_id[]'/>";
            $stock_max .= "<input type='hidden' value='".$a->stock_estimation_id."' name='stock_estimation_id[]'/>";
            
            
            $temp_data = array(
                'id' => $a->id,
                'name' => $a->product_name." ".$a->variant,
                'category' => $a->category_name,
                'sub_category' => $a->sub_name,
                'unit' => $a->unit,
                'stock_min' => $stock_min,
                'stock_max' => $stock_max,
                );
            array_push($dataArray, $temp_data);
        }

        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    //fungsi insert produk
    public function insertProduct()
    {

        header('Content-Type: application/json');     
        $count = count($_POST['stock_max']);
        // echo($count);die();
        for($i=0; $i < $count; $i++){
            if($_POST['stock_max'][$i] > 0 && $_POST['stock_min'][$i] > 0 ){
                $data[$i] = array(
                    'day_category_fkid' => $this->input->post('day_category_id[]'),
                    'min_stock' => $this->input->post('stock_min['.$i.']'),                
                    'max_stock' => $this->input->post('stock_max['.$i.']'),
                    'product_detail_fkid' => $this->input->post('product_id['.$i.']'),
                    'stock_estimation_id' => $this->input->post('stock_estimation_id['.$i.']'),
                    'data_created' => current_millis(),
                    'data_modified' => current_millis(),
                );
                // print_r($data);die();
                $exsis=$this->estimation_model->exsis($data[$i]['product_detail_fkid'],$data[$i]['day_category_fkid']);
                // print_r($exsis);die();
                if ($exsis) {
                    $response = $this->estimation_model->update($data[$i]['stock_estimation_id'],$data[$i]);
                    // echo $this->db->last_query();die();
                }else{
                    $response = $this->estimation_model->insert($data[$i]);
                } 
            }
            
        }
                   
       
        // echo $data[0]['product_detail_fkid'];die();
        
        $messageCreateSuccess = $this->session->set_flashdata('message', 'Record Success');
        //buat array untuk output json
        if ($response==true) {

            $response = array(               
                'status' => 'success',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }
         //buat output dalam format json
        header('Content-Type: application/json');
        echo json_encode($response);
    
    }


    // fungsi delete
    public function deleteEstimation($id)
    {
        $row = $this->estimation_model->get_by_id($id);

        if ($row) {
            $response = $this->estimation_model->delete($id);
            if ($response==true) {
                $this->session->set_flashdata('message', 'Record Deleted');
                $response = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            } else {
                $this->session->set_flashdata('message', 'Deleted Failed');
                $response = array(
                    'status' => 'error',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
                'status' => 'error',
                'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
        }

        //response output
        echo format_json($response);
    }

}

/* End of file Stock_estimation.php */
/* Location: ./application/controllers/products/stock/Stock_estimation.php */