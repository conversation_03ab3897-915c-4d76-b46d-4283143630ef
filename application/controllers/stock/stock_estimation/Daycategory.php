<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Daycategory extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->products_stock_...('read_access'); //employee role access page | sub-submenu thispage
        //$this->user_role->products_stock('read_access'); //employee role access page | submenu opcost
        //old_$this->user_role->products('read_access'); //employee role access page
        $this->user_role->stock_daycategory('read_access'); //employee role access page
	}

	public function index()
	{
		$this->load->view('stock/day_category_v');
	}

}

/* End of file Daycategory.php */
/* Location: ./application/controllers/products/stock/Daycategory.php */