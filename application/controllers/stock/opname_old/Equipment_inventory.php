<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Equipment_inventory extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('stock/Stock_opname_model', 'Stock_opname_model');
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('purchase/transfer/Transfer_model', 'Transfer_model');
        $this->load->model('purchasing/retur/Retur_products_model', 'Retur_model'); 
        $this->load->model('purchase/purchase_products/Purchase_products_model', 'Purchase_model'); 
        $this->load->model('stock/Spoil_model', 'Spoils_model');
        $this->load->model('outlet/Outlets_model', 'Unit_model');
        $this->load->model('stock/Sales_stock', 'Sales_model');
        $this->load->model('account/Login_model', 'Login_model');
    }

    public function index()
    {
        $link = site_url('stock/opname/opname_inventory/'); //URL dengan slash
        $data = array(
            'currentUser' => $this->session->userdata['user_name'],
            'kolomID' => 'opname_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json/equipment',
            );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        
        $data['page_title'] = 'Equipment Inventory';
        $this->template->view('stock/opname/v2/opname_equipment_v', $data);
    }

}

/* End of file Stock_opname.php */
/* Location: ./application/controllers/stock/opname/Stock_opname.php */