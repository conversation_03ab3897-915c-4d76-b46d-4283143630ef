<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Opname_inventory extends Auth_Controller {

    public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('stock/Stock_opname_model', 'Stock_opname_model');
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('purchase/transfer/Transfer_model', 'Transfer_model');
        $this->load->model('purchasing/retur/Retur_products_model', 'Retur_model'); 
        $this->load->model('purchase/purchase_products/Purchase_products_model', 'Purchase_model'); 
        $this->load->model('stock/Spoil_model', 'Spoils_model');
        $this->load->model('outlet/Outlets_model', 'Unit_model');
        $this->load->model('stock/Sales_stock', 'Sales_model');
        $this->load->model('account/Login_model', 'Login_model');
        $this->load->model('stock/Stock_estimation_model', 'estimation_model');
        $this->load->model('production/production/Production_model','Production_model');
        $this->load->model('products/products_catalogue/Products_catalogue_model','Catalogue_model');
        $this->load->model('stock/M_opname');
        $this->load->model('finance/M_set_jurnal_umum');
        $this->load->library('google/Pubsub');
    }

    public function index()
    {
        $link = site_url('stock/opname/opname_inventory/'); //URL dengan slash
        $data = array(
            'currentUser' => $this->session->userdata['user_name'],
            'kolomID' => 'opname_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'edit/', //ambil data yang akan diedit
            'ajaxByOutlet' => $link.'jsonSpoil',
            'ajaxAuth' => $link.'checkPasswordAuth',
            'saveSession' => $link.'saveSession'
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        
        //template
        $data['page_title'] = 'Opname & Inventory';
        $this->template->js('themes/plugins/numeral/numeral.min.js','bottom');
        $this->template->view('stock/opname/v2/opname_inventory_v', $data);
    }

    // data opname
    public function json($type=null,$outlet,$startDate,$endDate)
    {
        header('Content-Type: application/json');

        /* custom json output  start */        
        $jsondata = $this->Stock_opname_model->json($type,$outlet,$startDate,$endDate); //ambil data json

        $dataArray = array();
        foreach ($jsondata as $a) {
            $id = $a['opname_id'];
            $production = $a['production'];
            $alert = $a['alert'];
            $need = $a['need'];
            $ingridient_fkid = htmlentities($a['ingridient_fkid']);
            $ingridient_name = htmlentities($a['ingridient_name']);
            $catalogue_type = htmlentities($a['catalogue_type']);
            $outlet_name = htmlentities($a['outlet_name']);
            $ingridient_category_name = htmlentities($a['ingridient_category_name']);
            $ingridient_subcategory_name = htmlentities($a['ingridient_subcategory_name']);
            $date = millis_to_localtime('d-m-Y H:i',$a['time_created']);
            $sku = $a['sku'];
            $unit_name = htmlentities($a['unit_name']);
            $opname = $a['opname'];            
            $open = $a['open'];
            $spoil = $a['spoil'];
            $purchase = $a['purchase'];
            $transfer = $a['transfer'];
            $sales = $a['sales'];
            $closing = round($a['closing'], 3);
            $balance = round($opname - $closing, 3);
            $price_buy = $a['price_buy'];
            $a['lost']=='null' ?  $lost = round($balance * $price_buy, 3):$lost = $a['lost'];
            $data_status = htmlentities($a['data_status']);
            ($data_status=='on') ? $data_status='Yes' : $data_status='No';
            $admin_name = htmlentities($a['admin_name']);
            
            
            $temp_data = array(
                'opname_id' => $id,
                'ingridient_name' => $ingridient_name." ".$a['variant'],
                'outlet_name' => $outlet_name,
                'ingridient_category_name' => $ingridient_category_name,
                'ingridient_subcategory_name' => $ingridient_subcategory_name,
                'catalogue_type' => $catalogue_type,
                'unit_name' => $unit_name,
                'data_created' => $date,
                'opname' => $opname,
                'balance' => $balance,
                'open' => $open,
                'purchase' => $purchase,
                'spoil' => $spoil,
                'transfer' => $transfer,
                'sales' => $sales,
                'closing' => $closing,
                'balance' => $balance,
                'price_buy' => $price_buy,
                'lost' => $lost,
                'admin_name' => $admin_name,
                'data_status' => $data_status,
                'sku' => $sku,                                          
                'production' => $production,
                'need' =>$need,
                'alert' =>$alert,
                'retur' =>$a['retur'],
                'refund' => $a['refund'],
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    //get waktu opname
    public function getLast2($date,$product_detail_fkid){
        $jsondata = $this->Stock_opname_model->getLast2($product_detail_fkid);
        //$json_decode = json_decode($jsondata); //pecah data json
        if($jsondata)
            return $jsondata->data_created;
        else
            return 0;
    }

    // get produk to opname
    public function jsonSpoil($outlet_id=null){

        header('Content-Type: application/json');

        /* custom json output  start */
        $jsondata = $this->Catalogue_model->jsonSpoil($outlet_id); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->product_detail_id;
            $name = "<span id='name'>".htmlentities($a->name)."</span>"; //encoding string
            $price_update = "<span id='price_buy'>".($a->price_update)."</span";
            $price_sell = ($a->price_sell);
            $unit_name = htmlentities($a->unit_name);
            $catalogue_type = htmlentities($a->catalogue_type);
            $category_name=$a->sub_category;
            $date = $a->data_created;

            $value='';
            $value2=0;
            $ket="";
            if($this->session->has_userdata("jum_".$id) || $this->session->has_userdata("ket_".$id)){
                $value=$this->session->userdata("jum_".$id);
                $value2=($this->session->userdata("jum_".$id))*$a->price_update;
                $ket=$this->session->userdata("ket_".$id);
            }
            $keterangan = "<textarea name=keterangan[] id='ket' onfocusout='keterangan(this)' class='uniq_ket form-control'>".$ket."</textarea>";
            $total = "<span id='total'>".$value2."</span>";
            $jumlah = "<input type='number' min='0' id='inputJumlah' onchange='jumlah(this)' style='width:75px;' name='jumlah[]' class='uniq_jumlah form-control' value='".$value."' />";
            $jumlah .= "<input type='hidden' value=".$id." id='ing_id' name='ingridients_id[]'/>";
             //untuk spoil
            
            
            $temp_data = array(
                'product_detail_id' => $id,
                'name' => $name." ".$a->variant,
                'price_update' => $price_update,
                'price_sell' => $price_sell,
                'unit_name' => $unit_name,
                'keterangan' => $keterangan,
                'jumlah' => $jumlah,
                'total' => $total,
                'data_created' => $date,
                'sub_category' => $category_name,
                'category' => $a->category,
                'catalogue_type' => $catalogue_type,
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json); 
        /* custom json output  end */
    }

    public function jsonOpname($outlet_id)
    {
        $data = $this->M_opname->product_opname($outlet_id); //ambil data json
        $dataJson = array();
        foreach ($data as $key => $value) {
            $tmpdata = array(
                'name' => $value['name'].' '.$value['variant'], 
                'product_detail_id' => $value['product_detail_id'], 
                'catalogue_type' => $value['catalogue_type'], 
                'unit_name' => $value['unit_name'], 
                'data_status' => $value['data_status'], 
                'data_created' => $value['data_created'], 
                'price_buy_start' => $value['price_buy_start'], 
                'price_update' => $value['price_update'], 
                'price_sell' => $value['price_sell'], 
                'sub_category' => $value['sub_category'], 
                'category' => $value['category'], 
                'qty' => '',
                'subTotal' => 0
            );
            array_push($dataJson, $tmpdata);
        }
        return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output(json_encode($dataJson));
    }

    public function insert_opname()
    {
        // print_r($this->input->post());die();
        $dataProduct = json_decode($this->input->post('dataProduct'));
        // print_r($dataProduct);die();
        $data = array();
        $timeZone = $this->input->post('timeZone');
        $dayName = $this->input->post('dayNow');
        $outlet_id = $this->input->post('outlet_id');
        $date = "(SELECT unix_timestamp()*1000)";
        $time_created= current_millis();
        $time_modified = current_millis();
        $opname_ids = array();
        $opname_id = [];
        foreach ($dataProduct as $key => $value) {
            $last2date = $this->getLast2($date,$value->product_detail_id);
            $jsonopen = $this->Stock_opname_model->getLastOpname($value->product_detail_id);
            // echo $this->db->last_query();die();
            
            $open = 0;
                if($jsonopen){
                $open = $jsonopen[0]['opname'];
                }else{
                $open = 0;
                }
                

            //Ambil jumlah purchase
            $jsonpurchase = $this->Purchase_model->get_for_opname($value->product_detail_id, $last2date, $date,$timeZone);
            $purchase = 0;
                if($jsonpurchase)
                $purchase = $jsonpurchase[0]['qty'];

            //Ambil jumlah purchase
            $jsonretur = $this->Retur_model->get_for_opname($value->product_detail_id, $last2date, $date);
            $retur = 0;
                if($jsonretur)
                $retur = $jsonretur[0]['qty'];

            $totslPurchase = $purchase;

            //ambil jumlah sales
            $jsonsales = $this->Sales_model->get_for_opname($value->product_detail_id, $last2date, $date);
            $sales = 0;
                if($jsonsales)
                $sales = $jsonsales[0]['qty'];

            // ambil jumlah breakdown     
            $jsonBreakdown = $this->Sales_model->get_sales_breakdown($value->product_detail_id, $last2date, $date);
            if ($jsonBreakdown) {
                $breakdown = $jsonBreakdown[0]['qty'];
            }

            // get void
            $jsonVoid = $this->Sales_model->get_void($value->product_detail_id, $last2date, $date);   
            if ($jsonVoid) {
                $void = $jsonVoid[0]['void'];
            }
            $jsonRefund = $this->Sales_model->get_refund($value->product_detail_id, $last2date, $date); 
            if ($jsonRefund) {
                $refund = $jsonRefund[0]['refund'];
            }

            $totSales=$sales+$breakdown-$void;

            //Ambil jumlah transfer
            $jsontransferOri = $this->Transfer_model->get_for_opname_ori($value->product_detail_id, $last2date, $date,$timeZone);
            $transferOri = 0;
                if($jsontransferOri)
                $transferOri = $jsontransferOri[0]['qty'];

            //transfer destination
            $jsontransferDes = $this->Transfer_model->get_for_opname_des($value->product_detail_id, $last2date, $date,$timeZone);
            $transferDes = 0;
                if($jsontransferDes)
                $transferDes = $jsontransferDes[0]['qty'];

            $transfer = $transferDes-$transferOri;

            //Ambil jumlah spoil
            $jsonspoil = $this->Spoils_model->get_spoil_opname($value->product_detail_id, $last2date, $date);
            $spoil = 0;
            if($jsonspoil)
                $spoil = $jsonspoil[0]['qty'];
            //closing
            $closing = $open+$totslPurchase-$totSales-$spoil+$transfer-$retur+$refund;
            //stock estimation
            $jsonEstimation = $this->estimation_model->get_for_opname($value->product_detail_id,$dayName);
            $min_stock=0;
            $alertStock="Estimation Unset";
            if ($jsonEstimation) {
                $min_stock=$jsonEstimation[0]['min_stock'];
                $max_stock=$jsonEstimation[0]['max_stock'];
                $min_stockPersen= ($min_stock*50)/100;
                if ($closing>=$min_stock) {
                    $alertStock="Save";
                }elseif ($closing<=$max_stock) {
                    $alertStock="Need";
                }elseif ($closing<=$min_stockPersen) {
                    $alertStock="Urgent";
                }
                else{
                    $alertStock="Over";
                }
            }

            $jsonProduction = $this->M_opname->get_production($value->product_detail_id,$last2date,$date,$outlet_id,$timeZone);
            // echo $this->db->last_query();die();
            $production=0;
            if ($jsonProduction) {
                $ingridient = number_format($jsonProduction[0]['ingridient'], 2, '.', '');
                $endproduct = number_format($jsonProduction[0]['endproduct'], 2, '.', '');
                $residual = number_format($jsonProduction[0]['residual'], 2, '.', '');
            }

            $primaryProduction = $this->M_opname->get_primary($value->product_detail_id,$last2date,$date,$outlet_id,$timeZone);
            $primary =0;
            if ($primaryProduction) {
                $primary = $primaryProduction->qty;
            }
            $production = $endproduct+$residual-$ingridient-$primary;

            //closing
            $closing = $open+$purchase-$totSales-$spoil+$transfer-$retur+$refund+$production;
            // print_r($closing);die;

            //need
            if ($closing < $min_stock) {
                $need=$min_stock;
            }elseif ($closing > $min_stock) {
                $need=0;
            }else {
                $need=0;
            }

            // hpp
            $opname = $value->qty;
            $closing = round($closing, 3);
            $balance = round($opname - $closing, 3);
            // print_r($balance);die;
            $pd_id = $value->product_detail_id;
            $totalHpp = $this->M_set_jurnal_umum->get_hpp_opname($pd_id,$balance);
            // print_r($totalHpp);die;
            if($totalHpp !=0) {
                $hpp = $totalHpp/($balance<0?$balance*-1:$balance);
            }else{
                $hpp = 0;
            }

            // lost
            $lost = round($balance * $hpp, 3);


            $dataArray = array(
                'opname_id' => '',
                'product_detail_fkid' => $value->product_detail_id,
                'opname' => $value->qty,
                'spoil' => $spoil,
                'open' => $open,
                'purchase' => $totslPurchase,
                'retur' => $retur,
                'sales' => $totSales,
                'transfer' => $transfer,
                'closing' => $closing,                            
                'production' => $production,
                'need' =>$need,
                'alert' =>$alertStock,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'data_created' => $time_created,
                'time_modified' => $time_modified,
                'time_created' => $time_created,
                'refund' => $refund,
                'hpp' => $hpp,
                'lost' => $lost,
                'employee_fkid' => ($this->session->userdata('user_type')=='employee') ? $this->session->userdata('user_id') : null,
            );
            
            $res = $this->Stock_opname_model->insert($dataArray);
            $id = $this->db->insert_id();
            array_push($opname_id, $id);
        }        
        
        // insert finance jurnal
        if(getenv('ENABLE_FINANCE') == 'true' || (getenv('CI_ENV') !== 'production' && getenv('CI_ENV') !== 'staging')){
            $this->M_set_jurnal_umum->jurnal_opname($res);
        }        

        //publish to subscriber with pubsub
        $env = getenv('CI_ENV');
        if (!empty($_ENV['CI_ENV'])) {
            $env = $_ENV['CI_ENV'];
        }
        $topic = 'stock-opname-'.$env;
		$this->pubsub->publish($topic, json_encode(array('opname_ids' => $res)));

        return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output(count($opname_id));
    }

    public function _rules() 
    {
        $this->form_validation->set_rules('jumlah', 'jumlah[]', 'min[0]|is_numeric');
        $this->form_validation->set_rules('keterangan', 'keterangan[]', 'max_length[100]');
        //$this->form_validation->set_rules('ingridients_fkid', 'spoil_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }
    public function saveSession($key, $value){
        $this->session->set_userdata('insertopnameinventory['.$key.']',$value);
    }
    public function clearSession(){
        $this->session->unset_userdata('insertopnameinventory[]');
    }

}

/* End of file Stock_opname.php */
/* Location: ./application/controllers/products/stock/Stock_opname.php */