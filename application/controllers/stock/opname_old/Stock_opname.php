<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Stock_opname extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('stock/Stock_opname_model', 'Stock_opname_model');
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('purchase/transfer/Transfer_model', 'Transfer_model');
        $this->load->model('purchase/retur/Retur_products_model', 'Retur_model'); 
        $this->load->model('purchase/purchase_products/Purchase_products_model', 'Purchase_model'); 
        $this->load->model('stock/Spoil_model', 'Spoils_model');
        $this->load->model('outlet/Outlets_model', 'Unit_model');
        $this->load->model('stock/Sales_stock', 'Sales_model');
        $this->load->model('account/Login_model', 'Login_model');
    }

    public function index()
    {
        $link = site_url('stock/opname/opname_inventory/'); //URL dengan slash
        $data = array(
            'ajaxActionDatatableList' => $link.'json/productingridient',
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        
        // $this->load->view('stock/opname/stock_opname_v', $data);
        $data['page_title'] = 'Stock Opname';
        $this->template->view('stock/opname/v2/stock_opname_v', $data);
    }
}

/* End of file Stock_opname.php */
/* Location: ./application/controllers/stock/opname/Stock_opname.php */