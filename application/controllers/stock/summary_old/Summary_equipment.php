<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Summary_equipment extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('stock/Spoil_model', 'Spoil_model');
        $this->load->model('stock/Stock_opname_model', 'Stock_opname_model');
        $this->load->model('purchase/transfer/Transfer_model', 'Transfer_model');
        $this->load->model('purchase/retur/Retur_products_model', 'Retur_model'); 
        $this->load->model('purchase/purchase_products/Purchase_products_model', 'Purchase_model'); 
        $this->load->model('stock/Spoil_model', 'Spoils_model');
        $this->load->model('stock/Sales_stock', 'Sales_model');
        $this->load->model('stock/Stock_estimation_model', 'estimation_model');
        $this->load->model('production/production/Production_model','Production_model');
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('stock/M_opname');
    }

    public function index()
    {
        $link = site_url('stock/summary/summary_equipment/'); //URL dengan slash
        $data = array(
            'ajaxActionDatatableList' => $link.'json',
            );
        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['page_title'] = 'Stock Summary - Equipment';
        $this->template->view('stock/summary/summary_equipment_v2', $data);
    }

    public function json($limit,$ofset,$dayName,$timeZone,$outlet_id)
    {
        header('Content-Type: application/json');

        /* custom json output  start */
        $jsondata = $this->Spoil_model->jsonStockSummaryEquipment($limit,$ofset,$outlet_id); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json
        $date = "(SELECT unix_timestamp()*1000)";
        $dataArray = array();
        foreach ($json_decode as $a) {

            $id = $a->product_detail_id;            
            $last2date = $this->getLast2($date,$id);
            $outlet_id = $a->outlet_id;

            //abil last opname
            $jsonopen = $this->Stock_opname_model->getLastOpname($id);
            $open = 0;
            if($jsonopen)
            $open = number_format($jsonopen[0]['opname'], 2, '.', '');
            
            //ambil purchase
            $jsonpurchase = $this->Purchase_model->get_for_opname($id, $last2date, $date,$timeZone);          
            if($jsonpurchase)
            $purchase = number_format($jsonpurchase[0]['qty'], 2, '.', '');

            //Ambil jumlah retur
            $jsonretur = $this->Retur_model->get_for_opname($id, $last2date, $date);
             if($jsonretur)
                $retur = number_format($jsonretur[0]['qty'], 2, '.', '');

            //ambil jumlah sales
            $jsonsales = $this->Sales_model->get_for_opname($id, $last2date, $date);
            // echo $this->db->last_query();die();
            $sales = 0;
            if($jsonsales)
                $sales = $jsonsales[0]['qty'];

            // ambil jumlah breakdown     
            $jsonBreakdown = $this->Sales_model->get_sales_breakdown($id, $last2date, $date);
            // echo $this->db->last_query();die();
            if ($jsonBreakdown) {
                $breakdown =  number_format($jsonBreakdown[0]['qty'], 2, '.', '');
            }

            // get void
            $jsonVoid = $this->Sales_model->get_void($id, $last2date, $date);   
            if ($jsonVoid) {
                $void = $jsonVoid[0]['void'];
            }

            $jsonRefund = $this->Sales_model->get_refund($id, $last2date, $date);
            if ($jsonRefund) {
                $refund = $jsonRefund[0]['refund'] - $void;
            }

            $totSales=$sales+$breakdown-$void;

            //Ambil jumlah transfer
            $jsontransferOri = $this->Transfer_model->get_for_opname_ori($id, $last2date, $date,$timeZone);
            $transferOri = 0;
             if($jsontransferOri)
                $transferOri = number_format($jsontransferOri[0]['qty'], 2, '.', '');

            //transfer destination
            $jsontransferDes = $this->Transfer_model->get_for_opname_des($id, $last2date, $date,$timeZone);
            $transferDes = 0;
             if($jsontransferDes)
                $transferDes = number_format($jsontransferDes[0]['qty'], 2, '.', '');

            $transfer = $transferDes-$transferOri;

            //Ambil jumlah spoil
            $jsonspoil = $this->Spoils_model->get_spoil_opname($id, $last2date, $date);
            $spoil = 0;
            if($jsonspoil)
                $spoil = number_format($jsonspoil[0]['qty'], 2, '.', '');

            //get production
            $jsonProduction = $this->M_opname->get_production($id,$last2date,$date,$outlet_id,$timeZone);
            // echo $this->db->last_query();die();
            $production=0;
            if ($jsonProduction) {
                $ingridient = number_format($jsonProduction[0]['ingridient'], 2, '.', '');
                $endproduct = number_format($jsonProduction[0]['endproduct'], 2, '.', '');
                $residual = number_format($jsonProduction[0]['residual'], 2, '.', '');
                $primary = number_format($jsonProduction[0]['qty_primary'], 2, '.', '');
                $production = $endproduct+$residual-$ingridient-$primary;
            }

            //closing
            $closing = $open+$purchase-$totSales-$spoil+$transfer-$retur+$refund+$production;

            //ambil min stock bay day (stock estimation)
            $jsonEstimation = $this->estimation_model->get_for_opname($id,$dayName);
            $min_stock=0;
            $alertStock="Estimation Unset";
            if ($jsonEstimation) {
                $min_stock=$jsonEstimation[0]['min_stock'];
                $max_stock=$jsonEstimation[0]['max_stock'];
                $min_stockPersen= ($min_stock*50)/100;
                if ($closing>=$min_stock) {
                    $alertStock="Save";
                }elseif ($closing<=$max_stock) {
                    $alertStock="Need";
                }elseif ($closing<=$min_stockPersen) {
                    $alertStock="Urgent";
                }
                else{
                    $alertStock="Over";
                }
            }            

            //need
            if ($closing < $min_stock) {
                $need=$min_stock-$closing;
            }else {
                $need=0;
            }
        
            $temp_data = array( 
               'breakdown' =>$breakdown,
                'ingridient_name' => $a->ingridient_name." ".$a->variant,
                'outlet_name' => $a->outlet_name,
                'category' => $a->category,
                'subcategory' => $a->subcategory,
                'unit' => $a->unit,
                'open' => $open,
                'purchase' => $purchase,
                'sales' => $totSales,
                'transfer' => $transfer,
                'spoil' => $spoil,
                'closing' => $closing,
                'qty' => $a->qty,
                'alert' =>$alertStock,
                'min_stock' => $min_stock,
                'production' => $production,
                'retur' => $retur,
                'need' => $need,
                'breakdown' => $breakdown,
                'void' => $void,
                'refund' => $refund,
            );
            
            array_push($dataArray, $temp_data);
            
        }
        //remake json data (JANGAN DIEDIT)
        $draw_json = array(
            'data' => $dataArray
            );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */

        
    }
    public function getLast2($date){
        $jsondata = $this->Stock_opname_model->getLast2();
        //$json_decode = json_decode($jsondata); //pecah data json
        if($jsondata)
            return $jsondata->data_created;
        else
            return 0;
    }

}

/* End of file Summary_equipment.php */
/* Location: ./application/controllers/stock/summary/Summary_equipment.php */