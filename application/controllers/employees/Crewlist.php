<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Crewlist extends Auth_Controller {

    protected $urlpath = 'employees/crewlist';

	public function __construct()
	{
		parent::__construct();
        //Do your magic here
		$this->load->model('employees/Employee_model');
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('employees/Jabatan_model'); //form
        $this->load->model('user/Admin_model');


        $this->load->library('user_key/User_key_lib');        
	}

	public function index()
	{
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." using CrewList V2\n");
		$link = site_url($this->urlpath.'/'); //URL dengan slash
        $data = array(
            'kolomID' => 'employee_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'datatables',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'create', // 'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'update', // 'ajaxActionUpdate' => $link.'action/update',
            'ajaxActionUpdateUserRole' => $link.'update_role/',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_jabatan'] = $this->Jabatan_model->form_select();

        //setting untuk form role
        $data['menurole_web'] = $this->privilege->role_web();
        $data['menurole_mobilemenu'] = $this->user_role->accesslist('mobilemenu');
        $data['menurole_mobilemenu_authorization'] = $this->user_role->accesslist('mobilemenu_authorization');


        //feature access / permission access
        $data['permission'] = array(
            'add' => $this->privilege->check(uri_string(),'add'),
            'edit' => $this->privilege->check(uri_string(),'edit'),
            'delete' => $this->privilege->check(uri_string(),'delete'),
        );

		// $this->load->view('employees/crewlist/crewlist_v.php', $data);
        $data['page_title'] = 'Crew List';
        $this->template->view('employees/crewlist/v2/crewlist_v2', $data);
	}

    public function datatables()
    {
        $jsondata = $this->Employee_model->datatables();
        $json_decode = json_decode($jsondata);
        $data = array();
        foreach ($json_decode->data as $a) {
            $data[] = array(
                'employee_id' => $a->employee_id,
                'name' => htmlentities($a->name),
                'address' => htmlentities($a->address),
                'phone' => htmlentities($a->phone),
                'jabatan_name' => htmlentities($a->jabatan_name),
                'email' => htmlentities($a->email),
                'access_mode' => htmlentities(ucfirst($a->access_mode)),
                'access_status_web' => htmlentities(ucfirst($a->access_status_web)),
                'access_status_mobile' => htmlentities(ucfirst($a->access_status_mobile)),
                'level' => $a->level,
                'outlet_list' => htmlentities($a->outlet_list)
            );
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $data
        );

        //output
        echo format_json($draw_json);
    }

    public function _employee_allow_edit($id)
    {
        $valid = true;
        $user_type = $this->session->userdata('user_type');
        $user_id = $this->session->userdata('user_id');
        //get level daya yang diedit
        $row = $this->Employee_model->get_by_id($id); //get data id

        //disable edit dirinya sendiri
        if ($valid==true && $user_type=='employee' && $user_id==$id) {
            $valid = false;
            $draw_json = array(
                'status' => 'error',
                'message' => 'Action Forbidden!'
            );
        }

        //cek level (hanya level yang lebih tinggi yang bisa edit data)
        if ($valid==true && $user_type=='employee') {
            //get level employee yang login
            $mydata_row = $this->Employee_model->get_by_id($user_id);
            $mylevel = $mydata_row->level;

            if ($row->level > $mylevel) {
                $valid = false;
                $draw_json = array(
                    'status' => 'error',
                    'message'=> 'You don\'t allowed to modify this data'
                );
            }
        }

        //return data
        if ($valid == true) {
            return $row;
        }
        else{
            echo format_json($draw_json);die();
        }
    }

    public function get_data($id=null)
    {
        //inisialisasi
        $draw_json = array();

        //cek apakah yang melakukan edit diizinkan
        $row = $this->_employee_allow_edit($id); // $row = $this->Employee_model->get_by_id($id);
        if ($row) {
            $data_row = array(
                'id' => ($row->employee_id),
                'name' => htmlentities($row->name),
                'address' => htmlentities($row->address),
                'phone' => htmlentities($row->phone),
                //'outlet' => html_entity_decode($row->outlet_fkid),
                'jabatan' => htmlentities($row->jabatan_fkid),
                'access_mode' => htmlentities($row->access_mode),
                'email' => htmlentities($row->email),
                'level' => $row->level,
                // 'role' => json_decode($row->role),
                'role_mobile' => json_decode($row->role_mobile)
            );

            //get web role
            $data_role_web = array();
            $get_data_role_web = $this->Employee_model->get_role($id);
            if (!empty($get_data_role_web)) {
                foreach ($get_data_role_web as $key => $value) {
                    $data_role_web[] = array(
                        'url' => $value->url,
                        'view' => $value->role_view,
                        'add' => $value->role_add,
                        'edit' => $value->role_edit,
                        'delete' => $value->role_delete,
                        'access' => (is_json($value->role_access)) ? json_decode($value->role_access) : json_decode(json_encode(array()))
                    );
                }
            }
            $data_row['role_web'] = $data_role_web;

            //ambil daftar outlet yang dapat diakses
            $outlet_access = $this->Employee_model->get_access_outlet($id);
            $data_row_outlet = array();
            foreach ($outlet_access->result() as $a) {
                $data_row_outlet[] = array(
                    'outlet_id' => $a->outlet_fkid,
                );
            }
            $data_row['outlet'] = $data_row_outlet;

            //output format
            $draw_json = array(
                'status' => 'success',
                'data' => $data_row
            );
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //output
        echo format_json($draw_json);
    }

    public function create()
    {
        //init
        $draw_json = array();
        $email_used = false;

        //data yang di-kirim
        $datapost = array(
            'name' => $this->input->post('name',true),
            'address' => $this->input->post('address',true),
            'phone' => $this->input->post('phone',true),
            // 'outlet' => $this->input->post('outlet[]',true),
            'jabatan_fkid' => $this->input->post('jabatan',true),
            'level' => $this->input->post('level',true),
            'access_mode' => $this->input->post('access_mode',true),
            'email' => $this->input->post('email',true),
            'pin' => $this->input->post('pin',true)
        );

        //validasi
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('address', 'address', 'trim|required');
        $this->form_validation->set_rules('phone', 'phone', 'trim|required|max_length[15]|is_numeric');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
        $this->form_validation->set_rules('jabatan', 'jabatan', 'trim|required');
        $this->form_validation->set_rules('access_mode', 'access mode', 'trim|required|in_list[all,web,mobile]',array(
            'in_list' => 'The %s must All, Web, Mobile.'
        ));

        $email_on = '';
        if ($datapost['access_mode']=='all' || $datapost['access_mode']=='web') {
            $this->form_validation->set_rules('email', 'email', 'trim|required|callback_valid_email');

            //cek email admin
            if ($email_used==false) {
                $row_email = $this->Admin_model->get_by_email($datapost['email']);
                $email_used = ($row_email) ? true : false;
                $email_on = 'administrator';
            }
            //cek email employee
            if ($email_used==false) {
                $row_email = $this->Employee_model->get_all_employee_by_email($datapost['email']);
                $email_used = ($row_email) ? true : false;
                $email_on = 'employee';
            }
        }
        if ($datapost['access_mode']=='all' || $datapost['access_mode']=='mobile') {
            $this->form_validation->set_rules('pin', 'PIN', 'trim|required|exact_length[5]|is_numeric');
        }

        $this->form_validation->set_error_delimiters('', '');


        if ($this->form_validation->run() == FALSE || $email_used==true) {
            $error_email = ($email_used) ? 'This email already registered by '.$email_on.'.' : form_error('email');
            $draw_json = array(
                'status' => 'error',
                'message' => 'Create Record Failed',
                'data_error'   => array(
                    'name' => form_error('name'),
                    'address' => form_error('address'),
                    'phone' => form_error('phone'),
                    'outlet' => form_error('outlet[]'),
                    'jabatan' => form_error('jabatan'),
                    'access_mode' => form_error('access_mode'),
                    'email' => $error_email,
                    'pin' => form_error('pin'),
                )
            );
        } else {
            /*re-format value outlet yang dicentang untuk all outlet*/
            $outletSubmitted = $this->input->post('outlet[]');
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }

            //get role by jabatan id
            $data_jabatan_row = $this->Jabatan_model->get_by_id($datapost['jabatan_fkid']);
            $datapost['level'] = ($data_jabatan_row) ? $data_jabatan_row->level : 0;
            $datapost['role_mobile'] = ($data_jabatan_row) ? $data_jabatan_row->role_mobile : null;

            switch ($datapost['access_mode']) {
                case 'all':
                    $datapost['access_status_web'] = 'pending';
                    $datapost['access_status_mobile'] = 'activated';
                    break;
                case 'web':
                    $datapost['access_status_web'] = 'pending';
                    $datapost['access_status_mobile'] = 'deactivated';
                    break;
                case 'mobile':
                    $datapost['access_status_web'] = 'deactivated';
                    $datapost['access_status_mobile'] = 'activated';
                    unset($datapost['email']);
                    break;
                default: break;
            }

            $response = $this->Employee_model->insert($datapost);
            if ($response) {
                $employee_id = $this->db->insert_id();
                //tambahkan akses outlet employee
                foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                    $data_akses_outlet['employee_fkid'] = $employee_id;
                    $data_akses_outlet['outlet_fkid'] = $checkedoutlet;

                    $inputDB_detail = $this->Employee_model->add_access_outlet($data_akses_outlet);
                } //end foreach

                //kirim email aktivasi employee
                if ($datapost['access_mode']=='all' || $datapost['access_mode']=='web') {
                    //copy employee role by jabatan
                    $jabatan_role = $this->Jabatan_model->get_role($datapost['jabatan_fkid']);
                    $jabatan_role_list = array();
                    if (!empty($jabatan_role)) {
                        foreach ($jabatan_role as $key => $value) {
                            $jabatan_role_list[] = array(
                                'employee_fkid' => $employee_id,
                                'url' => $value->url,
                                'role_view' => $value->role_view,
                                'role_add' => $value->role_add,
                                'role_edit' => $value->role_edit,
                                'role_delete' => $value->role_delete,
                                'role_access' => $value->role_access
                            );
                        }
                        $this->db->insert_batch('employee_role', $jabatan_role_list);
                    }

                    //kirim email aktivasi
                    $this->user_key_lib->create_activation_key($datapost['email'], 'employee');
                }

                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Create Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Create Record Error'
                );
            }
        }

        //output
        echo format_json($draw_json);
    }

    public function update()
    {
        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." update employee V2\n");

        //init
        $draw_json = array();
        $email_used = false;

        //data yang di-kirim
        $id = $this->input->post('id', true);
        $row = $this->_employee_allow_edit($id); // $row = $this->Employee_model->get_by_id($id);
        if (!$row) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
            echo format_json($draw_json);die();
        }
        $datapost = array(
            'name' => $this->input->post('name',true),
            'address' => $this->input->post('address',true),
            'phone' => $this->input->post('phone',true),
            'jabatan_fkid' => $this->input->post('jabatan',true),
            'level' => $this->input->post('level',true),
            'access_mode' => $this->input->post('access_mode',true),
            'email' => $this->input->post('email')
        );

        //validasi
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('address', 'address', 'trim|required');
        $this->form_validation->set_rules('phone', 'phone', 'trim|required|max_length[15]|is_numeric');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
        $this->form_validation->set_rules('jabatan', 'jabatan', 'trim|required');
        $this->form_validation->set_rules('level', 'level', 'trim|required|numeric');
        $this->form_validation->set_rules('access_mode', 'access mode', 'trim|required|in_list[all,web,mobile,off]',array(
            'in_list' => 'The %s must All, Web, Mobile or No Access.'
        ));

        //new
        $email_on='';
        switch ($datapost['access_mode']) {
            case 'all':
            case 'web':
                $this->form_validation->set_rules('email', 'email', 'trim|required|callback_valid_email');

                //cek email admin
                if (!$email_used) {
                    $row_email = $this->Admin_model->get_by_email($datapost['email']);
                    if ($row_email) {
                        $email_used = true;
                        $email_on = 'administrator';
                    }
                }
                //cek email employee
                if (!$email_used) {
                    $row_email = $this->Employee_model->get_all_employee_by_email($datapost['email']);
                    if ($row_email) {
                        $email_used = true;
                        $email_used = ($row_email->employee_id==$id) ? false : true;
                        $email_on = 'employee';
                    }
                }
                break;
            
            default: break;
        }


        // if ($datapost['access_mode']=='all' || $datapost['access_mode']=='web') {
        //     $this->form_validation->set_rules('email', 'email', 'trim|required|callback_valid_email');
        //     //cek email admin
        //     if ($email_used==false) {
        //         $row_email = $this->Admin_model->get_by_email($datapost['email']);
        //         $email_used = ($row_email) ? true : false;
        //     }
        //     //cek email employee
        //     if ($email_used==false) {
        //         $row_email = $this->Employee_model->get_all_employee_by_email($datapost['email']);
        //         if ($row_email) {
        //             $email_used = true;
        //             $email_used = ($row_email->employee_id==$id) ? false : true;
        //         }
        //     }
        // }
        if ($datapost['access_mode'] != $row->access_mode) {
            if ($datapost['access_mode']=='all' || $datapost['access_mode']=='mobile') {
                $this->form_validation->set_rules('pin', 'PIN', 'trim|required|exact_length[5]|is_numeric');
            }
        }

        $this->form_validation->set_error_delimiters('', '');

        if ($this->form_validation->run() == FALSE || $email_used==true) {
            $error_email = ($email_used) ? 'This email already registered on '.$email_on.'.' : form_error('email');
            $draw_json = array(
                'status' => 'error',
                'message' => 'Update Record Failed',
                'data_error'   => array(
                    'name' => form_error('name'),
                    'address' => form_error('address'),
                    'phone' => form_error('phone'),
                    'outlet' => form_error('outlet[]'),
                    'jabatan' => form_error('jabatan'),
                    'access_mode' => form_error('access_mode'),
                    'email' => $error_email,
                    'pin' => form_error('pin'),
                )
            );
        } else {
            /*re-format value outlet yang dicentang untuk all outlet*/
            $outletSubmitted = $this->input->post('outlet[]');
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }

            //cek jabatan baru dan lama
            if ($row->jabatan_fkid!=$datapost['jabatan_fkid']) {
                $data_jabatan_row = $this->Jabatan_model->get_by_id($datapost['jabatan_fkid']);
                //hapus role jabatan lama
                $employee_oldrole_response = $this->Employee_model->delete_role_web($id);

                //masukkan role jabatan baru
                $datapost['role_mobile'] = $data_jabatan_row->role_mobile;

                $jabatan_role = $this->Jabatan_model->get_role($datapost['jabatan_fkid']);
                $jabatan_role_list = array();
                if (!empty($jabatan_role)) {
                    $employee_id = $id;
                    foreach ($jabatan_role as $key => $value) {
                        $jabatan_role_list[] = array(
                            'employee_fkid' => $employee_id,
                            'url' => $value->url,
                            'role_view' => $value->role_view,
                            'role_add' => $value->role_add,
                            'role_edit' => $value->role_edit,
                            'role_delete' => $value->role_delete,
                            'role_access' => $value->role_access
                        );
                    }
                    $this->db->insert_batch('employee_role', $jabatan_role_list);
                }
            }

            //access mode
            switch ($datapost['access_mode']) {
                case 'all':
                    $datapost['email'] = $this->input->post('email', true);
                    if (!empty($this->input->post('pin'))) {
                        $datapost['pin'] = $this->input->post('pin', true);
                    }
                    if ($row->email != $datapost['email']) {
                        $datapost['access_status_web'] = 'pending';
                        $this->user_key_lib->create_activation_key($datapost['email'], 'employee'); //kirim email aktivasi
                    }
                    $datapost['access_status_mobile'] = 'activated';
                    if (!empty($this->input->post('pin'))) {
                        $datapost['pin'] = $this->input->post('pin',true);
                    }
                    break;
                case 'web':
                    $datapost['email'] = $this->input->post('email', true);
                    if (!empty($this->input->post('pin'))) {
                        $datapost['pin'] = $this->input->post('pin', true);
                    }
                    if ($row->email != $datapost['email']) {
                        $datapost['access_status_web'] = 'pending';
                        $this->user_key_lib->create_activation_key($datapost['email'], 'employee'); //kirim email aktivasi
                    }
                    $datapost['access_status_mobile'] = 'deactivated';
                    break;
                case 'mobile':
                    $datapost['email'] = null;
                    $datapost['access_status_web'] = 'deactivated';
                    $datapost['access_status_mobile'] = 'activated';
                    if (!empty($this->input->post('pin'))) {
                        $datapost['pin'] = $this->input->post('pin',true);
                    }
                    break;
                case 'off':
                    $datapost['email'] = null;
                    $datapost['pin'] = null;
                    $datapost['access_status_web']      = 'deactivated';
                    $datapost['access_status_mobile']   = 'deactivated';
                    break;
                default: break;
            }

            //update
            $response = $this->Employee_model->update($id, $datapost);
            if ($response) {
                //hapus akses outlet lama & simpan akses outlet employee baru
                $deleteDB_detail = $this->Employee_model->delete_access_outlet($id);
                foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                    $data_akses_outlet['employee_fkid'] = $id;
                    $data_akses_outlet['outlet_fkid'] = $checkedoutlet;

                    $inputDB_detail = $this->Employee_model->add_access_outlet($data_akses_outlet);
                } //end foreach
            }

            //format output
            $draw_json = array(
                'status' => 'success',
                'message' => 'Update Record Success'
            );
        }

        echo format_json($draw_json);
    }

    public function update_role()
    {
        //init
        $draw_json = array();

        //cek employee
        $id = $this->input->post('id', true);
        $row = $this->_employee_allow_edit($id); // $row = $this->Employee_model->get_by_id($id);
        if ($row) {
            $checked_mobilerole = $this->_roleMobile();

            //data yang dikirim
            $datapost['role_mobile'] = json_encode($checked_mobilerole);

            //validasi
            $this->form_validation->set_rules('mobilerole[]', 'mobile role', 'trim');
            $this->form_validation->set_error_delimiters('', '');

            if ($this->form_validation->run() == FALSE) {
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Role Failed'
                );
            } else {
                //update role mobile
                $response = $this->Employee_model->update($id, $datapost);
                if ($response) {
                    //hapus role web lama
                    $this->Employee_model->delete_role_web($id);

                    //insert role baru
                    $this->_roleWeb($id);
                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Update Role Success'
                    );
                }
                else{
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Update Role Error'
                    );
                }
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //output
        echo format_json($draw_json);
    }
    
    public function delete($id=null)
    {
        //cek allow delete
        $this->_employee_allow_edit($id);

        //init
        $draw_json = array();
        $response = false;

        $row = $this->Employee_model->get_by_id($id);
        if ($row) {
            $response = $this->Employee_model->delete($id);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else {
                //set status off
                $response = $this->Employee_model->update($id, array(
                    'data_status'=>'off',
                    'access_status_web'=>'deactivated',
                    'access_status_mobile'=>'deactivated',
                    'email'=>null,
                    'pin'=>null,
                ));

                //set response output
                if ($response) {
                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Record Deleted'
                    );
                }
                else{
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Delete Record Failed'
                    );
                }
            }
        }
        else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }


        //delete success action
        if ($response) {
            //delete role
            $this->Employee_model->delete_role_web($id);

            //delete photo
        }

        //buat output
        echo format_json($draw_json);
    }

    public function resend_activation()
    {
        $id = $this->input->post('employee_id');
        $row = $this->Employee_model->get_by_id($id);
        if ($row) {
            //cek web access
            if ($row->access_mode=='all' || $row->access_mode=='web') {
                if ($row->access_status_web=='pending') {
                    if (!empty($row->email) && $row->email==$this->input->post('email')) {
                        //resend aktivasi
                        $this->user_key_lib->create_activation_key($row->email, 'employee');

                        $draw_json = array(
                            'status' => 'success',
                            'message' => 'Activation Link have been sent to '.$row->email
                        );
                    }
                    else{
                        $draw_json = array(
                            'status' => 'error',
                            'message' => 'Email didn\'t match!'
                        );
                    }

                }
                else{
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Access must on pending'
                    );
                }
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Web Access Forbidden for This User'
                );
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'User not Found'
            );
        }

        echo format_json($draw_json);
    }

    public function _roleWeb($employee_id)
    {
        //init
        $table = 'employee_role';

        //level1
        foreach ($this->privilege->role_web() as $keyLV1 => $mainmenu) {
            //init
            $level1_have = false;
            $level2_have = false;
            $level3_have = false;

            $mainmenu_role_view     = ($this->input->post($keyLV1.'_view')) ? true : false;
            $mainmenu_role_add      = ($this->input->post($keyLV1.'_add')) ? true : false;
            $mainmenu_role_edit     = ($this->input->post($keyLV1.'_edit')) ? true : false;
            $mainmenu_role_delete   = ($this->input->post($keyLV1.'_delete')) ? true : false;
            $mainmenu_role_access   = array();
            if ($this->input->post($keyLV1.'_accessdetail[]')) {
                foreach ($this->input->post($keyLV1.'_accessdetail[]') as $keyLV1ad  => $vad) {
                    //cek apakah inputan benar
                    if (isset($mainmenu['access_detail'][$keyLV1ad])) {
                        $obj_accessdetail = array($keyLV1ad=>true);
                        $mainmenu_role_access = array_merge($mainmenu_role_access, $obj_accessdetail);
                        $level1_have = true;
                    }
                }
            }
            $level1 = array(
                'employee_fkid' => $employee_id,
                'url' => !empty($mainmenu['url']) ? $mainmenu['url'] : false,
                'role_view' => $mainmenu_role_view,
                'role_add' => $mainmenu_role_add,
                'role_edit' => $mainmenu_role_edit,
                'role_delete' => $mainmenu_role_delete,
                'role_access' => ($mainmenu_role_access) ? json_encode($mainmenu_role_access) : false
            );
            if ($mainmenu_role_view) {
                $level1_have = true;
            }

            //level2
            if (isset($mainmenu['sub'])) {
                foreach ($mainmenu['sub'] as $keyLV2 => $menulist) {
                    //init
                    $level2_have = false;

                    $menulist_role_view     = ($this->input->post($menulist['url'].'_view')) ? true : false;
                    $menulist_role_add      = ($this->input->post($menulist['url'].'_add')) ? true : false;
                    $menulist_role_edit     = ($this->input->post($menulist['url'].'_edit')) ? true : false;
                    $menulist_role_delete   = ($this->input->post($menulist['url'].'_delete')) ? true : false;
                    $menulist_role_access   = array();
                    if ($this->input->post($menulist['url'].'_accessdetail[]')) {
                        foreach ($this->input->post($menulist['url'].'_accessdetail[]') as $keyLV2ad  => $vad) {
                            //cek apakah inputan benar
                            if (isset($menulist['access_detail'][$keyLV2ad])) {
                                $obj_accessdetail = array($keyLV2ad=>true);
                                $menulist_role_access = array_merge($menulist_role_access, $obj_accessdetail);
                                $level2_have = true;
                                $level1_have = true;
                            }
                        }
                    }
                    $level2 = array(
                        'employee_fkid' => $employee_id,
                        'url' => !empty($menulist['url']) ? $menulist['url'] : false,
                        'role_view' => $menulist_role_view,
                        'role_add' => $menulist_role_add,
                        'role_edit' => $menulist_role_edit,
                        'role_delete' => $menulist_role_delete,
                        'role_access' => ($menulist_role_access) ? json_encode($menulist_role_access) : false
                    );
                    if ($menulist_role_view) {
                        $level2_have = true;
                    }

                    //level3
                    if (isset($menulist['sub'])) {
                        foreach ($menulist['sub'] as $keyLV3 => $menu) {
                            //init
                            $level3_have = false;

                            $menu_role_view     = ($this->input->post($menu['url'].'_view')) ? true : false;
                            $menu_role_add      = ($this->input->post($menu['url'].'_add')) ? true : false;
                            $menu_role_edit     = ($this->input->post($menu['url'].'_edit')) ? true : false;
                            $menu_role_delete   = ($this->input->post($menu['url'].'_delete')) ? true : false;
                            $menu_role_access   = array();
                            if ($this->input->post($menu['url'].'_accessdetail[]')) {
                                foreach ($this->input->post($menu['url'].'_accessdetail[]') as $keyLV3ad => $vad) {
                                    //cek apakah inputan benar
                                    if (isset($menu['access_detail'][$keyLV3ad])) {
                                        $obj_accessdetail = array($keyLV3ad=>true);
                                        $menu_role_access = array_merge($menu_role_access, $obj_accessdetail);
                                        $level3_have = true;
                                    }
                                }
                            }
                            $level3 = array(
                                'employee_fkid' => $employee_id,
                                'url' => !empty($menu['url']) ? $menu['url'] : false,
                                'role_view' => $menu_role_view,
                                'role_add' => $menu_role_add,
                                'role_edit' => $menu_role_edit,
                                'role_delete' => $menu_role_delete,
                                'role_access' => ($menu_role_access) ? json_encode($menu_role_access) : false
                            );
                            if ($menu_role_view) {
                                $level3_have = true;
                            }
                            if ($level3_have) {
                                $this->db->insert($table, $level3);
                            }
                        }
                    }
                    if ($level2_have) {
                        $this->db->insert($table, $level2);
                    }
                }
            }
            if ($level1_have==true) {
                $this->db->insert($table, $level1);
            }
        }
    }

    public function _roleMobile()
    {
        //datapost mobile auth
        $checked_mobilerole = array();
        $mobilemenulist = $this->user_role->accesslist('mobilemenu');
        foreach ($mobilemenulist as $mobilemenu) {
            $checked = ($this->input->post('mobilerole['.$mobilemenu[0].']')=='on') ? true : false;
            $checked_mobilerole[$mobilemenu[0]] = $checked;
        }
        //--ROLE MOBILE AUTHORIZATION
        $checked_mobilerole['authorization'] = array();
        $mobilemenulist_authorization = $this->user_role->accesslist('mobilemenu_authorization');
        foreach ($mobilemenulist_authorization as $role) {
            $checked = ($this->input->post('mobilerole_authorization['.$role[0].']')=='on') ? true : false;
            if ($checked===true) {
                $checked_mobilerole['authorization'][$role[0]] = true;
            }
        }
        return $checked_mobilerole;
    }

    
    
    public function _check_registered_email($action, $table, $data)
    {
        $response = $this->Employee_model->check_registered_email($action, $table, $data);
        if ($response['data_count']>0) {
            $email_used = true;
        }
        else{
            $email_used = false;
        }

        return $email_used;
    }

    //custom validation with callback
    function valid_email($str)
    {
        return ( ! preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $str)) ? FALSE : TRUE;
    }




    /* UNUSED */
    public function json()
    {
        //header('Content-Type: application/json');
        //echo $this->Employee_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Employee_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->employee_id;
            $name = htmlentities($a->name); //encoding string
            $address = htmlentities($a->address); //encoding string
            $phone = htmlentities($a->phone); //encoding string
            //$outlet_name = htmlentities($a->outlet_name);
            $jabatan_name = htmlentities($a->jabatan_name);
            $email = htmlentities($a->email);
            $access_mode = htmlentities($a->access_mode);
            $access_status_web = htmlentities($a->access_status_web);
            $access_status_mobile = htmlentities($a->access_status_mobile);
            $level = $a->level;
            $joindate = htmlentities($a->date_join);
            

            //$role = "<a href='javascript:void(0)' onclick='actionShowRole({$id})'>role</a>";
            $role = "<button class='btn btn-primary btn-xs' onclick='actionShowRole({$id})'>Role</button>";
            $role .= "&nbsp;&nbsp;&nbsp;";

            $action  = $role."<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
            
            
            $temp_data = array(
                'employee_id' => $id,
                'name' => $name,
                'address' => $address,
                'phone' => $phone,
                //'outlet_name' => $outlet_name,
                'jabatan_name' => $jabatan_name,
                'email' => $email,
                'access_mode' => ucfirst($access_mode),
                'access_status_web' => ucfirst($access_status_web),
                'access_status_mobile' => ucfirst($access_status_mobile),
                'level' => $level,
                'join_date' => $joindate,
                'action' => $action
            );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }
    
    public function update_user_role()
    {
        //inisialisasi
        $draw_json = array();

        //data yang dikirim
        $datapost = array(
            'id' => $this->input->post('id', true),
            'role' => $this->input->post('menu[]', true),
            'role_mobile' => $this->input->post('mobilerole[]')
        );

        $user_id = $datapost['id'];
        if (empty($user_id) || !is_numeric($user_id)) {
            $draw_json = array(
                'status' => 'success',
                'message' => 'Invalid ID'
            );
            echo format_json($draw_json);
            die();
        }

        //rules input
        $this->form_validation->set_rules('menu[]', 'role', 'trim|required');
        $this->form_validation->set_rules('id', 'id', 'trim|required');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

        //validasi
        if ($this->form_validation->run() == FALSE) {
            //echo "apa ada yang salah?";
            $draw_json = array(
                'status' => 'error',
                'message' => 'Update Role Failed',
                'data' => array(
                    'role' => form_error('menu[]'),
                    'role_mobile' => form_error('mobilerole[]')
                )
            );
        } else {
            /* mengetahui role yang dicentang */
            //ROLE MAIN MENU
            $checked_webrole = array();
            $mainmenulist = $this->user_role->accesslist('mainmenu');
            foreach ($mainmenulist as $mainmenu) {
                $checked = ($this->input->post('menu['.$mainmenu[0].']')=='on') ? true : false;
                $checked_webrole[$mainmenu[0]]['read'] = $checked;
            }

            //ROLE STOCK LV 1
            $stock_checked = false;
            $mainmenu_stock_lv1 = $this->user_role->accesslist('stock_lv1');
            foreach ($mainmenu_stock_lv1 as $menu) {
                $checked = ($this->input->post('stocklv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $stock_checked = true;
                }
            }
            if ($stock_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['stock']['read']=false;
            }

            //ROLE PRODUCTS LV 1
            $products_checked = false;
            $mainmenu_products_lv1 = $this->user_role->accesslist('products_lv1');
            foreach ($mainmenu_products_lv1 as $menu) {
                $checked = ($this->input->post('productslv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $products_checked = true;
                }
            }
            if ($products_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['products']['read']=false;
            }

            //ROLE PURCHASE LV 1
            $purchase_checked = false;
            $mainmenu_purchase_lv1 = $this->user_role->accesslist('purchase_lv1');
            foreach ($mainmenu_purchase_lv1 as $menu) {
                $checked = ($this->input->post('purchaselv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $purchase_checked = true;
                }
            }
            if ($purchase_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['purchase']['read']=false;
            }


            //ROLE MOBILE
            $checked_mobilerole = array();
            $mobilemenulist = $this->user_role->accesslist('mobilemenu');
            foreach ($mobilemenulist as $mobilemenu) {
                $checked = ($this->input->post('mobilerole['.$mobilemenu[0].']')=='on') ? true : false;
                $checked_mobilerole[$mobilemenu[0]] = $checked;
            }
            //--ROLE MOBILE AUTHORIZATION
            $checked_mobilerole['authorization'] = array();
            $mobilemenulist_authorization = $this->user_role->accesslist('mobilemenu_authorization');
            foreach ($mobilemenulist_authorization as $role) {
                $checked = ($this->input->post('mobilerole_authorization['.$role[0].']')=='on') ? true : false;
                if ($checked===true) {
                    $checked_mobilerole['authorization'][$role[0]] = true;
                }
            }
            

            //update role
            $datapost_update['role_mobile'] = format_json($checked_mobilerole);
            $response = $this->Employee_model->update($datapost['id'], $datapost_update);
            if ($response===TRUE) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Update Role Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Role Failed on Server'
                );
            }
        }
        echo format_json($draw_json);
    }

    public function _checkedMobileRole()
    {
        $draw_json = array(
            'inputkasmasuk' => (!empty($this->input->post('mobilerole[inputkasmasuk]'))) ? true : false,
            'inputkaskeluar' => (!empty($this->input->post('mobilerole[inputkaskeluar]'))) ? true : false,
            'inputpembelian' => (!empty($this->input->post('mobilerole[inputpembelian]'))) ? true : false,
            'setting_printer' => (!empty($this->input->post('mobilerole[setting_printer]'))) ? true : false,
            'tutupkasir' => (!empty($this->input->post('mobilerole[tutupkasir]'))) ? true : false,
            'reprint_nota' => (!empty($this->input->post('mobilerole[reprint_nota]'))) ? true : false,
            'reprint_order' => (!empty($this->input->post('mobilerole[reprint_order]'))) ? true : false,
            'gantiharga' => (!empty($this->input->post('mobilerole[gantiharga]'))) ? true : false,
            'gantidiskontaxgratuity' => (!empty($this->input->post('mobilerole[gantidiskontaxgratuity]'))) ? true : false,
            'bukalaciuang' => (!empty($this->input->post('mobilerole[bukalaciuang]'))) ? true : false,
            'pembayaran' => (!empty($this->input->post('mobilerole[pembayaran]'))) ? true : false,
            'compliment' => (!empty($this->input->post('mobilerole[compliment]'))) ? true : false,
            'voucher' => (!empty($this->input->post('mobilerole[voucher]'))) ? true : false,
            'duty' => (!empty($this->input->post('mobilerole[duty]'))) ? true : false,
            'gantidiskon' => (!empty($this->input->post('mobilerole[gantidiskon]'))) ? true : false,
            'gantipajak' => (!empty($this->input->post('mobilerole[gantipajak]'))) ? true : false,
            'reprint_refund' => (!empty($this->input->post('mobilerole[reprint_refund]'))) ? true : false,
            'refund' => (!empty($this->input->post('mobilerole[refund]'))) ? true : false,
            'reprint_void' => (!empty($this->input->post('mobilerole[reprint_void]'))) ? true : false,
            'inputbarangrusak' => (!empty($this->input->post('mobilerole[inputbarangrusak]'))) ? true : false,
            'inputbaranghabis' => (!empty($this->input->post('mobilerole[inputbaranghabis]'))) ? true : false,
            'inputpromo' => (!empty($this->input->post('mobilerole[inputpromo]'))) ? true : false,
        );
        return format_json($draw_json);
    }

    public function _actionOLD($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        //inisialisasi
        $draw_json = array();
        $email_used = false; //apakah email sudah dipakai atau belum

        //data yang dikirim melalui post
        $data_post = array(
            'id' => $this->input->post('id'),
            'name' => $this->input->post('name',TRUE),
            'address' => $this->input->post('address', TRUE),
            'phone' => $this->input->post('phone', TRUE),
            'outlet' => $this->input->post('outlet[]', TRUE),
            'jabatan_id' => $this->input->post('jabatan', TRUE),
            'level' => $this->input->post('level', TRUE),
            'access_mode' => $this->input->post('access_mode', TRUE),
            'email' => $this->input->post('email', TRUE),
            'pin' => $this->input->post('pin', TRUE)
        );

        //cek email sudah dipakai atau belum START
        switch ($actionType) {
            case 'create':
                //cek email apakah sudah dipakai atau belum
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('create', 'employee', $data_post);
                }
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('create', 'admin', $data_post);
                }
                break;
            case 'update':
                //cek email apakah sudah dipakai atau belum
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('update', 'employee', $data_post);
                }
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('update', 'admin', $data_post);
                }
                break;
            default: break;
        }
        //cek email sudah dipakai atau belum END


        if ($this->form_validation->run() === FALSE || $email_used === TRUE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            //custom error output
            $error_email = (!empty(form_error('email'))) ? form_error('email') : '';
            $error_email = (empty($error_email) && $email_used===true) ? '<span class="text-danger">This email already registered.</span>' : $error_email;

            $draw_json = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                    'name' => form_error('name'),
                    'address' => form_error('address'),
                    'phone' => form_error('phone'),
                    'outlet' => form_error('outlet[]'),
                    'jabatan' => form_error('jabatan'),
                    'level' => form_error('level'),
                    'access_mode' => form_error('access_mode'),
                    'email' => $error_email,
                    'pin' => form_error('pin'),
                )
            );
        } else {
            /*re-format value outlet yang dicentang untuk all outlet*/
            $outletSubmitted = $data_post['outlet'];
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }



            //get role by jabatan id
            $data_jabatan_row = $this->Jabatan_model->get_by_id($data_post['jabatan_id']);
            $data_post['role_mobile'] = ($data_jabatan_row) ? $data_jabatan_row->role_mobile : null;
            

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                $data_post['level'] = ($data_jabatan_row) ? $data_jabatan_row->level : null;

                //action untuk create data
                $data_post_create = array(
                    'name' => $data_post['name'],
                    'address' => $data_post['address'],
                    'phone' => $data_post['phone'],
                    //'outlet_fkid' => $data_post['outlet_id'],
                    'jabatan_fkid' => $data_post['jabatan_id'],
                    'level'         => $data_post['level'],
                    'access_mode' => $data_post['access_mode'],
                    'role_mobile' => $data_post['role_mobile']
                );
                //user activation status by officer type
                switch ($data_post['access_mode']) {
                    case 'all':
                        $data_post_create['email'] = $data_post['email'];
                        $data_post_create['pin'] = $data_post['pin'];
                        $data_post_create['access_status_web'] = 'pending';
                        $data_post_create['access_status_mobile'] = 'activated';
                        break;
                    case 'web':
                        $data_post_create['email'] = $data_post['email'];
                        $data_post_create['access_status_web'] = 'pending';
                        $data_post_create['access_status_mobile'] = 'deactivated';
                        break;
                    case 'mobile':
                        $data_post_create['pin'] = $data_post['pin'];
                        $data_post_create['access_status_web'] = 'deactivated';
                        $data_post_create['access_status_mobile'] = 'activated';
                        break;
                    default: break;
                }


                $response = $this->Employee_model->insert($data_post_create);
                $primary_key = $this->db->insert_id();
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');

                if ($response) {
                    //tambahkan akses outlet employee
                    foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                        $data_akses_outlet['employee_fkid'] = $primary_key;
                        $data_akses_outlet['outlet_fkid'] = $checkedoutlet;

                        $inputDB_detail = $this->Employee_model->add_access_outlet($data_akses_outlet);
                    } //end foreach
                }

                //kirim email aktivasi untuk backoffice
                if ($response===true && ($data_post['access_mode']=='all' || $data_post['access_mode']=='web')) {
                    $this->user_key_lib->create_activation_key($data_post_create['email'], 'employee');
                }
            }
            elseif ($actionType=='update') {
                if ($this->session->userdata('user_type')=='employee') {
                    $id = $this->input->post('id');
                    $response = $this->_employee_allow_edit($id); //cek apakah employee diizinkan untuk mengedit data
                    if ($response['mylevel']<$data_post['level']) {
                        $draw_json = array(
                            'status' => 'error',
                            'message'=> 'You don\'t allowed to change level more than '.$response['mylevel']
                        );
                        echo format_json($draw_json);
                        die();
                    }
                    if ($this->input->post('id')==$this->session->userdata('user_id')) {
                        $draw_json = array(
                            'status' => 'error',
                            'message' => 'Action Forbidden!'
                        );
                        echo format_json($draw_json);
                        die();
                    }
                }


                //cek data yang akan diupdate
                $data_employee_row = $this->Employee_model->get_by_id($data_post['id']); //get data employee yang akan diupdate
                if ($data_employee_row) {
                    $data_row = array(
                        'id' => ($data_employee_row->employee_id),
                        'name' => html_entity_decode($data_employee_row->name),
                        'address' => html_entity_decode($data_employee_row->address),
                        'phone' => html_entity_decode($data_employee_row->phone),
                        'jabatan' => html_entity_decode($data_employee_row->jabatan_fkid),
                        'level' => html_entity_decode($data_employee_row->level),
                        'access_mode' => html_entity_decode($data_employee_row->access_mode),
                        'email' => html_entity_decode($data_employee_row->email),
                        'pin' => $data_employee_row->pin,
                        'role' => $data_employee_row->role, //json_decode($row->role)
                    );
                }

                //action untuk update data
                $data_post_update = array(
                    'name' => $data_post['name'],
                    'address' => $data_post['address'],
                    'phone' => $data_post['phone'],
                    //'outlet_fkid' => $data_post['outlet_id'],
                    'jabatan_fkid' => $data_post['jabatan_id'],
                    'level' => $data_post['level'],
                    'access_mode' => $data_post['access_mode'],
                );

                //update access_mode bila access_mode diubah
                if ($data_post['access_mode'] != $data_row['access_mode']) {
                    //officer setting
                    switch ($data_post['access_mode']) {
                        case 'all':
                            $data_post_update['email'] = $data_post['email'];
                            $data_post_update['password'] = null;
                            $data_post_update['pin'] = $data_post['pin'];
                            $data_post_update['access_status_web'] = 'pending';
                            $data_post_update['access_status_mobile'] = 'activated';

                            //KIRIM EMAIL AKTIVASI (NEXT)
                            break;
                        case 'mobile':
                            $data_post_update['email'] = null;
                            $data_post_update['password'] = null;
                            $data_post_update['pin'] = $data_post['pin'];
                            $data_post_update['access_status_web'] = 'deactivated';
                            $data_post_update['access_status_mobile'] = 'activated';
                            break;
                        case 'web':
                            $data_post_update['email'] = $data_post['email'];
                            $data_post_update['password'] = null; //password direset
                            $data_post_update['pin'] = null;
                            $data_post_update['access_status_web'] = 'pending';
                            $data_post_update['access_status_mobile'] = 'deactivated';

                            //KIRIM EMAIL AKTIVASI (NEXT)
                            break;
                        default: break;
                    }

                    //kirim email aktivasi untuk backoffice
                    if ($data_post['access_mode']=='all' || $data_post['access_mode']=='web') {
                        $this->user_key_lib->create_activation_key($data_post_update['email'],'employee'); //kirim aktivasi email
                    }
                }

                //kalau access mode masih sama
                if ($data_post['access_mode'] == $data_row['access_mode']) {
                    if ($data_post['access_mode']=='all' && ($data_post['email']!=$data_row['email'])) {
                        $data_post_update['email'] = $data_post['email'];
                        $data_post_update['password'] = null;
                        $data_post_update['access_status_web'] = 'pending';
                        $data_post_update['access_status_mobile'] = 'activated';

                        $this->user_key_lib->create_activation_key($data_post_update['email'],'employee'); //kirim email aktivasi
                    }
                    if ($data_post['access_mode']=='web' && ($data_post['email']!=$data_row['email'])) {
                        $data_post_update['email'] = $data_post['email'];
                        $data_post_update['password'] = null;
                        $data_post_update['access_status_web'] = 'pending';

                        $this->user_key_lib->create_activation_key($data_post_update['email'],'employee'); //kirim email aktivasi
                    }

                    if (!empty($data_post['pin'])) {
                        $data_post_update['pin'] = $data_post['pin'];
                    }
                }


                #update data
                $response = $this->Employee_model->update($this->input->post('id', TRUE), $data_post_update);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');

                //akses outlet 
                if ($response) {
                    //hapus akses outlet lama
                    $deleteDB_detail = $this->Employee_model->delete_access_outlet($data_post['id']);

                    //tambahkan akses outlet employee
                    foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                        $data_akses_outlet['employee_fkid'] = $data_post['id'];
                        $data_akses_outlet['outlet_fkid'] = $checkedoutlet;

                        $inputDB_detail = $this->Employee_model->add_access_outlet($data_akses_outlet);
                    } //end foreach
                }
            }
            
            //buat array untuk output json
            if ($response==true) {
                $draw_json = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Record Failed'
                );
            }
        }

        //buat output dalam format json
        echo format_json($draw_json);
    }

    public function _rules() 
    {
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('address', 'address', 'trim|required');
        $this->form_validation->set_rules('phone', 'phone', 'trim|required|max_length[15]|is_numeric');
        //$this->form_validation->set_rules('outlet', 'outlet', 'trim|required');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
        $this->form_validation->set_rules('jabatan', 'jabatan', 'trim|required');
        $this->form_validation->set_rules('access_mode', 'access mode', 'trim|required');
        
        //set input rules berdasarkan action
        switch ($this->input->post('action')) {
            case 'create':
                switch ($this->input->post('access_mode')) {
                    case 'all':
                        $this->form_validation->set_rules('email', 'email', 'trim|required|max_length[30]|valid_email');
                        $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                        break;
                    case 'web':
                        $this->form_validation->set_rules('email', 'email', 'trim|required|max_length[30]|valid_email');
                        break;
                    case 'mobile':
                        $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                        break;
                    default:
                        //$this->input->post('access_mode') = '';
                        $_POST['access_mode'] = "";
                        break;
                }
                break;
            case 'update':
                //add level validation
                $this->form_validation->set_rules('level', 'level', 'trim|required|is_numeric');

                //get role by jabatan id
                $data_post['id'] = $this->input->post('id');
                $data_post['access_mode'] = $this->input->post('access_mode');

                //* data yang akan diupdate *//
                $data_employee_row = $this->Employee_model->get_by_id($data_post['id']); //get data employee yang akan diupdate
                if ($data_employee_row) {
                    $data_row = array(
                        'id' => ($data_employee_row->employee_id),
                        'name' => html_entity_decode($data_employee_row->name),
                        'address' => html_entity_decode($data_employee_row->address),
                        'phone' => html_entity_decode($data_employee_row->phone),
                        //'outlet' => html_entity_decode($data_employee_row->outlet_fkid),
                        'jabatan' => html_entity_decode($data_employee_row->jabatan_fkid),
                        'level' => html_entity_decode($data_employee_row->level),
                        'access_mode' => html_entity_decode($data_employee_row->access_mode),
                        'email' => html_entity_decode($data_employee_row->email),
                        'pin' => $data_employee_row->pin,
                        'role' => $data_employee_row->role, //json_decode($row->role)
                    );
                }

                //validasi bila access_mode diubah
                if ($data_post['access_mode'] != $data_row['access_mode']) {
                    switch ($data_post['access_mode']) {
                        case 'all':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|max_length[30]|valid_email');
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'mobile':
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'web':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|max_length[30]|valid_email');
                            break;
                        default: break;
                    }
                }
                elseif ($data_post['access_mode']==$data_row['access_mode']) {
                    switch ($data_post['access_mode']) {
                        case 'all':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|max_length[30]|valid_email');
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'mobile':
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'web':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|max_length[30]|valid_email');
                            break;
                        default: break;
                    }
                }
                break;
            default: break;
        }

        $this->form_validation->set_rules('id', 'employee_id', 'trim');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Crewlist.php */
/* Location: ./application/controllers/employees/Crewlist.php */