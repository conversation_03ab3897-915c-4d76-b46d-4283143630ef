<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>ensi extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->employees_...('read_access'); //employee role access page | submenu
		$this->user_role->employees('read_access'); //employee role access page | menu
	}

	public function index()
	{
		$this->load->view('employees/absensi_v');
	}

}

/* End of file Absensi.php */
/* Location: ./application/controllers/employees/Absensi.php */