<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Smsblast extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->employees_internalmemo_smsblast('read_access'); //employee role access page | sub-submenu
		//$this->user_role->employees_internalmemo('read_access'); //employee submenu role access page | submenu
		$this->user_role->employees('read_access'); //employee role access page | menu
	}

	public function index()
	{
		$this->load->view('employees/internalmemo/smsblast_v');
	}

}

/* End of file Smsblast.php */
/* Location: ./application/controllers/employees/internalmemo/Smsblast.php */