<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Sallary_submenu extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		$this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

		//role setting untuk user tipe employee
		//$this->user_role->employees_sallary('read_access'); //employee submenu role access page
		$this->user_role->employees('read_access'); //employee role access page
	}

	public function index()
	{
		//pindahkan halaman
		redirect(site_url('employees/sallary/categories'));
	}

}

/* End of file Sallary_submenu.php */
/* Location: ./application/controllers/employees/sallary/Sallary_submenu.php */