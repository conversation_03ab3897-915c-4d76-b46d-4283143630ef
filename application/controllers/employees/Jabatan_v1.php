<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>abatan extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->employees_...('read_access'); //employee role access page | submenu
        $this->user_role->employees('read_access'); //employee role access page | menu
        
		$this->load->model('employees/Jabatan_model');
	}

	public function index()
	{
		$link = site_url('employees/jabatan/'); //URL dengan slash
        $data = array(
            'kolomID' => 'jabatan_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );

        //setting untuk form role
        $data['menurole_mainmenu'] = $this->user_role->accesslist('mainmenu');
        $data['menurole_mobilemenu'] = $this->user_role->accesslist('mobilemenu');
        $data['menurole_mobilemenu_authorization'] = $this->user_role->accesslist('mobilemenu_authorization');
        $data['menurole_stock_lv1'] = $this->user_role->accesslist('stock_lv1');
        $data['menurole_products_lv1'] = $this->user_role->accesslist('products_lv1');
        $data['menurole_purchase_lv1'] = $this->user_role->accesslist('purchase_lv1');

		$this->load->view('employees/jabatan/jabatan_v', $data);
	}

	public function json()
    {
        /* custom json output  start */
        $jsondata = $this->Jabatan_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->jabatan_id;
        	$name = htmlentities($a->name); //encoding string
        	$level = htmlentities($a->level); //encoding string
        	$action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
        	
        	$temp_data = array(
        		'jabatan_id' => $id,
        		'name' => $name,
        		'level' => $level,
        		'action' => $action
        		);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        if ($this->form_validation->run() == FALSE) {
            //error response
            if ($actionType=='create') {
                //$messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
                $error_message = 'Create Record Failed';
            }
            elseif ($actionType=='update') {
                //$messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
                $error_message = 'Update Record Failed';
            }

            $draw_json = array(
                'status' => 'error',
                'message'=> $error_message,
                'data'   => array(
                    'name' => form_error('name'),
                    'level' => form_error('level'),
                    'role' => form_error('menu[]'),
                    'role_mobile' => form_error('mobilerole[]')
                )
            );
        } else {
            /* mengetahui role yang dicentang */
            //ROLE MAIN MENU
            $checked_webrole = array();
            $mainmenulist = $this->user_role->accesslist('mainmenu');
            foreach ($mainmenulist as $mainmenu) {
                $checked = ($this->input->post('menu['.$mainmenu[0].']')=='on') ? true : false;
                $checked_webrole[$mainmenu[0]]['read'] = $checked;
            }

            //ROLE STOCK LV 1
            $stock_checked = false;
            $mainmenu_stock_lv1 = $this->user_role->accesslist('stock_lv1');
            foreach ($mainmenu_stock_lv1 as $menu) {
                $checked = ($this->input->post('stocklv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $stock_checked = true;
                }
            }
            if ($stock_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['stock']['read']=false;
            }

            //ROLE PRODUCTS LV 1
            $products_checked = false;
            $mainmenu_products_lv1 = $this->user_role->accesslist('products_lv1');
            foreach ($mainmenu_products_lv1 as $menu) {
                $checked = ($this->input->post('productslv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $products_checked = true;
                }
            }
            if ($products_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['products']['read']=false;
            }

            //ROLE PURCHASE LV 1
            $purchase_checked = false;
            $mainmenu_purchase_lv1 = $this->user_role->accesslist('purchase_lv1');
            foreach ($mainmenu_purchase_lv1 as $menu) {
                $checked = ($this->input->post('purchaselv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $purchase_checked = true;
                }
            }
            if ($purchase_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['purchase']['read']=false;
            }


            //ROLE MOBILE
            $checked_mobilerole = array();
            $mobilemenulist = $this->user_role->accesslist('mobilemenu');
            foreach ($mobilemenulist as $mobilemenu) {
                $checked = ($this->input->post('mobilerole['.$mobilemenu[0].']')=='on') ? true : false;
                $checked_mobilerole[$mobilemenu[0]] = $checked;
            }
            //--ROLE MOBILE AUTHORIZATION
            $checked_mobilerole['authorization'] = array();
            $mobilemenulist_authorization = $this->user_role->accesslist('mobilemenu_authorization');
            foreach ($mobilemenulist_authorization as $role) {
                $checked = ($this->input->post('mobilerole_authorization['.$role[0].']')=='on') ? true : false;
                if ($checked===true) {
                    $checked_mobilerole['authorization'][$role[0]] = true;
                }
            }
            

            //data yang dikirim
            $datapost = array(
                'name' => $this->input->post('name',TRUE),
                'level' => $this->input->post('level',TRUE),
                'role' => format_json($checked_webrole),
                'role_mobile' => format_json($checked_mobilerole)
            );

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                //action untuk create data
                $response = $this->Jabatan_model->insert($datapost);
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');
            }
            elseif ($actionType=='update') {
                //action untuk update data
                $response = $this->Jabatan_model->update($this->input->post('id', TRUE), $datapost);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');
            }
            
            //buat array untuk output json
            if ($response==true) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                );
            }
        }

        //buat output dalam format json
        echo format_json($draw_json);
    }

    public function get_data($id=null)
    {
        //inisialisasi
        $draw_json = array();

    	$row = $this->Jabatan_model->get_by_id($id);
        if ($row) {
            if ($row->admin_fkid==$this->session->userdata('admin_id')) { //cek apakah admin_fkid sama dengan admin_id user
                $data_row = array(
                    'id' => $row->jabatan_id,
                    'name' => html_entity_decode($row->name),
                    'level' => html_entity_decode($row->level),
                    'role' => json_decode($row->role),
                    'role_mobile' => json_decode($row->role_mobile)
                );
                $draw_json = array(
                    'status' => 'success',
                    'data' => $data_row
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Invalid Data'
                );
            }
        }
        else {
        	$this->session->set_flashdata('message', 'Record Not Found');
        	$draw_json = array(
	        		'status' => 'error',
	        		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
        		);
        }

        echo format_json($draw_json);
    }

    public function delete($id=null)
    {
    	$row = $this->Jabatan_model->get_by_id($id);

        if ($row) {
            $response = $this->Jabatan_model->delete($id);
            if ($response==true) {
            	$this->session->set_flashdata('message', 'Record Deleted');
            	$response = array(
            		'status' => 'success',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            } else {
            	$this->session->set_flashdata('message', 'Deleted Failed');
            	$response = array(
            		'status' => 'error',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
            	'status' => 'error',
            	'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            	);
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
    	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
    	$this->form_validation->set_rules('level', 'level', 'trim|required|is_numeric');
        $this->form_validation->set_rules('menu[]', 'Role', 'trim|required');
        $this->form_validation->set_rules('mobilerole[]', 'role mobile', 'trim');

    	$this->form_validation->set_rules('id', 'jabatan_id', 'trim');
    	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Jabatan.php */
/* Location: ./application/controllers/employees/Jabatan.php */