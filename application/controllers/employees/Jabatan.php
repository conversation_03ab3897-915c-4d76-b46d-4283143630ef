<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON> extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
        //Do your magic here
		$this->load->model('employees/Jabatan_model');
	}

	public function index()
	{
		$link = site_url('employees/jabatan/'); //URL dengan slash
        $data = array(
            'kolomID' => 'jabatan_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'datatables',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'create',
            'ajaxActionUpdate' => $link.'update',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );

        //setting untuk form role
        $data['menurole_web'] = $this->privilege->role_web();
        $data['menurole_mobilemenu'] = $this->user_role->accesslist('mobilemenu');
        $data['menurole_mobilemenu_authorization'] = $this->user_role->accesslist('mobilemenu_authorization');
        
        //output
        $data['page_title'] = 'Crew Position';
		$this->template->view('employees/jabatan/v2/jabatan_v2', $data);
	}

    public function datatables()
    {
        $jsondata = $this->Jabatan_model->datatables();
        $json_decode = json_decode($jsondata);
        $data = array();
        foreach ($json_decode->data as $a) {
            $data[] = array(
                'jabatan_id' => $a->jabatan_id,
                'name' => htmlentities($a->name),
                'level' => $a->level,
            );
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $data
        );

        //output
        echo format_json($draw_json);
    }

    public function create()
    {
        //init
        $draw_json = array();

        //datapost mobile auth
        $checked_mobilerole = $this->_roleMobile();
        
        //data yang dikirim
        $datapost = array(
            'name' => $this->input->post('name',TRUE),
            'level' => $this->input->post('level',TRUE),
            'role_mobile' => json_encode($checked_mobilerole)
        );

        //validasi
        $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
        $this->form_validation->set_rules('level', 'level', 'trim|required|is_numeric');
        $this->form_validation->set_rules('mobilerole[]', 'trim');
        $this->form_validation->set_error_delimiters('', '');

        if ($this->form_validation->run() == FALSE) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Create Record Failed',
                'data' => array(
                    'name' => form_error('name'),
                    'level' => form_error('level'),
                    'role' => form_error('mobilerole[]')
                )
            );
        } else {
            //insert data
            $response = $this->Jabatan_model->insert($datapost);
            if ($response) {
                $jabatan_id = $this->db->insert_id();
                $this->_roleWeb($jabatan_id);//insert web role

                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Create Record Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Create Record Error'
                );
            }
        }

        echo format_json($draw_json);
    }

    public function update()
    {
        //init
        $draw_json = array();

        //cek data
        $id = $this->input->post('id',true);
        $row = $this->Jabatan_model->get_by_id($id);
        if ($row) {
            //datapost mobile auth
            $checked_mobilerole = $this->_roleMobile();

            //data yang dikirim
            $datapost = array(
                'name' => $this->input->post('name',TRUE),
                'level' => $this->input->post('level',TRUE),
                'role_mobile' => json_encode($checked_mobilerole)
            );

            //validasi
            $this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
            $this->form_validation->set_rules('level', 'level', 'trim|required|is_numeric');
            $this->form_validation->set_rules('mobilerole[]', 'trim');
            $this->form_validation->set_error_delimiters('', '');

            if ($this->form_validation->run() == FALSE) {
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Create Record Failed',
                    'data' => array(
                        'name' => form_error('name'),
                        'level' => form_error('level'),
                        'role' => form_error('mobilerole[]')
                    )
                );
            } else {
                $response = $this->Jabatan_model->update($id, $datapost);
                if ($response) {
                    //delete role web lama
                    $this->Jabatan_model->delete_role($id);

                    //insert role jabatan baru
                    $this->_roleWeb($id);

                    $draw_json = array(
                        'status' => 'success',
                        'message' => 'Update Record Success'
                    );
                }
                else{
                    $draw_json = array(
                        'status' => 'error',
                        'message' => 'Update Record Error'
                    );
                }
            }
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Invalid ID'
            );
        }

        //output
        echo format_json($draw_json);
    }

    public function get_data($id=null)
    {
        //init
        $draw_json = array();

        //cek data
        $row = $this->Jabatan_model->get_by_id($id);
        if ($row) {
            $role = $this->Jabatan_model->get_role($id);
            $role_format = array();
            foreach ($role as $key => $r) {
                $role_format[] = array(
                    'url' => $r->url,
                    'view' => $r->role_view,
                    'add' => $r->role_add,
                    'edit' => $r->role_edit,
                    'delete' => $r->role_delete,
                    'access' => (is_json($r->role_access)) ? json_decode($r->role_access) : json_decode(json_encode(array()))
                );
            }
            $data = array(
                'id' => $row->jabatan_id,
                'name' => htmlentities($row->name),
                'level' => htmlentities($row->level),
                'role_mobile' => json_decode($row->role_mobile),
                'role' => $role_format
            );
            $draw_json = array(
                'status' => 'success',
                'data' => $data
            );
        }
        else{
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //output
        echo format_json($draw_json);
    }


    public function _roleWeb($jabatan_id)
    {
        //init
        $jabatan_fkid = $jabatan_id;

        //level1
        foreach ($this->privilege->role_web() as $keyLV1 => $mainmenu) {
            //init
            $level1_have = false;
            $level2_have = false;
            $level3_have = false;

            $mainmenu_role_view     = ($this->input->post($keyLV1.'_view')) ? true : false;
            $mainmenu_role_add      = ($this->input->post($keyLV1.'_add')) ? true : false;
            $mainmenu_role_edit     = ($this->input->post($keyLV1.'_edit')) ? true : false;
            $mainmenu_role_delete   = ($this->input->post($keyLV1.'_delete')) ? true : false;
            $mainmenu_role_access   = array();
            if ($this->input->post($keyLV1.'_accessdetail[]')) {
                foreach ($this->input->post($keyLV1.'_accessdetail[]') as $keyLV1ad  => $vad) {
                    //cek apakah inputan benar
                    if (isset($mainmenu['access_detail'][$keyLV1ad])) {
                        $obj_accessdetail = array($keyLV1ad=>true);
                        $mainmenu_role_access = array_merge($mainmenu_role_access, $obj_accessdetail);
                        $level1_have = true;
                    }
                }
            }
            $level1 = array(
                'jabatan_fkid' => $jabatan_fkid,
                'url' => !empty($mainmenu['url']) ? $mainmenu['url'] : false,
                'role_view' => $mainmenu_role_view,
                'role_add' => $mainmenu_role_add,
                'role_edit' => $mainmenu_role_edit,
                'role_delete' => $mainmenu_role_delete,
                'role_access' => ($mainmenu_role_access) ? json_encode($mainmenu_role_access) : false
            );
            if ($mainmenu_role_view) {
                $level1_have = true;
            }
            // $this->db->insert('employees_jabatan_role', $level1);

            //level2
            if (isset($mainmenu['sub'])) {
                foreach ($mainmenu['sub'] as $keyLV2 => $menulist) {
                    //init
                    $level2_have = false;

                    $menulist_role_view     = ($this->input->post($menulist['url'].'_view')) ? true : false;
                    $menulist_role_add      = ($this->input->post($menulist['url'].'_add')) ? true : false;
                    $menulist_role_edit     = ($this->input->post($menulist['url'].'_edit')) ? true : false;
                    $menulist_role_delete   = ($this->input->post($menulist['url'].'_delete')) ? true : false;
                    $menulist_role_access   = array();
                    if ($this->input->post($menulist['url'].'_accessdetail[]')) {
                        foreach ($this->input->post($menulist['url'].'_accessdetail[]') as $keyLV2ad  => $vad) {
                            //cek apakah inputan benar
                            if (isset($menulist['access_detail'][$keyLV2ad])) {
                                $obj_accessdetail = array($keyLV2ad=>true);
                                $menulist_role_access = array_merge($menulist_role_access, $obj_accessdetail);
                                $level2_have = true;
                                $level1_have = true;
                            }
                        }
                    }
                    $level2 = array(
                        'jabatan_fkid' => $jabatan_fkid,
                        'url' => !empty($menulist['url']) ? $menulist['url'] : false,
                        'role_view' => $menulist_role_view,
                        'role_add' => $menulist_role_add,
                        'role_edit' => $menulist_role_edit,
                        'role_delete' => $menulist_role_delete,
                        'role_access' => ($menulist_role_access) ? json_encode($menulist_role_access) : false
                    );
                    if ($menulist_role_view) {
                        $level2_have = true;
                    }
                    // $this->db->insert('employees_jabatan_role', $level2);

                    //level3
                    if (isset($menulist['sub'])) {
                        foreach ($menulist['sub'] as $keyLV3 => $menu) {
                            //init
                            $level3_have = false;

                            $menu_role_view     = ($this->input->post($menu['url'].'_view')) ? true : false;
                            $menu_role_add      = ($this->input->post($menu['url'].'_add')) ? true : false;
                            $menu_role_edit     = ($this->input->post($menu['url'].'_edit')) ? true : false;
                            $menu_role_delete   = ($this->input->post($menu['url'].'_delete')) ? true : false;
                            $menu_role_access   = array();
                            if ($this->input->post($menu['url'].'_accessdetail[]')) {
                                foreach ($this->input->post($menu['url'].'_accessdetail[]') as $keyLV3ad => $vad) {
                                    //cek apakah inputan benar
                                    if (isset($menu['access_detail'][$keyLV3ad])) {
                                        $obj_accessdetail = array($keyLV3ad=>true);
                                        $menu_role_access = array_merge($menu_role_access, $obj_accessdetail);
                                        $level3_have = true;
                                    }
                                }
                            }
                            $level3 = array(
                                'jabatan_fkid' => $jabatan_fkid,
                                'url' => !empty($menu['url']) ? $menu['url'] : false,
                                'role_view' => $menu_role_view,
                                'role_add' => $menu_role_add,
                                'role_edit' => $menu_role_edit,
                                'role_delete' => $menu_role_delete,
                                'role_access' => ($menu_role_access) ? json_encode($menu_role_access) : false
                            );
                            if ($menu_role_view) {
                                $level3_have = true;
                            }
                            if ($level3_have) {
                                $this->db->insert('employees_jabatan_role', $level3);
                            }
                        }
                    }
                    if ($level2_have) {
                        $this->db->insert('employees_jabatan_role', $level2);
                    }
                }
            }
            if ($level1_have==true) {
                $this->db->insert('employees_jabatan_role', $level1);
            }
        }
    }

    public function _roleMobile()
    {
        //datapost mobile auth
        $checked_mobilerole = array();
        $mobilemenulist = $this->user_role->accesslist('mobilemenu');
        foreach ($mobilemenulist as $mobilemenu) {
            $checked = ($this->input->post('mobilerole['.$mobilemenu[0].']')=='on') ? true : false;
            $checked_mobilerole[$mobilemenu[0]] = $checked;
        }
        //--ROLE MOBILE AUTHORIZATION
        $checked_mobilerole['authorization'] = array();
        $mobilemenulist_authorization = $this->user_role->accesslist('mobilemenu_authorization');
        foreach ($mobilemenulist_authorization as $role) {
            $checked = ($this->input->post('mobilerole_authorization['.$role[0].']')=='on') ? true : false;
            if ($checked===true) {
                $checked_mobilerole['authorization'][$role[0]] = true;
            }
        }
        return $checked_mobilerole;
    }

    public function delete($id=null)
    {
    	$row = $this->Jabatan_model->get_by_id($id);

        if ($row) {
            $response = $this->Jabatan_model->delete($id);
            if ($response==true) {
            	$this->session->set_flashdata('message', 'Record Deleted');
            	$response = array(
            		'status' => 'success',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            } else {
            	$this->session->set_flashdata('message', 'Deleted Failed');
            	$response = array(
            		'status' => 'error',
            		'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            		);
            }
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            $response = array(
            	'status' => 'error',
            	'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
            	);
        }

        //response output
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    public function _rules() 
    {
    	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
    	$this->form_validation->set_rules('level', 'level', 'trim|required|is_numeric');
        $this->form_validation->set_rules('menu[]', 'Role', 'trim|required');
        $this->form_validation->set_rules('mobilerole[]', 'role mobile', 'trim');

    	$this->form_validation->set_rules('id', 'jabatan_id', 'trim');
    	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }


    /* UNUSED DATA */
    public function json()
    {
        /* custom json output  start */
        $jsondata = $this->Jabatan_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id = $a->jabatan_id;
            $name = htmlentities($a->name); //encoding string
            $level = htmlentities($a->level); //encoding string
            $action  = "<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
            $action .= "&nbsp;&nbsp;&nbsp;";
            $action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
            
            
            $temp_data = array(
                'jabatan_id' => $id,
                'name' => $name,
                'level' => $level,
                'action' => $action
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo format_json($draw_json);
        /* custom json output  end */
    }

}

/* End of file Jabatan.php */
/* Location: ./application/controllers/employees/Jabatan.php */