<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Crewlist extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
        //proteksi halaman
        $this->protect_page->userlogged(TRUE); //halaman hanya bisa diakses bila login

        //role setting untuk user tipe employee
        //$this->user_role->employees_...('read_access'); //employee role access page | submenu
        $this->user_role->employees('read_access'); //employee role access page | menu
        
		$this->load->model('employees/Employee_model');
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('employees/Jabatan_model'); //form

        $this->load->library('user_key/User_key_lib');

        file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." using CrewList V1\n");
	}

	public function index()
	{
		$link = site_url('employees/crewlist/'); //URL dengan slash
        $data = array(
            'kolomID' => 'employee_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'json',
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActionUpdateUserRole' => $link.'update_user_role/',
            'ajaxActiongetDataEdit' => $link.'get_data/' //ambil data yang akan diedit
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();
        $data['form_select_jabatan'] = $this->Jabatan_model->form_select();

        //setting untuk form role
        $data['menurole_mainmenu'] = $this->user_role->accesslist('mainmenu');
        $data['menurole_mobilemenu'] = $this->user_role->accesslist('mobilemenu');
        $data['menurole_mobilemenu_authorization'] = $this->user_role->accesslist('mobilemenu_authorization');
        $data['menurole_stock_lv1'] = $this->user_role->accesslist('stock_lv1');
        $data['menurole_products_lv1'] = $this->user_role->accesslist('products_lv1');
        $data['menurole_purchase_lv1'] = $this->user_role->accesslist('purchase_lv1');

		$this->load->view('employees/crewlist/crewlist_v.php', $data);
	}

	public function json()
    {
        //header('Content-Type: application/json');
        //echo $this->Employee_model->json(); //default json output

        /* custom json output  start */
        $jsondata = $this->Employee_model->json(); //ambil data json
        $json_decode = json_decode($jsondata); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
        	$id = $a->employee_id;
        	$name = htmlentities($a->name); //encoding string
        	$address = htmlentities($a->address); //encoding string
        	$phone = htmlentities($a->phone); //encoding string
            //$outlet_name = htmlentities($a->outlet_name);
            $jabatan_name = htmlentities($a->jabatan_name);
            $email = htmlentities($a->email);
            $access_mode = htmlentities($a->access_mode);
            $access_status_web = htmlentities($a->access_status_web);
            $access_status_mobile = htmlentities($a->access_status_mobile);
            $level = $a->level;
        	$joindate = htmlentities($a->date_join);
        	

        	//$role = "<a href='javascript:void(0)' onclick='actionShowRole({$id})'>role</a>";
        	$role = "<button class='btn btn-primary btn-xs' onclick='actionShowRole({$id})'>Role</button>";
        	$role .= "&nbsp;&nbsp;&nbsp;";

        	$action  = $role."<a href='javascript:void(0)' onclick='actionEdit({$id})'><span class='glyphicon glyphicon-edit'></a>";
        	$action .= "&nbsp;&nbsp;&nbsp;";
        	$action .= "<a href='javascript:void(0)' onclick='actionDelete({$id})'><span class='glyphicon glyphicon-trash'></a>";
        	
        	
        	$temp_data = array(
        		'employee_id' => $id,
        		'name' => $name,
        		'address' => $address,
        		'phone' => $phone,
        		//'outlet_name' => $outlet_name,
        		'jabatan_name' => $jabatan_name,
                'email' => $email,
                'access_mode' => ucfirst($access_mode),
                'access_status_web' => ucfirst($access_status_web),
                'access_status_mobile' => ucfirst($access_status_mobile),
                'level' => $level,
                'join_date' => $joindate,
        		'action' => $action
        	);
        	array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
        	'draw' => $json_decode->draw,
        	'recordsTotal' => $json_decode->recordsTotal,
        	'recordsFiltered' => $json_decode->recordsFiltered,
        	'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }

    public function _employee_allow_edit($id)
    {
        if ($this->session->userdata('user_type')=='employee') {
            $myid = $this->session->userdata('user_id');
            $mydata_row = $this->Employee_model->get_by_id($myid); //get my level
            $mylevel = $mydata_row->level;

            $data_row = $this->Employee_model->get_by_id($id); //get data id


            if ($data_row->level > $mylevel) {
                $draw_json = array(
                    'status' => 'error',
                    'message'=> 'You don\'t allowed to modify this data'
                );

                echo format_json($draw_json);
                die();
            }
            else{
                $return['mylevel'] = $mylevel;
                return $return;
            }
        }
    }

    public function get_data($id=null)
    {
        //inisialisasi
        $draw_json = array();

        //cek apakah yang melakukan edit diizinkan
        $this->_employee_allow_edit($id);

        $row = $this->Employee_model->get_by_id($id);
        if ($row) {
            $data_row = array(
                'id' => ($row->employee_id),
                'name' => html_entity_decode($row->name),
                'address' => html_entity_decode($row->address),
                'phone' => html_entity_decode($row->phone),
                //'outlet' => html_entity_decode($row->outlet_fkid),
                'jabatan' => html_entity_decode($row->jabatan_fkid),
                'access_mode' => html_entity_decode($row->access_mode),
                'email' => html_entity_decode($row->email),
                'level' => $row->level,
                'role' => json_decode($row->role),
                'role_mobile' => json_decode($row->role_mobile)
            );

            //ambil daftar outlet yang dapat diakses
            $outlet_access = $this->Employee_model->get_access_outlet($id);
            $data_row_outlet = array();
            foreach ($outlet_access->result() as $a) {
                $temp = array(
                    'outlet_id' => $a->outlet_fkid
                );
                array_push($data_row_outlet, $temp);
            }

            $data_row['outlet'] = $data_row_outlet;
            $draw_json = array(
                'status' => 'success',
                'data' => $data_row
            );
        } else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        if ($this->session->userdata('user_type')=='employee' && ($id==$this->session->userdata('user_id'))) {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Action Forbidden!'
            );
        }
        echo format_json($draw_json);
    }

    public function action($actionType=null) //actionType = 'create / update'
    {
        $this->_rules(); //validasi rules

        //inisialisasi
        $draw_json = array();
        $email_used = false; //apakah email sudah dipakai atau belum

        //data yang dikirim melalui post
        $data_post = array(
            'id' => $this->input->post('id'),
            'name' => $this->input->post('name',TRUE),
            'address' => $this->input->post('address', TRUE),
            'phone' => $this->input->post('phone', TRUE),
            'outlet' => $this->input->post('outlet[]', TRUE),
            'jabatan_id' => $this->input->post('jabatan', TRUE),
            'level' => $this->input->post('level', TRUE),
            'access_mode' => $this->input->post('access_mode', TRUE),
            'email' => $this->input->post('email', TRUE),
            'pin' => $this->input->post('pin', TRUE)
        );

        //cek email sudah dipakai atau belum START
        switch ($actionType) {
            case 'create':
                //cek email apakah sudah dipakai atau belum
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('create', 'employee', $data_post);
                }
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('create', 'admin', $data_post);
                }
                break;
            case 'update':
                //cek email apakah sudah dipakai atau belum
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('update', 'employee', $data_post);
                }
                if ($email_used == false) {
                    $email_used = $this->_check_registered_email('update', 'admin', $data_post);
                }
                break;
            default: break;
        }
        //cek email sudah dipakai atau belum END


        if ($this->form_validation->run() === FALSE || $email_used === TRUE) {
            //error response
            if ($actionType=='create') {
                $messageCreateError = $this->session->set_flashdata('message', 'Error Record Failed');
            }
            elseif ($actionType=='update') {
                $messageUpdateError = $this->session->set_flashdata('message', 'Update Record Failed');
            }

            //custom error output
            $error_email = (!empty(form_error('email'))) ? form_error('email') : '';
            $error_email = (empty($error_email) && $email_used===true) ? '<span class="text-danger">This email already registered.</span>' : $error_email;

            $draw_json = array(
                'status' => 'error',
                'message'=> ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''),
                'data'   => array(
                    'name' => form_error('name'),
                    'address' => form_error('address'),
                    'phone' => form_error('phone'),
                    'outlet' => form_error('outlet[]'),
                    'jabatan' => form_error('jabatan'),
                    'level' => form_error('level'),
                    'access_mode' => form_error('access_mode'),
                    'email' => $error_email,
                    'pin' => form_error('pin'),
                )
            );
        } else {
            /*re-format value outlet yang dicentang untuk all outlet*/
            $outletSubmitted = $data_post['outlet'];
            if ($outletSubmitted[0]=='all_outlet') {
                $outletlist = $this->Outlets_model->form_select();
                $i=0;
                foreach ($outletlist as $value) {
                    $outletSubmitted[$i] = $value->outlet_id;
                    $i++;
                }
            }



            //get role by jabatan id
            $data_jabatan_row = $this->Jabatan_model->get_by_id($data_post['jabatan_id']);
            $data_post['role'] = ($data_jabatan_row) ? $data_jabatan_row->role : null;
            $data_post['role_mobile'] = ($data_jabatan_row) ? $data_jabatan_row->role_mobile : null;
            

            //deteksi actionType (create/update)
            if ($actionType=='create') {
                $data_post['level'] = ($data_jabatan_row) ? $data_jabatan_row->level : null;

                //action untuk create data
                $data_post_create = array(
                    'name' => $data_post['name'],
                    'address' => $data_post['address'],
                    'phone' => $data_post['phone'],
                    //'outlet_fkid' => $data_post['outlet_id'],
                    'jabatan_fkid' => $data_post['jabatan_id'],
                    'level'         => $data_post['level'],
                    'access_mode' => $data_post['access_mode'],
                    'role' => $data_post['role'],
                    'role_mobile' => $data_post['role_mobile']
                );
                //user activation status by officer type
                switch ($data_post['access_mode']) {
                    case 'all':
                        $data_post_create['email'] = $data_post['email'];
                        $data_post_create['pin'] = $data_post['pin'];
                        $data_post_create['access_status_web'] = 'pending';
                        $data_post_create['access_status_mobile'] = 'activated';
                        break;
                    case 'web':
                        $data_post_create['email'] = $data_post['email'];
                        $data_post_create['access_status_web'] = 'pending';
                        $data_post_create['access_status_mobile'] = 'deactivated';
                        break;
                    case 'mobile':
                        $data_post_create['pin'] = $data_post['pin'];
                        $data_post_create['access_status_web'] = 'deactivated';
                        $data_post_create['access_status_mobile'] = 'activated';
                        break;
                    default: break;
                }


                $response = $this->Employee_model->insert($data_post_create);
                $primary_key = $this->db->insert_id();
                $messageCreateSuccess = $this->session->set_flashdata('message', 'Create Record Success');

                if ($response) {
                    //tambahkan akses outlet employee
                    foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                        $data_akses_outlet['employee_fkid'] = $primary_key;
                        $data_akses_outlet['outlet_fkid'] = $checkedoutlet;

                        $inputDB_detail = $this->Employee_model->add_access_outlet($data_akses_outlet);
                    } //end foreach
                }

                //kirim email aktivasi untuk backoffice
                if ($response===true && ($data_post['access_mode']=='all' || $data_post['access_mode']=='web')) {
                    $this->user_key_lib->create_activation_key($data_post_create['email'], 'employee');
                }
            }
            elseif ($actionType=='update') {
                if ($this->session->userdata('user_type')=='employee') {
                    $id = $this->input->post('id');
                    $response = $this->_employee_allow_edit($id); //cek apakah employee diizinkan untuk mengedit data
                    if ($response['mylevel']<$data_post['level']) {
                        $draw_json = array(
                            'status' => 'error',
                            'message'=> 'You don\'t allowed to change level more than '.$response['mylevel']
                        );
                        echo format_json($draw_json);
                        die();
                    }
                    if ($this->input->post('id')==$this->session->userdata('user_id')) {
                        $draw_json = array(
                            'status' => 'error',
                            'message' => 'Action Forbidden!'
                        );
                        echo format_json($draw_json);
                        die();
                    }
                }


                //cek data yang akan diupdate
                $data_employee_row = $this->Employee_model->get_by_id($data_post['id']); //get data employee yang akan diupdate
                if ($data_employee_row) {
                    $data_row = array(
                        'id' => ($data_employee_row->employee_id),
                        'name' => html_entity_decode($data_employee_row->name),
                        'address' => html_entity_decode($data_employee_row->address),
                        'phone' => html_entity_decode($data_employee_row->phone),
                        'jabatan' => html_entity_decode($data_employee_row->jabatan_fkid),
                        'level' => html_entity_decode($data_employee_row->level),
                        'access_mode' => html_entity_decode($data_employee_row->access_mode),
                        'email' => html_entity_decode($data_employee_row->email),
                        'pin' => $data_employee_row->pin,
                        'role' => $data_employee_row->role, //json_decode($row->role)
                    );
                }

                //action untuk update data
                $data_post_update = array(
                    'name' => $data_post['name'],
                    'address' => $data_post['address'],
                    'phone' => $data_post['phone'],
                    //'outlet_fkid' => $data_post['outlet_id'],
                    'jabatan_fkid' => $data_post['jabatan_id'],
                    'level' => $data_post['level'],
                    'access_mode' => $data_post['access_mode'],
                );

                //update role bila jabatan diubah
                if ($data_post['jabatan_id'] != $data_row['jabatan']) {
                    $data_post_update['role'] = ($data_jabatan_row) ? $data_jabatan_row->role : null;
                }

                //update access_mode bila access_mode diubah
                if ($data_post['access_mode'] != $data_row['access_mode']) {
                    //officer setting
                    switch ($data_post['access_mode']) {
                        case 'all':
                            $data_post_update['email'] = $data_post['email'];
                            $data_post_update['password'] = null;
                            $data_post_update['pin'] = $data_post['pin'];
                            $data_post_update['access_status_web'] = 'pending';
                            $data_post_update['access_status_mobile'] = 'activated';

                            //KIRIM EMAIL AKTIVASI (NEXT)
                            break;
                        case 'mobile':
                            $data_post_update['email'] = null;
                            $data_post_update['password'] = null;
                            $data_post_update['pin'] = $data_post['pin'];
                            $data_post_update['access_status_web'] = 'deactivated';
                            $data_post_update['access_status_mobile'] = 'activated';
                            break;
                        case 'web':
                            $data_post_update['email'] = $data_post['email'];
                            $data_post_update['password'] = null; //password direset
                            $data_post_update['pin'] = null;
                            $data_post_update['access_status_web'] = 'pending';
                            $data_post_update['access_status_mobile'] = 'deactivated';

                            //KIRIM EMAIL AKTIVASI (NEXT)
                            break;
                        default: break;
                    }

                    //kirim email aktivasi untuk backoffice
                    if ($data_post['access_mode']=='all' || $data_post['access_mode']=='web') {
                        $this->user_key_lib->create_activation_key($data_post_update['email'],'employee'); //kirim aktivasi email
                    }
                }

                //kalau access mode masih sama
                if ($data_post['access_mode'] == $data_row['access_mode']) {
                    if ($data_post['access_mode']=='all' && ($data_post['email']!=$data_row['email'])) {
                        $data_post_update['email'] = $data_post['email'];
                        $data_post_update['password'] = null;
                        $data_post_update['access_status_web'] = 'pending';
                        $data_post_update['access_status_mobile'] = 'activated';

                        $this->user_key_lib->create_activation_key($data_post_update['email'],'employee'); //kirim email aktivasi
                    }
                    if ($data_post['access_mode']=='web' && ($data_post['email']!=$data_row['email'])) {
                        $data_post_update['email'] = $data_post['email'];
                        $data_post_update['password'] = null;
                        $data_post_update['access_status_web'] = 'pending';

                        $this->user_key_lib->create_activation_key($data_post_update['email'],'employee'); //kirim email aktivasi
                    }

                    if (!empty($data_post['pin'])) {
                        $data_post_update['pin'] = $data_post['pin'];
                    }
                }


                #update data
                $response = $this->Employee_model->update($this->input->post('id', TRUE), $data_post_update);
                $messageUpdateSuccess = $this->session->set_flashdata('message', 'Update Record Success');

                //akses outlet 
                if ($response) {
                    //hapus akses outlet lama
                    $deleteDB_detail = $this->Employee_model->delete_access_outlet($data_post['id']);

                    //tambahkan akses outlet employee
                    foreach ($outletSubmitted as $checkedoutlet) { //data checkbox outlet yang dikirim
                        $data_akses_outlet['employee_fkid'] = $data_post['id'];
                        $data_akses_outlet['outlet_fkid'] = $checkedoutlet;

                        $inputDB_detail = $this->Employee_model->add_access_outlet($data_akses_outlet);
                    } //end foreach
                }
            }
            
            //buat array untuk output json
            if ($response==true) {
                $draw_json = array(
                        'status' => 'success',
                        'message' => ($this->session->userdata('message') <> '' ? $this->session->userdata('message') : '')
                    );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Record Failed'
                );
            }
        }

        //buat output dalam format json
        echo format_json($draw_json);
    }

    public function update_user_role()
    {
        //inisialisasi
        $draw_json = array();

        //data yang dikirim
        $datapost = array(
            'id' => $this->input->post('id', true),
            'role' => $this->input->post('menu[]', true),
            'role_mobile' => $this->input->post('mobilerole[]')
        );

        $user_id = $datapost['id'];
        if (empty($user_id) || !is_numeric($user_id)) {
            $draw_json = array(
                'status' => 'success',
                'message' => 'Invalid ID'
            );
            echo format_json($draw_json);
            die();
        }

        //rules input
        $this->form_validation->set_rules('menu[]', 'role', 'trim|required');
        $this->form_validation->set_rules('id', 'id', 'trim|required');
        $this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');

        //validasi
        if ($this->form_validation->run() == FALSE) {
            //echo "apa ada yang salah?";
            $draw_json = array(
                'status' => 'error',
                'message' => 'Update Role Failed',
                'data' => array(
                    'role' => form_error('menu[]'),
                    'role_mobile' => form_error('mobilerole[]')
                )
            );
        } else {
            /* mengetahui role yang dicentang */
            //ROLE MAIN MENU
            $checked_webrole = array();
            $mainmenulist = $this->user_role->accesslist('mainmenu');
            foreach ($mainmenulist as $mainmenu) {
                $checked = ($this->input->post('menu['.$mainmenu[0].']')=='on') ? true : false;
                $checked_webrole[$mainmenu[0]]['read'] = $checked;
            }

            //ROLE STOCK LV 1
            $stock_checked = false;
            $mainmenu_stock_lv1 = $this->user_role->accesslist('stock_lv1');
            foreach ($mainmenu_stock_lv1 as $menu) {
                $checked = ($this->input->post('stocklv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $stock_checked = true;
                }
            }
            if ($stock_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['stock']['read']=false;
            }

            //ROLE PRODUCTS LV 1
            $products_checked = false;
            $mainmenu_products_lv1 = $this->user_role->accesslist('products_lv1');
            foreach ($mainmenu_products_lv1 as $menu) {
                $checked = ($this->input->post('productslv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $products_checked = true;
                }
            }
            if ($products_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['products']['read']=false;
            }

            //ROLE PURCHASE LV 1
            $purchase_checked = false;
            $mainmenu_purchase_lv1 = $this->user_role->accesslist('purchase_lv1');
            foreach ($mainmenu_purchase_lv1 as $menu) {
                $checked = ($this->input->post('purchaselv1['.$menu[0].']')=='on') ? true : false;
                $checked_webrole[$menu[0]]['read'] = $checked;

                //inisialisasi kalau ada stock yang dicentang
                if ($checked==true) {
                    $purchase_checked = true;
                }
            }
            if ($purchase_checked==false) {
                //disabled stock kalau tidak ada yang dicentang pada stock LV1
                $checked_webrole['purchase']['read']=false;
            }


            //ROLE MOBILE
            $checked_mobilerole = array();
            $mobilemenulist = $this->user_role->accesslist('mobilemenu');
            foreach ($mobilemenulist as $mobilemenu) {
                $checked = ($this->input->post('mobilerole['.$mobilemenu[0].']')=='on') ? true : false;
                $checked_mobilerole[$mobilemenu[0]] = $checked;
            }
            //--ROLE MOBILE AUTHORIZATION
            $checked_mobilerole['authorization'] = array();
            $mobilemenulist_authorization = $this->user_role->accesslist('mobilemenu_authorization');
            foreach ($mobilemenulist_authorization as $role) {
                $checked = ($this->input->post('mobilerole_authorization['.$role[0].']')=='on') ? true : false;
                if ($checked===true) {
                    $checked_mobilerole['authorization'][$role[0]] = true;
                }
            }
            

            //update role
            $datapost_update['role'] = format_json($checked_webrole);
            $datapost_update['role_mobile'] = format_json($checked_mobilerole);
            $response = $this->Employee_model->update($datapost['id'], $datapost_update);
            if ($response===TRUE) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Update Role Success'
                );
            }
            else{
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Update Role Failed on Server'
                );
            }
        }
        echo format_json($draw_json);
    }

    public function delete($id=null)
    {
        $draw_json = array();
        $row = $this->Employee_model->get_by_id($id);

        if ($row) {
            $response = $this->Employee_model->delete($id);
            if ($response) {
                $draw_json = array(
                    'status' => 'success',
                    'message' => 'Delete Record Success'
                );
            }
            else {
                $draw_json = array(
                    'status' => 'error',
                    'message' => 'Delete Record Failed'
                );
            }
        }
        else {
            $draw_json = array(
                'status' => 'error',
                'message' => 'Record Not Found'
            );
        }

        //buat output
        echo format_json($draw_json);
    }

    public function _rules() 
    {
    	$this->form_validation->set_rules('name', 'name', 'trim|required|max_length[30]');
    	$this->form_validation->set_rules('address', 'address', 'trim|required');
    	$this->form_validation->set_rules('phone', 'phone', 'trim|required|max_length[15]|is_numeric');
    	//$this->form_validation->set_rules('outlet', 'outlet', 'trim|required');
        $this->form_validation->set_rules('outlet[]', 'outlet', 'trim|required');
    	$this->form_validation->set_rules('jabatan', 'jabatan', 'trim|required');
        $this->form_validation->set_rules('access_mode', 'access mode', 'trim|required');
    	
        //set input rules berdasarkan action
        switch ($this->input->post('action')) {
            case 'create':
                switch ($this->input->post('access_mode')) {
                    case 'all':
                        $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                        $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                        break;
                    case 'web':
                        $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                        break;
                    case 'mobile':
                        $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                        break;
                    default:
                        //$this->input->post('access_mode') = '';
                        $_POST['access_mode'] = "";
                        break;
                }
                break;
            case 'update':
                //add level validation
                $this->form_validation->set_rules('level', 'level', 'trim|required|is_numeric');

                //get role by jabatan id
                $data_post['id'] = $this->input->post('id');
                $data_post['access_mode'] = $this->input->post('access_mode');

                //* data yang akan diupdate *//
                $data_employee_row = $this->Employee_model->get_by_id($data_post['id']); //get data employee yang akan diupdate
                if ($data_employee_row) {
                    $data_row = array(
                        'id' => ($data_employee_row->employee_id),
                        'name' => html_entity_decode($data_employee_row->name),
                        'address' => html_entity_decode($data_employee_row->address),
                        'phone' => html_entity_decode($data_employee_row->phone),
                        //'outlet' => html_entity_decode($data_employee_row->outlet_fkid),
                        'jabatan' => html_entity_decode($data_employee_row->jabatan_fkid),
                        'level' => html_entity_decode($data_employee_row->level),
                        'access_mode' => html_entity_decode($data_employee_row->access_mode),
                        'email' => html_entity_decode($data_employee_row->email),
                        'pin' => $data_employee_row->pin,
                        'role' => $data_employee_row->role, //json_decode($row->role)
                    );
                }

                //validasi bila access_mode diubah
                if ($data_post['access_mode'] != $data_row['access_mode']) {
                    switch ($data_post['access_mode']) {
                        case 'all':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'mobile':
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|required|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'web':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                            break;
                        default: break;
                    }
                }
                elseif ($data_post['access_mode']==$data_row['access_mode']) {
                    switch ($data_post['access_mode']) {
                        case 'all':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'mobile':
                            $this->form_validation->set_rules('pin', 'PIN', 'trim|min_length[5]|max_length[5]|is_numeric');
                            break;
                        case 'web':
                            $this->form_validation->set_rules('email', 'email', 'trim|required|valid_email');
                            break;
                        default: break;
                    }
                }
                break;
            default: break;
        }

    	$this->form_validation->set_rules('id', 'employee_id', 'trim');
    	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }
    public function _checkedMobileRole()
    {
        $draw_json = array(
            'inputkasmasuk' => (!empty($this->input->post('mobilerole[inputkasmasuk]'))) ? true : false,
            'inputkaskeluar' => (!empty($this->input->post('mobilerole[inputkaskeluar]'))) ? true : false,
            'inputpembelian' => (!empty($this->input->post('mobilerole[inputpembelian]'))) ? true : false,
            'setting_printer' => (!empty($this->input->post('mobilerole[setting_printer]'))) ? true : false,
            'tutupkasir' => (!empty($this->input->post('mobilerole[tutupkasir]'))) ? true : false,
            'reprint_nota' => (!empty($this->input->post('mobilerole[reprint_nota]'))) ? true : false,
            'reprint_order' => (!empty($this->input->post('mobilerole[reprint_order]'))) ? true : false,
            'gantiharga' => (!empty($this->input->post('mobilerole[gantiharga]'))) ? true : false,
            'gantidiskontaxgratuity' => (!empty($this->input->post('mobilerole[gantidiskontaxgratuity]'))) ? true : false,
            'bukalaciuang' => (!empty($this->input->post('mobilerole[bukalaciuang]'))) ? true : false,
            'pembayaran' => (!empty($this->input->post('mobilerole[pembayaran]'))) ? true : false,
            'compliment' => (!empty($this->input->post('mobilerole[compliment]'))) ? true : false,
            'voucher' => (!empty($this->input->post('mobilerole[voucher]'))) ? true : false,
            'duty' => (!empty($this->input->post('mobilerole[duty]'))) ? true : false,
            'gantidiskon' => (!empty($this->input->post('mobilerole[gantidiskon]'))) ? true : false,
            'gantipajak' => (!empty($this->input->post('mobilerole[gantipajak]'))) ? true : false,
            'reprint_refund' => (!empty($this->input->post('mobilerole[reprint_refund]'))) ? true : false,
            'refund' => (!empty($this->input->post('mobilerole[refund]'))) ? true : false,
            'reprint_void' => (!empty($this->input->post('mobilerole[reprint_void]'))) ? true : false,
            'inputbarangrusak' => (!empty($this->input->post('mobilerole[inputbarangrusak]'))) ? true : false,
            'inputbaranghabis' => (!empty($this->input->post('mobilerole[inputbaranghabis]'))) ? true : false,
            'inputpromo' => (!empty($this->input->post('mobilerole[inputpromo]'))) ? true : false,
        );
        return format_json($draw_json);
    }

    public function _check_registered_email($action, $table, $data)
    {
        $response = $this->Employee_model->check_registered_email($action, $table, $data);
        if ($response['data_count']>0) {
            $email_used = true;
        }
        else{
            $email_used = false;
        }

        return $email_used;
    }

}

/* End of file Crewlist.php */
/* Location: ./application/controllers/employees/Crewlist.php */