[{"mobile": {"inputkasmasuk": "Untuk menambah Cash awal pada saat Open Shift", "inputkaskeluar": "Menambah Operational Cost / Biaya Operational", "inputpembelian": "Coming Soon", "setting_printer": "Untuk Menambahkan Printer", "tutupkasir": "Melakuka<PERSON>", "reprint_nota": "Print ulang <PERSON>", "reprint_order": "Print ulang nota Order/Kitchen", "reprint_tutupkasir": "Print ulang nota akhir saat tutup kasir", "gantiharga": "Mengubah harga menu", "gantidiskonperbill": "Menambahkan diskon pada transaksi", "bukalaciuang": "Membuka cash drawer", "pembayaran": "Melakukan pem<PERSON>", "compliment": "Melakukan pembayaran dengan kompliment", "voucher": "<PERSON>ak<PERSON><PERSON> penambahan vocher pada satu transaksi", "gantivoucherperitem": "Melakukan penambahan vocher pada satu menu", "simpankeorderlist": "Menyimpan transaksi", "duty": "Melakukan pembayaran dengan duty meals", "gantidiskonperitem": "Menambahkan diskon pada satu menu", "gantipajak": "Menambahkan tax", "reprint_refund": "Mengeprint ulang transaksi yang sudah di refund", "refund": "Melakukan refund", "inputbarangrusak": "Coming Soon", "inputbaranghabis": "Menambahkan stock yang habis secara manual", "inputpromo": "Coming Soon", "masterlogin": "<PERSON><PERSON><PERSON><PERSON> login <PERSON>wal", "meja_split": "Memisah<PERSON> pembayaran pada satu transaksi", "meja_move": "<PERSON><PERSON><PERSON> meja", "viewcloseregister": "Melihat pendapatan shift sebelumnya", "viewtotalachievement": "Melihat total pendapatan pada shift yang sedang aktif", "viewtransactionhistory": "Melihat riwayat transaksi", "product_create": "Menambahkan produk", "print_dailyrecap": "Mengeprint rekapan transaksi pada saat tutup kasir", "authorization": "<PERSON><PERSON><PERSON><PERSON>", "otorisasi": {"diskonall": "<PERSON>i diskon semua <PERSON>", "diskonperitem": "Memberi diskon per Produk", "freeitem": "<PERSON><PERSON><PERSON> gratis ", "refund": "Melakukan Refund", "reprint_refund": "Print ulang nota Refund", "reprint_receipt": "Print ulang nota", "closeshift": "Melak<PERSON><PERSON> tutup kasir", "reprint_closeshift": "Print ulang nota tutup kasir", "void": "Melakukan Void"}}, "web": {"tes": "tes"}}]